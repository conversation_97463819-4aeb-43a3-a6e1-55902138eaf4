<?php

namespace FlashExpress\bi\App\Repository;

use App\Library\Validation\BuddyException;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\HrHcLogModel;
use FlashExpress\bi\App\Models\backyard\HrhcModel;
use FlashExpress\bi\App\Models\backyard\HrJdModel;
use FlashExpress\bi\App\Models\backyard\HrMyApprovalModel;
use FlashExpress\bi\App\Models\backyard\HrStateModel;
use FlashExpress\bi\App\Server\HcServer;
use FlashExpress\bi\App\Server\MessageQueueServer;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Models\backyard\HrStaffManageDepartmentModel;
use FlashExpress\bi\App\Models\backyard\SysDepartmentModel;
use FlashExpress\bi\App\Server\StaffServer;
use FlashExpress\bi\App\Server\SysServer;

class HcRepository extends BaseRepository
{
    public $db;
    public $db_rby;

    public function __construct()
    {
        parent::__construct();
        $this->db   = $this->getDI()->get('db');//by主库（写）
        $this->db_rby  = $this->getDI()->get('db_rby');//by从库（读）
    }

    /**
     * Hc列表
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function hcListNew($paramIn = [])
    {
        $searchWhere = isset($paramIn['searchWhere']) ? $paramIn['searchWhere'] : '';
        $pageSize    = isset($paramIn['page_size']) ? $paramIn['page_size'] : 10;
        $pageNum     = isset($paramIn['page_num']) ? $paramIn['page_num'] : 1;
        $export      = isset($paramIn['export']) ? $paramIn['export'] : 0;
        $fun         = isset($paramIn['fun']) ? $paramIn['fun'] : '';
        $userInfo    = isset($paramIn['userInfo']) ? $paramIn['userInfo'] : [];

        $builder = $this->modelsManager->createBuilder();
        $builder->from(['hr_hc' => HrhcModel::class]);
        $builder->where('hr_hc.deleted = 1');

        //过滤筛选条件两端空白
        array_walk($searchWhere,function (&$val,$key){
            if(!is_array($val)){
                $val = trim($val);
            }

        });

        //拼接where筛选条件
        if($searchWhere['submitter_id']){
            //submitter_id 筛选
            $builder->andWhere("hr_hc.submitter_id = :submitter_id:", [ "submitter_id" => $searchWhere['submitter_id'] ] );

        }
        if($searchWhere['hc_id']){
            //hc_id 筛选
            $builder->andWhere("hr_hc.hc_id = :hc_id:", [ "hc_id" => $searchWhere['hc_id'] ] );

        }

        //jd名称筛选
        if($searchWhere['job_name']){

            $jd_info =  HrJdModel::findFirst([
                'conditions' => ' job_name = :job_name: and state = 1 ',
                'bind' => ['job_name' => $searchWhere['job_name']],
            ]);
            $jd_id = 0;
            if($jd_info){
                $jd_id = $jd_info->job_id;
            }
            $builder->andWhere("hr_hc.job_id = :job_id:", [ "job_id" => $jd_id ] );

        }

        //职位名称 筛选
        if($searchWhere['job_title']){
            if (is_array($searchWhere['job_title'])) {
                $builder->andWhere("hr_hc.job_title IN ({job_title:array}) ", ["job_title" => array_values($searchWhere['job_title'])]);
            } else {
                $builder->andWhere("hr_hc.job_title = :job_title:", ["job_title" => $searchWhere['job_title']]);
            }
        }
            //部门
        if (!empty($searchWhere['department_id'])) {
            $deptIds = SysServer::getDepartmentConditionByParams($searchWhere);
            if (!empty($deptIds)) {
                $builder->inWhere('hr_hc.department_id', $deptIds);
            }
        }

        //工作地点（网点） 筛选
        if($searchWhere['worknode_id']){
            if(is_array($searchWhere['worknode_id'])){
                $builder->inWhere("hr_hc.worknode_id", $searchWhere['worknode_id']);
            }else{
                $builder->andWhere("hr_hc.worknode_id = :worknode_id:", [ "worknode_id" => $searchWhere['worknode_id'] ] );
            }
        }

        //用人原因 筛选
        if($searchWhere['reason_type']){

            $builder->andWhere("hr_hc.reason_type = :reason_type:", [ "reason_type" => $searchWhere['reason_type'] ] );

        }

        //最终审批时间-开始时间 筛选
        if($searchWhere['approval_completion_time_start']){
            $approval_completion_time_s = date("Y-m-d 00:00:00", strtotime($searchWhere['approval_completion_time_start']));

            $builder->andWhere(
                "hr_hc.approval_completion_time >= CONVERT_TZ('{$approval_completion_time_s}', '{$this->timezone}', '+00:00' )");
        }

        //最终审批时间-结束时间 筛选
        if($searchWhere['approval_completion_time_end']){
            $approval_completion_time_e = date("Y-m-d 23:59:59", strtotime($searchWhere['approval_completion_time_end']));

            $builder->andWhere(
                "hr_hc.approval_completion_time <= CONVERT_TZ('{$approval_completion_time_e}', '{$this->timezone}', '+00:00' )");
        }

        //创建时间-开始时间 筛选
        if($searchWhere['createtime_start']){
            $createtime_s = date("Y-m-d 00:00:00", strtotime($searchWhere['createtime_start']));

            $builder->andWhere(
                "hr_hc.createtime >= CONVERT_TZ('{$createtime_s}', '{$this->timezone}', '+00:00' )");
        }

        //创建时间-结束时间 筛选
        if($searchWhere['createtime_end']){
            $createtime_e = date("Y-m-d 23:59:59", strtotime($searchWhere['createtime_end']));

            $builder->andWhere(
                "hr_hc.createtime <= CONVERT_TZ('{$createtime_e}', '{$this->timezone}', '+00:00' )");
        }

        //截止时间-开始时间 筛选
        if($searchWhere['time_start']){
            $expirationdate_s = date("Y-m-d 00:00:00", strtotime($searchWhere['time_start']));

            $builder->andWhere(
                "hr_hc.expirationdate >= CONVERT_TZ('{$expirationdate_s}', '{$this->timezone}', '+00:00' )");
        }

        //截止时间-结束时间 筛选
        if($searchWhere['time_end']){
            $expirationdate_e = date("Y-m-d 23:59:59", strtotime($searchWhere['time_end']));

            $builder->andWhere(
                "hr_hc.expirationdate <= CONVERT_TZ('{.$expirationdate_e}', '{$this->timezone}', '+00:00' )");
        }

        //hc列表审批状态 筛选
        if ($searchWhere['approval_stage']) {
            switch ($searchWhere['approval_stage']){
                case 4:
                    $builder->andWhere("(hr_hc.approval_stage=3 or hr_hc.approval_stage=4) and hr_hc.approval_state_code=6 and hr_hc.state_code != 1");
                    break;
                case 3:
                    $builder->andWhere("hr_hc.approval_stage=3 and hr_hc.approval_state_code=7 and hr_hc.state_code = 1");
                    break;
                default:
                    $builder->andWhere("hr_hc.approval_stage={$searchWhere['approval_stage']} and hr_hc.state_code = 1");

                    break;
            }

        }


        //我的hc列表-审批状态 筛选
        if($searchWhere['approval_state_code']){
            $builder->andWhere("hr_hc.approval_state_code = :approval_state_code:", [ "approval_state_code" => $searchWhere['approval_state_code'] ] );

        }

        //状态 筛选
        if($searchWhere['state_code']){
            $builder->andWhere("hr_hc.state_code = :state_code:", [ "state_code" => $searchWhere['state_code'] ] );
        }

        //优先级 筛选
        if (isset($searchWhere['priority_id']) && $searchWhere['priority_id'] !== '') {
            $builder->andWhere("hr_hc.priority_id = :priority_id:", ["priority_id" => $searchWhere['priority_id']]);
        }

        //雇佣类型
        if ($searchWhere['hire_type']) {
            $builder->inWhere('hr_hc.hire_type', $searchWhere['hire_type']);
        }

        //数据权限
        if (isCountry('TH') || isCountry('MY') || isCountry('PH')){
            $is_menu_permission = SettingEnvServer::getMenuPermission('whr_hc_menu_list');
            if ($userInfo['is_admin'] == enums::$is_admin['off'] && $is_menu_permission && $fun != 'myHcList') {
                $authoritySql = (new HcRepository())->assembleAuthorityHcSql($userInfo);
                $builder->andWhere($authoritySql);
            }
        }

        //获取总页数
        $builder->columns('count(1) as cnt');
        $totalInfo  = $builder->getQuery()->getSingleResult();
        $this->getDI()->get('logger')->write_log("hcList-count-sql:" . var_export($builder->getQuery()->getSql(),true), 'info');
        $count = intval($totalInfo->cnt);//筛选总数

        //feature/week/v20221012
        $dataPage = [
            "count"     => intval($count),
            "pageCount" => ceil( $count / $pageSize ),
            "pageNum"   => intval($pageNum),
            "pageSize"  => $pageSize,
        ];

        if (!empty($paramIn['is_count'])) {
            return $dataPage;
        }

        //读取所需字段
        $builder->columns([
            'hr_jd.job_name',
            'hr_state.state_value',
            'hr_hc.hc_id',
            'hr_hc.serial_no',
            'hr_hc.job_id',
            'hr_hc.department_id',
            'hr_hc.type',
            'hr_hc.worknode_id',
            'hr_hc.expirationdate',
            'hr_hc.demandnumber',
            'hr_hc.surplusnumber',
            'hr_hc.remarks',
            'hr_hc.reason',
            'hr_hc.submitter_id',
            'hr_hc.country_code',
            'hr_hc.province_code',
            'hr_hc.city_code',
            'hr_hc.district_code',
            'hr_hc.state_code',
            'hr_hc.updated_at',
            'hr_hc.createtime',
            'hr_hc.priority_id',
            'hr_hc.approval_stage',
            'hr_hc.approval_state_code',
            'hr_hc.workflow_role',
            'hr_hc.approval_completion_time',
            'hr_hc.reason_type',
            'hr_hc.job_title as job_title_id',
            'hr_hc.hire_type',
            'hr_hc.hire_times',
            'hr_hc.manager_staff_id',
            "CONVERT_TZ(hr_hc.createtime, '+00:00', '{$this->timezone}' ) AS createtime",
            "CONVERT_TZ(hr_hc.updated_at, '+00:00', '{$this->timezone}' ) AS updated_at",
            "CONVERT_TZ(hr_hc.approval_completion_time, '+00:00', '{$this->timezone}' ) AS approval_completion_time",
            "DATE_FORMAT(CONVERT_TZ(hr_hc.expirationdate, '+00:00', '{$this->timezone}' ),'%Y-%m-%d') as expirationdate",
            'hr_hc.working_day_rest_type',
        ]);

        $builder->leftJoin(HrJdModel::class," hr_hc.job_id=hr_jd.job_id","hr_jd");
        $builder->leftJoin(HrStateModel::class," hr_hc.state_code=hr_state.state_code","hr_state");


        $builder->orderBy('hr_hc.hc_id desc');
        if ($export == 1) {
            $builder->limit(enums::EXPORT_SYNC_MAX_NUM_LIMIT);
        }else{
            $builder->limit($pageSize,($pageNum - 1) * $pageSize);
        }

        $dataHc = $builder->getQuery()->execute()->toArray();

        $this->getDI()->get('logger')->write_log("hcList-result-sql:" . var_export($builder->getQuery()->getSql(),true), 'info');

        /* 拼装返回数组 */
        $returnData['data']       = $dataHc ? $dataHc : [];
        $returnData['pagination'] = $dataPage ? $dataPage : [];
        return $returnData;
    }

    /**
     * 数据权限 hc列表 sql拼接
     * @param $userInfo
     * @return string
     */
    public function assembleAuthorityHcSql($userInfo)
    {
        $hcServer = new HcServer();
        $authority_stores_ids     = $userInfo['permission_stores_ids']; //网点权限

        if (!empty($authority_stores_ids) && is_array($authority_stores_ids)) {
            if (!in_array('-2', $authority_stores_ids)) {
                $authority_stores_ids = array_intersect($authority_stores_ids, $hcServer->getHcAllStoreId());
            }
        }
        
        $authority_department_ids = $userInfo['permission_department_ids'];   //部门权限
        $staff_info_id            = $userInfo['id'];
        $is_whr_permission        = $userInfo['is_whr_permission'];
        $staffDepartmentist = [];
        $deptManager        = [];
        if (false === $is_whr_permission) {
            //查看是否配置权限
            //查看是否配置管辖范围-部门
            $staffDepartmentist = HrStaffManageDepartmentModel::find([
                'conditions' => ' staff_info_id = :staff_info_id: and deleted = :deleted: and type = :type:',
                'bind'       => [
                    'staff_info_id' => intval($staff_info_id),
                    'type'          => HrStaffManageDepartmentModel::$type_1,
                    'deleted'       => enums::IS_DELETED_NO,
                ],
            ])->toArray();
            //判断是不是 部门负责人 & 部门负责人的助理
            $deptManager = SysDepartmentModel::findFirst([
                'conditions' => '(manager_id = :manager_id: or assistant_id = :assistant_id:) and type IN(2,3) and deleted = :deleted:',
                'bind'       => [
                    'manager_id'   => $staff_info_id,
                    'assistant_id' => $staff_info_id,
                    'deleted'      => enums::IS_DELETED_NO,
                ],
            ]);
        }
        if ($staffDepartmentist || $deptManager) {
            $workStr = '';
            if ($staffDepartmentist) {
                $workStr = ' and hr_hc.worknode_id = -1';
            }
            $where = '(';
            //获取部门下Head office
            if (empty($authority_department_ids)) {
                $authority_department_ids = 0;
                $where                    .= " hr_hc.department_id = " . $authority_department_ids;
            } else {
                if (is_array($authority_department_ids)) {
                    $authority_department_ids = getIdsStr($authority_department_ids);
                    $where                    .= " hr_hc.department_id IN (" . $authority_department_ids . ") ";
                } else {
                    $where .= " hr_hc.department_id = " . $authority_department_ids . ") ";
                }
            }
            $where .= " {$workStr} ) OR ( ";
            //网点
            if (empty($authority_stores_ids)) {
                $where .= " hr_hc.worknode_id = '' ";
            } else {
                if ($authority_stores_ids == '-2' || in_array('-2', $authority_stores_ids)) {
                    //-2 是全部网点 不包含 -1 网点
                    $authority_stores_ids = -1;
                    $where                .= " hr_hc.worknode_id != " . $authority_stores_ids;
                } else {
                    if (is_array($authority_stores_ids)) {
                        $authority_stores_ids = getIdsStr($authority_stores_ids);
                        $where                .= " hr_hc.worknode_id IN (" . $authority_stores_ids . ") ";
                    } else {
                        $where .= " hr_hc.worknode_id = " . $authority_stores_ids . " ";
                    }
                }
            }
            $where .= " )";
        } else {
            //网点
            $where = '(';
            if (empty($authority_stores_ids)) {
                $where .= " hr_hc.worknode_id = '' ";
            } else {
                if ($authority_stores_ids == '-2' || in_array('-2', $authority_stores_ids)) {
                    //-2 是全部网点 不包含 -1 网点
                    $authority_stores_ids = -1;
                    $where                .= " hr_hc.worknode_id != " . $authority_stores_ids;
                } else {
                    if (is_array($authority_stores_ids)) {
                        $authority_stores_ids = getIdsStr($authority_stores_ids);
                        $where                .= " hr_hc.worknode_id IN (" . $authority_stores_ids . ") ";
                    } else {
                        $where .= " hr_hc.worknode_id = " . $authority_stores_ids . " ";
                    }
                }
            }
            $where .= " )";
        }
        return $where;
    }

    /**
     * 我的审批列表
     * 该方法已废弃，不要再调用了，用下面新方法代替
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function myApprovalList($paramIn = [])
    {
        $searchWhere = isset($paramIn['searchWhere']) ? $paramIn['searchWhere'] : '';
        $pageSize    = isset($paramIn['page_size']) ? $paramIn['page_size'] : 10;
        $pageNum     = isset($paramIn['page_num']) ? $paramIn['page_num'] : 1;
        $staffId     = isset($paramIn['staff_id']) ? $paramIn['staff_id'] : '';

        /* 拼装sql */
        $sql      = "select 
                hr_jd.job_name,
                hr_state.state_value,
                hr_myapproval.state_code AS myapproval_state_code,
                hr_hc.hc_id,
                hr_hc.serial_no,
                hr_hc.job_id,
                hr_hc.department_id,
                hr_hc.type,
                hr_hc.worknode_id,
                hr_hc.expirationdate,
                hr_hc.demandnumber,
                hr_hc.surplusnumber,
                hr_hc.remarks,
                hr_hc.reason,
                hr_hc.submitter_id,
                hr_hc.country_code,
                hr_hc.province_code,
                hr_hc.city_code,
                hr_hc.district_code,
                hr_hc.state_code,
                hr_hc.updated_at,
                hr_hc.createtime,
                hr_hc.priority_id,
                hr_hc.approval_stage,
                hr_hc.approval_state_code,
                hr_hc.workflow_role,
                hr_hc.approval_completion_time,
                hr_hc.reason_type,
                hr_hc.job_title as job_title_id,
                hr_myapproval.submitter_id AS approval_submitter_id, 
                CONVERT_TZ(hr_hc.createtime, '+00:00', '" . $this->timezone . "' ) AS createtime,
                CONVERT_TZ(hr_hc.updated_at, '+00:00', '" . $this->timezone . "' ) AS updated_at,
                DATE_FORMAT(CONVERT_TZ(hr_hc.expirationdate, '+00:00', '" . $this->timezone . "' ),'%Y-%m-%d') as expirationdate 
                from hr_myapproval 
                left join hr_hc on hr_hc.hc_id = hr_myapproval.hc_id 
                left join hr_state on hr_state.state_code = hr_myapproval.state_code 
                LEFT JOIN hr_jd ON hr_hc.job_id = hr_jd.job_id 
                where 
                hr_myapproval.submitter_id=" . $staffId;
        $sqlCount = "select 
                          hr_hc.hc_id 
                          from hr_myapproval 
                          left join hr_hc on hr_hc.hc_id = hr_myapproval.hc_id 
                          left join hr_state on hr_state.state_code = hr_myapproval.state_code 
                          LEFT JOIN hr_jd ON hr_hc.job_id = hr_jd.job_id 
                          where 
                          hr_myapproval.submitter_id=" . $staffId;
        if (!empty($searchWhere)) {
            $sql      .= ' and ';
            $sqlCount .= ' and ';
            $i        = 1;
            foreach ($searchWhere as $k => $v) {
                $v              = str_replace('"', '', $v);
                if ($k == 'job_name') {
                    $sql      .= ' hr_hc.job_id' . ' in (select job_id from hr_jd where job_name="' . $v . '")';
                    $sqlCount .= ' hr_hc.job_id' . ' in (select job_id from hr_jd where job_name="' . $v . '")';
                } else if ($k == 'time_start') {
                    $expirationdate_s = date("Y-m-d 00:00:00", strtotime($v));
                    $sql              .= ' hr_hc.expirationdate>="' . $expirationdate_s . '"';
                    $sqlCount         .= ' hr_hc.expirationdate>="' . $expirationdate_s . '"';
                } else if ($k == 'time_end') {
                    $expirationdate_e = date("Y-m-d 23:59:59", strtotime($v));
                    $sql              .= ' hr_hc.expirationdate<="' . $expirationdate_e . '"';
                    $sqlCount         .= ' hr_hc.expirationdate<="' . $expirationdate_e . '"';
                } else if ($k == 'createtime_start') {
                    $createtime_s = date("Y-m-d 00:00:00", strtotime($v));
                    $sql          .= ' hr_hc.createtime>=CONVERT_TZ("' . $createtime_s . '", "' . $this->timezone . '", "+00:00")';
                    $sqlCount     .= ' hr_hc.createtime>=CONVERT_TZ("' . $createtime_s . '", "' . $this->timezone . '", "+00:00")';
                } else if ($k == 'createtime_end') {
                    $createtime_e = date("Y-m-d 23:59:59", strtotime($v));
                    $sql          .= ' hr_hc.createtime<=CONVERT_TZ("' . $createtime_e . '", "' . $this->timezone . '", "+00:00")';
                    $sqlCount     .= ' hr_hc.createtime<=CONVERT_TZ("' . $createtime_e . '", "' . $this->timezone . '", "+00:00")';
                } else if ($k == 'state_code') {
                    if ($v == 7) {
                        $sql      .= sprintf("hr_myapproval. %s = %s and hr_hc.state_code = 1 and approval_state_code = 7", $k, $v);
                        $sqlCount .= sprintf("hr_myapproval. %s = %s and hr_hc.state_code = 1 and approval_state_code = 7", $k, $v);
                    } else {
                        $sql      .= sprintf("hr_myapproval. %s = %s", $k, $v);
                        $sqlCount .= sprintf("hr_myapproval. %s = %s", $k, $v);
                    }
                } else if ($k == 'job_title_id') {
                    $sql      .= " hr_hc.job_title = {$v}";
                    $sqlCount .= " hr_hc.job_title = {$v}";
                } else {
                    $sql      .= ' hr_hc.' . $k . '="' . $v . '"';
                    $sqlCount .= ' hr_hc.' . $k . '="' . $v . '"';
                }
                if ($i !== count($searchWhere)) {
                    $sql      .= ' and ';
                    $sqlCount .= ' and ';
                }
                $i++;
            }
        }
        $sql .= ' ORDER BY hr_myapproval.updatetime desc';
        $sql .= " limit " . ($pageNum - 1) * $pageSize . "," . $pageSize;
        /* 获取hc列表数据 */
        $data   = $this->getDI()->get('db_rby')->query($sql);
        $dataHc = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);

        /* 获取分页数组 */
        $paramPage = [
            'sql'       => $sqlCount,
            'page_size' => $pageSize,
            'page_num'  => $pageNum,

        ];
        $dataPage  = self::getPageArr($paramPage);

        /* 拼装返回数组 */
        $returnData['data']       = $dataHc ? $dataHc : [];
        $returnData['pagination'] = $dataPage ? $dataPage : [];

        return $returnData;
    }

    /**
     * 我的审批列表
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function myApprovalListNew($paramIn = [])
    {
        $searchWhere = isset($paramIn['searchWhere']) ? $paramIn['searchWhere'] : '';
        $pageSize    = isset($paramIn['page_size']) ? $paramIn['page_size'] : 10;
        $pageNum     = isset($paramIn['page_num']) ? $paramIn['page_num'] : 1;
        $staffId     = isset($paramIn['staff_id']) ? $paramIn['staff_id'] : '';


        array_walk($searchWhere,function (&$val,$key){
            //过滤筛选条件两端空白
            if(!is_array($val)){
                $val = trim($val);
            }
            //历史代码就有此处理，具体哪个字段不知道，暂时保留
            $val = str_replace('"', '', $val);

        });

        $builder = $this->modelsManager->createBuilder();
        $builder->from(['hr_myapproval' => HrMyApprovalModel::class]);
        $builder->leftJoin(HrhcModel::class," hr_myapproval.hc_id=hr_hc.hc_id","hr_hc");
        $builder->where('hr_myapproval.submitter_id ='.$staffId);

        //jd名称筛选
        if($searchWhere['job_name']){

            $jd_info =  HrJdModel::findFirst([
                'conditions' => ' job_name = :job_name: and state = 1 ',
                'bind' => ['job_name' => $searchWhere['job_name']],
            ]);
            $jd_id = 0;
            if($jd_info){
                $jd_id = $jd_info->job_id;
            }
            $builder->andWhere("hr_hc.job_id = :job_id:", [ "job_id" => $jd_id ] );

        }

        //创建时间-开始时间 筛选
        if($searchWhere['createtime_start']){
            $createtime_s = date("Y-m-d 00:00:00", strtotime($searchWhere['createtime_start']));

            $builder->andWhere(
                "hr_hc.createtime >= CONVERT_TZ('{$createtime_s}', '{$this->timezone}', '+00:00' )");
        }

        //创建时间-结束时间 筛选
        if($searchWhere['createtime_end']){
            $createtime_e = date("Y-m-d 23:59:59", strtotime($searchWhere['createtime_end']));

            $builder->andWhere(
                "hr_hc.createtime <= CONVERT_TZ('{$createtime_e}', '{$this->timezone}', '+00:00' )");
        }

        //截止时间-开始时间 筛选
        if($searchWhere['time_start']){
            $expirationdate_s = date("Y-m-d 00:00:00", strtotime($searchWhere['time_start']));

            $builder->andWhere(
                "hr_hc.expirationdate >= CONVERT_TZ('{$expirationdate_s}', '{$this->timezone}', '+00:00' )");
        }

        //截止时间-结束时间 筛选
        if($searchWhere['time_end']){
            $expirationdate_e = date("Y-m-d 23:59:59", strtotime($searchWhere['time_end']));

            $builder->andWhere(
                "hr_hc.expirationdate <= CONVERT_TZ('{.$expirationdate_e}', '{$this->timezone}', '+00:00' )");
        }

        //审批状态 筛选
        if (isset($searchWhere['state_code'])) {

            $builder->andWhere("hr_myapproval.state_code={$searchWhere['state_code']}");

            if($searchWhere['state_code'] == 7){
                $builder->andWhere("hr_hc.state_code = 1 and  hr_hc.approval_state_code = 7");
            }

        }

//        //该筛选字段不存在，存在的是下面的job_title这个筛选，但是历史代码中有该字段筛选，暂时屏蔽
//        if($searchWhere['job_title_id']){
//
//            $builder->andWhere("hr_hc.job_title = :job_title:", [ "job_title" => $searchWhere['job_title_id'] ] );
//
//        }
//
        //todo ps:以下筛选项是根据server层传入参数字段来添加的

        //职位名称 筛选
        if($searchWhere['job_title']){

            $builder->andWhere("hr_hc.job_title = :job_title:", [ "job_title" => $searchWhere['job_title'] ] );

        }

//        if($searchWhere['department_id']){
//
//            $builder->andWhere("hr_hc.department_id = :department_id:", [ "department_id" => $searchWhere['department_id'] ] );
//
//        }
        //部门
        if (!empty($searchWhere['department_id'])) {
            $deptIds = SysServer::getDepartmentConditionByParams($searchWhere);
            if (!empty($deptIds)) {
                $builder->inWhere('hr_hc.department_id', $deptIds);
            }
        }

        if($searchWhere['worknode_id']){

            $builder->andWhere("hr_hc.worknode_id = :worknode_id:", [ "worknode_id" => $searchWhere['worknode_id'] ] );

        }

        if($searchWhere['hc_id']){

            $builder->andWhere("hr_hc.hc_id = :hc_id:", [ "hc_id" => $searchWhere['hc_id'] ] );

        }

        if (!empty($searchWhere['priority_id'])) {
            $builder->andWhere("hr_hc.priority_id = :priority_id:", [ "priority_id" => $searchWhere['priority_id'] ] );
        }

        //新版本用这个字段 兼容客户端
        if (isset($searchWhere['search_priority_id']) && $searchWhere['search_priority_id'] !== '') {
            $builder->andWhere("hr_hc.priority_id = :search_priority_id:", [ "search_priority_id" => $searchWhere['search_priority_id'] ] );
        }

        if(isset($searchWhere['approval_state_code'])){
            $builder->andWhere("hr_hc.approval_state_code = :approval_state_code:", [ "approval_state_code" => $searchWhere['approval_state_code'] ] );
        }

        if(isset($searchWhere['approval_stage'])){

            $builder->andWhere("hr_hc.approval_stage = :approval_stage:", [ "approval_stage" => $searchWhere['approval_stage'] ] );

        }

        if($searchWhere['job_title']){

            $builder->andWhere("hr_hc.job_title = :job_title:", [ "job_title" => $searchWhere['job_title'] ] );

        }
        //雇佣类型
        if ($searchWhere['hire_type']) {
            $builder->inWhere('hr_hc.hire_type', $searchWhere['hire_type']);
        }



        //获取总页数
        $builder->columns('count(1) as cnt');
        $totalInfo  = $builder->getQuery()->getSingleResult();
        $this->getDI()->get('logger')->write_log("hcList-count-sql:" . var_export($builder->getQuery()->getSql(),true), 'info');
        $count = intval($totalInfo->cnt);//筛选总数
        $dataPage = [
            "count"     => intval($count),
            "pageCount" => ceil( $count / $pageSize ),
            "pageNum"   => intval($pageNum),
            "pageSize"  => $pageSize,
        ];
        //读取所需字段
        $builder->columns([
            'hr_jd.job_name',
            'hr_myapproval.state_code AS myapproval_state_code',
            'hr_hc.hc_id',
            'hr_hc.serial_no',
            'hr_hc.job_id',
            'hr_hc.department_id',
            'hr_hc.type',
            'hr_hc.worknode_id',
            'hr_hc.expirationdate',
            'hr_hc.demandnumber',
            'hr_hc.surplusnumber',
            'hr_hc.remarks',
            'hr_hc.reason',
            'hr_hc.submitter_id',
            'hr_hc.country_code',
            'hr_hc.province_code',
            'hr_hc.city_code',
            'hr_hc.district_code',
            'hr_hc.state_code',
            'hr_hc.updated_at',
            'hr_hc.createtime',
            'hr_hc.priority_id',
            'hr_hc.approval_stage',
            'hr_hc.approval_state_code',
            'hr_hc.workflow_role',
            'hr_hc.approval_completion_time',
            'hr_hc.reason_type',
            'hr_hc.job_title as job_title_id',
            'hr_hc.hire_type',
            'hr_myapproval.submitter_id AS approval_submitter_id' ,
            "CONVERT_TZ(hr_hc.createtime, '+00:00', '" . $this->timezone . "' ) AS createtime",
            "CONVERT_TZ(hr_hc.updated_at, '+00:00', '" . $this->timezone . "' ) AS updated_at",
            "DATE_FORMAT(CONVERT_TZ(hr_hc.expirationdate, '+00:00', '" . $this->timezone . "' ),'%Y-%m-%d') as expirationdate",
        ]);
        $builder->leftJoin(HrJdModel::class," hr_hc.job_id = hr_jd.job_id","hr_jd");
        $builder->orderBy('hr_myapproval.updatetime desc');
        $builder->limit($pageSize,($pageNum - 1) * $pageSize);
        $dataHc = $builder->getQuery()->execute()->toArray();

        $this->getDI()->get('logger')->write_log("hcList-result-sql:" . var_export($builder->getQuery()->getSql(),true), 'info');
        /* 拼装返回数组 */
        $returnData['data']       = $dataHc ? $dataHc : [];
        $returnData['pagination'] = $dataPage ? $dataPage : [];

        return $returnData;
    }

    /**
     * 检查hcId是否存在
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function checkHc($paramIn = [])
    {
        $hcId        = $paramIn['hc_id'];
        $submitterId = $paramIn['submitter_id'];
        $stateCode   = $paramIn['state_code'];
        $for_update   = $paramIn['for_update'] ?? false;
        if (empty($hcId)) {
            return [];
        }
        $sql = "select * from hr_hc where hc_id=" . $hcId;
        if ($submitterId) {
            $sql .= " and submitter_id =" . $submitterId;
        } else if ($stateCode) {
            $sql .= " and state_code=" . $stateCode;
        }
        if ($for_update){
            $sql .= " for update";
        }
        // ToDo 不要改从库
        $data       = $this->getDI()->get('db')->query($sql);
        $returnData = $data->fetch(\Phalcon\Db::FETCH_ASSOC);
        $this->getDI()->get('logger')->write_log(['hcInfo' => $returnData], 'info');
        return $returnData;
    }

    /**
     * 创建Hc
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function insertHc($paramIn = [])
    {
        $insetHc        = isset($paramIn['inset_hc']) ? $paramIn['inset_hc'] : [];
        $staffId        = $paramIn['staff_id'];
        $positionId     = $paramIn['position_id'];
        $mangerStaffIds = $paramIn['manger_staff_ids'];
        $workflowData   = $paramIn['workflowData'];

        $db = $this->getDI()->get('db');
        $db->begin();
        $hcId = '';
        try {
            /* 入库 */
            $result = $db->insertAsDict(
                'hr_hc', $insetHc
            );
            $hcId   = $db->lastInsertId();
            /* 添加进审批流 */
            $insetApprovalData = [
                'hc_id'        => $hcId,
                'submitter_id' => $staffId,
                'reason'       => '',
                'position_id'  => $positionId,
                'staff_flag'   => 1,
                'staff_ids'    => $mangerStaffIds,
                'state_code'   => 8,
            ];
            $result            = $db->insertAsDict(
                'hr_approval', $insetApprovalData
            );
            /* 添加我的审批 */
            $staffIdsArr = $workflowData[1]['staff_id'];
            foreach ($staffIdsArr as $k => $v) {
                $staffIdArr = explode(',', $v['staff_id']);
                foreach ($staffIdArr as $kk => $vv) {
                    /* 添加进我的审批表 */
                    $insetMyApprovalData = [
                        'hc_id'        => $hcId,
                        'submitter_id' => $vv,
                        'staff_ids'    => $mangerStaffIds,
                        'state_code'   => 7,
                    ];
                    $result              = $db->insertAsDict(
                        'hr_myapproval', $insetMyApprovalData
                    );
                }

            }
        } catch (\Exception $e) {
            /* 有异常回滚 */
            $db->rollback();
        }
        $db->commit();
        return $hcId;
    }

    /**
     * 修改Hc
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function updateHc($paramIn)
    {
        $hc_id                    = $paramIn['hc_id'];
        $stateCode                = $paramIn['state_code'];
        $jobId                    = $paramIn['job_id'];
        $departmentId             = $paramIn['department_id'];
        $type                     = $paramIn['type'];
        $worknodeId               = $paramIn['worknode_id'];
        $expirationdate           = $paramIn['expirationdate'];
        $demandnumber             = $paramIn['demandnumber'];
        $surplusnumber            = $paramIn['surplusnumber'];
        $interviewer              = $paramIn['interviewer'];
        $remarks                  = $paramIn['remarks'];
        $reason                   = $paramIn['reason'];
        $submitterId              = $paramIn['submitter_id'];
        $priorityId               = $paramIn['priority_id'];
        $approvalStage            = $paramIn['approval_stage'];
        $approvalStateCode        = $paramIn['approval_state_code'];
        $approval_completion_time = $paramIn['approval_completion_time'];
        $hireType       = isset($paramIn['hire_type']) && $paramIn['hire_type'] ? $paramIn['hire_type'] : 0;
        $hireTimes       = isset($paramIn['hire_times']) && $paramIn['hire_times'] ? $paramIn['hire_times'] : 0;

        $staff_id =  $paramIn['staff_id'] ?? '';//操作人

        $updateParamIn = [
            'state_code'               => $stateCode,
            'job_id'                   => $jobId,
            'department_id'            => $departmentId,
            'type'                     => $type,
            'worknode_id'              => $worknodeId,
            'expirationdate'           => $expirationdate,
            'demandnumber'             => $demandnumber,
            'surplusnumber'            => $surplusnumber,
            'interviewer'              => $interviewer,
            'remarks'                  => $remarks,
            'reason'                   => $reason,
            'submitter_id'             => $submitterId,
            'priority_id'              => $priorityId,
            'approval_stage'           => $approvalStage,
            'approval_state_code'      => $approvalStateCode,
            'approval_completion_time' => $approval_completion_time,
            'hire_type'                => $hireType,
            'hire_times'               => $hireTimes,
        ];

        //过滤空值
        foreach ($updateParamIn as $k => $v) {
            if (empty($v) && $v !== 0) {
                unset($updateParamIn[$k]);
            }
        }
        //$updateParamIn = array_filter($updateParamIn);

        //记录hc 修改一下日志
        $log_result =  $this->addHrhcSaveLog($hc_id,$staff_id,$updateParamIn);

        $this->db->updateAsDict(
            'hr_hc',
            $updateParamIn,
            'hc_id = ' . $hc_id
        );
        if ($this->db->affectedRows() < 1) {
            return false;
        }
        return $hc_id;
    }

    /**
     * 获取Hc状态
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getState($paramIn = [])
    {
        $type = $paramIn['type'] ? $paramIn['type'] : 0;
        if ($type) {
            $stateSql = "select state_code,state_value from hr_state where state_code in (6,7,4,5)";
        } else {
            $stateSql = "select state_code,state_value from hr_state where state_code in (1,2,3,4,5)";
        }
        $data       = $this->getDI()->get('db_rby')->query($stateSql);
        $returnData = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        return $returnData;
    }

    /**
     * 获取Hc状态
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getStateArr($paramIn = [])
    {
        $stateSql   = "select state_code,state_value from hr_state";
        $data       = $this->getDI()->get('db_rby')->query($stateSql);
        $returnData = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        $stateArr   = [];
        foreach ($returnData as $k => $v) {
            $stateArr[$v['state_code']] = $v['state_value'];
        }
        return $stateArr;
    }

    /**
     * 新增审批流
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function insertApproval($insetData = [])
    {
        $data    = $this->db->insertAsDict(
            'hr_approval', $insetData
        );
        $auditId = $this->db->lastInsertId();
        return $auditId;
    }

    /**
     * 待审批流列表
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getApproval($positionId)
    {
        $approvalSql = "select * from hr_approval where position_id=" . $positionId;
        $data        = $this->getDI()->get('db_rby')->query($approvalSql);
        $returnData  = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        return $returnData;
    }

    /**
     * Hc详情
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function hcInfo($hcId)
    {
        $approvalSql = "select 
                    hr_jd.job_name,
                    hr_state.state_value,
                    hr_hc.hc_id,
                    hr_hc.interviewer,
                    hr_hc.serial_no,
                    hr_hc.job_id,
                    hr_hc.department_id,
                    hr_hc.type,
                    hr_hc.worknode_id,
                    hr_hc.expirationdate,
                    hr_hc.demandnumber,
                    hr_hc.surplusnumber,
                    hr_hc.remarks,
                    hr_hc.reason,
                    hr_hc.submitter_id,
                    hr_hc.country_code,
                    hr_hc.province_code,
                    hr_hc.city_code,
                    hr_hc.district_code,
                    hr_hc.state_code,
                    hr_hc.updated_at,
                    hr_hc.createtime,
                    hr_hc.priority_id,
                    hr_hc.approval_stage,
                    hr_hc.approval_state_code,
                    hr_hc.workflow_role,
                    hr_hc.approval_completion_time,
                    hr_hc.reason_type,
                    hr_hc.job_title as job_title_id,
                    hr_hc.hire_type,
                    hr_hc.hire_times,
                    hr_hc.language_ability,
                    hr_hc.task_new_hc_id,
                    CONVERT_TZ(hr_hc.createtime, '+00:00', '" . $this->timezone . "' ) AS createtime,
                    CONVERT_TZ(hr_hc.updated_at, '+00:00', '" . $this->timezone . "' ) AS updated_at,
                    CONVERT_TZ(NOW(), '+00:00', '" . $this->timezone . "') as now_date,
                    DATE_FORMAT(CONVERT_TZ(hr_hc.expirationdate, '+00:00', '" . $this->timezone . "' ),'%Y-%m-%d') as expirationdate,
                    working_day_rest_type 
                    from hr_hc 
                    left join hr_jd on hr_hc.job_id=hr_jd.job_id 
                    left join hr_state on hr_state.state_code=hr_hc.state_code 
                    where 
                    hr_hc.hc_id=" . $hcId;
        $data        = $this->getDI()->get('db_rby')->query($approvalSql);
        $returnData  = $data->fetch(\Phalcon\Db::FETCH_ASSOC);
        return $returnData;
    }

    /**
     * 审批流详情列表
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function approvalInfo($hcId)
    {
        $approvalSql  = "select 
                            hr_state.state_value,
                            hr_approval.id,
                            hr_approval.hc_id,
                            hr_approval.submitter_id,
                            hr_approval.reason,
                            hr_approval.state_code,
                            DATE_FORMAT(CONVERT_TZ(hr_approval.updatetime, '+00:00', '" . $this->timezone . "' ),'%Y-%m-%d %H:%i:%S') as updatetime,
                            CONVERT_TZ(NOW(), '+00:00', '" . $this->timezone . "') as now_date,
                            hr_approval.position_id,
                            hr_approval.staff_ids,
                            hr_hc.state_code as hc_state_code
                            from hr_approval 
                            left join hr_state on hr_state.state_code=hr_approval.state_code 
                            left join hr_hc on hr_hc.hc_id = hr_approval.hc_id
                            where 
                            hr_approval.hc_id=" . $hcId . " 
                            ORDER BY hr_approval.updatetime asc";
        $data         = $this->getDI()->get('db_rby')->query($approvalSql);
        $approvalInfo = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);

        //获取员工id对应名字数组
        $staffStr          = implode(',', array_column($approvalInfo, 'staff_ids'));
        $staffArr          = array_filter(explode(',', $staffStr));
        $staffData         = (new StaffRepository())->checkoutStaffBatch($staffArr);
        $staffData         = array_column($staffData, 'name', 'staff_info_id');

        $countApprovalInfo = count($approvalInfo);
        if ($approvalInfo[0]['hc_state_code'] == 1 && ($approvalInfo[$countApprovalInfo - 1]['state_code'] == 8 ||
                $approvalInfo[$countApprovalInfo - 1]['state_code'] == 7 ||
                $approvalInfo[$countApprovalInfo - 1]['state_code'] == 1 ||
                $approvalInfo[$countApprovalInfo - 1]['state_code'] == 6)) {
            $push = 1;
            if ($push == 1) {
                if ($approvalInfo[$countApprovalInfo - 1]['staff_ids']) {
                    $manger_staff_ids_arr = explode(',', $approvalInfo[$countApprovalInfo - 1]['staff_ids']);
                    if (count($manger_staff_ids_arr) > 1) {
                        $submitter_name   = $staffData[$manger_staff_ids_arr[0]];
                        $submitter_id     = $manger_staff_ids_arr[0];
                        $submitter_name_1 = $staffData[$manger_staff_ids_arr[1]];
                        $submitter_id_1   = $manger_staff_ids_arr[1];
                    } else {
                        $submitter_name = $staffData[$manger_staff_ids_arr[0]];
                        $submitter_id   = $manger_staff_ids_arr[0];
                    }
                }
                $position_id = 1;
                $arr         = [
                    'state_value'      => '4015',
                    'hc_id'            => $hcId,
                    'state_code'       => '7',
                    'type'             => 1,
                    'updatetime'       => $approvalInfo[$countApprovalInfo - 1]['updatetime'],
                    'position_id'      => $position_id,
                    'submitter_name'   => $submitter_name,
                    'submitter_id'     => $submitter_id,
                    'submitter_name_1' => $submitter_name_1 ? $submitter_name_1 : '',
                    'submitter_id_1'   => $submitter_id_1 ? $submitter_id_1 : '',

                ];
                array_push($approvalInfo, $arr);
            }

        }

        return $approvalInfo;
    }

    /**
     * 审批流状态修改
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function updateApproval($paramIn)
    {
        $hc_id      = $paramIn['hc_id'];
        $stateCode  = $paramIn['state_code'];
        $returnData = $this->db->updateAsDict(
            'hr_approval',
            [
                'state_code' => $stateCode,
            ],
            'hc_id = ' . $hc_id
        );
        return $returnData;
    }


    public function getInterviewList($hcId)
    {
        $sql        = "select interview_id from hr_interview where hc_id = '" . $hcId . "' and state=10";
        $data       = $this->getDI()->get('db_rby')->query($sql);
        $returnData = $data->fetch(\Phalcon\Db::FETCH_ASSOC);
        return $returnData;
    }

    /**
     * 面试官列表搜索
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getInterviewerList($paramIn = [])
    {

        $submitter_name    = isset($paramIn['submitter_name']) ? $paramIn['submitter_name'] : '';
        $interviewerJobIds = SettingEnvServer::getSetVal('interviewerJobIds') ? : env("interviewerJobIds");
        //初始化定义
        $staffInfoData = [];
        //查询员工职位
        $sql     = '
                          --
                          SELECT id FROM hr_job_title where id in (' . $interviewerJobIds . ') and status = 1';
        $data    = $this->getDI()->get('db_rby')->query($sql);
        $jobData = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        //查询面试官 hr_job_title
        if (!empty($jobData)) {
            $jobIds = array_unique(array_column($jobData, 'id'));
            if ($submitter_name) {
                $submitter_name              = str_replace('"', '', $submitter_name);
                $staffInfoSql = '--
                                SELECT 
                                id,`name` 
                                FROM `staff_info` 
                                where 
                                job_title in (" . implode(\',\', $jobIds) . ") and 
                                state=1 and 
                                (id like "%' . $submitter_name . '%" or `name` like "%' . $submitter_name . '%")';
            } else {
                $staffInfoSql = "--
                                  SELECT id,`name` FROM `staff_info` where job_title in (" . implode(',', $jobIds) . ") and state=1";
            }
            $data          = $this->getDI()->get('db_fle')->query($staffInfoSql);
            $staffInfoData = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        }
        return $staffInfoData;
    }

    public function getSuperiorPosition($positionId)
    {
        $sql        = "select * from hr_position_relation where position_category in ('" . $positionId . "')";
        $data       = $this->getDI()->get('db_rby')->query($sql);
        $returnData = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        return $returnData;
    }


    /**
     * 新增我的审批
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function insertMyApproval($insetData = [])
    {
        $data    = $this->db->insertAsDict(
            'hr_myapproval', $insetData
        );
        $auditId = $this->db->lastInsertId();
        return $auditId;
    }

    /**
     * 修改我的审批列表
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function updateMyapproval($paramIn)
    {
        $hc_id        = $paramIn['hc_id'];
        $stateCode    = $paramIn['state_code'];
        $submitter_id = $paramIn['submitter_id'];
        $returnData   = $this->db->updateAsDict(
            'hr_myapproval',
            [
                'state_code' => $stateCode,
            ],
            'hc_id = ' . $hc_id . ' and submitter_id = ' . $submitter_id
        );
        return $returnData;
    }

    /**
     * 检查我的审批列表状态
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function checkMyapproval($paramIn)
    {
        $hc_id        = $paramIn['hc_id'];
        $stateCode    = $paramIn['state_code'];
        $submitter_id = $paramIn['submitter_id'];
        $sql          = "select * from hr_myapproval where state_code=" . $stateCode;
        if (!empty($submitter_id)) {
            $sql .= " and hc_id=" . $hc_id . " and submitter_id =" . $submitter_id;
        } else {
            $sql .= " and hc_id=" . $hc_id;
        }
        $data           = $this->db_rby->query($sql);
        $myapprovalData = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        return $myapprovalData;
    }

    /**
     * 获取分页数组
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getPageArr($paramIn)
    {

        $sql      = isset($paramIn['sql']) ? $paramIn['sql'] : '';
        $pageSize = isset($paramIn['page_size']) ? $paramIn['page_size'] : 10;
        $pageNum  = isset($paramIn['page_num']) ? $paramIn['page_num'] : 1;

        $returnData = [
            "count"     => 0,
            "pageCount" => 0,
            "pageNum"   => $pageNum,
            "pageSize"  => $pageSize,
        ];
        if (empty($sql) || empty($pageSize) || empty($pageNum)) {
            return $returnData;
        } else {
            $_sql = "--
                     SELECT
                        count( 1 ) AS num 
                    FROM
                        ({$sql}) aaa";
            $obj  = $this->db_rby->query($_sql);
            $data = $obj->fetch(\Phalcon\Db::FETCH_ASSOC);

            //总条数
            $pageArr['count'] = (int)$data['num'];
            //总页数
            $pageArr['pageCount'] = ceil($pageArr['count'] / $pageSize);
            //当前页 默认1
            $pageArr['pageNum'] = $pageNum;
            //每页显示多少条默认10
            $pageArr['pageSize'] = $pageSize;
            $returnData          = $pageArr;
        }
        $this->getDI()->get('logger')->write_log('returnData: ' . json_encode($returnData), 'info');
        return $returnData;
    }

    /**
     * 获取有hc审批权限的人
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function hcApprovalStaffList($staffId = '')
    {
        if ($staffId) {
            $sql = "select 
                    hr_workflow_binding.staff_id,
                    hr_workflow_role.role_name 
                    from 
                    hr_workflow_binding 
                    left join hr_workflow_role on hr_workflow_binding.role_id = hr_workflow_role.id 
                    where 
                    hr_workflow_role.module_type = 1 and 
                    hr_workflow_role.id !=7 and 
                    hr_workflow_binding.staff_id=" . $staffId;
        } else {
            //返回有hc审批权限人员id列表
            $sql = "select 
                    hr_workflow_binding.staff_id,
                    hr_workflow_role.role_name 
                    from 
                    hr_workflow_binding 
                    left join 
                    hr_workflow_role on hr_workflow_binding.role_id = hr_workflow_role.id 
                    where 
                    hr_workflow_role.module_type = 1 and 
                    hr_workflow_role.id !=7";
        }
        $data = $this->db_rby->query($sql);
        $info = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        return $info;
    }

    /**
     * 获取有hc修改权限的人
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function hcUpdateStaffList($staffId = '')
    {
        if ($staffId) {
            $sql = "SELECT 
                hr_workflow_binding.staff_id 
                from 
                hr_workflow_binding left JOIN 
                hr_workflow_role on hr_workflow_binding.role_id = hr_workflow_role.id 
                where 
                hr_workflow_role.module_type = 1 and 
                hr_workflow_role.role_name = 'coo' and 
                hr_workflow_binding.staff_id = " . $staffId;
        } else {
            //返回有hc修改权限人员id列表
            //获取coo是staffId {涉及范围:[HcServer:getStoreHcList]}
            $sql = "SELECT 
                hr_workflow_binding.staff_id 
                from 
                hr_workflow_binding left JOIN 
                hr_workflow_role on hr_workflow_binding.role_id = hr_workflow_role.id 
                where 
                hr_workflow_role.module_type = 1 and 
                hr_workflow_role.role_name = 'coo'";
        }
        $data = $this->db_rby->query($sql);
        $info = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        return $info;
    }

    /**
     * 获取有hc作废权限的人
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function hcNullifyStaffList($staffId = '')
    {
        if ($staffId) {
            $sql = "SELECT 
                hr_workflow_binding.staff_id 
                from 
                hr_workflow_binding left JOIN 
                hr_workflow_role on hr_workflow_binding.role_id = hr_workflow_role.id 
                where 
                hr_workflow_role.module_type = 1 and 
                hr_workflow_role.role_name = 'coo' and 
                hr_workflow_binding.staff_id = " . $staffId;
        } else {
            //返回有hc修改权限人员id列表
            $sql = "SELECT 
                hr_workflow_binding.staff_id 
                from 
                hr_workflow_binding left JOIN 
                hr_workflow_role on hr_workflow_binding.role_id = hr_workflow_role.id 
                where 
                hr_workflow_role.module_type = 1 and 
                hr_workflow_role.role_name = 'coo'";
        }
        $data = $this->db_rby->query($sql);
        $info = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        return $info;
    }

    public function getHcStateList()
    {
        $sql  = "select * from hr_state";
        $data = $this->db_rby->query($sql);
        $info = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        return $info;
    }

    /**
     * 获取审批完成时间
     * @Access  public
     * @Param   request
     * @Return  array approval_completion_time
     */
    public function getHcApprovalCompletionTimeList($paramIn = [])
    {
        $hcIds = $paramIn["hc_ids"] ?? "";
        if (empty($hcIds)) {
            return [];
        }
        $sql  = "--
                SELECT
                    hc_id,
                    updatetime,
                    state_code 
                FROM
                    (
                    SELECT
                        * 
                    FROM
                        hr_approval 
                    WHERE
                        hc_id IN ( {$hcIds} ) 
                        AND state_code IN ( 6, 4, 5 ) 
                    ORDER BY
                        updatetime DESC 
                    ) a 
                GROUP BY
                    hc_id";
        $data = $this->db_rby->query($sql);
        $info = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        return $info;
    }

    /**
     * 获取网点HC统计列表
     * @param array $paramIn 传入参数
     * @return array
     */
    public function getStoreHcList($paramIn = []): array
    {
        $searchWhere     = $paramIn['searchWhere'] ?? '';
        $pageSize        = $paramIn['page_size'] ?? 10;
        $pageNum         = $paramIn['page_num'] ?? 1;
        $export          = $paramIn['export'] ?? 0;
        $userInfo        = $paramIn['userInfo'] ?? [];
        $searchCondition = [];
        $where           = "";

        //组织查询条件
        if ($searchWhere) {
            foreach ($searchWhere as $column => $v) {
                switch ($column) {
                    case 'jd_id':
                        $searchCondition[] = " hsh.jd_id = {$v}";
                        break;
                    case 'area_code':
                        $searchCondition[] = " hsh.area_code = '{$v}'";
                        break;
                    case 'store_id':
                        $searchCondition[] = " hsh.store_id = '{$v}'";
                        break;
                    case 'store_ids':
                        $searchCondition[] = " hsh.store_id in ({$v}) ";
                        break;
                    case 'region_id':
                        $searchCondition[] = " hsh.manage_region = ".intval($v)." ";
                        break;
                    case 'piece_id':
                        $searchCondition[] = " hsh.manage_piece = ".intval($v)." ";
                        break;
                    default:
                        break;
                }
            }
            if (sizeof($searchCondition) > 1) {
                $where = implode(' AND ', $searchCondition);
            } else {
                $where = current($searchCondition);
            }
            $where = " WHERE " . $where;
        }

        //数据权限
        if (isCountry('TH') || isCountry('MY') || isCountry('PH')) {
            $is_menu_permission = SettingEnvServer::getMenuPermission('whr_hc_menu_list');
            if ($userInfo['is_admin'] == enums::$is_admin['off'] && $is_menu_permission) {
                $authoritySql = (new HcRepository())->assembleAuthorityStoreHcSql($userInfo);
                $where        = $where ? $where . ' AND ' . $authoritySql : 'WHERE ' . $authoritySql;
            }
        }

        //排序及分页
        $orderAndLimit = $export == 1
            ? " order by hsh.store_id asc"
            : " order by hsh.store_id asc limit " . ($pageNum - 1) * $pageSize . "," . $pageSize;

        $querySql = "--
            SELECT
                hsh.store_id,
                hsh.jd_id,
                hsh.job_title_id,
                hj.job_name,
                hsh.area_code,
                hsh.hc_budget,
                hsh.employee_in_service,
                hsh.hc_pending,
                hsh.hc_demand,
                hsh.hc_approval,
                hsh.hc_request,
                hsh.hc_p1,
                hsh.hc_p2,
                hsh.hc_p3,
                hsh.hc_p4,
                hsh.manage_region,
                hsh.manage_piece 
            FROM
                `hr_statistical_hc` hsh
                LEFT JOIN `hr_jd` hj ON hj.job_id = hsh.jd_id
            {$where}";
        
        //获取分页数组
        $paramPage = [
            'sql'       => $querySql,
            'page_size' => $pageSize,
            'page_num'  => $pageNum,
        ];
        $dataPage  = self::getPageArr($paramPage);
        
        if (!empty($paramIn['is_count'])) {
            return $dataPage;
        }
    
        $dataHc   = $this->getDI()->get('db_rby')->query($querySql . $orderAndLimit)->fetchAll(\Phalcon\Db::FETCH_ASSOC);

        /* 拼装返回数组 */
        $returnData['data']       = $dataHc ? $dataHc : [];
        $returnData['pagination'] = $dataPage ? $dataPage : [];

        return $returnData;
    }

    /**
     * 数据权限 网点hc列表 sql拼接
     * @param $userInfo
     * @return string
     */
    public function assembleAuthorityStoreHcSql($userInfo)
    {
        $authority_stores_ids = $userInfo['permission_stores_ids']; //网点权限
        //网点
        $where = '(';
        if (empty($authority_stores_ids)) {
            $where .= " hsh.store_id = '' ";
        } else {
            if ($authority_stores_ids == '-2' || in_array('-2', $authority_stores_ids)) {
                //-2 是全部网点 不包含 -1 网点
                $authority_stores_ids = -1;
                $where                .= " hsh.store_id != " . $authority_stores_ids;
            } else {
                if (is_array($authority_stores_ids)) {
                    $authority_stores_ids = getIdsStr($authority_stores_ids);
                    $where                .= " hsh.store_id IN (" . $authority_stores_ids . ") ";
                } else {
                    $where .= " hsh.store_id = " . $authority_stores_ids . " ";
                }
            }
        }
        $where .= " )";
        return $where;
    }

    /**
     * 获取待入职员工列表
     * @param array $paramIn 传入参数
     * @return array
     */
    public function getPendingListBystoreId($paramIn = []): array
    {
        $searchWhere     = $paramIn['searchWhere'] ?? '';
        $pageSize        = $paramIn['page_size'] ?? 10;
        $pageNum         = $paramIn['page_num'] ?? 1;
        $export          = $paramIn['export'] ?? 0;
        $searchCondition = [];
        $where           = "";

        //组织查询条件
        if ($searchWhere) {
            foreach ($searchWhere as $column => $v) {
                switch ($column) {
                    case 'store_id':
                        $searchCondition[] = " hc.worknode_id = '{$v}'";
                        break;
                    case 'jd_id':
                        $searchCondition[] = " jd.job_id = {$v}";
                        break;
                    case 'status':
                        $searchCondition[] = " entry.`status` = '{$v}'";
                    default:
                        break;
                }
            }
            if (sizeof($searchCondition) > 1) {
                $where = implode(' AND ', $searchCondition);
            } else {
                $where = current($searchCondition);
            }
            $where = " WHERE " . $where;
        }
        //排序及分页
        $orderAndLimit = $export == 1
            ? " order by hc.hc_id asc"
            : " order by hc.hc_id asc limit " . ($pageNum - 1) * $pageSize . "," . $pageSize;

        $querySql = "--
            SELECT
                hc.hc_id,
                entry.resume_id,
                resume.name,
                CASE
                    WHEN resume.sex = 1 THEN
                    'M' ELSE 'F' 
                END AS sex,
                resume.phone,
                resume.credentials_num,
                jd.job_name,
                hc.worknode_id as store_id
            FROM
                hr_hc hc
                LEFT JOIN hr_entry entry ON hc.hc_id = entry.hc_id
                LEFT JOIN hr_resume resume ON entry.resume_id = resume.id
                LEFT JOIN hr_jd jd ON jd.job_id = hc.job_id
            {$where}";
        $dataHc   = $this->getDI()->get('db_rby')->query($querySql . $orderAndLimit)->fetchAll(\Phalcon\Db::FETCH_ASSOC);

        //获取分页数组
        $paramPage = [
            'sql'       => $querySql,
            'page_size' => $pageSize,
            'page_num'  => $pageNum,
        ];
        $dataPage  = self::getPageArr($paramPage);

        /* 拼装返回数组 */
        $returnData['data']       = $dataHc ? $dataHc : [];
        $returnData['pagination'] = $dataPage ? $dataPage : [];

        return $returnData;
    }

    /**
     * 获取待入职员工列表
     * @param array $paramIn 传入参数
     * @return array
     */
    public function getApprovalByStoreId($paramIn = []): array
    {
        $searchWhere     = $paramIn['searchWhere'] ?? '';
        $pageSize        = $paramIn['page_size'] ?? 10;
        $pageNum         = $paramIn['page_num'] ?? 1;
        $export          = $paramIn['export'] ?? 0;
        $searchCondition = [];
        $where           = "";

        //组织查询条件
        if ($searchWhere) {
            foreach ($searchWhere as $column => $v) {
                switch ($column) {
                    case 'store_id':
                        $searchCondition[] = " hc.worknode_id = '{$v}'";
                        break;
                    case 'jd_id':
                        $searchCondition[] = " jd.job_id = {$v}";
                        break;
                    case 'status':
                        $searchCondition[] = " hc.`state_code` = '{$v}'";
                        break;
                    default:
                        break;
                }
            }
            if (sizeof($searchCondition) > 1) {
                $where = implode(' AND ', $searchCondition);
            } else {
                $where = current($searchCondition);
            }
            $where = " WHERE " . $where;
        }
        //排序及分页
        $orderAndLimit = $export == 1
            ? " order by hc.hc_id asc"
            : " order by hc.hc_id asc limit " . ($pageNum - 1) * $pageSize . "," . $pageSize;

        $querySql = "--
            SELECT
                hc.hc_id,
                hc.worknode_id as store_id,
                CASE
                    WHEN hc.priority_id = 1 THEN 'P1'
                    WHEN hc.priority_id = 2 THEN 'P2'
                    WHEN hc.priority_id = 3 THEN 'P3'
                    ELSE 'P4' 
                END AS priority,
                jd.job_name,
                DATE_FORMAT(CONVERT_TZ(hc.expirationdate, '+00:00', '{$this->timezone}' ),'%Y-%m-%d') as expirationdate,
                hc.surplusnumber,
                hc.demandnumber,
                hc.state_code as status
            FROM
                hr_hc hc
                JOIN hr_jd jd ON jd.job_id = hc.job_id
            {$where} and deleted = 1"; // deleted -1 未删除 -2 已删除
        $dataHc   = $this->getDI()->get('db_rby')->query($querySql . $orderAndLimit)->fetchAll(\Phalcon\Db::FETCH_ASSOC);

        //获取分页数组
        $paramPage = [
            'sql'       => $querySql,
            'page_size' => $pageSize,
            'page_num'  => $pageNum,
        ];
        $dataPage  = self::getPageArr($paramPage);

        /* 拼装返回数组 */
        $returnData['data']       = $dataHc ? $dataHc : [];
        $returnData['pagination'] = $dataPage ? $dataPage : [];

        return $returnData;
    }

    /*
     * 基础-修改hc
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function changeHc($paramIn = [],$staff_id='')
    {
        $hcId       = $paramIn['hc_id'] ?? "";
        $updateData = $paramIn['updateData'] ?? [];


        if (!$updateData || !$hcId)
            return false;
        //记录hc 修改一下日志
        $log_result = $this->addHrhcSaveLog($hcId,$staff_id,$updateData);

        $this->db->updateAsDict(
            'hr_hc',
            $updateData,
            'hc_id = ' . $hcId
        );
        if ($this->db->affectedRows() < 1) {
            return false;
        }

        return true;
    }

    /*
     * 批量修改hc
     * @Access  public
     * @Param   $hc_ids 示例:1,2,3
     * @Param   $updateData 示例：["state_code"=>1]
     * @Return  array
     */
    public function batchUpdateHc($hc_ids,$updateData,$staff_id)
    {
        if (!$updateData || !$hc_ids)
            return false;
        //过滤空值
        foreach ($updateData as $k => $v) {
            if (empty($v) && $v !== 0) {
                unset($updateData[$k]);
            }
        }
        $this->addHrhcSaveLog($hc_ids,$staff_id,$updateData);
        $this->db->updateAsDict(
            'hr_hc',
            $updateData,
            "hc_id in ({$hc_ids})"
        );
        if ($this->db->affectedRows() < 1) {
            return false;
        }

        return true;
    }

    /*
     * 批量修改hc需求人数
     * @Access  public
     * @Param   $hc_ids 示例:1,2,3
     * @Param   $updateData 示例：["state_code"=>1]
     * @Return  array
     */
    /**
     * @throws \Exception
     */
    public function batchUpdateHcDemandnumber($updateData,$staff_info_id='')
    {
        $db = $this->getDI()->get('db');
        $db->begin();

        try {
            foreach ($updateData as $v){
                $this->addHrhcSaveLog($v['hc_id'], $staff_info_id,$v);

                $data = [
                    'demandnumber'  => $v["demandnumber"] ?? 0,
                    'surplusnumber' => $v["surplusnumber"] ?? 0,
                ];
                if (isset($v['state_code'])) {
                    $data['state_code'] = $v['state_code'];
                }
                $db->updateAsDict(
                    'hr_hc',
                    $data,
                    [
                        'conditions' => "hc_id = ?",
                        'bind'       => [$v['hc_id']],
                    ]
                );
            }
            $db->commit();
        } catch (\Exception $e) {
            /* 有异常回滚 */
            $db->rollback();
            throw $e;
        }
        return true;
    }

    /**
     * 基础-修改myapproval
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function changeMyapproval($paramIn = [])
    {
        $id         = $paramIn['id'] ?? "";
        $updateData = $paramIn['updateData'] ?? [];

        if (!$updateData || !$id)
            return false;

        $this->db->updateAsDict(
            'hr_myapproval',
            $updateData,
            'id = ' . $id
        );
        if ($this->db->affectedRows() < 1) {
            return false;
        }

        return true;
    }

    /**
     * 获取在职人数
     * @param array $paramIn 传入参数
     * @return int
     */
    public function getEmployeeInServiceCount($paramIn = []): int
    {
        $storeId  = $paramIn['store_id'];
        $jobTitle = $paramIn['job_title_id'];

        if (empty($jobTitle) || empty($storeId)) {
            return 0;
        }

        $querySql  = "--
            SELECT
                 count(DISTINCT(hsi.staff_info_id)) as cou
            FROM
                hr_staff_info hsi
                JOIN hr_staff_info_position hsip ON hsi.staff_info_id = hsip.staff_info_id
                JOIN sys_store ss ON ss.id = hsi.sys_store_id 
            WHERE
                hsi.job_title = :job_title
                AND ss.id = :store_id
                AND hsi.`state` = 1             -- 在职
                AND hsi.`formal` = 1            -- 在编
                AND hsi.`is_sub_staff` = 0      -- 非子账号
                AND ss.category IN ( 1, 2 ,10)  -- dc、sp网点
                AND hsi.working_country = :working_country
        ";
        $dataCount = $this->db_rby->query($querySql, [
            'job_title' => $jobTitle,
            'store_id' => $storeId,
            'working_country' => getCountryValue(),
        ])->fetch(\Phalcon\Db::FETCH_ASSOC);
        return $dataCount['cou'] ?? 0;
    }

    /**
     * 更新网点HC统计数据
     * @param array $paramIn 传入参数
     * @return boolean
     */
    public function updateStoreHcCount($data, $store_id)
    {
        $db = $this->getDI()->get('db');
        $db->begin();

        try {
            $data = array_column($data, null, 'jd_id');

            //更新的字段
            $columns = [
                'employee_in_service',
                'hc_pending',
                'hc_demand',
                'hc_approval',
                'hc_request',
                'hc_p1',
                'hc_p2',
                'hc_p3',
                'hc_p4',
                'manage_region',
                'manage_piece',
            ];

            //找到最大key
            end($columns);
            $key_last = key($columns);

            //拼接sql
            $sql  = "UPDATE `hr_statistical_hc` SET ";
            foreach ($columns as $k => $column) {
                $sql .= " `{$column}` = CASE jd_id ";
                foreach ($data as $jd_id => $v) {
                    $sql .= sprintf("WHEN %d THEN %d ", $jd_id, $v[$column]);
                }

                //如果是最大key
                $sql .= $key_last == $k ? "END" : "END,";
            }
            $sql .= " WHERE store_id  = '{$store_id}'";

            $db->execute($sql);
            $db->commit();
        } catch (\Exception $e) {
            /* 有异常回滚 */
            $db->rollback();
        }
        return true;
    }

    /**
     * 获取网点经理管理区域
     * @param int $staffInfoId 员工ID
     * @return array
     */
    public function getAreaManagerStoreIds($staffInfoId = null): array
    {
        if (empty($staffInfoId)) {
            return [];
        }
        $querySql = "--
            SELECT 
                sys_store_id
            FROM
                `hr_area_manager_store`
            WHERE
                staff_info_id = {$staffInfoId}
        ";
        $storeIds = $this->getDI()->get('db_rbi')->query($querySql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        return $storeIds;
    }

    /**
     * 获取待入职的人数
     * @param int $paramIn 传入参数
     * @return int
     */
    public function getPendingCount($paramIn = []): int
    {
        $storeId = $paramIn['store_id'];
        $jobId   = $paramIn['jd_id'];

        if (empty($jobId) || empty($storeId)) {
            return 0;
        }
        $querySql  = "--
            SELECT 
                count(1) as cou
            FROM 
                hr_entry entry
                JOIN hr_hc hc ON hc.hc_id = entry.hc_id
            WHERE
                hc.worknode_id = '{$storeId}' 
                AND hc.job_id = {$jobId}
                AND entry.`status` = 2
        ";
        $dataCount = $this->db_rby->query($querySql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        return array_sum(array_keys(array_column($dataCount, null, 'cou'))) ?? 0;
    }

    /**
     * 获取指定网点、指定优先级的hc人数
     * @param array $paramIn 传入参数
     * @return array
     */
    public function getPriorityCount($paramIn = []): array
    {
        $storeId = $paramIn['store_id'];
        $jobId   = $paramIn['jd_id'];

        if (empty($jobId) || empty($storeId)) {
            return [];
        }
        $querySql  = "--
            SELECT 
                sum(surplusnumber) as cou,
                hc.priority_id
            FROM 
                hr_hc hc
            WHERE
                hc.worknode_id = '{$storeId}' and hc.job_id = {$jobId}
                AND state_code = 2
            GROUP BY priority_id
        ";
        $dataCount = $this->db_rby->query($querySql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        return array_filter(array_column($dataCount, 'cou', 'priority_id'));
    }

    /**
     * 获取审批通过的人数
     * @param array $paramIn 传入参数
     * @return array
     */
    public function getApprovalCount($paramIn = []): array
    {
        $storeId = $paramIn['store_id'];
        $jobId   = $paramIn['jd_id'];

        if (empty($jobId) || empty($storeId)) {
            return [];
        }
        $querySql  = "--
            SELECT 
                surplusnumber as hc_approval
            FROM 
                hr_hc hc
            WHERE
                hc.worknode_id = '{$storeId}' AND hc.job_id = {$jobId}
                AND state_code = 2 and deleted = 1
        ";
        $dataCount = $this->db_rby->query($querySql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        return array_column($dataCount, 'hc_approval') ?? [];
    }

    /**
     * 更新网点HC预算人数
     * @param array $paramIn 传入参数
     * @return boolean
     */
    public function updateStoreHcBudget($data, $store_id)
    {
        $db = $this->getDI()->get('db');
        $db->begin();

        try {
            $sql = "UPDATE `hr_statistical_hc` SET `hc_budget` = CASE jd_id ";
            foreach ($data as $jd_id => $v) {
                $sql .= sprintf("WHEN %d THEN %d ", $jd_id, $v);
            }
            $sql .= "END WHERE store_id  = '{$store_id}'";
            $db->execute($sql);
            $db->commit();
        } catch (\Exception $e) {
            /* 有异常回滚 */
            $db->rollback();
        }
        return true;
    }


    /**
     * 更新网点HC预算人数根据job_title_id
     * @param array $data 传入参数
     * @param string $store_id 网点id
     * @return boolean
     */
    public function updateStoreHcBudgetByJobTitle($data, $store_id)
    {
        $db = $this->getDI()->get('db');
        $db->begin();

        try {
            $sql = "UPDATE `hr_statistical_hc` SET `hc_budget` = CASE job_title_id ";
            foreach ($data as $job_title_id => $v) {
                $sql .= sprintf("WHEN %d THEN %d ", $job_title_id, $v);
            }
            $sql .= "END WHERE store_id  = '{$store_id}'";
            $db->execute($sql);
            $db->commit();
        } catch (\Exception $e) {
            /* 有异常回滚 */
            $db->rollback();
        }
        return true;
    }


    /**
     * 修改一个网点HC预算
     * @param $store_id
     * @param $jd_id
     * @param $hc_budget
     * @return boolean|string
     */
    public function modifyHcBudget($store_id,$jd_id,$hc_budget){
        $db = $this->getDI()->get('db');
        $db->begin();
        try{
            $sql = "select * from hr_statistical_hc where store_id= ? and jd_id= ? ";
            $temp = $db->fetchOne($sql,\Phalcon\Db::FETCH_ASSOC,[$store_id,$jd_id]);
            if(empty($temp)){
                $db->commit();
                return false;
            }

            $data = [];
            $data['hc_budget'] = $hc_budget;
            $data['hc_demand'] = $hc_budget - $temp['employee_in_service'] - $temp['hc_pending'];
            $data['hc_request'] = $data['hc_demand'] - $temp['hc_approval'];

            $db->updateAsDict("hr_statistical_hc",$data,
                [
                    'conditions'=>'store_id=? and jd_id=?',
                    'bind'=>[$store_id,$jd_id],
                    'bindTypes'=>[\PDO::PARAM_STR,\PDO::PARAM_INT],
                ]
            );
            $db->commit();
        }catch (\Exception $e){
            $db->rollback();
            return false;
        }
        return true;
    }

    /**
     * 根据网点ID,jd_id找网点HC对应的数
     * @param $store_ids
     * @param $jd_ids
     * @return mixed
     */
    public function getRequestNumFromHrStaHc($store_ids,$jd_ids){
        $sql = "select  * from hr_statistical_hc where store_id in (".getIdsStr($store_ids).") and jd_id in (".getIdsStr($jd_ids).")";
        $this->getDI()->get('logger')->write_log(['sql'=>$sql], 'info');
        return $this->getDI()->get('db_rby')->fetchAll($sql,\Phalcon\Db::FETCH_ASSOC);
    }


    /**
     * 同步给buddy
     * @param $hc_id
     * @param int $state_code
     */
    public function syncHcToBuddy($hc_id,$state_code=0){

        //合作到期停用此功能
        return;



        $hc_str =<<<EOF
{
    "hc_info":{
        "hc_id":{
            "type": "text",
            "rule": "int",
            "len_min": "1",
            "len_max": "8",
            "info": "hc id",
            "list":[]
        },
        "priority_id":{
            "type":"text",
            "rule":"int",
            "len_min":"1",
            "len_min":"4",
            "info":"priority",
            "list":[]
        },
        "job_id":{
            "type":"text",
            "rule":"int",
            "len_min":"1",
            "len_min":"8",
            "info":"job description id",
            "list":[]
        },
        "job_name":{
            "type":"text",
            "rule":"int",
            "len_min":"1",
            "len_min":"8",
            "info":"job's name",
            "list":[]
        },
        "job_type":{
			"type": "radio",
			"rule": "int",
			"len_min": "1",
			"len_max": "4",
			"info": "job type",
			"list": [
			    {
					"id": "1",
					"val": "Motorcycle courier"
				},
				{
					"id": "2",
					"val": "Pickup truck courier"
				},
				{
					"id": "3",
					"val": "Non-courier"
				}
			]
		},
		"job_description":{
            "type":"text",
            "rule":"char",
            "len_min":"1",
            "len_min":"65535",
            "info":"job's name",
            "list":[]
        },
        "job_title_name":{
            "type":"text",
            "rule":"char",
            "len_min":"1",
            "len_min":"65535",
            "info":"job title name",
            "list":[]
        },
        "department_id": {
			"type": "text",
			"rule": "char",
			"len_min": "1",
			"len_max": "10",
			"info": "department id",
			"list":[]
		},
		"department_name": {
			"type": "text",
			"rule": "char",
			"len_min": "1",
			"len_max": "10",
			"info": "department name",
			"list":[]
		},
		
		"store_id":{
		    "type":"text",
		    "rule":"char",
		    "len_min":"1",
		    "len_max":"30",
		    "info":"store's id",
		    "list":[]
		},
		"store_name":{
		    "type":"text",
		    "rule":"char",
		    "len_min":"1",
		    "len_max":"30",
		    "info":"store's name",
		    "list":[]
		},
		"province_code":{
		    "type":"text",
		    "rule":"char",
		    "len_min":"1",
		    "len_max":"30",
		    "info":"province's code",
		    "list":[]
		},
		"province_name":{
		    "type":"text",
		    "rule":"char",
		    "len_min":"1",
		    "len_max":"30",
		    "info":"province's name",
		    "list":[]
		},		
		"province_name_th":{
		    "type":"text",
		    "rule":"char",
		    "len_min":"1",
		    "len_max":"30",
		    "info":"province's th name",
		    "list":[]
		},	
		"city_code":{
		    "type":"text",
		    "rule":"char",
		    "len_min":"1",
		    "len_max":"30",
		    "info":"city's code",
		    "list":[]
		},
		"city_name":{
		    "type":"text",
		    "rule":"char",
		    "len_min":"1",
		    "len_max":"30",
		    "info":"city's name",
		    "list":[]
		},		
		"city_name_th":{
		    "type":"text",
		    "rule":"char",
		    "len_min":"1",
		    "len_max":"30",
		    "info":"city's th name",
		    "list":[]
		},		
		"district_code":{
		    "type":"text",
		    "rule":"char",
		    "len_min":"1",
		    "len_max":"30",
		    "info":"district's code",
		    "list":[]
		},
		"district_name":{
		    "type":"text",
		    "rule":"char",
		    "len_min":"1",
		    "len_max":"30",
		    "info":"district's name",
		    "list":[]
		},		
		"district_name_th":{
		    "type":"text",
		    "rule":"char",
		    "len_min":"1",
		    "len_max":"30",
		    "info":"district's th name",
		    "list":[]
		},
		"category_id":{
		    "type":"select",
		    "rule":"char",
		    "len_min":"1",
		    "len_max":"30",
		    "info":"store category",
		    "list":[
		        {   
		           "id":"1",
		           "val":"station" 
		        },		        
		        {   
		           "id":"2",
		           "val":"distribution_center" 
		        },
		        {   
		           "id":"3",
		           "val":"agent_station" 
		        },
		        {   
		           "id":"4",
		           "val":"market_pickup_station" 
		        },
		        {   
		           "id":"5",
		           "val":"station_shop" 
		        },
		        {   
		           "id":"6",
		           "val":"franchisee_store" 
		        },
		        {   
		           "id":"7",
		           "val":"college_dot" 
		        },
		        {   
		           "id":"8",
		           "val":"HUB" 
		        },
		        {   
		           "id":"9",
		           "val":"OS" 
		        },
		        {   
		           "id":"10",
		           "val":"bdc" 
		        },
		        {   
		           "id":"11",
		           "val":"fulfillment" 
		        },
		        {   
		           "id":"12",
		           "val":"B-Hub" 
		        }		        		        		        		        		        		        		        		        		        		        		    
		    ]
		},		
		"state_code":{
		    "type":"radio",
		    "rule":"int",
		    "len_min":"1",
		    "len_max":"1",
		    "info":"state",
		    "is_required":true,
		    "list":[
		        {
		            "id":"2",
		            "val":"Recruitment"
		        },
		        {
		            "id":"3",
		            "val":"finish"
		        },
		        {
		            "id":"4",
		            "val":"Invalid"
		        },
		        {
		            "id":"9",
		            "val":"Expired"
		        }
		    ]
		},
		"expirationdate":{
		    "type":"datetime",
		    "rule":"char",
		    "len_min":"0",
		    "len_max":"19",
		    "info":"expire date end",
		    "list":[]
		},
		"surplusnumber":{
		    "type":"text",
		    "rule":"int",
		    "len_min":"1",
		    "len_max":"4",
		    "info":"surplus num",
		    "list":[]
		},
		"demandnumber":{
		    "type":"text",
		    "rule":"int",
		    "len_min":"1",
		    "len_max":"4",
		    "info":"demand number",
		    "list":[]
		},
		"reason_type":{
		    "type":"radio",
		    "rule":"int",
		    "len_min":"1",
		    "len_max":"4",
		    "info":"reason",
		    "list":[
		        {
		            "id":"1",
		            "val":"new"
		        },
		        {
		            "id":"3",
		            "val":"leave"
		        }
		    ]
		}
    },
    "hc_list":[]
}
EOF;

        $this->getDI()->get('logger')->write_log("syncHcToBuddy: start hc_id=".$hc_id."===state_code==".$state_code, 'info');

        $hcInfoArr = json_decode($hc_str,1);
        if(empty($hcInfoArr)){
            $this->getDI()->get("logger")->write_log("syncHcToBuddy data is error",'info');
            return;
        }

        $db = $this->getDI()->get("db");

        $sql = "select * from hr_hc where hc_id= :id";
        $hcArr = $db->fetchOne($sql,\Phalcon\Db::FETCH_ASSOC,['id'=>$hc_id]);
        if(empty($hcArr)){
            $this->getDI()->get("logger")->write_log("syncHcToBuddy hc is error==".$hc_id,'info');
            return;
        }

        //转岗的直接返回
        if($hcArr['reason_type'] == 2){
            $this->getDI()->get('logger')->write_log("syncHcToBuddy: reason_type==2==pass", 'info');
            return;
        }

        if(empty($hcArr['job_title'])){
            $this->getDI()->get('logger')->write_log("syncHcToBuddy: job_title==".$hcArr['job_title']."==pass", 'info');
            return;
        }


        $hcArr['job_title_name'] = '';
        if(!empty($hcArr['job_title'])){
            $sql = 'select * from hr_job_title where id=:job_title';
            $job_title = $db->fetchOne($sql,\Phalcon\Db::FETCH_ASSOC,['job_title'=>$hcArr['job_title']]);

            if(!empty($job_title)){
                $hcArr['job_title_name'] = $job_title['job_name'];
            }
        }

        //backyard中有事务，还没改
        if(!empty($state_code)){
            $hcArr['state_code'] = $state_code;
        }

        if(!in_array($hcArr['state_code'],[2,3,4,9])){
            $this->getDI()->get('logger')->write_log("syncHcToBuddy: state_code==".$hcArr['state_code']."==pass", 'info');
            return;
        }

        //如果不是招聘中，都算已完成
        /*if($hcArr['state_code'] != 2){
            $hcArr['state_code'] = 3;
        }*/

        $sql = "select job_id,job_name,`type` as job_type,job_description from hr_jd where job_id= :id";
        $jobArr = $db->fetchOne($sql,\Phalcon\Db::FETCH_ASSOC,['id'=>$hcArr['job_id']]);
        if(empty($jobArr)){
            $this->getDI()->get("logger")->write_log("syncHcToBuddy job is error==".$hcArr['job_id'],'info');
            return;
        }

        $hcArr = array_merge($hcArr,$jobArr);

        if(empty($hcArr['province_code'])){
            $hcArr['province_name'] = "";
            $hcArr['province_name_th'] = "";
        }else{
            $sql = "select name,en_name from sys_province where code=:code";
            $province = $db->fetchOne($sql,\Phalcon\Db::FETCH_ASSOC,['code'=>$hcArr['province_code']]);
            if(!empty($province)){
                $hcArr['province_name'] = $province['en_name'];
                $hcArr['province_name_th'] = $province['name'];
            }
        }

        if(empty($hcArr['city_code'])){
            $hcArr['city_name'] = "";
            $hcArr['city_name_th'] = "";
        }else{
            $sql = "select name,en_name from sys_city where code=:code";
            $city = $db->fetchOne($sql,\Phalcon\Db::FETCH_ASSOC,['code'=>$hcArr['city_code']]);
            if(!empty($province)){
                $hcArr['city_name'] = $city['en_name'];
                $hcArr['city_name_th'] = $city['name'];
            }
        }

        if(empty($hcArr['district_code'])){
            $hcArr['district_name'] = "";
            $hcArr['district_name_th'] = "";
        }else{
            $sql = "select name,en_name from sys_district where code=:code";
            $district = $db->fetchOne($sql,\Phalcon\Db::FETCH_ASSOC,['code'=>$hcArr['district_code']]);
            if(!empty($province)){
                $hcArr['district_name'] = $district['en_name'];
                $hcArr['district_name_th'] = $district['name'];
            }
        }


        $store_id = $hcArr['worknode_id'];

        $hcArr['store_id'] = $store_id;
        $hcArr['store_name'] = 'Head Office';
        $hcArr['category_id'] = 2;

        if($store_id == -1){


        }else{
            $sql = "select name,category from sys_store where id=:id";
            $store = $db->fetchOne($sql,\Phalcon\Db::FETCH_ASSOC,['id'=>$store_id]);
            if(!empty($store)){
                $hcArr['store_name'] = $store['name'];
                $hcArr['category_id'] = $store['category'];
            }
        }



        $hcArr['department_name'] = "";

        if(!empty($hcArr['department_id'])){
            $sql = "select * from sys_department where id=:id";
            $depart = $db->fetchOne($sql,\Phalcon\Db::FETCH_ASSOC,['id'=>$hcArr['department_id']]);
            if(!empty($depart)){
                $hcArr['department_name'] = $depart['name'];
            }
        }


        $item = [];

        foreach ($hcInfoArr['hc_info'] as $key=>$val){
            $item[$key] = $hcArr[$key];
        }

        $hcInfoArr['hc_list'][] = $item;

        $message = new MessageQueueServer();
        $res = $message->sendToMNS(MessageQueueServer::HC_QUEUE,$hcInfoArr);

        $this->getDI()->get('logger')->write_log("syncHcToBuddy: end", 'info');

        return $res;
    }

    public function getApprovedHc($staff_id)
    {
        $querySql = "SELECT 

	    hh.`hc_id` ,
	    hh.`hire_type` ,
	    hh.`worknode_id` ,
	    hj.`job_name` ,
	    case WHEN hh.`reason_type` = 1 then '招聘'
	    WHEN hh.`reason_type` = 2 then '转岗'
	    else '离职'
	    end as reason,
	    hh.demandnumber,
	    hh.priority_id
        FROM `audit_approval` aa 
        LEFT JOIN `hr_hc` hh on aa.`biz_value` = hh.`hc_id` and aa.`biz_type` = 6
        LEFT JOIN `hr_jd` hj on hj.`job_id` = hh.`job_id` 
        WHERE aa.`approval_id` = '{$staff_id}' and aa.`state` =1 and aa.`biz_type` =6";

        $dataHc = $this->getDI()->get('db_rby')->query($querySql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);

        return $dataHc;
    }

    /**
     * 新增hc 批量批次
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function insertApprovalTask($insetData = [])
    {
            $this->db->insertAsDict(
            'hc_approval_task', $insetData
        );
        $taskId= $this->db->lastInsertId();
        return $taskId;
    }

    public function insertPriorityRecord($insertData =[]){
        $this->db->insertAsDict(
            'hc_priority_record', $insertData
        );
        $taskId= $this->db->lastInsertId();
        return $taskId;
    }



    /**
     * @description: 记录修改 hc 的记录  主要是 优先级和需求人数, 注意这个方法要在修改之前调用
     * @param $hc_ids  str  hc_ids 示例:1,2
     * @param $staff_info_id  int  修改人
     * @param $after_data  []  修改的数据
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2022/4/20 18:45
     */
    public function addHrhcSaveLog($hc_ids = '', $staff_info_id = '', $after_data = []): bool
    {
        $result = false;

        if (empty($hc_ids)) {
            return $result;
        }
        $hc_ids_arr = explode(',', $hc_ids);
        foreach ($hc_ids_arr as $k => $v) {
            if (strstr($v, '\'') !== false || strstr($v, '"') !== false) {
                $hc_id = intval(substr($v, 1, -1));
            } else {
                $hc_id = $v;
            }
            //先查询一下 hc
            $hr_hc_data = HrhcModel::findFirst([
                'conditions' => "hc_id = :hc_id:",
                'bind'       => ['hc_id' => $hc_id],
            ]);
            if (empty($hr_hc_data) || empty($staff_info_id) || empty($after_data)) {
                throw new \Exception('没有找到 hc ==>hr_hc_id ==> '.$hc_id.' staff_info_id =>'.$staff_info_id);
            }
            $update_data = $after_data;

            $hr_hc_data                 = $hr_hc_data->toArray();
            $update_data['demandnumber'] = $after_data['demandnumber'] ?? $hr_hc_data['demandnumber'];//如果没有传就代表没有修改
            $update_data['priority_id']  = $after_data['priority_id'] ?? $hr_hc_data['priority_id'];  //如果没有传就代表没有修改
            $update_data['state_code']   = $after_data['state_code'] ?? $hr_hc_data['state_code'];    //如果没有传就代表没有修改
            $update_data['expirationdate'] =  $after_data['expirationdate'] ?? $hr_hc_data['expirationdate'];//如果没有传就代表没有修改

            $before_data = [];
            foreach ($update_data as $key => $val) {
                $before_data[$key] = $hr_hc_data[$key] ?? '';
            }
            //判断一下是否修改了优先级和需求人数
            $is_save_number_priority = HrHcLogModel::$is_save_number_priority_1;
            if ($before_data['demandnumber'] != $update_data['demandnumber'] || $before_data['priority_id'] != $update_data['priority_id']) {
                $is_save_number_priority = HrHcLogModel::$is_save_number_priority_2;
            }

            $insert = [
                'hc_id'                   => $hr_hc_data['hc_id'],
                'before_demandnumber'     => $before_data['demandnumber'],
                'after_demandnumber'      => $update_data['demandnumber'],
                'before_priority_id'      => $before_data['priority_id'],
                'after_priority_id'       => $update_data['priority_id'],
                'staff_info_id'           => $staff_info_id,
                'created_at'              => gmdate('Y-m-d H:i:s'),
                'before_data'             => json_encode($before_data, JSON_UNESCAPED_UNICODE),
                'after_data'              => json_encode($update_data, JSON_UNESCAPED_UNICODE),
                'is_save_number_priority' => $is_save_number_priority,
                'type'                    => HrHcLogModel::TYPE_DEFAULT,
            ];
            $insert_data = [];
            //1 修改优先级
            if ($before_data['priority_id'] != $update_data['priority_id']) {
                $insert['type'] = HrHcLogModel::TYPE_PRIORITY_ID;
                $insert_data[]  = $insert;
            }
            //2 激活 hc
            if ($before_data['state_code'] != $update_data['state_code'] && $update_data['state_code'] == HrhcModel::STATE_RECRUITING) {
                $insert['type'] = HrHcLogModel::TYPE_STATE_CODE_ACTIVATION;
                $insert_data[]  = $insert;
            }
            //3 作废 hc
            if ($before_data['state_code'] != $update_data['state_code'] && $update_data['state_code'] == HrhcModel::STATE_VOIDED) {
                $insert['type'] = HrHcLogModel::TYPE_STATE_CODE_VOID;
                $insert_data[]  = $insert;
            }
            //4. 修改截止时间
            if (date('Y-m-d',strtotime($before_data['expirationdate'])) != date('Y-m-d',strtotime($update_data['expirationdate']))) {
                $insert['type'] = HrHcLogModel::TYPE_EXPIRATIONDATE;
                $insert_data[]  = $insert;
            }
            // 5. 修改需求人数
            if ($before_data['demandnumber'] != $update_data['demandnumber']) {
                $insert['type'] = HrHcLogModel::TYPE_DEMANDNUMBER;
                $insert_data[]  = $insert;
            }
            if (empty($insert_data)) {
                $insert_data[] = $insert;
            }

            $result = $this->batch_insert('hr_hc_log', $insert_data);
        }


        return $result;
    }

    public static function getOne($params, $columns = ['*'])
    {
        if(empty($params)) {
            return [];
        }

        $conditions = '1 = 1';
        $bind = [];


        if(!empty($params['hc_id'])) {
            $conditions .= ' and hc_id = :hc_id:';
            $bind['hc_id'] = $params['hc_id'];
        }

        $data = HrhcModel::findFirst([
            'columns'    => $columns,
            'conditions' => $conditions,
            'bind'       => $bind,
        ]);

        return empty($data) ? [] : $data->toArray();
    }

    /**
     * 获取hc信息
     * @param $hcId
     * @return array
     */
    public function getInfoById($hcId): array
    {
        if (empty($hcId)) {
            return [];
        }

        $hcInfo = HrhcModel::findFirst([
            'conditions' => 'hc_id = :hc_id:',
            'bind'       => [
                'hc_id' => $hcId,
            ]
        ]);

        return $hcInfo ? $hcInfo->toArray() : [];
    }
}
