<?php


namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\Models\backyard\WhrPermissionOperatorLogModel;

class PermissionOperatorLog extends BaseServer
{
    public function add($params)
    {
        if (empty($params['permission_ids'])) {
            return false;
        }

        $whrPermissionOperatorLogModel = new WhrPermissionOperatorLogModel();
    
        $whrPermissionOperatorLogModel->res_type = $params['res_type'] ?: WhrPermissionOperatorLogModel::ResTypeRole;
        $whrPermissionOperatorLogModel->res_id = $params['res_id'] ?: 0;
        $whrPermissionOperatorLogModel->action = $params['action'] ?: WhrPermissionOperatorLogModel::ACTION_ADD;
        $whrPermissionOperatorLogModel->operator_id = $params['operator_id'] ?: $this->userInfo['id'];
        $whrPermissionOperatorLogModel->permission_ids = is_array($params['permission_ids']) ? implode(',',$params['permission_ids']) : $params['permission_ids'];
        
        return $whrPermissionOperatorLogModel->save();
    }
}