<?php

namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\Repository\StaffRepository;
use  FlashExpress\bi\App\Repository\DepartmentRepository;
use FlashExpress\bi\App\Repository\StatisticalRepository;
use  FlashExpress\bi\App\Repository\SysListRepository;

class StatisticalServer extends BaseServer
{
    public $staff;
    public $department;
    public $sysList;
    public $statistical;

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 按网点统计招聘中的 待招和已招
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function getStatisticalStore($paramIn = []){
        $this->sysList = new SysListRepository();
        $this->statistical = new StatisticalRepository();

        $userinfo = $paramIn['userinfo'];

        /* 获取工作网点列表 */
        $worknodeData = $this->sysList->getStoreList();
        $worknodeData = array_column($worknodeData, 'name', 'id');

        $data = $this->statistical->getStatisticalStore();
        $reData = array();
        foreach ($data as $k=>$v){
            $store_id = isset($v['store_id'])?$v['store_id']:'-1';
            $reData[$v["stat_date"]][$store_id]['store_name'] = isset($worknodeData[$v['store_id']])?$worknodeData[$v['store_id']]:'';
            $reData[$v["stat_date"]][$store_id]['store_id'] = $store_id;
            $reData[$v["stat_date"]][$store_id]['stat_date'] = isset($v['stat_date'])?$v['stat_date']:'';
            $reData[$v["stat_date"]][$store_id]['hc_demandnumber'] = isset($v['hc_demandnumber'])?$v['hc_demandnumber']:'';
            $reData[$v["stat_date"]][$store_id]['hc_surplusnumber'] = isset($v['hc_surplusnumber'])?$v['hc_surplusnumber']:'';
            $reData[$v["stat_date"]][$store_id]['hc_finishnumber'] = isset($v['hc_finishnumber'])?$v['hc_finishnumber']:'';
            $reData[$v["stat_date"]][$store_id]['updated_at'] = isset($v['updated_at'])?$v['updated_at']:'';
        }
        $returnData['data']['dataList'] = $reData;
        return $this->checkReturn($returnData);
    }

    /**
     * 按部门统计招聘中的 待招和已招
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function getStatisticalDepartment($paramIn = []){
        $this->sysList = new SysListRepository();
        $this->statistical = new StatisticalRepository();

        $userinfo = $paramIn['userinfo'];

        /* 获取部门列表 */
        $departmentDataList = $this->sysList->getDepartmentList();
        $departmentDataList = array_column($departmentDataList, 'name', 'id');

        $data = $this->statistical->getStatisticalDepartment();
        $reData = array();
        foreach ($data as $k=>$v){
            $department_id = isset($v['department_id'])?$v['department_id']:'';
            $reData[$v["stat_date"]][$department_id]['department_name'] = isset($departmentDataList[$v['department_id']])?$departmentDataList[$v['department_id']]:'';
            $reData[$v["stat_date"]][$department_id]['department_id'] = $department_id;
            $reData[$v["stat_date"]][$department_id]['stat_date'] = isset($v['stat_date'])?$v['stat_date']:'';
            $reData[$v["stat_date"]][$department_id]['hc_demandnumber'] = isset($v['hc_demandnumber'])?$v['hc_demandnumber']:'';
            $reData[$v["stat_date"]][$department_id]['hc_surplusnumber'] = isset($v['hc_surplusnumber'])?$v['hc_surplusnumber']:'';
            $reData[$v["stat_date"]][$department_id]['hc_finishnumber'] = isset($v['hc_finishnumber'])?$v['hc_finishnumber']:'';
            $reData[$v["stat_date"]][$department_id]['updated_at'] = isset($v['updated_at'])?$v['updated_at']:'';
        }
        $returnData['data']['dataList'] = $reData;
        return $this->checkReturn($returnData);
    }

    /**
     * 按大区统计招聘中的 待招和已招
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function getStatisticalArea($paramIn = []){
        $this->sysList = new SysListRepository();
        $this->statistical = new StatisticalRepository();

        $userinfo = $paramIn['userinfo'];

        /* 获取部门列表 */
        $storeAreaDataList = $this->sysList->getStoreArea();
        $storeAreaDataList = array_column($storeAreaDataList, null, 'id');
        $data = $this->statistical->getStatisticalStore();
        $areasArr = UC('entry')['areasArr'];
        $reData = array();
        foreach ($data as $k=>$v){
            $area_code = isset($storeAreaDataList[$v['store_id']]['manage_geography_code'])?$storeAreaDataList[$v['store_id']]['manage_geography_code']:'-1';
            $area_name = isset($storeAreaDataList[$v['store_id']]['sorting_no'])?$storeAreaDataList[$v['store_id']]['sorting_no']:'-1';
            $hc_demandnumber = isset($v['hc_demandnumber'])?($reData[$v["stat_date"]][$area_name]['hc_demandnumber']+$v['hc_demandnumber']):$reData[$v["stat_date"]][$area_name]['hc_demandnumber'];
            $hc_surplusnumber = isset($v['hc_surplusnumber'])?($reData[$v["stat_date"]][$area_name]['hc_surplusnumber']+$v['hc_surplusnumber']):$reData[$v["stat_date"]][$area_name]['hc_surplusnumber'];
            $hc_finishnumber = isset($v['hc_finishnumber'])?($reData[$v["stat_date"]][$area_name]['hc_finishnumber']+$v['hc_finishnumber']):$reData[$v["stat_date"]][$area_name]['hc_finishnumber'];
            $reData[$v["stat_date"]][$area_name]['area_code'] = $area_code;
            $reData[$v["stat_date"]][$area_name]['area_name'] = $area_name;
            $reData[$v["stat_date"]][$area_name]['area_val'] = isset($areasArr[$area_code])?$areasArr[$area_code]:'';
            $reData[$v["stat_date"]][$area_name]['store_id'] = isset($v['store_id'])?$v['store_id']:'';
            $reData[$v["stat_date"]][$area_name]['stat_date'] = isset($v['stat_date'])?$v['stat_date']:'';
            $reData[$v["stat_date"]][$area_name]['hc_demandnumber'] = $hc_demandnumber;
            $reData[$v["stat_date"]][$area_name]['hc_surplusnumber'] = $hc_surplusnumber;
            $reData[$v["stat_date"]][$area_name]['hc_finishnumber'] = $hc_finishnumber;
            $reData[$v["stat_date"]][$area_name]['updated_at'] = isset($v['updated_at'])?$v['updated_at']:'';
        }
        $returnData['data']['dataList'] = $reData;
        return $this->checkReturn($returnData);
    }
}