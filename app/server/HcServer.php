<?php

namespace FlashExpress\bi\App\Server;

use Exception;
use FlashExpress\bi\App\Helper\PermissionHelper;
use FlashExpress\bi\App\library\enumsMy;
use FlashExpress\bi\App\library\enumsPh;
use FlashExpress\bi\App\library\enumsTh;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Library\OssHelper;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\RocketMQ;
use FlashExpress\bi\App\library\Tools;
use FlashExpress\bi\App\Models\backyard\HrEntryModel;
use FlashExpress\bi\App\Models\backyard\HrhcModel;
use FlashExpress\bi\App\Models\backyard\HrInterviewModel;
use FlashExpress\bi\App\Models\backyard\HrInterviewOfferModel;
use FlashExpress\bi\App\Models\backyard\HrJdModel;
use FlashExpress\bi\App\Models\backyard\HrJobDepartmentRelationModel;
use FlashExpress\bi\App\Models\backyard\HrOrganizationDepartmentStoreRelationModel;
use FlashExpress\bi\App\Models\backyard\HrResumeModel;
use FlashExpress\bi\App\Models\backyard\HRStaffingModel;
use FlashExpress\bi\App\Models\backyard\HrStaffManageDepartmentModel;
use FlashExpress\bi\App\Models\backyard\JobTransferModel;
use FlashExpress\bi\App\Models\backyard\SendEmailRecordModel;
use FlashExpress\bi\App\Models\backyard\SettingEnvModel;
use FlashExpress\bi\App\Models\backyard\SysDepartmentModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Models\backyard\WinHrAsyncImportTaskModel;
use FlashExpress\bi\App\Repository\BaseRepository;
use FlashExpress\bi\App\Repository\FlashJdRepository;
use  FlashExpress\bi\App\Repository\HcRepository;
use FlashExpress\bi\App\Repository\HrJobDepartmentRelationRepository;
use FlashExpress\bi\App\Repository\HrJobTitleRepository;
use FlashExpress\bi\App\Repository\HrStaffContractRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use  FlashExpress\bi\App\Repository\DepartmentRepository;
use  FlashExpress\bi\App\Repository\ResumeRepository;
use FlashExpress\bi\App\Repository\SysDepartmentRepository;
use  FlashExpress\bi\App\Repository\SysListRepository;
use  FlashExpress\bi\App\Repository\PermissionsRepository;
use  FlashExpress\bi\App\Repository\OfferRepository;
use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\Repository\SysManagePieceRepository;
use FlashExpress\bi\App\Repository\SysManageRegionRepository;
use FlashExpress\bi\App\Repository\SysStoreRepository;
use FlashExpress\bi\App\Repository\WinHrAsyncImportTaskRepository;
use PhpOffice\PhpSpreadsheet\IOFactory;
use FlashExpress\bi\App\library\Excel;
use FlashExpress\bi\App\Models\backyard\HrHcLogModel;


class HcServer extends BaseServer
{
    const ROLE_OPERATION_MANAGER = 8; //运营经理角色
    const ROLE_SYSTEM_MANAGER = 14;//系统管理员角色
    const ROLE_GENERAL_MANAGER = 15;//总经理
    const ROLE_HR_SPECIALIST = 16;//人事专员
    const ROLE_TALENT_ACQUISITION = 81;//人事专员
    const ROLE_HR_MANAGEMENT = 17;//人事管理员
    const ROLE_DOT_ADMIN = 18;//网点主管
    const ROLE_AREA_MANAGER = 21;//区域经理
    const ROLE_INTERNATIONAL_HR_MANAGER = 35;//国际人力经理
    const ROLE_HRIS_MANAGER = 41;//HRIS管理员
    const ROLE_PAYROLL_MANAGER = 42;//薪酬管理员ST
    const ROLE_HR_OFFICER = 43;//人力资源
    const ROLE_HRIS_SALES = 51;//HRIS业务员

    public $hc;
    public $jd;
    public $department;
    public $resume;
    public $offer;
    public $sysList;
    public $permissions;

    public $priorityArr  = [];

    public static $network_operations_dept_id;
    public static $network_bulky_operations_dept_id;
    public static $department_store_job_title_hc_num = [];

    public $handleDiscrepancyHcTask = [];

    public $excelAddHeader = [
        0 => ['所属部门ID', 'Department ID', 'แผนก ID'],
        1 => ['职位ID', 'Position ID', 'ตำแหน่ง ID'],
        2 => ['JD ID', 'JD name', 'JD',],
        3 => ['雇佣类型', 'Type of employment', 'ประเภทการจ้างงาน'],
        4 => ['工作地点 ID', 'work place ID', 'สถานที่ทำงาน ID'],
        5 => ['截至日期', 'Deadline', 'วันสิ้นสุด'],
        6 => ['招聘优先级', 'Hiring Priorities', 'ระดับความเร่งด่วนในการสรรหา'],
        7 => ['用人原因', 'Reason for employing', 'สาเหตุที่ใช้คน'],
        8 => ['需求人数', 'Number of recruits', 'จำนวนคนที่ต้องการ'],
    ];

    public function __construct()
    {
        parent::__construct();
        //状态表code,1=审批中，2=招聘中，3=已完成，4=已作废，5=已拒绝，6=已同意，7=待审批，8=申请，9=过期
        /* hc优先级*/
        $this->priorityArr = PermissionHelper::getUcPriorityArr();
    }

    /**
     * 格式化输出hc的优先级
     * @param int $priority_id
     * @return string
     */
    public static function formatPriority(int $priority_id): string
    {
        $str = "P%d";
        return sprintf($str,$priority_id);
    }

    /**
     * hc列表
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getHcList($paramIn = [])
    {
        $this->hc         = new HcRepository();
        $this->department = new DepartmentRepository();
        $this->sysList    = new SysListRepository();

        $pageSize  = $this->processingDefault($paramIn, 'page_size', 2, 10);
        $pageNum   = $this->processingDefault($paramIn, 'page_num', 2, 1);
        $export    = $this->processingDefault($paramIn, 'export', 2, 0);
        $hcNullify = $this->processingDefault($paramIn, 'hcNullify', 2, 0);
        $hcUpdate  = $this->processingDefault($paramIn, 'hcUpdate', 2, 0);
        $is_count  = $this->processingDefault($paramIn, 'is_count', 2, 0);

        //查询未删除的数据
        $paramIn['deleted'] = 1;
        /* 检查搜索条件 */
        $param = [
            'job_name',
            'department_id',
            'worknode_id',
            'createtime_start',
            'createtime_end',
            'hc_id',
            'state_code',
            'approval_stage',
            'approval_state_code',
            'approval_completion_time_start',
            'approval_completion_time_end',
            'reason_type',
            'deleted',
            'job_title',
            'hire_type',
            'is_sub_department',
        ];
        $searchWhere = checkParam($param, $paramIn);
        if (empty($searchWhere['department_id']) && $this->ifHrisSales) {
            $searchWhere['department_id'] = $this->department->getDepartmentIdsByRole();
            if (empty($searchWhere['department_id'])) {
                return $this->checkReturn(['data' => [], 'msg' => '当前用户角色是HrisSales，所属部门异常，数据不存在！']);
            }
        }

        $searchWhere['priority_id'] = trim($paramIn['priority_id'] ?? '');

        /* 获取hc列表数据 */
        $paramHc = [
            'searchWhere' => $searchWhere,
            'userInfo'    => $this->userInfo,
            'page_size'   => $pageSize,
            'page_num'    => $pageNum,
            'export'      => $export,
            'is_count'    => $is_count,
        ];

        $data = $this->hc->hcListNew($paramHc);

        if ($is_count) {
            return $data;
        }

        if (empty($data['data'])) {
            return $this->checkReturn($data);
        }
        /*获取hc列表数组 */
        $hcData = $data['data'];
        /* 获取分页数组 */
        $pageArr = $data['pagination'];

        /* 获取员工id对应名字数组 */
        $submitter_staff_ids = array_column($hcData, "submitter_id"); //提交人ID
        $interviewer_staff_ids = array_column($hcData, "interviewer"); //面试官ID

        $staffIdsArr = array_unique(array_merge($submitter_staff_ids, $interviewer_staff_ids));
        if(!empty($staffIdsArr)){
            $staffIds = getIdsStr($staffIdsArr);
            $staffList = $this->sysList->getStaffList([
                "ids" => $staffIds,
            ]);
        }
        $staffData = isset($staffList) ? array_column($staffList, null, 'staff_info_id') :[];

        /* 获取hc负责人信息 */
        $manager_staff_ids = array_column($hcData, "manager_staff_id"); //负责人ID
        if (!empty($manager_staff_ids)) {
            $manager_staffs = HrStaffInfoModel::find([
                'conditions' => ' staff_info_id in ({staff_ids:array}) ',
                'columns' => 'staff_info_id, name, nick_name',
                'bind' => [
                    'staff_ids' => $manager_staff_ids,
                ],
            ])->toArray();
            $manager_staffs_map = array_column($manager_staffs, null, 'staff_info_id');
        }


        //查询部门名称 ids
        $departmentDataList = array_column((new SysDepartmentRepository())->getDepartmentListFromCache(['id','name']), 'name', 'id');


        //查询网点名称 ids

        $worknodeData = array_column((new SysStoreRepository())->getStoreListFromCache(['id','name','sorting_no']), null, 'id');
        $worknodeData['-1'] = ['name'=>enums::HEAD_OFFICE,'sorting_no'=>'','id'=>'-1'];


        //查询职位名称 ids
        $positionList = array_column((new HrJobTitleRepository())->getJobTitleListFromCache(['id','job_name']), 'job_name', 'id');

        // 获取hc状态列表
        $hcStateList = $this->hc->getHcStateListFromCache();
        $hcStateList = array_column($hcStateList, 'state_value', 'state_code');

        self::$network_operations_dept_id = SettingEnvServer::getSetVal('dept_network_operations_id');
        self::$network_bulky_operations_dept_id = SettingEnvServer::getSetVal('dept_network_bulky_operations_id');


        /* 业务处理 */
        foreach ($hcData as $k => $v) {
            if ($v['submitter_id']) {
                if ($staffData[$v['submitter_id']]["organization_type"] == 1) {
                    //网点
                    $hcData[$k]['submitter_department_id'] = $staffData[$v['submitter_id']]["store_id"];
                    $hcData[$k]['submitter_department_name'] = $worknodeData[$staffData[$v['submitter_id']]["store_id"]]["name"];
                } else if ($staffData[$v['submitter_id']]["organization_type"] == 2) {
                    //部门
                    $hcData[$k]['submitter_department_id'] = $staffData[$v['submitter_id']]["store_id"];
                    $hcData[$k]['submitter_department_name'] = $departmentDataList[$staffData[$v['submitter_id']]["store_id"]];
                }
                $hcData[$k]['submitter_mobile'] = $staffData[$v['submitter_id']]["mobile"];
                $hcData[$k]['submitter_job_title'] = $v['submitter_id'] == 17245 ? 'Chief Operating Officer' : $positionList[$staffData[$v['submitter_id']]["job_title"]];
                $hcData[$k]['submitter_organization_type'] = $staffData[$v['submitter_id']]['organization_type'];
            }
            $hcData[$k]['department_name'] = $departmentDataList[$v['department_id']];
            $hcData[$k]['worknode_name'] = $worknodeData[$v['worknode_id']]["name"];
            $hcData[$k]['sorting_no'] = $worknodeData[$v['worknode_id']]["sorting_no"];
            $hcData[$k]['submitter_name'] = $staffData[$v['submitter_id']]["name"];
            $hcData[$k]['priority_name'] = $this->priorityArr[$v['priority_id']] ? $this->getTranslation()->_($this->priorityArr[$v['priority_id']]) : '';

            $hcData[$k]['hcNullify'] = $hcNullify;
            $hcData[$k]['reason_type'] = $v['reason_type'];
            $hcData[$k]['reason_type_txt'] = str_replace(["招聘", "Recruitment"], ["新增", "Add"], $this->getTranslation()->_("hc_reason_type_" . $v['reason_type']));
            $hcData[$k]['job_title_name'] = !empty($v['job_title_id']) ? sprintf("(%s) %s", $v['job_title_id'], $positionList[$v['job_title_id']]) : '';
            $hcData[$k]['job_title_proper_name'] = !empty($v['job_title_id']) ? $positionList[$v['job_title_id']] : '';

            $hcData[$k]['hire_type_text'] = $v['hire_type'] ? $this->getTranslation()->_('hire_type_' . $v['hire_type']) : '';
            if (in_array($v['hire_type'], [3, 4])) {
                $hcData[$k]['hire_times_text'] = $v['hire_times'] . $this->getTranslation()->_('daily');
            } else if (in_array($v['hire_type'], HrStaffInfoModel::$hireTypeMonthList)) {
                $hcData[$k]['hire_times_text'] = $v['hire_times'] . $this->getTranslation()->_('monthly');
            }

            if ($hcUpdate == 1 && in_array($v['state_code'], [1, 2, 3, 4, 9]) && in_array($v['approval_state_code'], [5, 6])) {
                //验证是否有修改基本权限，验证hc主状态是否是未生效，招聘中，已招满，已作废，验证审批阶段是否是已完成审批，验证审批状态是否是已同意或已驳回
                $hcData[$k]['hcUpdate'] = $hcUpdate;
            } else {
                $hcData[$k]['hcUpdate'] = 0;
            }
            $hcData[$k]['interview_charge'] = isCountry('TH') ? '(19921)ธีรภัทร ตระการวิจิตร' : '';
            $hcData[$k]['state_value'] = $this->getTranslation()->_($v['state_value']);
            $hcData[$k]['interviewer'] = $v['interviewer'] ? $staffData[$v['interviewer']]["name"] . '(' . $v['interviewer'] . ')' : '';
            if ($hcStateList[$v['approval_state_code']] == '4004') {
                $hcData[$k]['approval_state_code_name'] = $hcStateList[$v['approval_state_code']] ? $this->getTranslation()->_('4031') : '';
            } else {
                $hcData[$k]['approval_state_code_name'] = $hcStateList[$v['approval_state_code']] ? $this->getTranslation()->_($hcStateList[$v['approval_state_code']]) : '';
            }
            if ($v['approval_stage'] == 1 && $v['state_code'] == 1) {
                $hcData[$k]['approval_stage_name'] = $this->getTranslation()->_('4024');
            } else if ($v['approval_stage'] == 2 && $v['state_code'] == 1) {
                $hcData[$k]['approval_stage_name'] = $this->getTranslation()->_('4025');
            } else if ($v['approval_stage'] == 3 && $v['state_code'] == 1) {
                $hcData[$k]['approval_stage_name'] = $this->getTranslation()->_('4038');
            } else if (($v['approval_stage'] == 4 || $v['approval_stage'] == 3) && $v['state_code'] != 1) {
                $hcData[$k]['approval_stage_name'] = $this->getTranslation()->_('4026');
            } else {
                $hcData[$k]['approval_stage_name'] = '';
            }
            if ($v['approval_state_code'] == 5) {
                $hcData[$k]['approval_stage_name'] = $this->getTranslation()->_('4026');
            }

            $hcData[$k]['manager_staff_name'] = '';
            $hcData[$k]['manager_staff_id'] = empty($v['manager_staff_id']) ? '' : $v['manager_staff_id'];

            if (isset($manager_staffs_map[$v['manager_staff_id']])) {
                $current_staff = $manager_staffs_map[$v['manager_staff_id']];
                $hcData[$k]['manager_staff_name'] = $current_staff["nick_name"] ? $current_staff["nick_name"] : $current_staff["name"];
            }
            //列表编辑按钮操作是否展示使用的hcUpdate字段，1：展示，0：不展示
            //以下按钮是在编辑按钮展示的条件下才会显示的
            $hcData[$k]['edit_void_btn_show'] = $this->_isShowVoidBtn($v['state_code']); //作废按钮 1展示，0不展示

            $hcData[$k]['edit_activate_btn_show'] = $this->_isShowActivateBtn($v['state_code'], $v['department_id'], $v['worknode_id']);//激活按钮 1展示，0不展示

            $hcData[$k]['edit_update_number_btn_show'] = $this->_isShowUpdateNumberBtn($v['state_code'], $v['department_id'], $v['worknode_id']);//修改需求人数按钮 1展示，0不展示

            $hcData[$k]['edit_update_time_btn_show'] = $this->_isShowUpdateTimeBtn($v['state_code'], $v['department_id'], $v['worknode_id']);//修改截止时间 按钮 1展示，0不展示

            $hcData[$k]['edit_update_other_btn_show'] = $this->_isShowUpdateOtherBtn($v['state_code'], $v['department_id'], $v['worknode_id']);//修改其他信息按钮 1展示，0不展示


            $working_day_rest_type = [];
            if(!empty($v['working_day_rest_type'])) {
                $working_day_rest_type = explode(',', $v['working_day_rest_type']);
                array_walk($working_day_rest_type, function (&$val) {
                    $val = $this->getTranslation()->_('working_day_rest_type_' . $val);
                });
            }

            $hcData[$k]['working_day_rest_type_text'] = $working_day_rest_type;//工作天数轮休规则


        }
        /* 返回格式 */
        $returnArr['data']['dataList'] = $hcData;
        $returnArr['data']['pagination'] = $pageArr;
        return $this->checkReturn($returnArr);
    }

    /**
     * 作废 按钮展示权限
     *
     * @param $hc_state_code
     * @return int
     */
    private function _isShowVoidBtn($hc_state_code)
    {

        //该逻辑是将前端判断逻辑迁移到后端，返给前端是否展示的标识字段即可
        if (in_array($hc_state_code, [2, 3])) {
            $is_show = 1;
        } else {
            $is_show = 0;
        }
        return $is_show;
    }

    /**
     * 获取关联数据通过hcid
     * @return array
     * @throws ValidationException
     */
    public function getRelationInfoByHcId($hcId): array
    {
        if (empty($hcId)) {
            throw new ValidationException(self::$t->_('operation_data_not_exist'));
        }

        //获取hc信息
        $hcInfo = HrhcModel::findFirst([
            'columns'    => 'hc_id,job_title,department_id,job_id',
            'conditions'=>'hc_id=:hc_id:',
            'bind'=>['hc_id'=>$hcId],
        ]);

        if (empty($hcInfo)) {
            return [];
        }

        $hcInfo = $hcInfo->toArray();

        $relation = HrJobDepartmentRelationModel::findFirst([
            'columns'    => 'job_id as job_title,jd_desc,job_requirements_jybj,job_requirements_zyjl,jd_desc_supply',
            'conditions' => 'department_id = :department_id: and job_id = :job_id:',
            'bind'       => [
                'department_id' => $hcInfo['department_id'],
                'job_id'        => $hcInfo['job_title'],
            ],
        ]);

        if (empty($relation)) {
            return [];
        }

        $relation = $relation->toArray();

        //获取职位名称
        $jobInfo = (new HrJobTitleRepository())->getInfoById($relation['job_title']);

        return [
            'job_title'             => $relation['job_title'] ?? '',
            'job_title_name'        => $jobInfo['job_name'] ?? '',
            'jd_desc'               => !empty($relation['jd_desc']) ? htmlspecialchars_decode($relation['jd_desc'],ENT_QUOTES) : '',
            'jd_desc_supply'        => !empty($relation['jd_desc_supply']) ? htmlspecialchars_decode($relation['jd_desc_supply'],ENT_QUOTES) : '',
            'job_requirements_jybj' => !empty($relation['job_requirements_jybj']) ? htmlspecialchars_decode($relation['job_requirements_jybj'],ENT_QUOTES) : '',
            'job_requirements_zyjl' => !empty($relation['job_requirements_zyjl']) ? htmlspecialchars_decode($relation['job_requirements_zyjl'],ENT_QUOTES) : '',
        ];
    }

    /**
     * 激活 按钮展示权限
     *
     * @param $hc_state_code
     * @param $hc_dept_id
     * @param $hc_worknode_id
     * @return int
     */
    private function _isShowActivateBtn($hc_state_code, $hc_dept_id, $hc_worknode_id)
    {

        //该逻辑是将前端判断逻辑迁移到后端，返给前端是否展示的标识字段即可
        $network_depts = [
            self::$network_operations_dept_id,
            self::$network_bulky_operations_dept_id,
        ];
        $country_code = strtoupper(env('country_code'));
        if ($country_code == 'TH' && in_array($hc_dept_id, $network_depts) && $hc_worknode_id != '-1') {
            //泰国 不显示逻辑
            $is_show = 0;
        } elseif ($hc_dept_id == self::$network_operations_dept_id || !in_array($hc_state_code, [1, 4])) {
            //非泰国 不显示逻辑
            $is_show = 0;
        } else {
            $is_show = 1;
        }

        return $is_show;
    }

    /**
     * 修改需求人数 按钮展示权限
     *
     * @param $hc_state_code
     * @param $hc_dept_id
     * @param $hc_worknode_id
     * @return int
     */
    private function _isShowUpdateNumberBtn($hc_state_code, $hc_dept_id, $hc_worknode_id)
    {

        //该逻辑是将前端判断逻辑迁移到后端，返给前端是否展示的标识字段即可
//        $network_depts = [
//            self::$network_operations_dept_id,
//            self::$network_bulky_operations_dept_id
//        ];
//        $country_code = strtoupper(env('country_code'));
//        if($country_code == 'TH' && in_array($hc_dept_id,$network_depts) && $hc_worknode_id != '-1' ){
//            //泰国 不显示逻辑
//            $is_show = 0;
//        }elseif($hc_state_code != 2){
//            //非泰国 不显示逻辑
//            $is_show = 0;
//        }else{
//            $is_show = 1;
//        }

        //"【紧急-需4.11上线-全部国家-WHR】修改需求人数入口放开 "该邮件中提出修改此处逻辑
        $is_show = 0; //不显示
        if ($hc_state_code == 2) {
            $is_show = 1; //显示
        }

        return $is_show;
    }

    /**
     * 修改截止时间 按钮展示权限
     *
     * @param $hc_state_code
     * @param $hc_dept_id
     * @param $hc_worknode_id
     * @return int
     */
    private function _isShowUpdateTimeBtn($hc_state_code, $hc_dept_id, $hc_worknode_id)
    {

        //该逻辑是将前端判断逻辑迁移到后端，返给前端是否展示的标识字段即可
        $network_depts = [
            self::$network_operations_dept_id,
            self::$network_bulky_operations_dept_id,
        ];
        $country_code = strtoupper(env('country_code'));
        if ($country_code == 'TH' && in_array($hc_dept_id, $network_depts) && $hc_worknode_id != '-1') {
            //泰国 不显示逻辑
            $is_show = 0;
        } elseif ($hc_dept_id == self::$network_operations_dept_id || !in_array($hc_state_code, [2, 9])) {
            //非泰国 不显示逻辑
            $is_show = 0;
        } else {
            $is_show = 1;
        }

        return $is_show;
    }

    /**
     * 修改其他信息 按钮展示权限
     *
     * @param $hc_state_code
     * @param $hc_dept_id
     * @param $hc_worknode_id
     * @return int
     */
    private function _isShowUpdateOtherBtn($hc_state_code, $hc_dept_id, $hc_worknode_id)
    {

        //该逻辑是将前端判断逻辑迁移到后端，返给前端是否展示的标识字段即可
        $network_depts = [
            self::$network_operations_dept_id,
            self::$network_bulky_operations_dept_id,
        ];
        $country_code = strtoupper(env('country_code'));
        if ($country_code == 'TH' && in_array($hc_dept_id, $network_depts) && $hc_worknode_id != '-1') {
            //泰国 不显示逻辑
            $is_show = 0;
        } elseif ($this->userInfo['is_admin'] == enums::$is_admin['off'] || !in_array($hc_state_code, [1, 2])) {
            //非泰国 不显示逻辑
            $is_show = 0;
        } else {
            $is_show = 1;
        }

        return $is_show;
    }

    /**
     * 设置负责人工号
     * @param $paramIn
     * @return mixed
     */
    public function saveHcManager($paramIn)
    {
        $hc_id = $paramIn['hc_id'];
        $manager_staff_id = $paramIn['manager_staff_id'] ?? 0;
        $result = $this->getDI()->get('db')->updateAsDict(
            'hr_hc',
            [
                'manager_staff_id' => intval($manager_staff_id),
            ],
            'hc_id = ' . $hc_id
        );
        return $result;
    }

    /**
     * 我的hc列表
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function myHcList($paramIn)
    {
        $this->hc = new HcRepository();
        $this->sysList = new SysListRepository();

        $pageSize = $this->processingDefault($paramIn, 'page_size', 2, 10);
        $pageNum = $this->processingDefault($paramIn, 'page_num', 2, 1);
        $userinfo = $paramIn['userinfo'];

        //查询未删除的数据
        $paramIn['deleted'] = 1;

        /* 检查搜索条件 */
        $parpam = [
            'job_name',
            'department_id',
            'worknode_id',
            'time_start',
            'time_end',
            'createtime_start',
            'createtime_end',
            'hc_id',
            'state_code',
            'approval_state_code',
            'approval_stage',
            'deleted',
            'job_title',
            'hire_type',
            'is_sub_department',
        ];
        $searchWhere = checkParam($parpam, $paramIn);
        $searchWhere['submitter_id'] = $userinfo['id'];
        $searchWhere['priority_id'] = trim($paramIn['priority_id'] ?? '');

        /* 获取我的hc列表数据 */
        $paramHc = [
            'searchWhere' => $searchWhere,
            'userInfo' => $this->userInfo,
            'page_size' => $pageSize,
            'page_num' => $pageNum,
            'fun' => 'myHcList',
        ];

        $data = $this->hc->hcListNew($paramHc);

        if (empty($data['data'])) {
            $returnArr['data']['dataList'] = [];
            $returnArr['data']['pagination'] = $data['pagination'];
            return $this->checkReturn($returnArr);
        }

        /*获取我的hc列表数组 */
        $hcData = $data['data'];

        /* 获取分页数组 */
        $pageArr = $data['pagination'];

        /* 获取员工id对应名字数组 */
        $staffIdsArr = array_unique(array_merge(array_column($hcData, "submitter_id"), array_column($hcData, "interviewer")));
        $staffIds = getIdsStr($staffIdsArr);
        $staffList = $this->sysList->getStaffList([
            "ids" => $staffIds,
        ]);
        $staffData = array_column($staffList, null, 'staff_info_id');

        //查询部门名称 ids
        $departmentIdsArr = array_unique(array_merge(array_column($hcData, "department_id"), array_column($staffList, "store_id")));
        $departmentIds = getIdsStr($departmentIdsArr);
        $departmentData = $this->sysList->getDepartmentList([
            "ids" => $departmentIds,
        ]);
        $departmentDataList = array_column($departmentData, 'name', 'id');

        //查询网点名称 ids
        $worknodeIdsArr = array_unique(array_merge(array_column($hcData, "worknode_id"), array_column($staffList, "store_id")));
        $worknodeIds = getIdsStr($worknodeIdsArr);
        $worknodeData = $this->sysList->getStoreList([
            "ids" => $worknodeIds,
        ]);
        $worknodeData = array_column($worknodeData, 'name', 'id');

        //查询职位名称 ids
        $positionIdsArr = array_filter(array_unique(array_merge(array_column($staffList, "job_title"), array_column($hcData, "job_title_id"))));
        $positionIds = getIdsWithoutQuotes($positionIdsArr);
        $positionData = $this->sysList->getPositionList([
            "ids" => $positionIds,
        ]);
        $positionList = array_column($positionData, 'job_name', 'id');

        // 获取hc状态列表
        $hcStateList = $this->hc->getHcStateList();
        $hcStateList = array_column($hcStateList, 'state_value', 'state_code');

        /* 业务处理 */
        foreach ($hcData as $k => $v) {
            if ($v['submitter_id']) {
                if ($staffData[$v['submitter_id']]["organization_type"] == 1) {
                    //网点
                    $hcData[$k]['submitter_department_id'] = $staffData[$v['submitter_id']]["store_id"];
                    $hcData[$k]['submitter_department_name'] = $worknodeData[$staffData[$v['submitter_id']]["store_id"]];
                } else if ($staffData[$v['submitter_id']]["organization_type"] == 2) {
                    //部门
                    $hcData[$k]['submitter_department_id'] = $staffData[$v['submitter_id']]["store_id"];
                    $hcData[$k]['submitter_department_name'] = $departmentDataList[$staffData[$v['submitter_id']]["store_id"]];
                }
                $hcData[$k]['submitter_bile'] = $staffData[$v['submitter_id']]["mobile"];
                $hcData[$k]['submitter_job_title'] = $v['submitter_id'] == 17245 ? 'Chief Operating Officer' : $positionList[$staffData[$v['submitter_id']]["job_title"]];
                $hcData[$k]['submitter_organization_type'] = $staffData[$v['submitter_id']]['organization_type'];
            }
            $hcData[$k]['department_name'] = $departmentDataList[$v['department_id']];
            $hcData[$k]['worknode_name'] = $worknodeData[$v['worknode_id']];
            $hcData[$k]['submitter_name'] = $staffData[$v['submitter_id']]['name'];
            $hcData[$k]['priority_name'] = $this->priorityArr[$v['priority_id']] ? $this->getTranslation()->_($this->priorityArr[$v['priority_id']]) : '';
            $hcData[$k]['interview_charge'] = isCountry('TH') ? '(19921)ธีรภัทร ตระการวิจิตร' : '';
            $hcData[$k]['state_value'] = $this->getTranslation()->_($v['state_value']);
            $hcData[$k]['interviewer'] = $v['interviewer'] ? $staffData[$v['interviewer']]['name'] . '(' . $v['interviewer'] . ')' : '';
            $hcData[$k]['job_title_name'] = !empty($v['job_title_id']) ? sprintf("(%s) %s", $v['job_title_id'], $positionList[$v['job_title_id']] ?? '') : '';
            $hcData[$k]['hire_type_text'] = $this->getTranslation()->_('hire_type_' . $v['hire_type']);
            if ($hcStateList[$v['approval_state_code']] == '4004') {
                $hcData[$k]['approval_state_code_name'] = $hcStateList[$v['approval_state_code']] ? $this->getTranslation()->_('4031') : '';
            } else {
                $hcData[$k]['approval_state_code_name'] = $hcStateList[$v['approval_state_code']] ? $this->getTranslation()->_($hcStateList[$v['approval_state_code']]) : '';
            }
            if ($v['approval_stage'] == 1 && $v['state_code'] == 1) {
                $hcData[$k]['approval_stage_name'] = $this->getTranslation()->_('4024');
            } else if ($v['approval_stage'] == 2 && $v['state_code'] == 1) {
                $hcData[$k]['approval_stage_name'] = $this->getTranslation()->_('4025');
            } else if ($v['approval_stage'] == 3 && $v['state_code'] == 1) {
                $hcData[$k]['approval_stage_name'] = $this->getTranslation()->_('4038');
            } else if (($v['approval_stage'] == 4 || $v['approval_stage'] == 3) && $v['state_code'] != 1) {
                $hcData[$k]['approval_stage_name'] = $this->getTranslation()->_('4026');
            } else {
                $hcData[$k]['approval_stage_name'] = '';
            }
            if ($v['approval_state_code'] == 5) {
                $hcData[$k]['approval_stage_name'] = $this->getTranslation()->_('4026');
            }

        }

        /* 返回格式 */
        $returnArr['data']['dataList'] = $hcData;
        $returnArr['data']['pagination'] = $pageArr;

        return $this->checkReturn($returnArr);
    }

    /**
     * hc创建
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function addHc($paramIn = [])
    {
        $this->jd = new FlashJdRepository();
        $this->department = new DepartmentRepository();
        $this->resume = new ResumeRepository();
        $this->sysList = new SysListRepository();

        $departmentId = $this->processingDefault($paramIn, 'department_id', 2);
        $worknodeId = $this->processingDefault($paramIn, 'worknode_id', 2);
        $expirationDate = $this->processingDefault($paramIn, 'expirationdate', 1);
        $demandnumber = $this->processingDefault($paramIn, 'demandnumber', 2);
        $remarks = $this->processingDefault($paramIn, 'remarks', 1);
        $reason = $this->processingDefault($paramIn, 'reason', 1);
        $interviewer = $this->processingDefault($paramIn, 'interviewer', 1);
        $jobId = $this->processingDefault($paramIn, 'job_id', 2);
        $type = $this->processingDefault($paramIn, 'type', 2);
        $priority_id = $this->processingDefault($paramIn, 'priority_id', 2);
        $file_url = $this->processingDefault($paramIn, 'file_url', 2);
        $reason_type = $this->processingDefault($paramIn, 'reason_type', 2, 1);
        $job_title = $this->processingDefault($paramIn, 'job_title_id', 2, 1);
        $hire_type = $this->processingDefault($paramIn, 'hire_type', 2, 1);
        $hire_times = $this->processingDefault($paramIn, 'hire_times', 2, 1);
        $leave_staffs = $this->processingDefault($paramIn, 'leave_staffs', 3, []);
        $working_day_rest_type = $this->processingDefault($paramIn, 'working_day_rest_type', 1);
        $language_ability = $this->processingDefault($paramIn, 'language_ability');

        $staffId = $paramIn['userinfo']['id'];
        /* 获取上级 */
        $positionId = $paramIn['userinfo']['position_category'];
        /* 校验jd */
        $jdData = $this->jd->jdDetail(['job_id' => $jobId]);
        if (empty($jdData)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('3007'));
        }


        /* 校验网点 */
        $worknodeData = $this->sysList->getStoreList();
        $worknodeData = array_column($worknodeData, 'name', 'id');
        if (empty($worknodeData[$worknodeId])) {
            return $this->checkReturn(-3, $this->getTranslation()->_('4010'));
        }

        /* 校验部门 */
        $checkData = $this->department->departmentDetail(['department_id' => $departmentId]);
        if (empty($checkData)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('3009'));
        }

        //新增hc数组拼装
        $params = [
            'job_id' => $jobId,
            'staff_id' => $staffId,
            'worknode_id' => $worknodeId,
            'department_id' => $departmentId,
            'expirationdate' => $expirationDate,
            'demandnumber' => $demandnumber,
            'surplusnumber' => $demandnumber,
            'remarks' => $remarks,
            'reason' => $reason,
            'type' => $type,
            'priority_id' => $priority_id,
            'reason_type' => $reason_type,
            'job_title_id' => $job_title,
            'hire_type' => $hire_type,
            'hire_times' => $hire_times,
            'leave_staffs' => $leave_staffs,
            'working_day_rest_type' => $working_day_rest_type,
            'language_ability' => $language_ability,
        ];
        $this->getDI()->get('logger')->write_log("hc: addHc request:" . json_encode($params), 'info');
        $apiClient = new ApiClient('by_rpc', '', 'create_hc', $this->lang);
        $apiClient->setParams($params);
        $result = $apiClient->execute();

        //记录返回值并返回申请结果
        $this->getDI()->get('logger')->write_log("hc: addHc " . json_encode($result), 'info');
        if (isset($result['result']['code']) && $result['result']['code'] == 1) {
            //保存附件
            if (!empty($file_url) && !empty($result['result']['data']['hc_id'])) {
                $this->resume->createfile($result['result']['data']['hc_id'], $file_url);
            }
            return $this->checkReturn(['data' => $result['result']['data'] ?? []]);
        } else {
            return $this->jsonReturn($this->checkReturn(-3, $result['result']['msg'] ?? $this->getTranslation()->_('4008')));
        }
    }

    /**
     * hc 申请 离职人员列表
     */
    public function getHcStaffLeaveList($params)
    {
        try {
            $this->getDI()->get('logger')->write_log("hc: get_hc_leave_staff_list request:" . json_encode($params), 'info');
            $apiClient = new ApiClient('by_rpc', '', 'get_hc_leave_staff_list', $this->lang);
            $apiClient->setParams($params);
            $result = $apiClient->execute();
            //记录返回值并返回申请结果
            $this->getDI()->get('logger')->write_log("hc: get_hc_leave_staff_list " . json_encode($result), 'info');
            if (isset($result['result']['code']) && $result['result']['code'] == 1) {
                return $this->checkReturn(['data' => $result['result']['data'] ?? []]);
            } else {
                return $this->jsonReturn($this->checkReturn(-3, $result['result']['msg'] ?? $this->getTranslation()->_('4008')));
            }
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("getHcStaffLeaveList 异常信息:" . $e->getMessage() . '---' . $e->getLine(), 'error');
            return $this->jsonReturn($this->checkReturn(-3, $result['result']['msg'] ?? $this->getTranslation()->_('4008')));
        }
    }

    /**
     * hc修改
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function modifyHc($paramIn = [])
    {
        $this->hc = new HcRepository();

        $hcId = $this->processingDefault($paramIn, 'hc_id', 2);
        $state_code = $this->processingDefault($paramIn, 'state_code', 2);
        $reason = $this->processingDefault($paramIn, 'reason', 1);
        $is_agree = $this->processingDefault($paramIn, 'is_agree', 1);
        $priority_id = $this->processingDefault($paramIn, 'priority_id', 2, 0);
        $new_demand_number = $this->processingDefault($paramIn, 'new_demand_number', 2, 0);
        $staff_id = $paramIn['userinfo']['id'];
        /* 检查参数hcId是否存在 获取hc详情 */
        $hcInfo = $this->hc->hcInfo($hcId);
        if (empty($hcInfo) or $hcInfo['state_code'] == 4) {
            return $this->checkReturn(-3, $this->getTranslation()->_('4012'));
        }

        /* 查询当前登陆者是否有审批权限 */
        if ($is_agree == 1 || $is_agree == 2) {

            $data = $this->hc->checkMyapproval([
                'hc_id' => $hcId,
                'submitter_id' => $staff_id,
                'state_code' => 7,
            ]);
            if (empty($data)) {
                return $this->checkReturn(-3, $this->getTranslation()->_('4018'));
            }
        }

        /* 检查我的审批状态 */
        $myapprovalData = $this->hc->checkMyapproval([
            'state_code' => 7,
            'hc_id' => $hcId,
        ]);
        if (empty($myapprovalData)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('4012'));
        }

        // 检查修改需求人数权限
        $d = new DepartmentRepository();
        $departId = SettingEnvServer::getSetValToArray('edit_hc_demand_num_dept_ids');
        $dept_ids = $departId ? $d->getDepartmentChildInfo($departId) : [];
        $edit_demand_num_staffids_arr            = SettingEnvServer::getSetValToArray('hrlist_edit_demand_num_staffids',
            ',');

        $supplement_num = intval($new_demand_number) - intval($hcInfo["demandnumber"]);
        if ($supplement_num > 0) {
            // 加需求人数
            if (!empty($new_demand_number) && !in_array($staff_id, $edit_demand_num_staffids_arr)) {
                return $this->checkReturn(-3, $this->getTranslation()->_('update_hc_err_2'));
            }
            // 检查hc预算
            // hc预算共享部门职位
            $shareDepartmentPosition = $this->getShareDepartmentPosition();
            $share_department_position = $shareDepartmentPosition['department_position']; //部门=>[共享职位,共享职位]
            // 校验hc预算
            $config = SettingEnvServer::getSetVal('hc_budget_configured');
            if (!empty($config)) {
                $server = HcStatisticServer::getInstance();
            } else {
                $server = Tools::reBuildCountryInstance(new HcServer());
            }
            $total = $server->getHrStaffing($hcInfo['department_id'], $hcInfo['worknode_id'], $hcInfo['job_title_id'], $share_department_position);
            $leftTotal = $server->getSurplusHcRequestCount($total, $hcInfo['department_id'], $hcInfo['worknode_id'], $hcInfo['job_title_id'], $share_department_position);
            if ($leftTotal  < $supplement_num) {
                return $this->checkReturn(-3, $this->getTranslation()->_('11006'));
            }
            
        } elseif ($supplement_num < 0) {
            // 减需求人数
            if (
                !empty($new_demand_number) &&
                !in_array($hcInfo['department_id'], $dept_ids) &&
                !in_array($staff_id, $edit_demand_num_staffids_arr)
            ) {
                return $this->checkReturn(-3, $this->getTranslation()->_('update_hc_err_2'));
            }
        }
        
        //处理
        if ($hcInfo['workflow_role'] == 'hc_v2') {

            $state = $state_code == 4 ? 4 : ($is_agree == 1 ? 2 : 3);
            $apiClient = (new ApiClient('by_rpc', '', 'update_hc', $this->lang));
            $apiClient->setParams([
                'staff_id' => $staff_id,
                'audit_id' => $hcId,
                'reject_reason' => $reason,
                'status' => $state,
                'priority_id' => $priority_id,
                'new_demand_number' => $new_demand_number,
            ]);
            $result = $apiClient->execute();
            if (empty($result['result']['code']) || $result['result']['code'] != 1){
                return $this->checkReturn(-3, $result['result']['msg'] ?? $this->getTranslation()->_('11006'));
            }

            //同步buddy
            if ($is_agree == 1) {
                $this->hc->syncHcToBuddy($hcId);
            }

            return $this->checkReturn(['data' => $hcId]);
        }

        //获取对应员工信息
        $staffInfo = (new \FlashExpress\bi\App\Server\StaffServer)->getStaffInfo($hcInfo['submitter_id']);
        $staffInfo['request_department_id'] = $hcInfo['department_id'];
        //获取对应员工审批流
        if ($hcInfo["workflow_role"]) {
            $workflowData = (new WorkflowServer())->getWorkflow($staffInfo, 1, $hcInfo["workflow_role"]);
        } else {
            $roleName = (new WorkflowServer())->getWorkflowRoleName([
                "userinfo" => $paramIn['userinfo'],
                "hc_department_id" => $hcInfo['department_id'],
                'jd_id' => $hcInfo['job_id'],
            ]);
            $workflowData = (new WorkflowServer())->getWorkflow($staffInfo, 1, $roleName);
        }
        $mangerStaffIds = '';
        foreach ($workflowData[1]['staff_id'] as $k => $v) {
            $mangerStaffIds .= $v['staff_id'] . ',';
        }
        $mangerStaffIds = substr($mangerStaffIds, 0, -1);
        /* 获取最终状态值 */
        if ($is_agree == 1) {
            $priority_id = $priority_id ? $priority_id : $hcInfo['priority_id'];
            // ceo 或者coo
            foreach ($workflowData as $k => $v) {
                if (isset($v['staff_id'][$staff_id])) {
                    if ($v['level'] == 1) {
                        //ceo 或 coo

                        /* 添加进审批流 */
                        $insetApprovalData = [
                            'hc_id' => $hcId,
                            'submitter_id' => $staff_id,
                            'reason' => $reason,
                            'position_id' => '',
                            'staff_flag' => 1,
                            'staff_ids' => $mangerStaffIds,
                            'state_code' => 6,
                        ];
                        $approvalStaffIdsArr[$staff_id] = 6;
                        $this->hc->insertApproval($insetApprovalData);

                        foreach ($v['staff_id'] as $key => $val) {
                            /* 修改我的审批 */
                            $myApprovalData = [
                                'hc_id' => $hcId,
                                'submitter_id' => $val['staff_id'],
                                'staff_ids' => '',
                                'state_code' => 6,
                            ];
                            $this->hc->updateMyapproval($myApprovalData);
                            $approvalStaffIdsArr[$val['staff_id']] = 6;
                        }

                        //hc主表最终状态
                        $state_code = 2;
                        $approval_stage = $k + 1;
                        $approval_state_code = 6;
                        $approval_completion_time = gmdate('Y-m-d H:i:s', time());

                    } else {
                        //hrm 或者hrbp
                        $mangerStaffIds = '';
                        $levelkey = 0;
                        foreach ($workflowData as $kk => $vv) {
                            foreach ($vv['staff_id'] as $key => $val) {
                                if ($val['staff_id'] == $staff_id) {
                                    $levelkey = $kk + 1;
                                }
                            }
                        }
                        foreach ($workflowData[$levelkey]['staff_id'] as $kk => $vv) {
                            $mangerStaffIds .= $vv['staff_id'] . ',';
                        }
                        $mangerStaffIds = substr($mangerStaffIds, 0, -1);
                        /* 添加进审批流 */
                        $insetApprovalData = [
                            'hc_id' => $hcId,
                            'submitter_id' => $staff_id,
                            'reason' => $reason,
                            'position_id' => '',
                            'staff_flag' => 1,
                            'staff_ids' => $mangerStaffIds,
                            'state_code' => 6,
                        ];
                        $this->hc->insertApproval($insetApprovalData);

                        /* 修改我的审批 */
                        $myApprovalData = [
                            'hc_id' => $hcId,
                            'submitter_id' => $staff_id,
                            'staff_ids' => $mangerStaffIds,
                            'state_code' => 6,
                        ];
                        $approvalStaffIdsArr[$staff_id] = 6;
                        $this->hc->updateMyapproval($myApprovalData);
                        foreach ($workflowData[$k + 1]['staff_id'] as $key => $val) {

                            /* 添加进我的审批 ceo和coo */
                            $insetMyApprovalData = [
                                'hc_id' => $hcId,
                                'submitter_id' => $val['staff_id'],
                                'staff_ids' => '',
                                'state_code' => 7,
                            ];
                            $this->hc->insertMyApproval($insetMyApprovalData);
                            $approvalStaffIdsArr[$val['staff_id']] = 7;
                        }

                        //hc主表最终状态
                        $approval_stage = $k + 1;
                        $approval_state_code = 7;
                        $state_code = 1;
                        $approval_completion_time = "";
                    }
                }

            }
        } else if ($is_agree == 2) {
            //不同意
            $priority_id = $hcInfo['priority_id'] ? $hcInfo['priority_id'] : 0;
            /* 添加进审批流 */
            $insetApprovalData = [
                'hc_id' => $hcId,
                'submitter_id' => $staff_id,
                'reason' => $reason,
                'position_id' => '',
                'staff_flag' => 1,
                'staff_ids' => $mangerStaffIds,
                'state_code' => 5,
            ];
            $this->hc->insertApproval($insetApprovalData);
            // ceo 或者coo
            foreach ($workflowData as $k => $v) {
                if (isset($v['staff_id'][$staff_id])) {
                    if ($v['level'] == 1) {

                        foreach ($v['staff_id'] as $key => $val) {
                            /* 修改我的审批 */
                            $myApprovalData = [
                                'hc_id' => $hcId,
                                'submitter_id' => $val['staff_id'],
                                'staff_ids' => '',
                                'state_code' => 5,
                            ];
                            $this->hc->updateMyapproval($myApprovalData);
                            $approvalStaffIdsArr[$val['staff_id']] = 5;
                        }
                    } else {
                        /* 修改我的审批 */
                        $myApprovalData = [
                            'hc_id' => $hcId,
                            'submitter_id' => $staff_id,
                            'staff_ids' => '',
                            'state_code' => 5,
                        ];
                        $this->hc->updateMyapproval($myApprovalData);
                        $approvalStaffIdsArr[$staff_id] = 5;
                    }
                    $approval_stage = $k + 1;
                }
            }
            //hc主表最终状态
            $state_code = 1;
            $approval_state_code = 5;
            $approval_completion_time = gmdate('Y-m-d H:i:s', time());
        } else {
            return $this->checkReturn(-3, $this->getTranslation()->_('4018'));
        }

        //修改hc主表
        $this->hc->updateHc([
            'hc_id' => $hcId,
            'approval_stage' => $approval_stage,
            'approval_state_code' => $approval_state_code,
            'priority_id' => $priority_id,
            'state_code' => $state_code,
            'approval_completion_time' => $approval_completion_time,
            'staff_id' => $staff_id,
        ]);


        if ($state_code == 2) {
            $this->hc->syncHcToBuddy($hcId);
        }


        if (empty($paramIn['is_by'])) {
            //添加hc记录
            self::sync_hc($hcId);

            //添加hr_myapproval 记录
            if ($approvalStaffIdsArr) {
                $approval['created_at'] = $hcInfo['createtime'];
                $approval['hc_id'] = $hcId;
                $insetHcData['hc_id'] = $hcId;
                $approval['data'] = json_encode($hcInfo);
                $approval['submitter_id'] = $hcInfo['submitter_id'];
                $approval['store_id'] = $hcInfo['worknode_id'];
                $approval['status_list'] = $approvalStaffIdsArr;//hc记录 审批人是空
                $approval['hc_id'] = $hcId;
                $approval['timezone'] = $this->timezone;

                $flag = $this->syncBy($approval, 'sync_my_approval');
                if ($flag['code'] != 1) {//同步失败 记录日志
                    $this->wLog("hc: addHc(sync_approval)", $flag['smg'], 'hc');
                }
            }
        }

        return $this->checkReturn(1);
    }

    /**
     * hc作废
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function nullifyHc($paramIn = [])
    {
        $this->hc = new HcRepository();

        //参数
        $hcId = $this->processingDefault($paramIn, 'hc_id', 2);
        $reason = $this->processingDefault($paramIn, 'reason', 1);
        $hcNullify = $this->processingDefault($paramIn, 'hcNullify', 2);
        $type = $this->processingDefault($paramIn, 'obsolete', 2);
        $staff_id = $paramIn['userinfo']['id'];
        $state_code = 4;

        //校验
        if (!$hcId)
            return $this->checkReturn(-3, $this->getTranslation()->_('miss_args'));

        $hcInfo = $this->hc->checkHc([
            "hc_id" => $hcId,
        ]);

        // 校验状态
        if (!$hcInfo || $hcInfo['state_code'] == 4) {
            return $this->checkReturn(-3, $this->getTranslation()->_('4012'));
        }

        //业务逻辑
        if ($type == 1) {
            //校验作废权限
            if ($hcInfo['submitter_id'] != $staff_id && $hcNullify != 1) {
                return $this->checkReturn(-3, $this->getTranslation()->_('4013'));
            }

            //作废不需要同步by，直接修改主状态即可
            $this->hc->changeHc([
                'hc_id' => $hcId,
                'updateData' => [
                    'state_code' => $state_code,
                ],
            ], $staff_id);


            $this->hc->syncHcToBuddy($hcId);

        } else {
            // 校验提交人是否是自己和校验状态是否是未生效
            if ($hcInfo['submitter_id'] != $staff_id || $hcInfo['state_code'] != 1) {
                return $this->checkReturn(-3, $this->getTranslation()->_('4012'));
            }

            //校验是否还有待审批
            $workflow = $this->hc->approvalInfo($hcId);
            $lastState = array_reverse($workflow)[0]["state_code"] ?? "";
            if ($lastState != 7) {
                return $this->checkReturn(-3, $this->getTranslation()->_('4012'));
            }

            //处理
            if ($hcInfo['workflow_role'] == 'hc_v2') {
                $apiClient = (new ApiClient('by_rpc', '', 'update_hc', $this->lang));
                $apiClient->setParams([
                    'staff_id' => $staff_id,
                    'audit_id' => $hcId,
                    'reject_reason' => '',
                    'status' => $state_code,
                ]);
                $result = $apiClient->execute();
                $this->getDI()->get('logger')->write_log("svc update_hc 数据返回:" . json_encode($result), 'info');
            }
        }

        return $this->checkReturn(1);
    }

    /**
     * 状态列表
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function statelList($paramIn)
    {
        $this->hc = new HcRepository();

        $stateData = $this->hc->getState($paramIn);
        foreach ($stateData as $k => $v) {
            $stateData[$k]['state_value'] = $this->getTranslation()->_($v['state_value']);
        }
        $returnArr['data']['dataList'] = $stateData;
        return $this->checkReturn($returnArr);
    }

    /**
     * 我的审批列表
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function myApprovalList($paramIn)
    {
        $this->hc = new HcRepository();
        $this->sysList = new SysListRepository();

        $pageSize = $this->processingDefault($paramIn, 'page_size', 2, 10);
        $pageNum = $this->processingDefault($paramIn, 'page_num', 2, 1);
        $staffId = $paramIn['userinfo']['id'];


        /* 获取状态数组列表 */
        $syateArr = $this->hc->getStateArrFromCache();

        $parpam = [
            'job_name',
            'department_id',
            'worknode_id',
            'hc_id',
            'time_start',
            'time_end',
            'createtime_start',
            'createtime_end',
            'state_code',
            'priority_id',
            'approval_state_code',
            'approval_stage',
            'job_title',
            'hire_type',
            'is_sub_department',
        ];

        $searchWhere = checkParam($parpam, $paramIn);
        $searchWhere['search_priority_id'] = trim($paramIn['search_priority_id'] ?? '');

        /* 获取我的审批列表数据 */
        $paramHc = [
            'searchWhere' => $searchWhere,
            'page_size' => $pageSize,
            'page_num' => $pageNum,
            'staff_id' => $staffId,
        ];

        $data = $this->hc->myApprovalListNew($paramHc);
        $this->getDI()->get('logger')->write_log(['step'=>1], 'info');
        /*获取hc列表数组 */
        $hcData = $data['data'];
        /* 获取分页数组 */
        $pageArr = $data['pagination'];
        if(empty($hcData)){
            /* 返回格式 */
            $returnArr['data']['dataList'] = $hcData;
            $returnArr['data']['pagination'] = $pageArr;
            return $this->checkReturn($returnArr);
        }

        // 获取员工id对应名字数组 ids
        $staffIdsArr = array_unique(array_column($hcData, "submitter_id"));

        if (!empty($staffIdsArr)) {
            $staffIds  = getIdsStr($staffIdsArr);
            $staffList = $this->sysList->getStaffList([
                "ids" => $staffIds,
            ]);
        }

        $staffData = isset($staffList) ? array_column($staffList, null, 'staff_info_id') : [];

        //查询部门名称 ids
        $departmentDataList = array_column((new SysDepartmentRepository())->getDepartmentListFromCache(['id','name']), 'name', 'id');

        //查询网点名称 ids
        $worknodeIdsArr = array_unique(array_merge(array_column($hcData, "worknode_id"), array_column($staffList, "store_id")));

        $worknodeData = array_column((new SysStoreRepository())->getStoreListFromCache(['id','name']), 'name', 'id');
        $worknodeData['-1']=enums::HEAD_OFFICE;
        //查询职位名称 ids
        $positionList = array_column((new HrJobTitleRepository())->getJobTitleListFromCache(['id','job_name']), 'job_name', 'id');

        $job_ids = array_column($hcData, "job_id");
        $this->getDI()->get('logger')->write_log(['step'=>2], 'info');

        $storeHcHash = [];
        //查询hr_statistical_hc hr_request当做还需提交人数
        if (!empty($worknodeIdsArr) && !empty($job_ids)) {
            $storeHcList = $this->hc->getRequestNumFromHrStaHc($worknodeIdsArr, $job_ids);
            foreach ($storeHcList as $item) {
                $storeHcHash[$item['store_id'] . "_" . $item['jd_id']] = $item['hc_request'];
            }
        }
        $this->getDI()->get('logger')->write_log(['step'=>3], 'info');


        /* 业务处理 */
        foreach ($hcData as $k => $v) {
            if ($v['submitter_id']) {
                if ($staffData[$v['submitter_id']]["organization_type"] == 1) {
                    //网点
                    $hcData[$k]['submitter_department_id'] = $staffData[$v['submitter_id']]["store_id"];
                    $hcData[$k]['submitter_department_name'] = $worknodeData[$staffData[$v['submitter_id']]["store_id"]]??'';
                } else if ($staffData[$v['submitter_id']]["organization_type"] == 2) {
                    //部门
                    $hcData[$k]['submitter_department_id'] = $staffData[$v['submitter_id']]["store_id"];
                    $hcData[$k]['submitter_department_name'] = $departmentDataList[$staffData[$v['submitter_id']]["store_id"]]??'';
                }
                $hcData[$k]['submitter_mobile'] = $staffData[$v['submitter_id']]["mobile"];
                $hcData[$k]['submitter_job_title'] = $v['submitter_id'] == 17245 ? 'Chief Operating Officer' : $positionList[$staffData[$v['submitter_id']]["job_title"]]??'';
                $hcData[$k]['submitter_organization_type'] = $staffData[$v['submitter_id']]['organization_type'];
            }
            if ($v['approval_state_code'] == 7) {
                $hcData[$k]['approval_state'] = 1;
            }
            $hcData[$k]['department_name'] = $departmentDataList[$v['department_id']]??'';
            $hcData[$k]['expirationdate'] = $v['expirationdate'];
            $hcData[$k]['worknode_name'] = $worknodeData[$v['worknode_id']]??"";
            $hcData[$k]['submitter_name'] = $staffData[$v['submitter_id']]['name'];
            $hcData[$k]['priority_name'] = $this->priorityArr[$v['priority_id']] ? $this->getTranslation()->_($this->priorityArr[$v['priority_id']]) : '';
            $hcData[$k]['interview_charge'] = isCountry('TH') ? '(19921)ธีรภัทร ตระการวิจิตร' : '';
            $hcData[$k]['job_title_name'] = !empty($v['job_title_id']) ? sprintf("(%s) %s", $v['job_title_id'], $positionList[$v['job_title_id']] ?? '') : '';

            if ($syateArr[$v['myapproval_state_code']] == '4004') {
                $hcData[$k]['state_value'] = $this->getTranslation()->_('4031');
            } else {
                $hcData[$k]['state_value'] = $this->getTranslation()->_($syateArr[$v['myapproval_state_code']]);
            }
            $hcData[$k]['state_code'] = $v['myapproval_state_code'];
            $hcData[$k]['interviewer'] = $v['interviewer'] ? $staffData[$v['interviewer']] . '(' . $v['interviewer'] . ')' : '';

            //该参数，改成网点hc对应的职位还需提交人数hr_request
            $hcData[$k]['surplusnumber'] = "";
            if (isset($storeHcHash[$hcData[$k]['worknode_id'] . "_" . $hcData[$k]['job_id']])) {
                $hcData[$k]['surplusnumber'] = "" . $storeHcHash[$hcData[$k]['worknode_id'] . "_" . $hcData[$k]['job_id']];
            }

            $hcData[$k]['hire_type_text'] = $v['hire_type'] ? $this->getTranslation()->_('hire_type_' . $v['hire_type']) : '';
            if (in_array($v['hire_type'], [3, 4])) {
                $hcData[$k]['hire_times_text'] = $v['hire_times'] . $this->getTranslation()->_('daily');
            } else if (in_array($v['hire_type'], HrStaffInfoModel::$hireTypeMonthList)) {
                $hcData[$k]['hire_times_text'] = $v['hire_times'] . $this->getTranslation()->_('monthly');
            }

        }
        $this->getDI()->get('logger')->write_log(['step'=>4], 'info');

        /* 返回格式 */
        $returnArr['data']['dataList'] = $hcData;
        $returnArr['data']['pagination'] = $pageArr;

        return $this->checkReturn($returnArr);

    }

    /**
     * hc基本信息详情
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function hcInfoBase($paramIn = [])
    {
        $hc_id  = $this->processingDefault($paramIn, 'hc_id', 2);
        $hcInfo = (new HcRepository())->hcInfo($hc_id);
        return $this->checkReturn(["data" => $hcInfo ?? []]);
    }

    /**
     * 审批详情
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function hcInfo($paramIn)
    {
        $this->hc = new HcRepository();
        $this->resume = new ResumeRepository();
        $this->offer = new OfferRepository();
        $this->sysList = new SysListRepository();

        $hc_id = $this->processingDefault($paramIn, 'hc_id', 2);
        $staffId = $paramIn['userinfo']['id'];

        /* 获取hc详情 */
        $hcInfo = $this->hc->hcInfo($hc_id);

        //添加hc实习生信息
        if (isCountry('TH')) {
            $hcInfo['is_th_fulfillment_internship'] = (new TemplateServer())->checkThFulfillmentInternship($hcInfo);
        }

        $approvalInfo = $this->hc->approvalInfo($hc_id);
        // 获取员工id对应名字数组 ids
        $staffIdsArr = array_unique(array_merge(array_column($approvalInfo, "submitter_id"), [$hcInfo['submitter_id']]));
        if(!empty($staffIdsArr)){
            $staffIds = getIdsStr($staffIdsArr);
            $staffList = $this->sysList->getStaffList([
                "ids" => $staffIds,
            ]);
        }

        $staffData = isset($staffList)? array_column($staffList, null, 'staff_info_id'):[];

        //查询部门名称 ids
        $departmentDataList = array_column((new SysDepartmentRepository())->getDepartmentListFromCache(['id','name']), 'name', 'id');

        //查询网点名称 ids
        $worknodeData = array_column((new SysStoreRepository())->getStoreListFromCache(['id','name']), 'name', 'id');
        $worknodeData['-1']=enums::HEAD_OFFICE;

        //查询职位名称 ids
        $positionList = array_column((new HrJobTitleRepository())->getJobTitleListFromCache(['id','job_name']), 'job_name', 'id');

        foreach ($approvalInfo as $k => $v) {
            $approvalInfo[$k]['submitter_name'] = $staffData[$v['submitter_id']]['name'];
            if ($v['state_value'] == '4004') {
                $approvalInfo[$k]['state_value'] = $this->getTranslation()->_('4031');
            } else {
                $approvalInfo[$k]['state_value'] = $this->getTranslation()->_($v['state_value']);
            }
            $approvalInfo[$k]['updatetime'] = $v['updatetime'];
            if ($v['submitter_id']) {
                if ($staffData[$v['submitter_id']]['organization_type'] == 1) {
                    //网点
                    $approvalInfo[$k]['submitter_department_id'] = $staffData[$v['submitter_id']]['store_id'];
                    $approvalInfo[$k]['submitter_department_name'] = $v['submitter_id'] == 17245 ? 'Management' : $worknodeData[$staffData[$v['submitter_id']]['store_id']];
                } else if ($staffData[$v['submitter_id']]['organization_type'] == 2) {
                    //部门
                    $approvalInfo[$k]['submitter_department_id'] = $staffData[$v['submitter_id']]['store_id'];
                    $approvalInfo[$k]['submitter_department_name'] = $v['submitter_id'] == 17245 ? 'Management' : $departmentDataList[$staffData[$v['submitter_id']]['store_id']];
                }
                $approvalInfo[$k]['submitter_mobile'] = $staffData[$v['submitter_id']]['mobile'];
                $approvalInfo[$k]['submitter_job_title'] = $v['submitter_id'] == 17245 ? 'Chief Operating Officer' : $positionList[$staffData[$v['submitter_id']]['job_title']];
                $approvalInfo[$k]['submitter_organization_type'] = $staffData[$v['submitter_id']]['organization_type'];
            }
        }

        /* 获取附件列表 */
        $annexDataList = $this->resume->annexInfo($hc_id, 2);
        //获取已招人员名单
        $issuedOfferResume = $this->offer->getIssuedOfferResume([
            "hc_id" => $hc_id,
        ]);
        $hcInfo['issuedOfferResume'] = $issuedOfferResume;

        if ($staffData[$hcInfo['submitter_id']]['organization_type'] == 1) {
            //网点
            $hcData['submitter_department_id'] = $staffData[$hcInfo['submitter_id']]['store_id'];
            $hcData['submitter_department_name'] = $worknodeData[$staffData[$hcInfo['submitter_id']]['store_id']];
        } else if ($staffData[$hcInfo['submitter_id']]['organization_type'] == 2) {
            //部门
            $hcData['submitter_department_id'] = $staffData[$hcInfo['submitter_id']]['store_id'];
            $hcData['submitter_department_name'] = $departmentDataList[$staffData[$hcInfo['submitter_id']]['store_id']];
        }
        $hcData['submitter_mobile'] = $staffData[$hcInfo['submitter_id']]['mobile'];
        $hcData['submitter_job_title'] = $hcInfo['submitter_id'] == 17245 ? 'Chief Operating Officer' : $positionList[$staffData[$hcInfo['submitter_id']]['job_title']];
        $hcData['submitter_organization_type'] = $staffData[$hcInfo['submitter_id']]['organization_type'];

        $hcInfo['department_name'] = $departmentDataList[$hcInfo['department_id']];
        $hcInfo['worknode_name'] = $worknodeData[$hcInfo['worknode_id']];
        $hcInfo['priority_name'] = $this->priorityArr[$hcInfo['priority_id']] ? $this->getTranslation()->_($this->priorityArr[$hcInfo['priority_id']]) : '';
        $hcInfo['submitter_name'] = $staffData[$hcInfo['submitter_id']]['name'];
        $hcInfo['interviewer'] = $hcInfo['interviewer'] ? $staffData[$hcInfo['interviewer']] . '(' . $hcInfo['interviewer'] . ')' : '';
        $hcInfo['nowtime'] = $hcInfo['now_date'];
        $hcInfo['approval'] = $approvalInfo;
        $hcInfo['interview_charge'] = isCountry('TH') ? '(19921)ธีรภัทร ตระการวิจิตร':'';
        $hcInfo['state_value'] = $this->getTranslation()->_($hcInfo['state_value']);
        $hcInfo['annexList'] = $annexDataList;
        $hcInfo['job_title_name'] = !empty($hcInfo['job_title_id']) ? sprintf("(%s) %s", $hcInfo['job_title_id'], $positionList[$hcInfo['job_title_id']] ?? '') : '';

        //用人原因类型
        $hcInfo['reason_type_text'] = str_replace(["招聘", "Recruitment"], ["新增", "Add"], $this->getTranslation()->_("hc_reason_type_" . $hcInfo['reason_type']));

        $hcInfo['hire_type_text'] = $hcInfo['hire_type'] ? $this->getTranslation()->_('hire_type_' . $hcInfo['hire_type']) : ''; // 雇佣类型
        if (in_array($hcInfo['hire_type'], [3, 4])) {

            $hcInfo['hire_times_text'] = $hcInfo['hire_times'] . $this->getTranslation()->_('daily');
        } else if (in_array($hcInfo['hire_type'], HrStaffInfoModel::$hireTypeMonthList)) {

            $hcInfo['hire_times_text'] = $hcInfo['hire_times'] . $this->getTranslation()->_('monthly');
        } else {
            $hcInfo['hire_times_text'] = '';
        }
        $hcInfo['language_ability']      = $hcInfo['language_ability'] ?: '';
        $hcInfo['language_ability_text'] = $this->getLanguageAbilityText($hcInfo['language_ability']);

        $hcInfo['leave_staff_list'] = [];

        //获取详情
        if ($hcInfo['workflow_role'] == 'hc_v2') {
            $apiClient = (new ApiClient('by_rpc', '', 'get_hc_workflow', $this->lang));
            $apiClient->setParams([
                'staff_id' => $staffId,
                'audit_id' => $hc_id,
                'audit_type' => 6,
            ]);
            $result = $apiClient->execute();

            if (isset($result['result']) && $result['result']) {
                $workflow = [];
                // V2 审批流日志
                foreach ($result['result'] as $item) {
                    $workflow[] = [
                        "state_value" => $item['status'] ?? '',
                        "hc_id" => $hc_id,
                        "state_code" => $item['status_code'],
                        "updatetime" => $item['time'],
                        "submitter_name" => $item['name'] ?? '',
                        "submitter_id" => $item['staff_id'],
                        "submitter_department_name" => $item['department'] ?? "",
                        "submitter_job_title" => $item['position'] ?? "",
                        'reason' => $item['remark'] ?? '',
                    ];
                }
                $hcInfo['approval'] = $workflow;

                // 解决旧版本审批流无部门id问题: 若V2版本审批流有数据，则从V2审批流中取部门，并填充到旧版本审批流中
                // 临时方案: 后续可视情况, V2取代旧版本日志
                $v2_log_department_item = array_column($hcInfo['approval1'], 'department', 'staff_id');

                foreach ($hcInfo['approval'] as $_log_key => $_log_val) {
                    if (!empty($_log_val['submitter_department_name'])) {
                        continue;
                    }

                    $_log_val['submitter_department_name'] = $v2_log_department_item[$_log_val['submitter_id']] ?? '';
                    $hcInfo['approval'][$_log_key] = $_log_val;
                }
            }
            
            $apiClient = (new ApiClient('by_rpc', '', 'get_hr_hc_leave_staff_list', $this->lang));
            $apiClient->setParams([
                'hc_id' => $hc_id,
            ]);
            $leave_staff_list = $apiClient->execute();

            if (isset($leave_staff_list['result']['code']) && $leave_staff_list['result']['code'] == 1) {
                $hcInfo['leave_staff_list'] = $leave_staff_list['result']['data'];
            }
        }

        $hcInfo['working_day_rest_type_text'] = [];

        if(!empty($hcInfo['working_day_rest_type'])) {
            $working_day_rest_type = explode(',', $hcInfo['working_day_rest_type']);
            array_walk($working_day_rest_type, function (&$val) {
                $val =  $this->getTranslation()->_('working_day_rest_type_' . $val);
            });
            $hcInfo['working_day_rest_type_text'] = $working_day_rest_type;
        }

        $hrJobDepartmentRelation = (new HrJobDepartmentRelationRepository());
        $jobInfo                 = $hrJobDepartmentRelation->getInfo(['job_level'],
            [
                'department_id = :department_id:',
                'job_id = :job_id:',
            ]
            , [
                'department_id' => $hcInfo['department_id'],
                'job_id'        => $hcInfo['job_title_id'],
            ]);

        $jobIds = !empty($jobInfo['job_level']) ? explode(',', $jobInfo['job_level']) : [];

        $jobTitleGradeNames = $hrJobDepartmentRelation->formatLevel($jobIds);

        $hcInfo['job_title_grade_name'] = implode(',',$jobTitleGradeNames);

        $returnArr['data'] = $hcInfo;

        return $this->checkReturn($returnArr);
    }

    /**
     * 面试官列表
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function interviewerList($paramIn = [])
    {
        $this->hc = new HcRepository();

        $submitter_name = $this->processingDefault($paramIn, 'submitter_name', 2);

        $param = [
            'submitter_name' => $submitter_name,
        ];

        $staffData = $this->hc->getInterviewerList($param);
        $interviewerArr = [];
        foreach ($staffData as $k => $v) {
            $interviewerArr[$k]['name'] = '(' . $v["id"] . ')' . $v['name'];
            $interviewerArr[$k]['id'] = $v["id"];
        }
        $returnArr['data'] = $interviewerArr;
        return $this->checkReturn($returnArr);
    }

    /**
     * 取当前登陆者对应权限
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function checkRolePermissions($userInfo = [])
    {
        $this->permissions = new PermissionsRepository();

        if ($this->ifHrisSales) {
            //只要基本的查看权限
            $rolePermissionsArr = $this->permissions->checkRolePermissions([]);
        } else {
            $rolePermissionsArr = $this->permissions->checkRolePermissions($userInfo);


        }
        return $rolePermissionsArr;
    }

    /**
     * 工作网点列表
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function worknodeList($paramIn = [])
    {
        $this->sysList = new SysListRepository();
        $data = $this->sysList->getStoreList();

        $returnArr['data'] = $data;
        return $this->checkReturn($returnArr);
    }


    /**
     * @param $param
     * @param $method
     * @return array|mixed
     */
    public function syncBy($param, $method)
    {
        return;
        $this->getDI()->get('logger')->write_log("hc syncBy 参数列表 param:" . json_encode($param) . ";method:json_encode($method);", 'info');
        try {
//            $url = env('hc_by_url') . '/svc/call';
//            $base_controller = new Controllers\ControllerBase();
//            $res = $base_controller->GetApiData($method, $param, $url);

            $apiClient = (new ApiClient('by_rpc', '', $method, $this->lang));
            $apiClient->setParams($param);
            $res = $apiClient->execute();
            $this->getDI()->get('logger')->write_log("hc syncBy 数据返回:" . json_encode($res), 'info');
        } catch (Exception $e) {
            $this->getDI()->get('logger')->write_log("hc syncBy 异常信息:" . $e->getMessage(), 'error');
            return $this->checkReturn(-3, $e->getMessage());
        }
        return $res['result'];
    }


    /**
     * 获取大区
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getGeographyCode($storeId)
    {
        $sql = "select sys_province.manage_geography_code from sys_store LEFT JOIN sys_province ON sys_store.province_code = sys_province.code where sys_store.id = '" . $storeId . "'";
        $data = $this->getDI()->get('db_fle')->query($sql);
        $info = $data->fetch(\Phalcon\Db::FETCH_ASSOC);
        $geographyCode = '';
        if ($info) {
            $geographyCode = $info['manage_geography_code'];
        }
        return $geographyCode;
    }

    /**
     * 同步hc主表
     * @Access  public
     * @Param   request
     * @Return  array
     */
    function sync_hc($hcId)
    {
        $this->hc = new HcRepository();

        $hcInfo = $this->hc->hcInfo($hcId);
        //添加hc记录
        $sync['created_at'] = $hcInfo['createtime'];
        $sync['approval_state_code'] = $hcInfo['approval_state_code'];
        $sync['hc_id'] = $hcInfo['hc_id'];
        $insetHcData['hc_id'] = $hcInfo['hc_id'];
        $sync['data'] = json_encode($hcInfo);
        $sync['status'] = $hcInfo['state_code'];
        $sync['submitter_id'] = $hcInfo['submitter_id'];
        $sync['store_id'] = $hcInfo['worknode_id'];
        $sync['timezone'] = $this->timezone;
        $sync['approval_id'] = 0;//hc记录 审批人是空

        $this->wLog("hc: sync_hc", $sync, 'hc');

        //同步给buddy
        (new HcRepository())->syncHcToBuddy($hcId);

        $flag = $this->syncBy($sync, 'sync_hc');

        if ($flag['code'] != 1) {//同步失败 记录日志
            $this->wLog("hc: addHc(sync_hc)", $flag['smg'], 'hc');
        }
    }

    /**
     * hc修改
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function updateHc($paramIn = [])
    {
        $this->hc = new HcRepository();
        $this->resume = new ResumeRepository();

        $hcUpdate = $paramIn['hcUpdate'];
        $hcId = $paramIn['hc_id'];
        $stateCode = $paramIn['state_code'];
        $remarks = $paramIn['remarks'];
        $reason = $paramIn['reason'];
        $priorityId = $paramIn['priority_id'];
        $expirationdate = $paramIn['expirationdate'];
        $demandnumber = $paramIn['demandnumber'];
        $surplusnumber = $paramIn['surplusnumber'];
        $hireType = isset($paramIn['hire_type']) && $paramIn['hire_type'] ? $paramIn['hire_type'] : 0;
        $hireTimes = isset($paramIn['hire_times']) && $paramIn['hire_times'] ? $paramIn['hire_times'] : 0;
        $file_url = $this->processingDefault($paramIn, 'file_url', 2);

        $staff_id = $paramIn['userinfo']['id'];//操作人

        //校验需求数
        if (isset($demandnumber) && ($demandnumber < 0 || $demandnumber >= 999)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('4020'));
        }
        //校验修改权限
        if (!$hcUpdate) {
            return $this->checkReturn(-3, $this->getTranslation()->_('4102'));
        }
        $data = $this->hc->hcInfo($hcId);
        if (!empty($data['task_new_hc_id']) && $data['state_code'] == HrhcModel::STATE_VOIDED){
            return $this->checkReturn(-3, $this->getTranslation()->_('update_hc_err_4'));
        }

        // hc预算共享部门职位
        $shareDepartmentPosition = $this->getShareDepartmentPosition();
        $share_department_position = $shareDepartmentPosition['department_position']; //部门=>[共享职位,共享职位]
        
        // 校验hc预算
        $config = SettingEnvServer::getSetVal('hc_budget_configured');
        if (!empty($config)) {
            $server = HcStatisticServer::getInstance();
        } else {
            $server = Tools::reBuildCountryInstance(new HcServer());
        }
        $supplement_num = $demandnumber - $data["demandnumber"];
        if ($supplement_num > 0){
            //校验新增需求数权限
            $edit_demand_num_staffids_arr = SettingEnvServer::getSetValToArray('hrlist_edit_demand_num_staffids',',');
            if (
                !empty($staff_id) &&
                !empty($edit_demand_num_staffids_arr) &&
                !in_array($staff_id,$edit_demand_num_staffids_arr)
            ){
                return $this->checkReturn(-3, $this->getTranslation()->_('update_hc_err_1'));
            }
            
            // 获取预算
            $total = $server->getHrStaffing($data['department_id'], $data['worknode_id'], $data['job_title_id'], $share_department_position);
            $leftTotal = $server->getSurplusHcRequestCount($total, $data['department_id'], $data['worknode_id'], $data['job_title_id'], $share_department_position);
            if ($leftTotal  < $supplement_num) {
                return $this->checkReturn(-3, $this->getTranslation()->_('11006'));
            }
        }
        
        
        
        if (!$stateCode) {
            $stateCode = $data['state_code'];
        }
        if (!$remarks) {
            $remarks = $data['remarks'];
        }
        if (!$reason) {
            $reason = $data['reason'];
        }
        if (!$priorityId) {
            $priorityId = $data['priority_id'];
        }
        if (!$expirationdate) {
            $expirationdate = $data['expirationdate'];
        }
        if (!$demandnumber) {
            $demandnumber = $data['demandnumber'];
        }
        if (!$surplusnumber) {
            $surplusnumber = $data['surplusnumber'];
        }
        if (!$hireType) {
            $hireType = $data['hire_type'];
        }
        if (!$hireTimes) {
            $hireTimes = $data['hire_times'];
        }

        if (!$hireType) {
            $hireType = $data['hire_type'];
        }

        if (!$hireTimes) {
            $hireTimes = $data['hire_times'];
        }

        //取最后审批状态
        $approvalInfo = $this->hc->approvalInfo($hcId);
        $lastState = array_reverse($approvalInfo)[0]["state_code"] ?? "";

        if (($lastState != 7 && ($data['approval_state_code'] == 6 || $data['approval_state_code'] == 5)) || $data['state_code'] == 1 || $data['state_code'] == 2 || $data['state_code'] == 3 || $data['state_code'] == 4 || $data['state_code'] == 9) {
            if ($data['state_code'] == 1) {
                //已招人数
                $issued = $data['demandnumber'] - $data['surplusnumber'];
                if ($data['demandnumber'] == $issued) {
                    $stateCode = 3;
                }
                //未生效
                $updateHc = [
                    'hc_id' => $hcId,
                    'state_code' => $stateCode,
                    'remarks' => $remarks,
                    'reason' => $reason,
                    'priority_id' => $priorityId,
                ];

            } else if ($data['state_code'] == 2 || $data['state_code'] == 9) {
                //招聘中
                if ($stateCode != 4 && $stateCode != 2 && $stateCode != 9) {
                    return $this->checkReturn(-3, $this->getTranslation()->_('4102'));
                }

                if ($stateCode == 4) {
                    $stateCode = 4;
                } else if ($expirationdate < date("Y-m-d")) {
                    $stateCode = 9;
                } else {
                    $stateCode = 2;
                }

                //已招人数
                $issued = $data['demandnumber'] - $data['surplusnumber'];
                if ($demandnumber < $issued) {
                    return $this->checkReturn(-3, $this->getTranslation()->_('4102'));
                }
                if ($demandnumber != $data['demandnumber']) {
                    $surplusnumber = $surplusnumber + ($demandnumber - $data['demandnumber']);
                }
                if ($demandnumber == $issued) {
                    $surplusnumber = 0;
                    $stateCode = 3;
                }

                $updateHc = [
                    'hc_id' => $hcId,
                    'state_code' => $stateCode,
                    'expirationdate' => $expirationdate,
                    'demandnumber' => $demandnumber,
                    'surplusnumber' => $surplusnumber,
                    'remarks' => $remarks,
                    'reason' => $reason,
                    'priority_id' => $priorityId,
                ];
            } else if ($data['state_code'] == 3) {
                //已招满

                if ($stateCode != 4) {
                    return $this->checkReturn(-3, $this->getTranslation()->_('4102'));
                } elseif ($stateCode == 4) {
                    $stateCode = 3;
                }

                $updateHc = [
                    'hc_id' => $hcId,
                    'state_code' => $stateCode,
                ];
            } else if ($data['state_code'] == 4) {
                //已作废

                if ($data['surplusnumber'] == 0) {
                    // 已招满
                    $stateCode = 3;
                } else {
                    // 招聘中
                    $stateCode = 2;
                }
                $updateHc = [
                    'hc_id' => $hcId,
                    'state_code' => $stateCode,
                ];
            }
            $updateHc['hire_type'] = $hireType;
            $updateHc['hire_times'] = $hireTimes;
            $updateHc['staff_id'] = $staff_id;//操作人
            $this->hc->updateHc($updateHc);
            //添加附件
            if ($file_url) {
                $this->resume->createfile($hcId, $file_url);
            }

            //如果是招聘中，已完成，已作废，已超时同步给buddy

            if (in_array($stateCode, [2, 3, 4, 9])) {
                $this->hc->syncHcToBuddy($hcId);
            }

            // 关联的offer 修改 hire——type
            if ($hireType) {
                $interviews = HrInterviewModel::find([
                    'conditions' => ' hc_id = :hc_id: and state < 25 ',
                    'bind' => [
                        'hc_id' => $hcId,
                    ],
                ])->toArray();
                if ($interviews) {
                    $interviewIds = array_column($interviews, 'interview_id');
                    $interviewOffers = HrInterviewOfferModel::find([
                        'conditions' => 'interview_id in ({interviewIds:array})',
                        'bind' => [
                            'interviewIds' => $interviewIds,
                        ],
                    ]);
                    foreach ($interviewOffers as $interviewOffer) {
                        $interviewOffer->hire_type = $hireType;
                        $interviewOffer->save();
                    }
                }
            }
            $return['data'] = ['hc_id' => $hcId];
            return $this->checkReturn($return);
        } else {
            return $this->checkReturn(-3, $this->getTranslation()->_('4102'));
        }
    }

    public function modifyHcHelpers($paramIn = [])
    {
        $this->hc = new HcRepository();

        $hc_state_code = $paramIn['hc_state_code'];
        $hc_approval_stage = $paramIn['hc_approval_stage'];
        $hc_approval_state_code = $paramIn['hc_approval_state_code'];
        $hc_approval_staff_id = $paramIn['hc_approval_staff_id'];
        $hc_id = $paramIn['hc_id'];

        $modify_hc_state_code = $paramIn['modify_hc_state_code'];
        $modify_hc_approval_stage = $paramIn['modify_hc_approval_stage'];
        $modify_hc_approval_state_code = $paramIn['modify_hc_approval_state_code'];
        $modify_hc_approval_staff_id = $paramIn['modify_hc_approval_staff_id'];

        $staff_id = $paramIn['userinfo']['id'];//操作人
        //业务处理
        $hc_id_arr = explode(",", $hc_id);
        foreach ($hc_id_arr as $v) {
            //校验
            $hcInfo = $this->hc->hcInfo($v);
            if (empty($hcInfo) || $hcInfo['approval_state_code'] != $hc_approval_state_code || $hcInfo['approval_state_code'] == $modify_hc_approval_state_code || $hcInfo['approval_stage'] == $modify_hc_approval_stage) {
                echo "hcId:" . $v . "\n";
                echo "state_code：" . $hcInfo['state_code'] . "|" . $modify_hc_state_code . "\n";
                echo "approval_state_code：" . $hcInfo['approval_state_code'] . "|" . $modify_hc_approval_state_code . "\n";
                echo "approval_stage：" . $hcInfo['approval_state_code'] . "|" . $modify_hc_approval_stage . "\n";
                continue;
            }
            //修改
            $this->hc->updateHc([
                'hc_id' => $v,
                'approval_stage' => $modify_hc_approval_stage,
                'approval_state_code' => $modify_hc_approval_state_code,
                'state_code' => $modify_hc_state_code,
                'staff_id' => $staff_id,
            ]);

            /* 添加进审批流 */
            $insetApprovalData = [
                'hc_id' => $v,
                'submitter_id' => $modify_hc_approval_staff_id,
                'reason' => '',
                'position_id' => '',
                'staff_flag' => 1,
                'staff_ids' => "",
                'state_code' => $modify_hc_approval_state_code,
            ];
            $this->hc->insertApproval($insetApprovalData);

            /* 修改我的审批 */
            $this->getDI()->get('db')->updateAsDict(
                'hr_myapproval',
                [
                    'state_code' => $modify_hc_approval_state_code,
                    'submitter_id' => $modify_hc_approval_staff_id,
                ],
                'hc_id = ' . $v . ' and submitter_id = ' . $hc_approval_staff_id
            );
            echo $v . "-处理成功\n";
        }
        die;
    }

    /**
     * 获取网点HC列表
     * @param array $paramIn 出入参数列表
     * @return array
     */
    public function getStoreHcList($paramIn = [])
    {
        $this->hc = new HcRepository();
        $this->sysList = new SysListRepository();

        //[1]获取请求参数
        $pageSize  = $this->processingDefault($paramIn, 'page_size', 2, 10);
        $pageNum   = $this->processingDefault($paramIn, 'page_num', 2, 1);
        $userInfo  = $this->processingDefault($paramIn, 'userinfo', 3);
        $export    = $this->processingDefault($paramIn, 'export', 2, 0);
        $is_count  = $this->processingDefault($paramIn, 'is_count', 2, 0);
        $positions = explode(',', $userInfo['position_category']);

        //[2]权限判断
        //获取coo staffID
        $cooStaffId = $this->hc->hcUpdateStaffList();
        $cooStaffId = isset($cooStaffId) && $cooStaffId ? current($cooStaffId)['staff_id'] : '';

        // 产品让把th my ph 这块逻辑砍了
        if (!isCountry(['TH', 'PH', 'MY'])) {
            if ($userInfo['id'] == $cooStaffId ||
                array_intersect($positions, [
                    self::ROLE_OPERATION_MANAGER,
                    self::ROLE_SYSTEM_MANAGER,
                    self::ROLE_GENERAL_MANAGER,
                    self::ROLE_TALENT_ACQUISITION,
                    self::ROLE_HR_MANAGEMENT,
                    self::ROLE_INTERNATIONAL_HR_MANAGER,
                    self::ROLE_HRIS_MANAGER,
                    self::ROLE_PAYROLL_MANAGER,
                    self::ROLE_HR_OFFICER,
                    self::ROLE_HRIS_SALES,
                ])
            ) {

                $paramIn['store_id'] = $paramIn['store_id'] ?? '';
            } else if (array_intersect($positions, [self::ROLE_AREA_MANAGER])) {
                if (empty($paramIn['store_id'])) {
                    if ($this->ifRegionOrDistrictManagerRole()) {
                        $paramIn['store_ids'] = $this->getMySetStoreIds();
                    } else {
                        $storeIDs = $this->hc->getAreaManagerStoreIds($userInfo['id']);
                        $paramIn['store_ids'] = array_column($storeIDs, 'sys_store_id');
                    }
                    //会过滤掉为空
                    $paramIn['store_ids'] = getIdsStr($paramIn['store_ids']);
                }
            } else {
                $paramIn['store_id'] = $userInfo['organization_id'] ?? "";
            }
        }


        //[3]获取列表数据
        $column = [
            'area_code',
            'store_id',
            'store_ids',
            'jd_id',
            'region_id',
            'piece_id',
        ];
        $searchWhere = checkParam($column, $paramIn);
        $param = [
            'searchWhere' => $searchWhere,
            'userInfo'    => $userInfo,
            'page_size'   => $pageSize,
            'page_num'    => $pageNum,
            'export'      => $export,
            'is_count'    => $is_count,
        ];

        $data = $this->hc->getStoreHcList(array_filter($param));

        if ($is_count) {
            return $data;
        }

        if (isset($data['data']) && $data['data']) {

            $storeIDs = array_unique(array_keys(array_column($data['data'], null, 'store_id')));
            $worknodeIds = getIdsStr($storeIDs);
            $worknodeData = $this->sysList->getStoreList([
                "ids" => $worknodeIds,
            ]);
            $worknodeData = array_column($worknodeData, 'name', 'id');

            $regionAndPiece = $this->sysList->getManageRegionAndPieceList(0);
            foreach ($data['data'] as $k => &$v) {
                $v['store_name'] = $worknodeData[$v['store_id']] ?? '';
                $v['area_code'] = ($regionAndPiece['region'][$v['manage_region']] ?? "") . "-" . ($regionAndPiece['piece'][$v['manage_piece']] ?? "");
            }
        }

        //[4]组织返回参数
        $returnArr['data']['dataList'] = $data['data'] ?? [];
        $returnArr['data']['pagination'] = $data['pagination'] ?? [];

        return $this->checkReturn($returnArr);
    }

    /**
     * 网点HC待入职列表
     * @param array $paramIn 出入参数列表
     * @return array
     */
    public function getPendingByStore($paramIn = [])
    {
        $this->hc = new HcRepository();
        $this->sysList = new SysListRepository();

        //[1]获取请求参数
        $pageSize = $this->processingDefault($paramIn, 'page_size', 2, 10);
        $pageNum = $this->processingDefault($paramIn, 'page_num', 2, 1);

        //[2]获取列表数据
        $column = [
            'store_id',
            'status',
            'jd_id',
        ];
        $paramIn['status'] = 2;
        $searchWhere = checkParam($column, $paramIn);

        $param = [
            'searchWhere' => $searchWhere,
            'page_size' => $pageSize,
            'page_num' => $pageNum,
        ];
        $data = $this->hc->getPendingListBystoreId(array_filter($param));
        if (isset($data['data']) && $data['data']) {

            $storeIDs = array_unique(array_keys(array_column($data['data'], null, 'store_id')));
            $worknodeIds = getIdsStr($storeIDs);
            $worknodeData = $this->sysList->getStoreList([
                "ids" => $worknodeIds,
            ]);
            $worknodeData = array_column($worknodeData, 'name', 'id');
            foreach ($data['data'] as $k => &$v) {
                $v['store_name'] = $worknodeData[$v['store_id']] ?? '';
            }
        }

        //[3]组织返回参数
        $returnArr['data']['dataList'] = $data['data'] ?? [];
        $returnArr['data']['pagination'] = $data['pagination'] ?? [];

        return $this->checkReturn($returnArr);
    }

    /**
     * 网点HC审批通过并且在招聘中的人数
     * @param array $paramIn 出入参数列表
     * @return array
     */
    public function getApprovalByStore($paramIn = [])
    {
        $this->hc = new HcRepository();
        $this->sysList = new SysListRepository();

        //[1]获取请求参数
        $pageSize = $this->processingDefault($paramIn, 'page_size', 2, 10);
        $pageNum = $this->processingDefault($paramIn, 'page_num', 2, 1);

        //[2]获取列表数据
        $column = [
            'store_id',
            'status',
            'jd_id',
        ];
        $paramIn['status'] = 2;
        $searchWhere = checkParam($column, $paramIn);

        $param = [
            'searchWhere' => $searchWhere,
            'page_size' => $pageSize,
            'page_num' => $pageNum,
        ];
        $data = $this->hc->getApprovalByStoreId(array_filter($param));
        if (isset($data['data']) && $data['data']) {
            //追加网点名
            $storeIDs = array_unique(array_keys(array_column($data['data'], null, 'store_id')));
            $worknodeIds = getIdsStr($storeIDs);
            $worknodeData = $this->sysList->getStoreList([
                "ids" => $worknodeIds,
            ]);
            $worknodeData = array_column($worknodeData, 'name', 'id');

            //翻译状态字段
            $hcStateList = (new \FlashExpress\bi\App\Server\SysListServer())->getHcStateList();
            $hcStateList = array_column($hcStateList, 'name', 'key');

            foreach ($data['data'] as $k => &$v) {
                $v['store_name'] = $worknodeData[$v['store_id']] ?? '';
                $v['status_title'] = $hcStateList[$v['status']] ?? '';
            }
        }

        //[3]组织返回参数
        $returnArr['data']['dataList'] = $data['data'] ?? [];
        $returnArr['data']['pagination'] = $data['pagination'] ?? [];

        return $this->checkReturn($returnArr);
    }

    /**
     * hc修改需求数
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function modifyDemandnumber($paramIn = [])
    {
        $this->hc = new HcRepository();

        //[1]获取请求参数
        $hcId = $this->processingDefault($paramIn, 'hc_id', 2);
        $reduceNumber = $this->processingDefault($paramIn, 'demandnumber', 2);

        //[2]校验参数
        if (!$hcId || $reduceNumber === "") {
            throw new \Exception($this->getTranslation()->_('miss_args'));
        }

        $hcData = $this->hc->checkHc([
            "hc_id" => $hcId,
        ]);

        if (!$hcData || $hcData["state_code"] != 2) { //只能招聘中的能修改
            throw new \Exception($this->getTranslation()->_('8014'));
        }

        $returnArr = [
            "data" => [
                "hc_id" => $hcId,
            ],
        ];
        if ($reduceNumber == 0)
            return $this->checkReturn($returnArr);

        //获取总需求数
        $_demandNumber = $hcData["demandnumber"] ?? 0;

        //HC调整后的总需求数 = HC总需求数 - 应减少人数
        $demandNumber = $_demandNumber - $reduceNumber;
        //HC调整后的剩余数 = HC剩余人数 - 应减少人数
        $surplusNumber = $hcData["surplusnumber"] - $reduceNumber;
        if ($surplusNumber < 0 || $demandNumber < 0) { //剩余人数必须大于等于0
            throw new \Exception($this->getTranslation()->_('8013'));
        }
        //修改hc逻辑
        if ($surplusNumber == 0 && $demandNumber != 0) {
            //改状态为已完成
            $stateCode = 3;
        } else if ($surplusNumber == 0 && $demandNumber == 0) {
            //改状态为已作废
            $stateCode = 4;
        } else {
            //改状态为招聘中
            $stateCode = 2;
        }

        $changeHc = [
            "hc_id" => $hcId,
            "updateData" => [
                "demandnumber" => $demandNumber,
                "surplusnumber" => $surplusNumber,
                "state_code" => $stateCode,
            ],
        ];
        $_re = $this->hc->changeHc($changeHc, $paramIn['userinfo']['id'] ?? '');

        $this->hc->syncHcToBuddy($hcId);

        if (!$_re)
            return $this->checkReturn(-3, $this->getTranslation()->_('4102'));

        return $this->checkReturn($returnArr);
    }

    /**
     * hc修改需求数
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function modifyPriority($paramIn = [])
    {
        $this->hc = new HcRepository();

        $hcId = $paramIn["hc_id"] ?? "";
        $priorityId = $paramIn["priority_id"] ?? "";
        $staff_id = $paramIn["staff_id"] ?? "";

        //切换枚举验证
        $keys = $this->getPriorityUrgentKeys($staff_id);

        //校验参数
        if (!$hcId || !in_array($priorityId,$keys)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('8021'));
        }

        $changeHc = [
            "hc_id" => $hcId,
            "updateData" => [
                "priority_id" => $priorityId,
            ],
        ];
        $this->hc->changeHc($changeHc, $staff_id);
        $returnArr = [
            "data" => [
                "hc_id" => $hcId,
            ],
        ];
        return $this->checkReturn($returnArr);

    }


    /**
     * 验证是否拦截用户  true拦截   false不拦截
     */
    public function getPriorityUrgentKeys($staffId): array
    {
        $priorityList = (new SysListServer())->getHcPriorityList();
        $editHcPriorityStaff = (new SettingEnvServer())->getSetValToArray('edit_hc_priority_staff');

        //不是马来 不拦截
        if (
            isCountry('MY') &&
            !in_array($staffId,$editHcPriorityStaff)
        ) {
            $hcPriorityList = array_filter($priorityList, function($item) {
                return $item['key'] !== 0;
            });
        
            // 重新索引数组
            $priorityList = array_values($hcPriorityList);
        }

        return array_column($priorityList,'key');
    }

    /**
     * hr_statistical_hc表修改需求数
     * @param array $paramIn
     * @return array
     */

    public function modifyHcBudget($paramIn = [])
    {
        $this->hc = new HcRepository();

        $store_id = $this->processingDefault($paramIn, "store_id", 1);
        $jd_id = $this->processingDefault($paramIn, 'jd_id', 2);
        $budget = $this->processingDefault($paramIn, 'budget', 2);


        $res = $this->hc->modifyHcBudget($store_id, $jd_id, $budget);
        if ($res === true) {
            return $this->checkReturn(1);
        } else {
            //修改失败
            return $this->checkReturn(-3, $this->getTranslation()->_('4102'));
        }
    }


    /**
     * 导入预算人数
     * @param $file
     * @return bool|string
     */
    public function importHcBudget($file)
    {
        $this->hc = new HcRepository();

        try {
            ini_set("memory_limit", "1024M");
            ini_set('max_execution_time', 0);

            if (!is_file($file)) {
                return "is not file";
            }

            $objReader = IOFactory::createReader('Xlsx');//use excel2007 for 2007 format
            $objPHPExcel = $objReader->load($file);
            $objWorksheet = $objPHPExcel->getSheetByName("Header");
            if (empty($objWorksheet)) {
                return "sheet is null";
            }

            $highestRow = $objWorksheet->getHighestRow();//获取总行数
            //在导入时，启动行号为1，第1行为标题，第4行开始为数据
            $startRow = 2;
            $startColumn = 2;
            $highestColumn = $objWorksheet->getHighestColumn();//获取总列数
            $highestColumnIndex = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::columnIndexFromString($highestColumn);//总列数


            $storeIds = [];
            for ($row = $startRow; $row <= $highestRow; $row++) {
                //获取网点
                $storeId = $objWorksheet->getCellByColumnAndRow(1, $row)->getValue();
                $storeIds[] = $storeId;
            }

            if (empty($storeIds)) {
                return "store_id is null";
            }

            $filterStoreIds = array_unique($storeIds);
            if (count($filterStoreIds) != count($storeIds)) {
                return "store_id is not unique";
            }

            $flag = (new SysStoreServer())->isHaveAllStoreIds($storeIds);
            if (!$flag) {
                return "not exist in store_ids==" . json_encode($storeIds, JSON_UNESCAPED_UNICODE);
            }


            $jobTitleIds = [];

            for ($col = $startColumn; $col <= $highestColumnIndex; $col++) {
                $jobTitleStr = $objWorksheet->getCellByColumnAndRow($col, 1)->getValue();

                //hr_job_title_16
                $jobTileIdStr = str_replace("hr_job_title_", "", $jobTitleStr);

                if (!is_numeric($jobTileIdStr)) {
                    return "col=$col $jobTitleStr=$jobTileIdStr is not number";
                }

                if ($jobTileIdStr != intval($jobTileIdStr)) {
                    return "col=$col $jobTitleStr=$jobTileIdStr is not integer";
                }

                $jobTitleIds[$col] = intval($jobTileIdStr);
            }

            $data = [];

            for ($row = $startRow; $row <= $highestRow; $row++) {
                //获取网点
                $storeId = $objWorksheet->getCellByColumnAndRow(1, $row)->getValue();

                //获取不同job_id下预算人数
                for ($col = $startColumn; $col <= $highestColumnIndex; $col++) {
                    $numStr = $objWorksheet->getCellByColumnAndRow($col, $row)->getValue();

                    //先判断是数字，'1'这样也算数字
                    if (!is_numeric($numStr)) {
                        return "row=$row col=$col value=$numStr is not number";
                    }

                    //在判断是整型
                    if ($numStr != intval($numStr)) {
                        return "row=$row col=$col value=$numStr is not integer";
                    }

                    $num = intval($numStr);
                    if ($num > 999 || $num < 0) {
                        return "row=$row col=$col value=$numStr is not between 0 and 999";
                    }
                    $data[$storeId][$jobTitleIds[$col]] = $num;
                }
            }

            if (empty($data)) {
                return "import data is null";
            }

            foreach ($data as $storeId => $hcBudget) {
                $this->hc->updateStoreHcBudgetByJobTitle($hcBudget, $storeId);
            }
            return true;
        } catch (\Exception $e) {
            return $e->getMessage();
        }
    }

    /**
     * 获取重复的hc申请
     * 在操作者提交用人申请时，校验HC所属部门、JD ID、所属网点、用人原因title（新增/转岗/离职）
     * 若该HC满足以上四项校验条件与HC列表中状态为1-审批中，2-招聘中，7-待审批的HC信息存在完全相同的情况
     * @param array $paramIn
     * @return mixed
     */
    public function checkRepeatHcRequestList($paramIn = [])
    {
        $this->sysList = new SysListRepository();

        //[1]获取参数
        $departmentId = $this->processingDefault($paramIn, 'department_id');
        $jobId = $this->processingDefault($paramIn, 'job_id');
        $storeId = $this->processingDefault($paramIn, 'worknode_id');
        $reasonType = $this->processingDefault($paramIn, 'reason_type');

        if (empty($departmentId) || empty($jobId) || empty($storeId) || empty($reasonType)) {
            return [];
        }

        $sql = "--
            SELECT 
                hc.hc_id,
                hc.job_id,
                jd.job_name,
                hc.department_id 
            FROM hr_hc hc
            JOIN hr_jd jd ON jd.job_id = hc.job_id
            WHERE hc.department_id = ? AND 
                hc.worknode_id = ? AND
                hc.job_id = ? AND
                hc.reason_type = ? AND
                (hc.state_code = 1 and hc.approval_state_code = 7 OR hc.state_code = 2)";
        $data = $this->getDI()->get('db_rby')->fetchAll($sql, \Phalcon\Db::FETCH_ASSOC, [$departmentId, $storeId, $jobId, $reasonType]);
        if (empty($data)) {
            return [];
        }

        //获取部门列表
        $departmentDataList = $this->sysList->getDepartmentList();
        $departmentDataList = array_column($departmentDataList, 'name', 'id');

        foreach ($data as $k => $v) {
            $data[$k]['department_name'] = $departmentDataList[$v['department_id']] ?? '';
        }

        $ret['dataList'] = $data ?? [];
        $ret['count'] = count($data) ?? 0;
        return $ret;
    }

    /**
     *
     * 获取hc带审批hc
     * */
    public function getApproveHc($staff_id)
    {
        $this->hc = new HcRepository();
        if (empty($staff_id)) {
            return [];
        }
        return $this->hc->getApprovedHc($staff_id);

    }

    /**
     * 导出待审批hc
     * @param $staff_id
     * @return array
     * @throws BusinessException
     */
    public function exportApproveHc($staff_id)
    {
        $data = $this->getApproveHc($staff_id);

        $header = [
            'Export time',
            'HC_id',
            'Type of employment',
            'Branch_id',
            'Job_name',
            'Reason of recruitment',
            'DemandNumber',
            'Priority',
            'Approval results(Agree/Reject)',
            'Cause of rejection',
        ];
        $dataExport = [];
        foreach ($data as $value) {
            $item = [];

            $item[] = date('Y-m-d H:i:s');
            $item[] = $value['hc_id'];
            $item[] = $value['hire_type'] ? $this->getTranslation()->_('hire_type_'.$value['hire_type']) : '';
            $item[] = $value['worknode_id'];
            $item[] = $value['job_name'];
            $item[] = $value['reason'];
            $item[] = $value['demandnumber'];
            $item[] = $value['priority_id'];
            $item[] = '';
            $item[] = '';


            $dataExport[] = $item;
        }

        $file_name = "hc_list_" . date("YmdHis");


        $file_path = Excel::exportExcel($header, $dataExport, $file_name);

        return ['export_url' => $file_path['object_url']];
    }

    /**
     * HC 批量审批
     * @param $params
     * @return bool
     * @throws BusinessException
     */
    public function uploadHcAuditImport($params)
    {
        $result = $this->uploadCheck($params['file_url']);
        if(empty($result['excel_data'])) {
            throw new BusinessException($this->getTranslation()->_('8402'));
        }
        if (count($result['excel_data'][0]) != 10) {
            throw new ValidationException($this->getTranslation()->_('file_num_error'));
        }

        $args['lang']  = $this->lang;
        $res = (new AsyncImportTaskServer())->insertTask($params['staff_info']['id'], WinHrAsyncImportTaskModel::BATCH_AUDIT_HC ,$params['file_url'], $args);
        if(!$res) {
            throw new ValidationException($this->getTranslation()->_('4008'));
        }
        return true;
    }

    /**
     * @param $uid
     * @param $data
     * @return array|string
     */
    public function dealAgentApproval($uid, $data)
    {
        //删除空行
        foreach (static::yieldData()($data) as $key => $value) {
            if (empty($value) || empty(array_filter($value))) {
                unset($data[$key]);
                continue;
            }
        }
        $this->hc = new HcRepository();

        $db = $this->getDI()->get('db');
        $db->begin();
        $task_id = $this->hc->insertApprovalTask([
            'is_deal' => 0,
            'staff_id' => $uid,
            'total_num' => count($data),
        ]);

        if(empty($task_id)) {
            $db->rollback();
            $returnArr = [
                'code' => 0,
                'msg' => 'fail',
                'data' => ['task_id' => 0],
            ];
            return $this->checkReturn($returnArr);
        }

        $hc_data = $this->getApproveHc($uid);
        $hc_data = array_column($hc_data, 'hc_id');
        $insert_data = [];
        foreach ($data as $value) {
            $state = 1;
            $error_message = '';

            if (!in_array($value[1], $hc_data)) {
                $state = 4;
                $error_message = $this->getTranslation()->_('8016');
            }
            if (!in_array((string)$value[8], ['Agree', 'Reject'])) {
                $state = 4;
                $error_message = $this->getTranslation()->_('8017');
            }
            if ((string)$value[8] == 'Reject' && empty($value[9])) {
                $state = 4;
                $error_message = $this->getTranslation()->_('8018');
            }
            if ((string)$value[8] == 'Reject' && mb_strlen($value[9]) > 1000) {
                $state = 4;
                $error_message = $this->getTranslation()->_('reject_reason_limit');
            }
            $insert_data[] = [
                'hc_task_id' => $task_id,
                'export_time' => empty($value[0]) ? NULL : date('Y-m-d H:i:s', strtotime($value[0])),
                'hc_id' => empty($value[1]) ? NULL : $value[1],
                'branch_id' => $value[3],
                'job_name' => (string)$value[4],
                'reason' => (string)$value[5],
                'demand_num' => empty($value[6]) ? NULL : $value[6],
                'priority' => empty($value[7]) ? NULL : $value[7],
                'approval_result' => (string)$value[8],
                'cause_rejection' => (string)$value[9],
                'state' => $state,
                'error_message' => $error_message,
            ];
        }
        $res = $this->hc->batch_insert('hc_approval_detail', $insert_data);
        $returnArr = [
            'code' => 1,
            'msg' => 'success',
            'data' => ['task_id' => $task_id],
        ];
        if($res) {
            $db->commit();
            return $this->checkReturn($returnArr);
        }

        $db->rollback();
        $returnArr = [
            'code' => 0,
            'msg' => 'fail',
            'data' => ['task_id' => 0],
        ];

        return $this->checkReturn($returnArr);
    }

    public function getApprovalResult($task_id)
    {
        //查询审批task
        $db = $this->getDI()->get("db");

        $sql = "select id,is_deal,total_num,success_num from hc_approval_task where id= :id";
        $task_res = $db->fetchOne($sql, \Phalcon\Db::FETCH_ASSOC, ['id' => $task_id]);
        if (1 == $task_res['is_deal']) {//处理完成

            return [
                'is_deal' => 1,
                'total_num' => $task_res['total_num'],
                'success_num' => $task_res['success_num'],
                'fail_num' => $task_res['total_num'] - $task_res['success_num'],

            ];
        } else {//查询进度
            $sql = "select count(*) as num from hc_approval_detail where hc_task_id={$task_id} and state =1";
            $task_detail = $db->fetchOne($sql, \Phalcon\Db::FETCH_ASSOC);

            return [
                'is_deal' => 0,
                'total_num' => $task_res['total_num'],
                'success_num' => $task_res['total_num'] - $task_detail['num'],
                'fail_num' => 0,

            ];

        }
    }

    public function exportHc($task_id)
    {
        $db = $this->getDI()->get("db");

        $sql = "select *  from hc_approval_detail where hc_task_id={$task_id} and state = 4";
        $data = $db->fetchAll($sql, \Phalcon\Db::FETCH_ASSOC);

        if (empty($data)) {
            return [];
        }
        $header = [
            'Export time',
            'HC_id',
            'Branch_id',
            'Job_name',
            'Reason of recruitment',
            'DemandNumber',
            'Priority',
            'Approval results(Agree/Reject)',
            'Cause of rejection',
            'Error Message',
        ];
        $dataExport = [];
        try {
            foreach ($data as $value) {
                $item = [];

                $item[] = $value['export_time'];
                $item[] = $value['hc_id'];
                $item[] = $value['branch_id'];

                $item[] = $value['job_name'];
                $item[] = $value['reason'];
                $item[] = $value['demand_num'];
                $item[] = $value['priority'];
                $item[] = $value['approval_result'];
                $item[] = $value['cause_rejection'];
                $item[] = $value['error_message'];


                $dataExport[] = $item;
            }

            $file_name = "hc_fail_list_" . date("YmdHis");


            $file_path = Excel::exportExcel($header, $dataExport, $file_name);

            return ['export_url' => $file_path['object_url']];
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("hc: exportHc " . $task_id . $e->getMessage() . $e->getTraceAsString(), 'info');

            return $e->getMessage();

        }

    }

    /**
     * hc批量修改 优先级
     * @param $params
     * @return bool
     * @throws BusinessException
     */
    public function BatchPriorityImport($params)
    {
        $this->uploadCheck($params['file_url']);
        $args['lang']  = $this->lang;
        $res = (new AsyncImportTaskServer())->insertTask($params['staff_id'], WinHrAsyncImportTaskModel::BATCH_EDIT_PRIORITY_NUM_HC ,$params['file_url'], $args);

        if(!$res) {
            throw new ValidationException($this->getTranslation()->_('4008'));
        }

        return true;
    }

    public function BatchPriority($data, $staff_id, $file_name)
    {
        $this->hc = new HcRepository();

        $success_num = 0;
        $fail_num = 0;
        $fail_data = [];

        //切换枚举验证
        $priorityKeys = $this->getPriorityUrgentKeys($staff_id);
        $priorityKeysStr = implode('/', $priorityKeys);

        try {
            foreach (static::yieldData()($data) as $key => $value) {
                if (empty($value) || empty(array_filter($value))) {
                    unset($data[$key]);
                    continue;
                }
                if (empty($value[0])) {
                    $fail_num++;

                    $fail_data[] = [$value[0], $value[1], $this->getTranslation()->_('8019')];
                    continue;
                }

                //判断是0
                if (empty($value[1]) && in_array(0, $priorityKeys)) {
                    if ($value[1] !== '0' && $value[1] !== 0) {
                        $fail_num++;
                        $fail_data[] = [$value[0], $value[1], $this->getTranslation()->_('8021')];
                        continue;
                    }
                } else {
                    if (!in_array($value[1], [1, 2, 3, 4])) {
                        $fail_num++;
                        $fail_data[] = [$value[0], $value[1], $this->getTranslation()->_('8021')];
                        continue;
                    }
                }


                $hc_data = $this->hc->checkHc([
                    "hc_id" => $value[0],
                ]);
                if (!$hc_data) {
                    $fail_num++;
                    $fail_data[] = [$value[0], $value[1], $this->getTranslation()->_('8019')];
                    continue;

                }

                $hc_id = $this->modifyPriority(['hc_id' => $value[0], 'priority_id' => $value[1], 'staff_id' => $staff_id]);

                if (isset($hc_id['data']['hc_id']) && !empty($hc_id['data']['hc_id'])) {
                    $this->hc->insertPriorityRecord(['staff_id' => $staff_id, 'hc_id' => $value[0], 'priority_id' => $value[1], 'is_success' => 1]);
                    $success_num++;
                    continue;

                } else {
                    $fail_data[] = [$value[0], $value[1], $hc_id['msg']];
                    $this->hc->insertPriorityRecord(['staff_id' => $staff_id, 'hc_id' => $value[0], 'priority_id' => $value[1], 'is_success' => 2, 'error_message' => $hc_id['msg']]);

                    $fail_num++;
                    continue;

                }

            }

            if ($fail_num > 0) {
                $header = [
                    'HC_id',
                    "Priority_adjust({$priorityKeysStr})",
                    'Error Message',
                ];


                $fileName = !empty($file_name) ? $file_name : (new AsyncImportTaskServer())->getResultFileName(WinHrAsyncImportTaskModel::BATCH_EDIT_PRIORITY_NUM_HC);

                $file_path = Excel::exportExcel($header, $fail_data, $fileName);
            }
            return [
                'success_num' => $success_num,
                'fail_num' => $fail_num,
                'file_url' => $file_path['object_url'] ?? '',

            ];

        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("hc: exportHc " . $staff_id . $e->getMessage() . $e->getTraceAsString(), 'info');

            return $e->getMessage();
        }

    }




    public function getApprovalTask()
    {
        //查询审批task
        $db = $this->getDI()->get("db");
        $sql = "select id,is_deal,total_num,success_num from hc_approval_task where is_deal= 0";
        $task_res = $db->fetchOne($sql, \Phalcon\Db::FETCH_ASSOC);
        return $task_res;
    }

    /**
     * 获取Hc招聘负责人工号
     * 临时方法，先写死静态数据，二期会做成功能
     * @param $dept_id
     */
    public function getHcManagerByDeptId($dept_id)
    {
        if (strtolower(env('country_code')) != 'th') return 0; //时间紧，临时写法

        // 定义一个hc负责人为key值，部门ID为value的一个关系数组
        //注意 此数据均为泰国数据，其他国家调用无效
        $dept_hc_manager_map = [
            '57873' => ['65', '18', '86', '84', '87', '83', '25', '57', '107', '68', '70', '67', '66', '69', '71', '106', '58', '59', '95'],
            '59833' => ['216', '7', '47', '47', '48', '90', '183', '118', '119', '439', '120', '436', '437', '438', '294', '132', '185', '240', '133', '134', '227', '17', '229', '225', '226', '305'],
            '62592' => ['41', '193', '194', '196', '40', '371', '192', '191', '187', '189', '188', '190'],
            '64100' => ['231', '173', '27', '91', '19', '21', '44', '45', '46', '26', '62', '64', '60', '61', '94', '63'],
            '64857' => ['20', '12', '390', '391', '236'],
            '68164' => ['110', '228', '442', '3', '175', '89', '177', '88', '176', '93', '162', '161', '160', '159', '32', '30', '33', '31', '13', '51', '104', '105', '415', '75', '77', '76', '34', '4'],
            '75733' => ['217', '389', '413', '394', '397', '434', '428', '430', '427', '432', '429', '431', '233', '310', '49', '22', '36', '35', '243', '178', '146', '20003', '172', '238', '239', '149', '20002', '458', '459', '72'],

        ];
        $hc_manager_staff_id = 0;
        foreach ($dept_hc_manager_map as $manager_staff_id => $deptid_arr) {
            if (in_array($dept_id, $deptid_arr)) {
                $hc_manager_staff_id = $manager_staff_id;
                break;
            }
        }

        return $hc_manager_staff_id;
    }


    /**
     * @description:获取部门链 判断申请人部门在 //Network Bulky Operations //Network Bulky Operations部门
     *
     * @param null
     *
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2022/1/14 20:18
     */

    public function isShowHc($deptId = 0)
    {
        $is_show_hc = 1;  //显示
        //获取 Network Bulky Operations
        $bulky_departId         = SettingEnvServer::getSetVal('dept_network_bulky_operations_id');
        $share_department_ids[] = !empty($bulky_departId) ? $bulky_departId : 0;
        //获取 Network  Operations
        $share_department_ids[] = SettingEnvServer::getSetVal('dept_network_operations_id');
        if (!empty($deptId) && !empty($share_department_ids)) {
            $deptInfo    = SysDepartmentModel::findFirst([
                'conditions' => "id = :id:",
                'bind'       => [
                    'id' => $deptId,
                ],
                'columns'    => ['ancestry_v3'],
            ]);
            $ancestry_v3 = !empty($deptInfo) ? $deptInfo->ancestry_v3 : '';
            $deptIds     = !empty($ancestry_v3) ? explode('/', $ancestry_v3) : [];
            //判断是否存在交集  如果存在交集 是共享部门
            if (array_intersect($deptIds, $share_department_ids)) {
                $is_show_hc = 0;
            }
        }
        return $is_show_hc;
    }

    /**
     * 获取hc表全部网点
     * @return array
     */
    public function getHcAllStoreId(): array
    {
        $list = HrhcModel::find([
            'columns' => 'DISTINCT(worknode_id) as worknode_id',
        ])->toArray();
        return array_column($list, 'worknode_id');
    }


    /**
     * 权限范围SQL拼接
     * @param string $tableName
     * @param $userInfo
     * @return string
     */
    public function assembleAuthoritySql($tableName = '', $userInfo)
    {
        $authority_stores_ids = $userInfo['permission_stores_ids']; //网点权限

        if (!empty($authority_stores_ids) && is_array($authority_stores_ids)) {
            if (!in_array('-2', $authority_stores_ids)) {
                $authority_stores_ids = array_intersect($authority_stores_ids, $this->getHcAllStoreId());
            }
        }

        $authority_department_ids = $userInfo['permission_department_ids'];   //部门权限
        $staff_info_id = $userInfo['id'];
        $manage_hc_priority = $userInfo['manage_hc_priority'];
        $is_whr_permission = $userInfo['is_whr_permission'];

        if (false === $is_whr_permission) {
            //查看是否配置权限
            //查看是否配置管辖范围-部门
            $staffDepartmentist = HrStaffManageDepartmentModel::find([
                'conditions' => ' staff_info_id = :staff_info_id: and deleted = :deleted: and type = :type:',
                'bind'       => [
                    'staff_info_id' => intval($staff_info_id),
                    'type'          => HrStaffManageDepartmentModel::$type_1,
                    'deleted'       => enums::IS_DELETED_NO,
                ],
            ])->toArray();

            //判断是不是 部门负责人 & 部门负责人的助理
            $deptManager = SysDepartmentModel::findFirst([
                'conditions' => '(manager_id = :manager_id: or assistant_id = :assistant_id:) and type IN(2,3) and deleted = :deleted:',
                'bind'       => [
                    'manager_id'    => $staff_info_id,
                    'assistant_id'  => $staff_info_id,
                    'deleted'       => enums::IS_DELETED_NO,
                ],
            ]);
        }

        if ($staffDepartmentist || $deptManager) {

            $workStr = '';
            if ($staffDepartmentist) {
                $workStr = "and hc.worknode_id = '-1'";
            }

            $where = '(';
            //获取部门下Head office
            if (empty($authority_department_ids)) {
                $authority_department_ids = 0;
                $where .= " hc.department_id = " . $authority_department_ids;
            } else if (is_array($authority_department_ids)) {
                $authority_department_ids = getIdsStr($authority_department_ids);
                $where .= " hc.department_id IN (" . $authority_department_ids . ") ";
            } else {
                $where .= " hc.department_id = " . $authority_department_ids . ") ";
            }

            if(isCountry(['TH','PH','MY']) && !empty($manage_hc_priority)){
                $where .= " AND  hc.priority_id in (".implode(',',$manage_hc_priority).") ";
            }

            $where .= " {$workStr} ) OR ( ";
            //网点
            if (empty($authority_stores_ids)) {
                $where .= " hc.worknode_id = '' ";
            } else if ($authority_stores_ids == '-2' || in_array('-2', $authority_stores_ids)) {

                //-2 是全部网点 不包含 -1 网点
                $where .= " hc.worknode_id !=  '-1' " ;

            } else if (is_array($authority_stores_ids)) {

                $authority_stores_ids = getIdsStr($authority_stores_ids);
                $where .= " hc.worknode_id IN (" . $authority_stores_ids . ") ";

            } else {
                $where .= " hc.worknode_id = " . $authority_stores_ids . " ";
            }
        } else {
            //网点
            $where = '(';

            if (empty($authority_stores_ids)) {
                $where .= " hc.worknode_id = '' ";
            } else if ($authority_stores_ids == '-2' || in_array('-2', $authority_stores_ids)) {

                //-2 是全部网点 不包含 -1 网点
                $where .= " hc.worknode_id != '-1' " ;

            } else if (is_array($authority_stores_ids)) {

                $authority_stores_ids = getIdsStr($authority_stores_ids);
                $where .= " hc.worknode_id IN (" . $authority_stores_ids . ") ";

            } else {
                $where .= " hc.worknode_id = " . $authority_stores_ids . " ";
            }
        }


        if(isCountry(['TH','PH','MY']) && !empty($manage_hc_priority)){
            $where .= " AND  hc.priority_id in (".implode(',',$manage_hc_priority).") ";
        }

        if ($tableName) {
            $where .= " ) OR {$tableName}.hc_id = 0";
        } else {
            $where .= " )";
        }

        if(isCountry(['TH','PH','MY'])){
            $where .= " OR (resume_last_operator = $staff_info_id )";
        }

        return $where;
    }

    /**
     * @description: 获取共享部门和职位
     *
     * department_ids 所有的共享部门 id   position_ids 所有的共享职位 id    department_position  部门=>[共享职位,共享职位]
     * @return     :['department_ids'=>[1,2],'position_ids'=>[3,4,5,6],'department_position'=>[1=>[3,4],2=>[5,6]]];
     */
    public function getShareDepartmentPosition(): array
    {
        $result = ['department_ids' => [], 'position_ids' => [], 'department_position' => []];

        try {
            $envModel = new SettingEnvServer();
            //格式是  部门|职位,职位&部门|职位,职位
            $share = $envModel->getSetVal('hc_share_department_position');
            if (!empty($share)) {
                $share = explode('#', $share);
                foreach ($share as $k => $v) {
                    if (empty($v)) {
                        continue;
                    }
                    $department_position = explode('|', $v);
                    $department_id = $department_position[0] ?? 0;//部门 id
                    $result['department_ids'][] = $department_id;              //部门 id
                    $result['department_position'][$department_id] = [];
                    if (isset($department_position[1]) && !empty($department_position[1])) {
                        $position_ids = explode(',', $department_position[1]); //职位 id
                        $result['position_ids'] = array_merge($result['position_ids'], $position_ids);
                        $result['department_position'][$department_id] = $position_ids;
                    }
                }
            }
        } catch (\Exception $e) {
            $this->getDI()->get("logger")->write_log(
                'file ' . $e->getFile() .
                ' line ' . $e->getLine() .
                ' message ' . $e->getMessage() .
                ' trace ' . $e->getTraceAsString()
            );
        }
        return $result;
    }


    /**
     * hc批量作废
     * @param $excel_data
     * @param $staff_id
     * @param string $file_name
     * @return array
     * @throws BusinessException
     */
    public function batchNullifyHc($excel_data, $staff_id, $file_name = '')
    {
        $this->getDI()->get("logger")->write_log(['excel_data'=>$excel_data,'userInfo'=>$staff_id],'info');
        $returnData = [
            'code' => 1,
            'msg' => '',
            'data' => [],
        ];

        $s = 0;
        $e = 0;
        $updateIds = [];
        $hcIdsArr = array_values(array_unique(array_column($excel_data, "0")));

        if(empty($hcIdsArr)){
            $this->logger->write_log('batchNullifyHc hcid 为空', 'error');
            return [
                'code' => 0,
                'msg' => '',
                'data' => [],
            ];
        }
        $_hcData = HrhcModel::find([
            'conditions' => ' hc_id in ({ids:array})',
            'bind' => [
                'ids' => $hcIdsArr,
            ],
        ])->toArray();
        $hcData = array_column($_hcData, null, 'hc_id');
        foreach (static::yieldData()($excel_data) as $k => $v) {
            if (empty($v) || empty(array_filter($v))) {
                unset($excel_data[$k]);
                continue;
            }
            if (empty($v[0])) {
                continue;
            }
            $hc_id = intval($v[0]) ?? 0;
            if (empty($hc_id) || empty($hcData[$hc_id])) {
                $e++;
                $excel_data[$k][1] = 'HCID error';
                continue;
            }
            if ($hcData[$hc_id]["state_code"] != 2) {
                $e++;
                $excel_data[$k][1] = $this->getTranslation()->_('11025');
                continue;
            }
            $updateIds[] = $hc_id;
            $this->getDI()->get('logger')->write_log("batchNullifyHc data:" . json_encode($updateIds), 'info');
            $s++;
        }

        $header = [
            "HC ID",
//            "错误原因",
            $this->getTranslation()->_('11017'),
        ];

        $fileName = !empty($file_name) ? $file_name : (new AsyncImportTaskServer())->getResultFileName(WinHrAsyncImportTaskModel::BATCH_VOID_HC);

        $file_path = Excel::exportExcel($header, $excel_data, $fileName);
        $fileUrl = $file_path ? $file_path['object_url'] : '';
        if ($updateIds) {
            $hc_ids = getIdsStr($updateIds);
            $updeteDate = ["state_code" => 4];
            $result = (new HcRepository())->batchUpdateHc($hc_ids, $updeteDate, $staff_id);
            if ($result) {
                $returnData["code"] = 1;
                $returnData["msg"] = $this->getTranslation()->_('id_card_upload_success');
            }
        }
        $returnData["data"]["file_url"] = $fileUrl;
        $returnData["data"]["success_num"] = $s;
        $returnData["data"]["error_num"] = $e;
        return $returnData;

    }

    /**
     * 批量作废
     * @param $params
     * @return bool
     * @throws BusinessException
     * @throws ValidationException
     */
    public function batchNullifyHcImport($params)
    {
        /* 检查自己权限 */
        $rolePermissions = $this->checkRolePermissions($params['staff_info']);
        if ($rolePermissions['hcUpdateBatch'] != 1) {
            throw new ValidationException($this->getTranslation()->_('4009'));
        }
        $this->uploadCheck($params['file_url']);
        $args['lang']  = $this->lang;
        $res = (new AsyncImportTaskServer())->insertTask($params['staff_info']['id'], WinHrAsyncImportTaskModel::BATCH_VOID_HC ,$params['file_url'], $args);

        if(!$res) {
            throw new ValidationException($this->getTranslation()->_('4008'));
        }

        return true;
    }

    /**
     * hc批量修改需求人数
     */
    public function batchUpdateHc($excel_data, $staff_id, $file_name = '')
    {
        $this->getDI()->get("logger")->write_log(['excel_data'=>$excel_data,'userInfo'=>$staff_id],'info');

        $returnData = [
            'code' => 1,
            'msg' => '',
            'data' => [],
        ];

        $s = 0;
        $e = 0;
        $updateIds = [];
        $hcIdsArr = array_values(array_unique(array_column($excel_data, "0")));

        if(empty($hcIdsArr)){
            $this->logger->write_log('batchUpdateHc hcid 为空', 'error');

            $returnData = [
                'code' => 0,
                'msg' => '',
                'data' => [],
            ];
        }

        $_hcData = HrhcModel::find([
            'conditions' => ' hc_id in ({ids:array})',
            'bind' => [
                'ids' => $hcIdsArr,
            ],
        ])->toArray();
        $hcData = array_column($_hcData, null, 'hc_id');
        $config = SettingEnvServer::getSetVal('hc_budget_configured');
        if (!empty($config)) {
            $server = HcStatisticServer::getInstance();

            //兼容旧版代码
            $share_department_position = [];
        } else {
            $server = Tools::reBuildCountryInstance(new HcServer());

            // hc预算共享部门职位
            $shareDepartmentPosition = $this->getShareDepartmentPosition();
            $share_department_position = $shareDepartmentPosition['department_position'];
        }
        $mq = new RocketMQ('audit-list-update');
        foreach (static::yieldData()($excel_data) as $k => $v) {
            if (empty($v) || empty(array_filter($v))) {
                unset($excel_data[$k]);
                continue;
            }

            if (empty($v[0])) {
                continue;
            }

            $hc_id = intval($v[0]) ?? 0;
            if (empty($hc_id) || empty($hcData[$hc_id])) {
                $e++;
                $excel_data[$k][2] = 'HCID error';
                continue;
            }
            if ($hcData[$hc_id]["state_code"] != 2) {
                $e++;
                $excel_data[$k][2] = $this->getTranslation()->_('11025');
                continue;
            }
            $_demandnumber = intval($v[1]) ?? 0;
            if (empty($_demandnumber) || $_demandnumber < 0 || $_demandnumber < (intval($hcData[$hc_id]["demandnumber"]) - intval($hcData[$hc_id]["surplusnumber"]))) {
                $e++;
                $excel_data[$k][2] = $this->getTranslation()->_('11030');
                continue;
            }
            //部门 id
            $_department_id = $hcData[$hc_id]["department_id"] ?? 0;
            //职位 id
            $_job_title = $hcData[$hc_id]["job_title"] ?? 0;
            $_store_id = $hcData[$hc_id]["worknode_id"] ?? 0;
            if (empty($_department_id) || empty($_job_title)) {
                $e++;
                $excel_data[$k][2] = $this->getTranslation()->_('11001');
                continue;
            }
            // 检查是否有预算
            //补充
            $supplement_num = $_demandnumber - $hcData[$hc_id]["demandnumber"];
            if ($supplement_num > 0){
                // 检查增加需求人数权限
                $edit_demand_num_staffids_arr            = SettingEnvServer::getSetValToArray('hrlist_edit_demand_num_staffids',',');
                if (!empty($_demandnumber) && !in_array($staff_id, $edit_demand_num_staffids_arr)) {
                    $e++;
                    $excel_data[$k][2] = $this->getTranslation()->_('update_hc_err_1');
                    continue;
                }
                
                // 获取预算
                $total = $server->getHrStaffing($_department_id, $_store_id, $_job_title, $share_department_position);
                $leftTotal = $server->getSurplusHcRequestCount($total, $_department_id, $_store_id, $_job_title, $share_department_position);
                $tmpNum = $server->getDepartmentStoreJobTitleHCTmpNum($_department_id, $_store_id, $_job_title);
                if ($leftTotal  < $supplement_num + $tmpNum) {
                    $e++;
                    $excel_data[$k][2] = $this->getTranslation()->_('11006');
                    continue;
                }
            }

            $updateIds[] = $hc_id;
            //修改后的需求人数≥已招人数（总需求-剩余），可修改成功。
            //当修改后的需求人数=已招人数，HC状态变更为已招满。
            $row_update = ["hc_id" => $hc_id,"demandnumber" => $_demandnumber, "surplusnumber" => (intval($hcData[$hc_id]["surplusnumber"]) + ($_demandnumber - intval($hcData[$hc_id]["demandnumber"])))];
            if($_demandnumber == (intval($hcData[$hc_id]["demandnumber"]) - intval($hcData[$hc_id]["surplusnumber"]))){
                $row_update = array_merge($row_update,[ 'state_code' => 3]);
            }
            $insertParams = [
                'audit_type' => 6,
                'audit_value'=> $hc_id,
                'update_list' => [
                    'demandnumber' => $_demandnumber,
                ],
            ];
            $rid = $mq->sendMsgByTag($insertParams,5);
            $this->logger->write_log('hr-contract-transfer rid:' . $rid . 'data:' . json_encode($insertParams), $rid ? 'info' : 'error');

            /******绑在一起*******/
            $updeteDate[] = $row_update;
            $supplement_num > 0 && $server->setDepartmentStoreJobTitleHCTmpNum($_department_id, $_store_id, $_job_title,$supplement_num);
            /******绑在一起*******/

            $this->getDI()->get('logger')->write_log("batchNullifyHc data:" . json_encode($updateIds), 'info');
            $s++;
        }

        $header = [
            "HC ID",
            //需求人数
            $this->getTranslation()->_('11016'),
//            "错误原因",
            $this->getTranslation()->_('11017'),
        ];

        $fileName = !empty($file_name) ? $file_name : (new AsyncImportTaskServer())->getResultFileName(WinHrAsyncImportTaskModel::BATCH_EDIT_DEMAND_NUM_HC);

        $file_path = Excel::exportExcel($header, $excel_data, $fileName);
        $fileUrl = $file_path ? $file_path['object_url'] : '';
        if ($updateIds) {
            $result = (new HcRepository())->batchUpdateHcDemandnumber($updeteDate,$staff_id);
            if ($result) {
                $returnData["code"] = 1;
                $returnData["msg"] = $this->getTranslation()->_('id_card_upload_success');
            }
        }
        $returnData["data"]["file_url"] = $fileUrl;
        $returnData["data"]["success_num"] = $s;
        $returnData["data"]["error_num"] = $e;
        return $returnData;
    }

    /**
     * hc批量修改需求人数
     * @param $params
     * @return bool
     * @throws BusinessException
     * @throws ValidationException
     */
    public function hcUpdateBatchImport($params)
    {
        /* 检查自己权限 */
        $rolePermissions = $this->checkRolePermissions($params['staff_info']);
        if ($rolePermissions['hcUpdateBatch'] != 1) {
            throw new ValidationException($this->getTranslation()->_('4009'));
        }
        $res = $this->uploadCheck($params['file_url']);

        $hcIdsArr = array_values(array_unique(array_column($res['excel_data'], "0")));

        if(empty($hcIdsArr)){
            throw new ValidationException($this->getTranslation()->_('4412'));
        }

        $args['lang']  = $this->lang;
        $res = (new AsyncImportTaskServer())->insertTask($params['staff_info']['id'], WinHrAsyncImportTaskModel::BATCH_EDIT_DEMAND_NUM_HC ,$params['file_url'], $args);

        if(!$res) {
            throw new ValidationException($this->getTranslation()->_('4008'));
        }
        return true;
    }

    /**
     * hc批量申请
     * @param $params
     * @return bool
     * @throws BusinessException
     * @throws ValidationException
     */
    public function batchInsertHcImport($params)
    {
        /* 检查自己权限 */
        $rolePermissions = $this->checkRolePermissions($params['staff_info']);
        if ($rolePermissions['hcAddBatch'] != 1) {
            throw new ValidationException($this->getTranslation()->_('4009'));
        }

        //验证
        if (isCountry()) {
            $importPath = $params['file_url'];
            $tmpDir     = sys_get_temp_dir();
            $fileName   = basename($importPath);
            $fileInfo   = pathinfo($fileName);

            if($fileInfo['extension'] != 'xlsx') {
                throw new BusinessException($this->getTranslation()->_('11029'));
            }

            $tmpFilePath = $tmpDir . '/' . $fileName;
            if (!file_put_contents($tmpFilePath, file_get_contents($importPath))) {
                throw new BusinessException('System error');
            }

            $config       = ['path' => dirname($tmpFilePath)];
            $fileRealName = basename($tmpFilePath);
            $excel        = new \Vtiful\Kernel\Excel($config);

            $excel->openFile($fileRealName)->openSheet();
            $excelData = $excel->getSheetData();

            $header = array_shift($excelData);

            if ($this->checkIsImportDataBeyondMaxLineCount($excelData, 1000)) {
                throw new BusinessException($this->getTranslation()->_('error_msg_over_line_limit_num', ['line_limit_num' => 1000]));
            }

            @unlink($tmpFilePath);

            if (!$this->checkExcelHeader($header)) {
                throw new ValidationException($this->getTranslation()->_('hc_import_format_error'));
            }
        } else {
            $this->uploadCheck($params['file_url']);
        }

        $args['lang']  = $this->lang;

        $res = (new AsyncImportTaskServer())->insertTask($params['staff_info']['id'], WinHrAsyncImportTaskModel::BATCH_ADD_HC ,$params['file_url'], $args);

        if(!$res) {
            throw new ValidationException($this->getTranslation()->_('4008'));
        }
        return true;
    }

    /**
     * header验证
     * @param $data
     * @return bool
     */
    public function checkExcelHeader($data): bool
    {
        $header = $this->excelAddHeader;

        foreach ($data as $k => $v) {
            if (!in_array(trim($v), $header[$k] ?? [])) {
                return false;
            }
        }

        return true;
    }

    /**
     * hc批量申请
     * @param $excel_data
     * @param $staff_id
     * @param $file_name
     * @return array
     */
    public function batchInsertHc($excel_data, $staff_id, $file_name = '')
    {
        $returnData = [
            'code' => 1,
            'msg' => '',
            'data' => [],
        ];
        try {
            $this->sysList = new SysListRepository();
            $svr = new BaseServer();
            $params = [];

            // 网点id匹配
            $worknodeIdsArr = array_unique(array_column($excel_data, "4"));
            $worknodeIds = getIdsStr($worknodeIdsArr);
            $worknodeData = $this->sysList->getStoreList([
                "ids" => $worknodeIds,
            ]);
            $worknodeData = array_column($worknodeData, null, 'id');

            //获取JD名称 匹配用的 ids
            $jobIdsArr = array_unique(array_column($excel_data, "2"));
            $jobIds = getIdsStr($jobIdsArr);
            $jobList = $this->sysList->getJobList([
                "ids" => $jobIds,
            ]);
            $jobListData = array_column($jobList, null, 'job_id');

            //查询部门名称 ids
            $departmentIdsArr = array_unique(array_column($excel_data, "0"));
            $departmentIds = getIdsStr($departmentIdsArr);
            $departmentData = $this->sysList->getDepartmentList([
                "ids" => $departmentIds,
            ]);
            $departmentDataList = array_column($departmentData, null, 'id');

            //查询职位名称 ids
            $positionIdsArr = array_unique(array_column($excel_data, "1"));
            $positionIds = getIdsWithoutQuotes($positionIdsArr);
            $positionData = $this->sysList->getPositionList([
                "ids" => $positionIds,
            ]);
            $positionList = array_column($positionData, null, 'id');

            // 查询网点部门关联关系
            $worknodeIdsArr                                 = array_values(array_filter($worknodeIdsArr));
            $hr_organization_department_store_relation_list = [];
            if (!empty($worknodeIdsArr)) {
                $hr_organization_department_store_relation_list = HrOrganizationDepartmentStoreRelationModel::find([
                    'conditions' => 'level_state = :level_state: and state = :state: and is_deleted = :is_deleted: and store_id in ({store_id:array})',
                    'bind'       => [
                        'level_state' => HrOrganizationDepartmentStoreRelationModel::LEVEL_STATE_ON,
                        'state'       => HrOrganizationDepartmentStoreRelationModel::STATE_ON,
                        'is_deleted'  => HrOrganizationDepartmentStoreRelationModel::IS_DELETED_NO,
                        'store_id'    => $worknodeIdsArr,
                    ],
                ])->toArray();
                $hr_organization_department_store_relation_list = array_column($hr_organization_department_store_relation_list,
                    null, 'store_id');
            }

            $s = 0;
            $e = 0;
            $insert = [];
            $config = SettingEnvServer::getSetVal('hc_budget_configured');
            if (!empty($config)) {
                $server = HcStatisticServer::getInstance();

                //兼容接口参数
                $share_department_position = [];
            } else {
                $server = Tools::reBuildCountryInstance(new HcServer());

                // hc预算共享部门职位
                $shareDepartmentPosition = $this->getShareDepartmentPosition();
                $share_department_position = $shareDepartmentPosition['department_position']; //部门=>[共享职位,共享职位]
            }
            
            foreach (static::yieldData()($excel_data) as $k => $v) {
                if (empty($v) || empty(array_filter($v))) {
                    unset($excel_data[$k]);
                    continue;
                }

                $department_id = intval($v[0]) ?? 0;
                $job_title     = intval($v[1]) ?? 0;
                $job_id        = intval($v[2]) ?? 0;
                $hire_type     = intval($v[3]) ?? '';
                $store_id      = $v[4] ?? '';


                if (isCountry()) {
                    $expirationdate = $v[5] ?? '';//截至日期
                    $priority_id    = $v[6] ?? '';//招聘优先级
                    $reason_type    = $v[7] ?? '';//用人原因
                    $demandnumber   = $v[8] ?? 0; //需求人数
                } else {
                    $expirationdate = $v[6] ?? '';//截至日期
                    $priority_id    = $v[7] ?? '';//招聘优先级
                    $reason_type    = $v[8] ?? '';//用人原因
                    $demandnumber   = $v[9] ?? 0; //需求人数
                }

                $_date = gmdate('Y-m-d H:i:s', time());
                $params['submitter_id']             = $staff_id;//提交人
                $params['state_code']               = 2; //招聘中
                $params['approval_stage']           = 0;
                $params['approval_state_code']      = 6; //审批已同意
                $params['approval_completion_time'] = $_date; //审批已同意
                $params['createtime']               = $_date; //创建时间
                $params['updated_at']               = $_date; //更新时间
                $params['remarks']                  = 'batchInsertHc'; //备注
                $params['workflow_role']            = 'hc_v2';

                $params['serial_no'] = $svr->getRandomId();
                //部门 id
                $params['department_id'] = $department_id;
                if (empty($params['department_id']) || empty($departmentDataList[$params['department_id']])) {
                    $e++;
                    $excel_data[$k][] = $this->getTranslation()->_('11021');
                    continue;
                }
                //职位 id
                $params['job_title'] = $job_title;
                if (empty($params['job_title']) || empty($positionList[$params['job_title']])) {
                    $e++;
                    $excel_data[$k][] = $this->getTranslation()->_('11022');
                    continue;
                }
                $hr_job_relation = HrJobDepartmentRelationModel::findFirst([
                    'conditions' => 'department_id = :department_id: and job_id = :job_id:',
                    'bind' => [
                        'department_id' => $params['department_id'],
                        'job_id' => $params['job_title'],
                    ],
                ]);
                if (empty($hr_job_relation)) {
                    $e++;
                    $excel_data[$k][] = $this->getTranslation()->_('11001');
                    continue;
                }

                //JD id
                $params['job_id'] = $job_id;
                if (empty($params['job_id']) || empty($jobListData[$params['job_id']])) {
                    $e++;
                    $excel_data[$k][] = $this->getTranslation()->_('11023');
                    continue;
                }

                //雇佣类型
                if ($hire_type) {
                    //1 正式员工 2 月薪制特殊合同工  3 日薪制特殊合同工 4 时薪制特殊合同工  5  实习生员工
                    $params['hire_type'] = intval(explode('|', $hire_type)[0]) ?? 0;  //雇佣类型
                    if ($params['hire_type'] == 0 || !in_array($params['hire_type'], [
                            HrStaffInfoModel::HIRE_TYPE_OFFICIAL,
                            HrStaffInfoModel::HIRE_TYPE_MONTH,
                            HrStaffInfoModel::HIRE_TYPE_DAY,
                            HrStaffInfoModel::HIRE_TYPE_HOUR,
                            HrStaffInfoModel::HIRE_TYPE_OTHER,
                            HrStaffInfoModel::HIRE_TYPE_AGENT,
                            HrStaffInfoModel::HIRE_TYPE_PART_TIME_AGENT,
                        ])) {
                        $e++;
                        $excel_data[$k][] = $this->getTranslation()->_('11002');
                        continue;
                    }
                } else {
                    $e++;
                    $excel_data[$k][] = $this->getTranslation()->_('11002');
                    continue;
                }
                //工作地点 id
                if (!empty($store_id) && !empty($worknodeData[$store_id]) && isset($hr_organization_department_store_relation_list[$store_id]) && $hr_organization_department_store_relation_list[$store_id]['department_id'] == $params['department_id']) {
                    $params['country_code'] = $worknodeData[$store_id]['country_code'];
                    $params['province_code'] = $worknodeData[$store_id]['province_code'];
                    $params['city_code'] = $worknodeData[$store_id]['city_code'];
                    $params['district_code'] = $worknodeData[$store_id]['district_code'];
                    $params['worknode_id'] = $worknodeData[$store_id]['id'];
                    $params['type'] = 1; //类型：1=快递员工，2=总部员工
                } else {
                    $e++;
                    $excel_data[$k][] = $this->getTranslation()->_('11003');
                    continue;
                }

                if (isCountry()) {
                    $params['hire_times'] = 0;
                    //月薪制特殊合同工/个人代理
                    if (in_array($hire_type, [HrStaffInfoModel::HIRE_TYPE_MONTH, HrStaffInfoModel::HIRE_TYPE_AGENT])) {
                        $params['hire_times'] = 12;
                    } elseif ($hire_type == HrStaffInfoModel::HIRE_TYPE_DAY) {
                        $params['hire_times'] = 365;
                    }
                } else {
                    $params['hire_times'] = intval($v[5]) ?? 0;
                }

                //截止有效时间
                if (!$expirationdate) {
                    $e++;
                    $excel_data[$k][] = $this->getTranslation()->_('11019');
                    continue;
                }

                if (strtotime(date('Y-m-d', strtotime($expirationdate))) ==  strtotime($expirationdate)){
                    $params['expirationdate'] = date('Y-m-d', strtotime($expirationdate));
                }else{
                    $n = intval(($expirationdate - 25569) * 3600 * 24); //转换成1970年以来的秒数
                    $params['expirationdate'] = gmdate('Y-m-d', $n);//格式化时间,不是用date哦, 时区相差8小时的

                    if (isCountry()) {
                        $excel_data[$k][5] = $params['expirationdate'];
                    } else {
                        $excel_data[$k][6] = $params['expirationdate'];
                    }
                }
                if (strtotime($params['expirationdate']) < time()) {
                    $e++;
                    $excel_data[$k][] = $this->getTranslation()->_('11024');
                    continue;
                }

                // 招聘优先级
                if (!$priority_id || !in_array($priority_id, ["P1", "P2", "P3", "P4"])) {
                    $e++;
                    $excel_data[$k][] = $this->getTranslation()->_('11004');
                    continue;
                }
                $params['priority_id'] = substr($priority_id, -1);

                //用人原因
                $params['reason_type'] = intval(explode('|', $reason_type)[0]) ?? 0;
                if (!$reason_type || !in_array($params['reason_type'], [1, 2, 3])) {
                    $e++;
                    $excel_data[$k][] = $this->getTranslation()->_('11005');
                    continue;
                }
                //需求人数
                if (!$demandnumber || !is_numeric($demandnumber) || intval($demandnumber) <= 0) {
                    $e++;
                    $excel_data[$k][] = $this->getTranslation()->_('11020');
                    continue;
                }
                $params['demandnumber'] = intval($demandnumber) ?? 0;
                //剩余人数
                $params['surplusnumber'] = $params['demandnumber'];
                // 获取预算
                $total = $server->getHrStaffing($params['department_id'], $store_id, $params['job_title'], $share_department_position);
                $leftTotal = $server->getSurplusHcRequestCount($total, $params['department_id'], $store_id, $params['job_title'], $share_department_position);
                $tmpNum = $server->getDepartmentStoreJobTitleHCTmpNum($params['department_id'], $store_id, $params['job_title']);
                if ($leftTotal < $params['demandnumber'] + $tmpNum) {
                    $e++;
                    $excel_data[$k][] = $this->getTranslation()->_('11006');
                    continue;
                }
                $this->getDI()->get('logger')->write_log("batchInsertHc data:" . json_encode(array_merge($params,['tmpNum'=>$tmpNum,'leftTotal'=>$leftTotal])), 'info');
                /******绑在一起*******/
                $insert[] = $params;
                $server->setDepartmentStoreJobTitleHCTmpNum($params['department_id'], $store_id, $params['job_title'],$params['demandnumber']);
                /******绑在一起*******/
                $s++;
            }
            $header = [
//            "所属部门ID",
                $this->getTranslation()->_('11007'),
//            "职位ID",
                $this->getTranslation()->_('11008'),
//            "JD ID",
                $this->getTranslation()->_('11009'),
//            "雇佣类型",
                $this->getTranslation()->_('11010'),
//            "工作地点ID",
                $this->getTranslation()->_('11011'),
//            "雇佣期间（月）",
                $this->getTranslation()->_('11012'),
//            "截止日期",
                $this->getTranslation()->_('11013'),
//            "招聘优先级",
                $this->getTranslation()->_('11014'),
//            "用人原因",
                $this->getTranslation()->_('11015'),
//            "需求人数",
                $this->getTranslation()->_('11016'),
//            "错误原因",
                $this->getTranslation()->_('11017'),
            ];

            if (isCountry()) {
                array_splice($header,5,1);
            }

            $fileName = !empty($file_name) ? $file_name : (new AsyncImportTaskServer())->getResultFileName(WinHrAsyncImportTaskModel::BATCH_ADD_HC);
            $file_path = Excel::exportExcel($header, $excel_data, $fileName);
            $fileUrl = $file_path ? $file_path['object_url'] : '';
            if ($insert) {
                $result = (new BaseRepository())->batch_insert("hr_hc", $insert);
                if ($result) {
                    $returnData["code"] = 1;
                    $returnData["msg"] = $this->getTranslation()->_('id_card_upload_success');
                }
            }
            $returnData["data"]["file_url"] = $fileUrl;
            $returnData["data"]["success_num"] = $s;
            $returnData["data"]["error_num"] = $e;
            return $returnData;
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log('batchInsertHc error: ' .
                'file ' . $e->getFile() .
                ' line ' . $e->getLine() .
                ' message ' . $e->getMessage() .
                ' trace ' . $e->getTraceAsString()
                , 'error');
            $returnData = [
                'code' => 0,
                'msg' => $this->getTranslation()->_('4008'),
                'data' => [],
            ];
            return $returnData;
        }

    }

    /**
     * 查询在职、待入职、hc剩余人数
     * @param $departmentId
     * @param $storeId
     * @param $jobTitle
     * @return array
     */
    public function getHcBudgetCount($departmentId, $storeId, $jobTitle, $share_department_position): array
    {
        if (empty($departmentId) || empty($jobTitle)) {
            return 0;
        }
        //如果是共享部门
        if (isset($share_department_position[$departmentId])) {
            $share_position_arr = $share_department_position[$departmentId] ?? [];
            //如果是共享职位
            if (!empty($share_position_arr) && in_array($jobTitle, $share_position_arr)) {
                $jobTitle = $share_position_arr;
            } else {
                $jobTitle = [$jobTitle];
            }
        } else {
            $jobTitle = [$jobTitle];
        }
        //获取在职人数
        $onJobCnt = $this->getOnJobStaffCount($departmentId, $storeId, $jobTitle, $share_department_position);

        //获取待入职人数
        $pendingEntryCnt = $this->getPendingEntryCount($departmentId, $storeId, $jobTitle, $share_department_position);

        //获取剩余人数  相当于招聘中的人数
        $surplusCnt = $this->getSurplusCount($departmentId, $storeId, $jobTitle, $share_department_position);

        //4.获取已提交的HC总人数（待审批）
        $pendingCon = $this->getBudgetAdoptCount($departmentId, $storeId, $jobTitle, $share_department_position);

        //当月预算人数
        //$curMonthBudgetCnt = $this->getCurrentMonthBudgetCount($month, $departmentId, $storeId, $jobTitle);

        //当月计划人数
        //当月计划人数 = 新增招聘人数+在职人数+待入职人数+招聘中人数
        //$budget = $this->getHrStaffing($month, $departmentId, $storeId, $jobTitle);

        //当月已提交的新增招聘人数
        //$adoptCon = $this->getCurrentMothBudgetAdoptCount($month, $departmentId, $storeId, $jobTitle);

        return [
            'on_job' => $onJobCnt,//在职人数
            'pending_entry' => $pendingEntryCnt,//待入职人数
            'surplus' => $surplusCnt,//招聘中的人数
            'pendingCon' => $pendingCon, //已提交待审批
//            'cur_month_budget' => $curMonthBudgetCnt,
//            'cur_month_planed' => $adoptCon + $onJobCnt + $pendingEntryCnt + $surplusCnt,
//            'adopt_count' => $adoptCon
        ];
    }

    /**
     * 获取在职人数
     * @param $departmentId
     * @param $storeId
     * @param $jobTitle
     * @return int
     */
    public function getOnJobStaffCount($departmentId, $storeId, $jobTitle, $share_department_position): int
    {
        if (empty($departmentId) || empty($jobTitle)) {
            return 0;
        }

        $conditions = 'node_department_id = :node_department_id:
                              and job_title in ({job_title_id:array}) and
                             formal in (1,4) and state = 1 and is_sub_staff = 0 and wait_leave_state = 0';
        $bind = [
            'node_department_id' => $departmentId,
            'job_title_id' => $jobTitle,
        ];

        $now_store_id = '';
        if (!empty($storeId) && !isset($share_department_position[$departmentId])) {
            $conditions .= ' and sys_store_id = :store_id:';
            $bind['store_id'] = $storeId;
            $now_store_id = $storeId;
        }
        //获取指定部门、网点、职位的，在职、在编、非子账号的人数
        //v8919+不要待离职的+实习生
        $onJobCou = HrStaffInfoModel::find([
            'conditions' => $conditions,
            'bind' => $bind,
            'columns' => "staff_info_id",
        ])->count();


        $transfer_num = $this->getJobTransferCount($departmentId, $now_store_id, $jobTitle);
        //在职 - 待转岗人数
        $res = $onJobCou - $transfer_num;
        if ($res < 0) {
            $res = 0;
        }
        return $res;
    }

    /**
     * 获取待入职人数
     * @param $departmentId
     * @param $storeId
     * @param $jobTitle
     * @return int
     */
    public function getPendingEntryCount($departmentId, $storeId, $jobTitle, $share_department_position): int
    {
        if (empty($departmentId) || empty($jobTitle)) {
            return 0;
        }

        $builder = $this->modelsManager->createBuilder();
        $builder->columns("count(1) as cou");
        $builder->from(['h' => HrHcModel::class]);
        $builder->leftJoin(HrEntryModel::class,'e.hc_id = h.hc_id','e');
        $builder->andWhere('e.deleted = :deleted:', ['deleted' => enums::IS_DELETED_NO]);
        $builder->andWhere('e.status = :status:', ['status' => HrEntryModel::STATUS_TO_BE_EMPLOYED]);
        $builder->andWhere('h.department_id = :dept_id:', ['dept_id' => $departmentId]);
        $builder->inWhere('h.job_title', $jobTitle);
        if (!empty($storeId) && !isset($share_department_position[$departmentId])) {
            $builder->andWhere('h.worknode_id = :store_id:', ['store_id' => $storeId]);
        }
        $count = $builder->getQuery()->execute()->getFirst();

        return intval($count->cou);
    }

    /**
     * 获取招聘中人数
     * @param $departmentId
     * @param $storeId
     * @param $jobTitle
     * @return int
     */
    public function getSurplusCount($departmentId, $storeId, $jobTitle, $share_department_position): int
    {
        if (empty($departmentId) || empty($jobTitle)) {
            return 0;
        }
        $conditions = 'state_code = 2 and department_id = :department_id: and
                            job_title in ({job_title_id:array})';
        $bind = [
            'department_id' => $departmentId,
            'job_title_id' => $jobTitle,
        ];
        if (!empty($storeId) && !isset($share_department_position[$departmentId])) {
            $conditions .= ' and worknode_id = :store_id:';
            $bind['store_id'] = $storeId;
        }
        $count = HrHcModel::findFirst([
            'conditions' => $conditions,
            'bind' => $bind,
            'columns' => "sum(surplusnumber) as cou",
        ])->toArray();

        return $count['cou'] ?? 0;
    }

    /**
     * @desc 获取已提交的HC总人数（待审批）
     * @param $departmentId
     * @param $storeId
     * @param $jobTitle
     * @return int
     */
    public function getBudgetAdoptCount($departmentId, $storeId, $jobTitle, $share_department_position)
    {
        //approval_state_code = 7  state_code = 1
        if (empty($departmentId) || empty($jobTitle)) {
            return 0;
        }
        $conditions = 'state_code = 1 and approval_state_code = 7 and department_id = :department_id: and
                            job_title in ({job_title_id:array})';
        $bind = [
            'department_id' => $departmentId,
            'job_title_id' => $jobTitle,
        ];


        if (!empty($storeId) && !isset($share_department_position[$departmentId])) {
            $conditions .= ' and worknode_id = :store_id:';
            $bind['store_id'] = $storeId;
        }
        $count = HrhcModel::findFirst([
            'conditions' => $conditions,
            'bind' => $bind,
            'columns' => "sum(demandnumber) as cou",
        ])->toArray();

        return $count['cou'] ?? 0;
    }

    /**
     * 获得待转岗人数
     * @param $department_id
     * @param $store_id
     * @param $jobTitleIds  array  这是个数组，放ids
     * @return int
     */

    public function getJobTransferCount($department_id, $store_id, $jobTitleIds)
    {
        //审核通过，待转岗
        $conditions = 'approval_state =2 and state=1 and current_department_id = :department_id: and current_position_id and current_position_id in ({job_title_id:array}) ';
        $bind = [];
        $bind['department_id'] = $department_id;
        $bind['job_title_id'] = $jobTitleIds;

        if (!empty($store_id)) {
            $conditions .= ' and current_store_id = :store_id:';
            $bind['store_id'] = $store_id;
        }

        return JobTransferModel::count(
            [
                'conditions' => $conditions,
                'bind' => $bind,
            ]
        );
    }

    /**
     * 获取预算人数
     * @param $departmentId
     * @param $storeId
     * @param $jobTitle
     * @return int
     */
    public function getHrStaffing($departmentId, $storeId, $jobTitle, $share_department_position): int
    {

        $this->getDI()->get('logger')->write_log('getHrStaffing: params - departmentId:' . $departmentId . '-storeId:' . $storeId . '-jobTitle:' . $jobTitle, 'info');

        if (empty($departmentId) || empty($jobTitle)) {
            return 0;
        }

        //获取全部的部门、职位、网点
        $builder = $this->modelsManager->createBuilder();

        $builder->columns("count");
        $builder->from(HRStaffingModel::class);
        $builder->andWhere('dept_id = :dept_id:', ['dept_id' => $departmentId]);
        if (isset($share_department_position[$departmentId])) {

            $share_position_arr = $share_department_position[$departmentId] ?? [];
            if (!empty($share_position_arr) && in_array($jobTitle, $share_position_arr)) {
                $builder->inWhere('job_title_id', $share_position_arr);
            } else {
                $builder->andWhere('job_title_id = :job_title_id:', ['job_title_id' => $jobTitle]);
            }
        } else {
            if (!empty($storeId)) {
                $builder->andWhere('store_id = :store_id:', ['store_id' => $storeId]);
            }
            $builder->andWhere('job_title_id = :job_title_id:', ['job_title_id' => $jobTitle]);
        }

        //$builder->groupBy("dept_id,job_title_id,store_id");
        $builder->andWhere('count > 0');
        $builder->orderBy('id desc');
        $builder->limit(1);
        $list = $builder->getQuery()->execute()->getFirst();
        $count = 0;
        if (!empty($list)) {
            $listArr = $list->toArray();
            $count = $listArr['count'] ?? 0;
        }

        $this->getDI()->get('logger')->write_log('getHrStaffing: params - departmentId:' . $departmentId . ' share_department_position:' . json_encode($this->share_department_position ?? [], JSON_UNESCAPED_UNICODE) . ' storeId:' . $storeId . '-jobTitle:' . $jobTitle . ' count:' . $count, 'info');

        return $count;
    }


    /**
     * todo 批量新增修改HC 预算剩余数量同步修改点[1]
     * 临时数量汇总
     * @param $departmentId
     * @param $storeId
     * @param $jobTitleId
     * @return int|mixed
     */
    public function getDepartmentStoreJobTitleHCTmpNum($departmentId, $storeId, $jobTitleId)
    {
        $department_store_job_tile_key = sprintf('%s-%s-%s', $departmentId, $storeId, $jobTitleId);
        return HcServer::$department_store_job_title_hc_num[$department_store_job_tile_key] ?? 0;
    }

    public function setDepartmentStoreJobTitleHCTmpNum($departmentId, $storeId, $jobTitleId, $demandnumber): bool
    {
        $department_store_job_tile_key = sprintf('%s-%s-%s', $departmentId, $storeId, $jobTitleId);
        HcServer::$department_store_job_title_hc_num[$department_store_job_tile_key] += $demandnumber;
        return true;
    }


    /**
     * todo 批量新增修改HC 预算剩余数量同步修改点[2]
     * @desc 获取HC预算剩余数量
     * @param $total
     * @param $departmentId
     * @param $storeId
     * @param $jobTitleId
     * * @return mixed
     */
    public function getSurplusHcRequestCount($total, $departmentId, $storeId, $jobTitleId, $share_department_position)
    {
        if ($total <= 0 || empty($departmentId) || empty($jobTitleId)) {
            return 0;
        }
        if (isset($share_department_position[$departmentId])) {
            $share_position_arr = $share_department_position[$departmentId];
            if (!empty($share_position_arr) && in_array($jobTitleId, $share_position_arr)) {
                $jobTitle = $share_position_arr;
            } else {
                $jobTitle = [$jobTitleId];
            }
        } else {
            $jobTitle = [$jobTitleId];
        }
        //HC预算剩余数量:预算人数 - 在职人数 - 待入职人数 - 招聘中人数 - 已提交HC总人数(待审批)
        //1.获取在职人数
        $onJobCnt = $this->getOnJobStaffCount($departmentId, $storeId, $jobTitle, $share_department_position);
        //2.获取待入职人数
        $pendingEntryCnt = $this->getPendingEntryCount($departmentId, $storeId, $jobTitle, $share_department_position);
        //3.获取招聘中人数
        $surplusCnt = $this->getSurplusCount($departmentId, $storeId, $jobTitle, $share_department_position);
        //4.获取已提交的HC总人数（待审批）
        $adoptCon = $this->getBudgetAdoptCount($departmentId, $storeId, $jobTitle, $share_department_position);

        //HC预算剩余数量 100000-37-11-173
        $leftCount = $total - $onJobCnt - $pendingEntryCnt - $surplusCnt - $adoptCon;

        $this->logger->write_log(sprintf("getSurplusHcRequestCount %s %s %s: onJobCnt: %s,pendingEntryCnt: %s,surplusCnt: %s,adoptCon:%s,leftCount:%s.",
            $departmentId, $storeId, $jobTitleId, $onJobCnt, $pendingEntryCnt, $surplusCnt, $adoptCon, $leftCount), 'info');
        return $leftCount;
    }

    /**
     * @description: 获取 hc 日志
     * @param null
     * @return:
     * @author: L.J
     * @time: 2023/2/13 10:11
     */
    public function getHcLog($paramIn = [])
    {
        $hc_id    = $paramIn['hc_id'] ?? '';
        $pageSize = isset($paramIn['page_size']) ? $paramIn['page_size'] : 100;
        $pageNum  = isset($paramIn['page_num']) ? $paramIn['page_num'] : 1;

        $builder = $this->modelsManager->createBuilder();

        $builder->from(['log' => HrHcLogModel::class]);
        $builder->leftjoin(HrStaffInfoModel::class, 'log.staff_info_id=staff.staff_info_id', 'staff');
        $builder->andWhere('log.hc_id = :hc_id: and log.type != :type:',
            ['hc_id' => $hc_id, 'type' => HrHcLogModel::TYPE_DEFAULT]);

        $builder->columns('count(1) as count');
        $count                      = $builder->getQuery()->getSingleResult()->toArray();
        $returnArr['data']['count'] = $count['count'] ?? 0;
        $returnArr['data']['dataList']  = [];

        $builder->limit($pageSize, ($pageNum - 1) * $pageSize);
        $builder->orderBy('log.id desc');
        $builder->columns('log.id,log.created_at, log.before_data, log.after_data,log.type,log.hc_id,staff.staff_info_id,staff.name');

        $list     = $builder->getQuery()->execute()->toArray();
        $add_hour = $this->getDI()['config']['application']['add_hour'];

        foreach ($list as &$v) {
            $before_data = json_decode($v['before_data'], true);
            $after_data  = json_decode($v['after_data'], true);

            $date      = date('Y-m-d H:i:s', strtotime($v['created_at']) + $add_hour * 3600);
            $v['info'] = '';
            switch ($v['type']) {
                case HrHcLogModel::TYPE_PRIORITY_ID :
                    //年月日时分秒+姓名工号+HCID+修改前的优先级+修改后的优先级   ：%date%   %name%（%staff_info_id%）修改HCID：%hc_id%优先级P%before_priority_id%为P%after_priority_id%
                    $v['info'] = self::$t->_('hc_log_type_priority_id', [
                        'date'               => $date,
                        'staff_info_id'      => $v['staff_info_id'],
                        'name'               => $v['name'],
                        'hc_id'              => $v['hc_id'],
                        'before_priority_id' => $before_data['priority_id'],
                        'after_priority_id'  => $after_data['priority_id'],
                    ]);
                    break;
                case HrHcLogModel::TYPE_STATE_CODE_ACTIVATION :
                    //%date%  %name%（%staff_info_id%）激活HCID：%hc_id%
                    $v['info'] = self::$t->_('hc_log_type_state_code_activation',
                        [
                            'date'          => $date,
                            'staff_info_id' => $v['staff_info_id'],
                            'name'          => $v['name'],
                            'hc_id'         => $v['hc_id'],
                        ]);
                    break;
                case HrHcLogModel::TYPE_STATE_CODE_VOID :
                    //%date%  %name%（%staff_info_id%）作废HCID：%hc_id%
                    $v['info'] = self::$t->_('hc_log_type_state_code_void',
                        [
                            'date'          => $date,
                            'staff_info_id' => $v['staff_info_id'],
                            'name'          => $v['name'],
                            'hc_id'         => $v['hc_id'],
                        ]);
                    break;
                case HrHcLogModel::TYPE_EXPIRATIONDATE :
                    $before_expirationdate = !empty($before_data['expirationdate']) ? date('Y-m-d',
                        strtotime($before_data['expirationdate'])) : '';
                    $after_expirationdate  = !empty($after_data['expirationdate']) ? date('Y-m-d',
                        strtotime($after_data['expirationdate'])) : '';
                    //%date%  %name%（%staff_info_id%）修改HCID：%hc_id% 截止有效时间 %before_expirationdate% 为 %after_expirationdate%
                    $v['info'] = self::$t->_('hc_log_type_expirationdate', [
                        'date'                  => $date,
                        'staff_info_id'         => $v['staff_info_id'],
                        'name'                  => $v['name'],
                        'hc_id'                 => $v['hc_id'],
                        'before_expirationdate' => $before_expirationdate,
                        'after_expirationdate'  => $after_expirationdate,
                    ]);
                    break;
                case HrHcLogModel::TYPE_DEMANDNUMBER :
                    //%date%  %name%（%staff_info_id%）修改HCID：%hc_id%  需求人数 %before_demandnumber% 为 %after_demandnumber%
                    $v['info'] = self::$t->_('hc_log_type_demandnumber', [
                        'date'                => $date,
                        'staff_info_id'       => $v['staff_info_id'],
                        'name'                => $v['name'],
                        'hc_id'               => $v['hc_id'],
                        'before_demandnumber' => $before_data['demandnumber'],
                        'after_demandnumber'  => $after_data['demandnumber'],
                    ]);
                    break;
            }
            $returnArr['data']['dataList'][] = [
                'info' => $v['info'],
                'id'   => $v['id'],
            ];
        }
        return $this->checkReturn($returnArr);
    }

    /**
     * 获取语言翻译
     */
    public function getLanguageAbilityText($languageKeys)
    {
        if (empty($languageKeys)) {
            return '';
        }

        $languageKeys = explode(',', $languageKeys);

        $data = [];

        foreach (enums::$languageAbility as $k => $v) {
            if (in_array($k,$languageKeys)) {
                $data[] = $v;
            }
        }

        return implode(', ',$data);
    }

    /**
     * 我的HC 导入结果列表
     * @param $params
     * @return mixed
     */
    public function importList($params)
    {
        $params['import_types'] = [
            WinHrAsyncImportTaskModel::BATCH_ADD_HC,
            WinHrAsyncImportTaskModel::BATCH_VOID_HC,
            WinHrAsyncImportTaskModel::BATCH_EDIT_DEMAND_NUM_HC,
            WinHrAsyncImportTaskModel::BATCH_EDIT_PRIORITY_NUM_HC,
        ];
        return $this->importListQuery($params);
    }

    /**
     * 我的HC审批 导入结果列表
     * @param $params
     * @return mixed
     */
    public function importAuditList($params)
    {
        $params['import_types'] = [WinHrAsyncImportTaskModel::BATCH_AUDIT_HC];

        $data = $this->importListQuery($params);
        $returnData = [
            'dataList'   => $data['list'],
            'pagination' => [
                'count'     => $data['total'],
                'pageCount' => ceil($data['total'] / intval($params['page_size'])),
                'pageNum'   => intval($params['page_num']),
                'pageSize'  => intval($params['page_size']),
            ],
        ];
        return $returnData;
    }

    /**
     * 导入结果列表
     * @param $params
     * @return mixed
     */
    public function importListQuery($params)
    {
        $params['page_num'] = empty($params['page_num']) ? 1 : $params['page_num'];
        $params['page_size'] = empty($params['page_size']) ? 20 : $params['page_size'];
        $params['page_size'] = ($params['page_size'] > 100) ? 100 : $params['page_size'];

        $total = $this->getImportListQuery($params, true);
        $list  = $this->getImportListQuery($params);
        if ($list) {
            $list = $this->formatList($list);
        }

        $data['total'] = !empty($total) ? intval($total['count']) : 0;
        $data['list']  = $list;
        return $data;
    }

    /**
     * 获取数据
     * @param $params
     * @param bool $isCount
     * @return mixed
     */
    public function getImportListQuery($params, $isCount = false)
    {
        $builder = $this->modelsManager->createBuilder();
        if ($isCount) {
            $builder->columns('count(*) as count');
        }
        $builder->from(['ait' => WinHrAsyncImportTaskModel::class]);

        $builder = $this->getBuilderWhere($builder, $params);
        if ($isCount) {
            return $builder->getQuery()->getSingleResult()->toArray();
        }
        $builder->columns([
            'ait.id',
            'ait.result_file_name as name',
            'ait.operator_id',
            'ait.import_type',
            'ait.result_path as file_url',
            'ait.status',
            'ait.success_number',
            'ait.fail_number',
            "DATE_FORMAT(CONVERT_TZ(ait.created_at, '+00:00', '{$this->timezone}'), '%Y-%m-%d %H:%i:%s') as created_at",
        ]);

        $builder->limit($params['page_size'], $params['page_size'] * ($params['page_num'] - 1));
        
        $builder->orderBy('ait.id desc');
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 格式化数据
     * @param $list
     * @return mixed
     */
    public function formatList($list)
    {
        foreach ($list as &$oneData) {
            $oneData['status_text']    = WinHrAsyncImportTaskModel::STATE_WAIT_EXECUTE == $oneData['status'] ? $this->getTranslation()->_('download_status_name_0') : $this->getTranslation()->_('download_status_name_1');
            $oneData['fail_number']    = intval($oneData['fail_number']);
            $oneData['success_number'] = intval($oneData['success_number']);
        }

        return $list;
    }

    /**
     * 查询条件
     * @param $builder
     * @param $params
     * @return mixed
     */
    public function getBuilderWhere($builder, $params)
    {
        $builder->where("ait.is_deleted = :is_deleted:", ['is_deleted' => WinHrAsyncImportTaskModel::IS_NOT_DELETED]);

        if (!empty($params['staff_id'])) {
            $builder->andWhere("ait.operator_id = :operator_id:", ['operator_id' => $params['staff_id']]);
        }

        if (!empty($params['import_types'])) {
            $builder->andWhere("ait.import_type in ({import_types:array})",
                ['import_types' => $params['import_types']]);
        }

        return $builder;
    }

    /**
     * 删除任务
     * @param $params
     * @return mixed
     * @throws ValidationException
     */
    public function deleteImportTask($params)
    {
        if (empty($params['id'])) {
            throw new ValidationException($this->getTranslation()->_('8402'));
        }
        $data = WinHrAsyncImportTaskRepository::getInfo($params, false);

        if (empty($data)) {
            throw new ValidationException($this->getTranslation()->_('8402'));
        }

        $result = $this->getDI()->get('db')->updateAsDict(
            'winhr_async_import_task',
            [
                'operator_id' => $params['staff_id'],
                'is_deleted' => WinHrAsyncImportTaskModel::IS_DELETED,
            ],
            'id = ' . $params['id']
        );

        return $result;
    }

    /**
     * @return bool
     */
    public function handleDiscrepancyHc(): bool
    {
        if (!isCountry('TH')){
            return false;
        }
        $setting = (new SettingEnvServer())->getMultiEnvByCodeToArray([
            'daily_special_contract_employees_job',
            'daily_special_contract_employees_branch',
            'monthly_special_contract_employees_job',
            'hc_hire_type_incorrect_email',
        ]);
        // 招聘中
        $hc_data_recruiting = HrhcModel::find([
            'conditions' => 'state_code = :state_code: and deleted = :is_deleted: and reason_type IN ({reason_type:array})',
            'bind'       => [
                'state_code'  => HrhcModel::STATE_RECRUITING,
                'is_deleted'  => HrhcModel::IS_DELETED_NO,
                'reason_type' => [HrhcModel::REASON_TYPE_RECRUIT, HrhcModel::REASON_TYPE_RESIGNATION],
            ],
        ])->toArray();

        // 已招满但存在待入职的简历
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['hc' => HrHcModel::class]);
        $builder->leftJoin(HrEntryModel::class, 'entry.hc_id = hc.hc_id', 'entry');
        $builder->where('hc.state_code = :state_code: and hc.deleted = :is_deleted: and hc.reason_type IN ({reason_type:array}) and entry.status = :status: and entry.deleted = :deleted:',
            [
                'state_code'  => HrhcModel::STATE_FULL_RECRUITMENT,
                'is_deleted'  => HrhcModel::IS_DELETED_NO,
                'reason_type' => [HrhcModel::REASON_TYPE_RECRUIT, HrhcModel::REASON_TYPE_RESIGNATION],
                'status'      => HrEntryModel::STATUS_TO_BE_EMPLOYED,
                'deleted'     => enums::IS_DELETED_NO,
            ]);
        $hc_data_full_recruitment = $builder->getQuery()->execute()->toArray();
        $hc_data                  = array_values(array_column(array_merge($hc_data_recruiting, $hc_data_full_recruitment),null, 'hc_id'));
        if (empty($hc_data)) {
            $this->getDI()->get('logger')->write_log("handleDiscrepancyHc 无数据", 'info');
            return false;
        }
        $db = $this->getDI()->get('db');
        $db->begin();
        try {
            foreach ($hc_data as $v) {
                if (
                    in_array($v['hire_type'],
                        [
                            HrhcModel::HIRE_TYPE_SPECIAL_CONTRACT_MONTHLY_WORKERS,
                            HrhcModel::HIRE_TYPE_PERMANENT_EMPLOYEE,
                        ]) &&
                    !empty($setting['daily_special_contract_employees_job']) &&
                    is_array($setting['daily_special_contract_employees_job']) &&
                    !empty($v['job_title']) &&
                    in_array($v['job_title'], $setting['daily_special_contract_employees_job']) &&
                    !empty($setting['daily_special_contract_employees_branch']) &&
                    is_array($setting['daily_special_contract_employees_branch']) &&
                    !empty($v['worknode_id']) &&
                    in_array($v['worknode_id'], $setting['daily_special_contract_employees_branch'])
                ) {
                    $this->handleHcLogic($v, HrhcModel::HIRE_TYPE_SPECIAL_CONTRACT_WEEKLY_WORKERS, $db);
                }
                if (
                    $v['hire_type'] == HrhcModel::HIRE_TYPE_SPECIAL_CONTRACT_WEEKLY_WORKERS &&
                    (
                        (
                            !empty($setting['daily_special_contract_employees_job']) && 
                            is_array($setting['daily_special_contract_employees_job']) &&
                            !empty($v['job_title']) &&
                            !in_array($v['job_title'], $setting['daily_special_contract_employees_job'])
                        ) or 
                        (
                            !empty($setting['daily_special_contract_employees_branch']) && 
                            is_array($setting['daily_special_contract_employees_branch']) &&
                            !empty($v['worknode_id']) &&
                            !in_array($v['worknode_id'], $setting['daily_special_contract_employees_branch'])
                        )
                    ) &&
                    !empty($setting['monthly_special_contract_employees_job']) && 
                    is_array($setting['monthly_special_contract_employees_job']) &&
                    in_array($v['job_title'], $setting['monthly_special_contract_employees_job'])
                ) {
                    $this->handleHcLogic($v, HrhcModel::HIRE_TYPE_SPECIAL_CONTRACT_MONTHLY_WORKERS, $db);
                }
                if (
                    $v['hire_type'] == HrhcModel::HIRE_TYPE_SPECIAL_CONTRACT_WEEKLY_WORKERS &&
                    (
                        (
                            !empty($setting['daily_special_contract_employees_job']) && 
                            is_array($setting['daily_special_contract_employees_job']) &&
                            !empty($v['job_title']) &&
                            !in_array($v['job_title'], $setting['daily_special_contract_employees_job'])
                        ) or 
                        (
                            !empty($setting['daily_special_contract_employees_branch']) && 
                            is_array($setting['daily_special_contract_employees_branch']) &&
                            !empty($v['worknode_id']) &&
                            !in_array($v['worknode_id'], $setting['daily_special_contract_employees_branch'])
                        )
                    ) &&
                    !empty($setting['monthly_special_contract_employees_job']) && 
                    is_array($setting['monthly_special_contract_employees_job']) &&
                    !in_array($v['job_title'], $setting['monthly_special_contract_employees_job'])
                ) {
                    $this->handleHcLogic($v, HrhcModel::HIRE_TYPE_PERMANENT_EMPLOYEE, $db);
                }
            }
            $re = $this->handleHcSendEmail($setting['hc_hire_type_incorrect_email'] ?? [],$db);
            if ($re !== true) {
                throw new \Exception('handleHcSendEmail失败');
            }
            $db->commit();
        } catch (BusinessException|\Exception $e) {
            $db->rollback();
            $this->getDI()->get('logger')->write_log(["handleDiscrepancyHcAction 错误原因:" => $e->getMessage()],
                'error');
            return false;
        }
        return true;
    }
    
    /**
     * @param $hc_info
     * @param $hire_type
     * @param $db
     * @return true
     * @throws Exception
     */
    public function handleHcLogic($hc_info, $hire_type, $db): bool
    {
        $updateParamIn = [];
        $new_hc_data   = [];
        // 已入职简历数
        $entry_employed_count = HrEntryModel::count([
            'conditions' => 'deleted = :deleted: and status = :status: and hc_id = :hc_id:',
            'bind'       => [
                'deleted' => enums::IS_DELETED_NO,
                'status'  => HrEntryModel::STATUS_EMPLOYED,
                'hc_id'   => $hc_info['hc_id'],
            ],
        ]);
        if (empty($entry_employed_count)) {
            if ($hire_type == HrhcModel::HIRE_TYPE_SPECIAL_CONTRACT_MONTHLY_WORKERS) {
                $_hire_type  = HrhcModel::HIRE_TYPE_SPECIAL_CONTRACT_MONTHLY_WORKERS;
                $_hire_times = 12;
            } elseif ($hire_type == HrhcModel::HIRE_TYPE_SPECIAL_CONTRACT_WEEKLY_WORKERS) {
                $_hire_type  = HrhcModel::HIRE_TYPE_SPECIAL_CONTRACT_WEEKLY_WORKERS;
                $_hire_times = 365;
            } else {
                $_hire_type  = 1;
                $_hire_times = 0;
            }
            $new_hc_data = $this->copyHc([
                'demandnumber'             => $hc_info['demandnumber'],
                'surplusnumber'            => $hc_info['demandnumber'],
                'submitter_id'             => 10000,
                'state_code'               => HrhcModel::STATE_RECRUITING,
                'approval_state_code'      => 6,
                'approval_completion_time' => gmdate('Y-m-d H:i:s', time()),
                'hire_type'                => $_hire_type,
                'hire_times'               => $_hire_times,
            ], $hc_info,$db);
            $updateParamIn = [
                'state_code' => HrhcModel::STATE_VOIDED,
                'task_new_hc_id' => $new_hc_data['hc_id'],
            ];
        } elseif ($entry_employed_count > 0) {
            if ($hc_info['demandnumber'] - $entry_employed_count > 0) {
                if ($hire_type == HrhcModel::HIRE_TYPE_SPECIAL_CONTRACT_MONTHLY_WORKERS) {
                    $_hire_type  = HrhcModel::HIRE_TYPE_SPECIAL_CONTRACT_MONTHLY_WORKERS;
                    $_hire_times = 12;
                } elseif ($hire_type == HrhcModel::HIRE_TYPE_SPECIAL_CONTRACT_WEEKLY_WORKERS) {
                    $_hire_type  = HrhcModel::HIRE_TYPE_SPECIAL_CONTRACT_WEEKLY_WORKERS;
                    $_hire_times = 365;
                } else {
                    $_hire_type  = 1;
                    $_hire_times = 0;
                }
                $new_hc_data = $this->copyHc([
                    'demandnumber'             => $hc_info['demandnumber'] - $entry_employed_count,
                    'surplusnumber'            => $hc_info['demandnumber'] - $entry_employed_count,
                    'submitter_id'             => 10000,
                    'state_code'               => HrhcModel::STATE_RECRUITING,
                    'approval_state_code'      => 6,
                    'approval_completion_time' => gmdate('Y-m-d H:i:s', time()),
                    'hire_type'                => $_hire_type,
                    'hire_times'               => $_hire_times,
                ], $hc_info,$db);
                $updateParamIn = [
                    'state_code'    => HrhcModel::STATE_FULL_RECRUITMENT,
                    'demandnumber'  => $entry_employed_count,
                    'surplusnumber' => 0,
                    'task_new_hc_id' => $new_hc_data['hc_id'],
                ];
            }
        }
        if (!empty($updateParamIn)) {
            $log_result = (new HcRepository())->addHrhcSaveLog($hc_info['hc_id'], '10000', $updateParamIn);
            if (empty($log_result)) {
                throw new \Exception('hc日志记录失败 hc_id:' . $hc_info['hc_id']);
            }
            $db->updateAsDict(
                'hr_hc',
                $updateParamIn,
                'hc_id = ' . $hc_info['hc_id']
            );
            if (!empty($new_hc_data)) {
                $this->handleDiscrepancyHcTask[$hc_info['hc_id']]['new_hc_id']     = $new_hc_data['hc_id'];
                $this->handleDiscrepancyHcTask[$hc_info['hc_id']]['new_hire_type'] = $new_hc_data['hire_type'];
                $this->handleDiscrepancyHcTask[$hc_info['hc_id']]['hire_type']     = $hc_info['hire_type'];
            }
        }
        return true;
    }
    
    /**
     * @param $params
     * @param $old_hc_data
     * @param $db
     * @return array
     */
    public function copyHc($params, $old_hc_data, $db): array
    {
        if (empty($params) || empty($old_hc_data)) {
            return [];
        }
        $insetData['serial_no']                = $old_hc_data['serial_no'];
        $insetData['job_id']                   = $old_hc_data['job_id'];
        $insetData['department_id']            = $old_hc_data['department_id'];
        $insetData['type']                     = $old_hc_data['type'];
        $insetData['worknode_id']              = $old_hc_data['worknode_id'];
        $insetData['expirationdate']           = $old_hc_data['expirationdate'];
        $insetData['demandnumber']             = $params['demandnumber'];
        $insetData['surplusnumber']            = $params['surplusnumber'];
        $insetData['interviewer']              = $old_hc_data['interviewer'];
        $insetData['remarks']                  = $old_hc_data['remarks'];
        $insetData['reason']                   = $old_hc_data['reason'];
        $insetData['submitter_id']             = $params['submitter_id'];
        $insetData['country_code']             = $old_hc_data['country_code'];
        $insetData['province_code']            = $old_hc_data['province_code'];
        $insetData['city_code']                = $old_hc_data['city_code'];
        $insetData['district_code']            = $old_hc_data['district_code'];
        $insetData['state_code']               = $params['state_code'];
        $insetData['priority_id']              = $old_hc_data['priority_id'];
        $insetData['approval_stage']           = $old_hc_data['approval_stage'];
        $insetData['approval_state_code']      = $params['approval_state_code'];
        $insetData['workflow_role']            = $old_hc_data['workflow_role'];
        $insetData['approval_completion_time'] = $params['approval_completion_time'];
        $insetData['reason_type']              = $old_hc_data['reason_type'];
        $insetData['hire_type']                = $params['hire_type'];
        $insetData['hire_times']               = $params['hire_times'];
        $insetData['deleted']                  = $old_hc_data['deleted'];
        $insetData['job_title']                = $old_hc_data['job_title'];
        $insetData['request_info']             = $old_hc_data['request_info'];
        $insetData['manager_staff_id']         = $old_hc_data['manager_staff_id'];
        $insetData['working_day_rest_type']    = $old_hc_data['working_day_rest_type'];
        $insetData['language_ability']         = $old_hc_data['language_ability'];
        $insetData['dco_courier_ratio']        = $old_hc_data['dco_courier_ratio'];
        $insetData['store_data']               = $old_hc_data['store_data'];
        $insetData['createtime']               = gmdate('Y-m-d H:i:s', time());
        $insetData['updated_at']               = gmdate('Y-m-d H:i:s', time());
        $success                               = $db->insertAsDict('hr_hc', $insetData);
        if (!$success) {
            return [];
        }
        $hc_id = $db->lastInsertId();
        return ['hire_type' => $params['hire_type'], 'hc_id' => $hc_id];
    }

    /**
     * @param $hc_hire_type_incorrect_email
     * @param $db
     * @return bool
     * @throws BusinessException
     * @throws Exception
     */
    public function handleHcSendEmail($hc_hire_type_incorrect_email,$db): bool
    {
        if (empty($this->handleDiscrepancyHcTask)) {
            throw new \Exception('handleHcSendEmail失败 参数有空值');
        }
        $old_hc_ids       = array_keys($this->handleDiscrepancyHcTask);
        $entry_data       = HrEntryModel::find([
            'conditions' => 'deleted = :deleted: and hc_id in ({hc_ids:array}) and status = :status:',
            'bind'       => [
                'deleted' => enums::IS_DELETED_NO,
                'hc_ids'  => $old_hc_ids,
                'status'  => HrEntryModel::STATUS_EMPLOYED,
            ],
        ])->toArray();
        $entry_resume_ids = array_column($entry_data, 'resume_id');
        $conditions       = 'deleted = :deleted: and hc_id in ({hc_ids:array})';
        $bind             = [
            'deleted' => HrResumeModel::IS_DELETE_NO,
            'hc_ids'  => $old_hc_ids,
        ];
        if (!empty($entry_data)) {
            $conditions         .= ' and id not in ({resume_ids:array})';
            $bind['resume_ids'] = $entry_resume_ids;
        }
        $resume_data = HrResumeModel::find([
            'conditions' => $conditions,
            'bind'       => $bind,
        ])->toArray();
        if (empty($resume_data)) {
            $this->getDI()->get('logger')->write_log(["handleHcSendEmail 无数据" => $hc_hire_type_incorrect_email],
                'info');
            return true;
        }

        $excel_data = [];
        $field      = [
            'CV ID',
            'Current HCID',
            'Current Hire Type',
            'Correct HCID',
            'Correct Hire Type',
        ];
        $file_name  = 'Hire_Type_Incorrect_Resume_List_' . date('Ymd') . '.xlsx';
        $t = $this->getTranslation('en');
        foreach ($resume_data as $resume_info) {
            $excel_data[] = [
                $resume_info['id'] ?? '',
                $resume_info['hc_id'] ?? '',
                !empty($this->handleDiscrepancyHcTask[$resume_info['hc_id']]['hire_type']) ? $t->_('hire_type_' . $this->handleDiscrepancyHcTask[$resume_info['hc_id']]['hire_type']) : '',
                $this->handleDiscrepancyHcTask[$resume_info['hc_id']]['new_hc_id'] ?? '',
                !empty($this->handleDiscrepancyHcTask[$resume_info['hc_id']]['new_hire_type']) ? $t->_('hire_type_' . $this->handleDiscrepancyHcTask[$resume_info['hc_id']]['new_hire_type']) : '',
            ];
            $db->updateAsDict(
                'hr_resume',
                [
                    'task_old_hc_id' => $resume_info['hc_id'],
                ],
                'id = ' . $resume_info['id']
            );
        }
        if (empty($hc_hire_type_incorrect_email) || !is_array($hc_hire_type_incorrect_email)){
            $this->getDI()->get('logger')->write_log("handleHcSendEmail 无邮箱配置数据",'info');
            return true;
        }
        $file_path      = Excel::exportExcel($field, $excel_data, $file_name);
        $fileUrl        = $file_path ? $file_path['object_url'] : '';
        if ($fileUrl) {
            $attachmentPath = BASE_PATH . '/app/runtime/hire_type_incorrect_resume_list_' . md5(time()) . '.xlsx';
            file_put_contents($attachmentPath, file_get_contents($fileUrl));
        } else {
            throw new \Exception('handleHcSendEmail 文件生成失败' . json_encode($file_path,JSON_UNESCAPED_UNICODE));
        }
        $title     = 'HC Hire Type Incorrect Resume List';
        $content   = 'Dear all，<br/>
Attached is a list of resumes with incorrect HC hire type. Please handle it in time.<br/>
Thank you!';
        $sendEmail = (new MailServer())->send_mail('default', $hc_hire_type_incorrect_email, $title, $content,
            $attachmentPath, $file_name);
        if (file_exists($attachmentPath)) {
            unlink($attachmentPath);
        }
        if (!$sendEmail) {
            throw new \Exception('handleHcSendEmail 发送失败 检查邮箱是否正确' . json_encode($hc_hire_type_incorrect_email,
                    JSON_UNESCAPED_UNICODE));
        }
        return true;
    }
    
    /**
     * @param $hc_id
     * @return true
     * @throws ValidationException
     */
    public function checkHcHireType($hc_id,$lang = ''): bool
    {
        if (!isCountry('TH')){
            return true;
        }
        $t = $this->getTranslation(!empty($lang) ? $lang : $this->lang);
        $setting = (new SettingEnvServer())->getMultiEnvByCodeToArray([
            'daily_special_contract_employees_job',
            'daily_special_contract_employees_branch',
        ]);
        
        $hc_info = HrhcModel::findFirst([
            'conditions' => "hc_id = :hc_id:",
            'bind' => ['hc_id' => $hc_id],
        ]);
        if (empty($hc_info)) {
            throw new ValidationException($t->_('8019'));
        }
        $hc_info = $hc_info->toArray();
        if (!empty($hc_info['task_new_hc_id']) && !empty($hc_info['state_code']) && in_array($hc_info['state_code'], [HrhcModel::STATE_FULL_RECRUITMENT,HrhcModel::STATE_VOIDED])){
            throw new ValidationException($t->_('hc_err_1'));
        }
        if (
            !empty($hc_info['job_title']) && 
            !empty($setting['daily_special_contract_employees_job']) && 
            is_array($setting['daily_special_contract_employees_job']) && 
            in_array($hc_info['job_title'],$setting['daily_special_contract_employees_job']) && 
            !empty($setting['daily_special_contract_employees_branch']) &&
            is_array($setting['daily_special_contract_employees_branch']) &&
            in_array($hc_info['worknode_id'],$setting['daily_special_contract_employees_branch'])
        ){
            if (!empty($hc_info['hire_type']) && in_array($hc_info['hire_type'],[HrhcModel::HIRE_TYPE_PERMANENT_EMPLOYEE,HrhcModel::HIRE_TYPE_SPECIAL_CONTRACT_MONTHLY_WORKERS])){
                throw new ValidationException($t->_('hc_hire_type_err_1'));
            }
        }else{
            if (!empty($hc_info['hire_type']) && $hc_info['hire_type'] == HrhcModel::HIRE_TYPE_SPECIAL_CONTRACT_WEEKLY_WORKERS){
                throw new ValidationException($t->_('hc_hire_type_err_2'));
            }
        }
        return true;
    }

    
    /**
     * @param $paramIn
     * @return int
     */
    public function getHandleDiscrepancyHcNum($paramIn): int
    {
        if(!isCountry('TH') || empty($paramIn['userinfo'])) {
            return 0;
        }
        $userinfo = $paramIn['userinfo'];
        $param = [];
        $param['is_task_new_hc_id'] = true;
        $param['is_count'] = 1;
        try {
            $serverObj = Tools::reBuildCountryInstance(new InterviewServer());
            return $serverObj->interviewList($param, $userinfo) ?? 0;
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log(["getHandleDiscrepancyHcNum error" => $e->getMessage()],'error');
            return 0;
        }
    }

    /**
     * hc审批通过消息通知
     * @return bool|void
     * @throws BusinessException
     */
    public function approvalAgreeNotice()
    {
        if (!isCountry(['TH','PH','MY'])){
            return false;
        }
        $cache_time = 86399;
//        $cache_time = 5;
        $cache    = $this->getDI()->get('redisLib');
        $log = $this->getDI()->get('logger');
        $t = $this->getTranslation();
        $approval_completion_time_s = date("Y-m-d 09:00:00", strtotime("-1 day"));
        $approval_completion_time_e = date("Y-m-d 09:00:00");
        $redisKey = 'approvalAgreeNotice:'.md5($approval_completion_time_s.'_'.$approval_completion_time_e);
        $redis_data_arr = [];
        if ($redis_data = $cache->get($redisKey)){
            $redis_data_arr = json_decode($redis_data, true);
        }
        $log->write_log("approvalAgreeNotice redis_data_arr " . json_encode($redis_data_arr), 'info');
        
        $list = $this->task_hc_list([
            'state_code'=>[HrHcModel::STATE_RECRUITING],
            'approval_completion_time_s'=>$approval_completion_time_s,
            'approval_completion_time_e'=>$approval_completion_time_e,
        ]);
        $log->write_log("approvalAgreeNotice list_count_num : ".count($list), 'info');
        if (empty($list)){
            return false;
        }
        
        $worknodeIdsArr = array_column($list, 'worknode_id');
        $relationFind = HrOrganizationDepartmentStoreRelationModel::find([
            'conditions' => 'level_state = :level_state: and state = :state: and is_deleted = :is_deleted: and store_id in ({store_id:array})',
            'bind'       => [
                'level_state' => HrOrganizationDepartmentStoreRelationModel::LEVEL_STATE_ON,
                'state'       => HrOrganizationDepartmentStoreRelationModel::STATE_ON,
                'is_deleted'  => HrOrganizationDepartmentStoreRelationModel::IS_DELETED_NO,
                'store_id'    => $worknodeIdsArr,
            ],
        ])->toArray();
        $relationFind = array_column($relationFind,null, 'store_id');
        $regionIds            = array_column($relationFind, 'region_id');
        $pieceIds             = array_column($relationFind, 'piece_id');
        $storeIds             = array_column($relationFind, 'store_id');
        $regionMap            = (new SysManageRegionRepository())->getListByIds($regionIds, 'id,manager_id');
        $pieceMap             = (new SysManagePieceRepository())->getListByIds($pieceIds, 'id,manager_id');
        $storeMap             = (new SysStoreRepository())->getManagerByIds($storeIds);
        if (isCountry('TH')) {
            $network_management_id = enumsTh::DEPARTMENT_TH_NETWORK_MANAGEMENT;
        } elseif (isCountry('PH')) {
            $network_management_id = enumsPh::DEPARTMENT_TH_NETWORK_MANAGEMENT;
        } elseif (isCountry('MY')) {
            $network_management_id = enumsMy::DEPARTMENT_TH_NETWORK_MANAGEMENT;
        } else {
            $network_management_id = 0;
        }
        $departmentInfoIds = (new DepartmentRepository())->getDepartmentListById($network_management_id);
        $file_name = 'HC_list_'.date('Y-m-d').'.xlsx';
        $redis_staff_ids = [];
        foreach ($list as $v){
            $am_staff_id = $regionMap[$relationFind[$v['worknode_id']]['region_id']]['manager_id'] ?? '';
            $dm_staff_id = $pieceMap[$relationFind[$v['worknode_id']]['piece_id']]['manager_id'] ?? '';
            $manager_staff_id = $storeMap[$v['worknode_id']]['manager_id'] ?? '';
            
            $am_store_ids = (new DepartmentServer())->getSelfManagerStoreIds($am_staff_id);
            $dm_store_ids = (new DepartmentServer())->getSelfManagerStoreIds($dm_staff_id);
            $manager_store_ids = (new DepartmentServer())->getSelfManagerStoreIds($manager_staff_id);
            
            // am
            $log->write_log("approvalAgreeNotice am_staff_id : ".$am_staff_id." am_store_ids : " . json_encode($am_store_ids), 'info');
            if (!empty($am_store_ids) && !empty($departmentInfoIds) && !in_array($am_staff_id,$redis_data_arr)){
                $am_lang = HrStaffContractRepository::getInstance()->getByStaffLang($am_staff_id);
                $this->lang = $am_lang;
                $t = $this->getTranslation($this->lang);
                $field = [
                    'HC ID',
                    $t->_('4032'),//优先级
                    $t->_('7001'),//JD名称
                    $t->_('job_name'),//职位
                    $t->_('7002'),//所属部门
                    $t->_('7003'),//所属网点
                    $t->_('7017'),//用人原因
                    $t->_('hire_type'),//雇佣类型
                    $t->_('hire_times'),//雇佣期间
                    $t->_('7007'),//剩余/总需求数
                    $t->_('7008'),//状态
                    $t->_('7005'),//创建时间
                    $t->_('8600'),//最终审批时间
                    $t->_('7006'),//创建人
                ];
                $am_hc_list = $this->task_hc_list([
                    'state_code'=>[HrHcModel::STATE_RECRUITING,HrHcModel::STATE_NOT_EFFECTIVE],
                    'store_ids'=>$am_store_ids,
                    'department_ids'=>$departmentInfoIds,
                ]);
                if (!empty($am_hc_list)){
                    $am_hc_excel_data = $this->formatHcExcelData($am_hc_list);
                    $file_path = Excel::exportExcel($field, $am_hc_excel_data, $file_name,'hcApprovalAgreeNotice');
                    $am_file_url   = $file_path ? $file_path['object_url'] : '';
                    $log->write_log("approvalAgreeNotice am_staff_id : ".$am_staff_id." am_file_url : " . $am_file_url, $am_file_url ? 'info' : 'error');
                    if (!empty($am_file_url)){
                        $message_params['id']                 = time() . $am_staff_id . rand(1000000, 9999999);
                        $message_params['staff_users']        = [$am_staff_id];
                        $message_params['message_title']      = $t->_('approval_agree_notice_title');
                        $message_params['message_content']    = addslashes("<div style='font-size: 35px'>".$t->_('approval_agree_notice_content', ['excel_url' => $am_file_url]). "</div>");
                        $message_params['staff_info_ids_str'] = $am_staff_id;
                        $message_params['category']           = '-1';
                        $result = (new MessageServer())->add_kit_message($message_params);
                        if ($result[1] != 1) {
                            $log->write_log("approvalAgreeNotice add_kit_message error1 params: ".json_encode($message_params), 'error');
                        }else{
                            $redis_staff_ids[]=$am_staff_id;
                            $redis_data_arr = array_values(array_unique(array_merge($redis_data_arr,$redis_staff_ids)));
                            $cache->set($redisKey, json_encode($redis_data_arr), $cache_time);
                            $log->write_log("approvalAgreeNotice add_kit_message success1 params: ".json_encode($message_params), 'info');
                        }
                    }
                }
            }
            
            // dm
            $log->write_log("approvalAgreeNotice dm_staff_id : ".$dm_staff_id." dm_store_ids : " . json_encode($dm_store_ids), 'info');
            if (!empty($dm_store_ids) && !empty($departmentInfoIds) && !in_array($dm_staff_id,$redis_data_arr)){
                $dm_lang = HrStaffContractRepository::getInstance()->getByStaffLang($dm_staff_id);
                $this->lang = $dm_lang;
                $t = $this->getTranslation($this->lang);
                $field = [
                    'HC ID',
                    $t->_('4032'),//优先级
                    $t->_('7001'),//JD名称
                    $t->_('job_name'),//职位
                    $t->_('7002'),//所属部门
                    $t->_('7003'),//所属网点
                    $t->_('7017'),//用人原因
                    $t->_('hire_type'),//雇佣类型
                    $t->_('hire_times'),//雇佣期间
                    $t->_('7007'),//剩余/总需求数
                    $t->_('7008'),//状态
                    $t->_('7005'),//创建时间
                    $t->_('8600'),//最终审批时间
                    $t->_('7006'),//创建人
                ];
                $dm_hc_list = $this->task_hc_list([
                    'state_code'=>[HrHcModel::STATE_RECRUITING,HrHcModel::STATE_NOT_EFFECTIVE],
                    'store_ids'=>$dm_store_ids,
                    'department_ids'=>$departmentInfoIds,
                ]);
                if (!empty($dm_hc_list)){
                    $dm_hc_excel_data = $this->formatHcExcelData($dm_hc_list);
                    $file_path = Excel::exportExcel($field, $dm_hc_excel_data, $file_name,'hcApprovalAgreeNotice');
                    $dm_file_url   = $file_path ? $file_path['object_url'] : '';
                    $log->write_log("approvalAgreeNotice dm_staff_id : ".$dm_staff_id." dm_file_url : " . $dm_file_url, $dm_file_url ? 'info' : 'error');
                    if (!empty($dm_file_url)){
                        $message_params['id']                 = time() . $dm_staff_id . rand(1000000, 9999999);
                        $message_params['staff_users']        = [$dm_staff_id];
                        $message_params['message_title']      = $t->_('approval_agree_notice_title');
                        $message_params['message_content']    = addslashes("<div style='font-size: 35px'>".$t->_('approval_agree_notice_content', ['excel_url' => $dm_file_url]). "</div>");
                        $message_params['staff_info_ids_str'] = $dm_staff_id;
                        $message_params['category']           = '-1';
                        $result = (new MessageServer())->add_kit_message($message_params);
                        if ($result[1] != 1) {
                            $log->write_log("approvalAgreeNotice add_kit_message error2 params: ".json_encode($message_params), 'error');
                        }else{
                            $redis_staff_ids[]=$dm_staff_id;
                            $redis_data_arr = array_values(array_unique(array_merge($redis_data_arr,$redis_staff_ids)));
                            $cache->set($redisKey, json_encode($redis_data_arr), $cache_time);
                            $log->write_log("approvalAgreeNotice add_kit_message success2 params: ".json_encode($message_params), 'info');
                        }
                    }
                }
            }
            
            // manager
            $log->write_log("approvalAgreeNotice manager_staff_id : ".$manager_staff_id." manager_store_ids : " . json_encode($manager_store_ids), 'info');
            if (!empty($manager_store_ids) && !empty($departmentInfoIds) && !in_array($manager_staff_id,$redis_data_arr)){
                $manager_lang = HrStaffContractRepository::getInstance()->getByStaffLang($manager_staff_id);
                $this->lang = $manager_lang;
                $t = $this->getTranslation($this->lang);
                $field = [
                    'HC ID',
                    $t->_('4032'),//优先级
                    $t->_('7001'),//JD名称
                    $t->_('job_name'),//职位
                    $t->_('7002'),//所属部门
                    $t->_('7003'),//所属网点
                    $t->_('7017'),//用人原因
                    $t->_('hire_type'),//雇佣类型
                    $t->_('hire_times'),//雇佣期间
                    $t->_('7007'),//剩余/总需求数
                    $t->_('7008'),//状态
                    $t->_('7005'),//创建时间
                    $t->_('8600'),//最终审批时间
                    $t->_('7006'),//创建人
                ];
                $manager_hc_list = $this->task_hc_list([
                    'state_code'=>[HrHcModel::STATE_RECRUITING,HrHcModel::STATE_NOT_EFFECTIVE],
                    'store_ids'=>$manager_store_ids,
                    'department_ids'=>$departmentInfoIds,
                ]);
                if (!empty($manager_hc_list)){
                    $manager_hc_excel_data = $this->formatHcExcelData($manager_hc_list);
                    $file_path = Excel::exportExcel($field, $manager_hc_excel_data, $file_name,'hcApprovalAgreeNotice');
                    $manager_file_url   = $file_path ? $file_path['object_url'] : '';
                    $log->write_log("approvalAgreeNotice manager_staff_id : ".$manager_staff_id." manager_file_url : " . $manager_file_url, $manager_file_url ? 'info' : 'error');
                    if (!empty($manager_file_url)){
                        $message_params['id']                 = time() . $manager_staff_id . rand(1000000, 9999999);
                        $message_params['staff_users']        = [$manager_staff_id];
                        $message_params['message_title']      = $t->_('approval_agree_notice_title');
                        $message_params['message_content']    = addslashes("<div style='font-size: 35px'>".$t->_('approval_agree_notice_content', ['excel_url' => $manager_file_url]). "</div>");
                        $message_params['staff_info_ids_str'] = $manager_staff_id;
                        $message_params['category']           = '-1';
                        $result = (new MessageServer())->add_kit_message($message_params);
                        if ($result[1] != 1) {
                            $log->write_log("approvalAgreeNotice add_kit_message error3 params: ".json_encode($message_params), 'error');
                        }else{
                            $redis_staff_ids[]=$manager_staff_id;
                            $redis_data_arr = array_values(array_unique(array_merge($redis_data_arr,$redis_staff_ids)));
                            $cache->set($redisKey, json_encode($redis_data_arr), $cache_time);
                            $log->write_log("approvalAgreeNotice add_kit_message success3 params: ".json_encode($message_params), 'info');
                        }
                    }
                }
            }
            
        }
        return true;
    }
    
    
    public function task_hc_list($params)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['hc' => HrHcModel::class]);
        $builder->leftJoin(HrJdModel::class," hc.job_id=hr_jd.job_id","hr_jd");
        $builder->andWhere('hc.deleted = 1');
        if (!empty($params['state_code']) && is_array($params['state_code'])){
            $builder->andWhere('hc.state_code IN ({state_code:array})',
                ['state_code' => $params['state_code']]);
        }
        if (!empty($params['store_ids']) && is_array($params['store_ids'])){
            $builder->andWhere('hc.worknode_id IN ({store_ids:array})',
                ['store_ids' => $params['store_ids']]);
        }
        if (!empty($params['department_ids']) && is_array($params['department_ids'])){
            $builder->andWhere('hc.department_id IN ({department_ids:array})',
                ['department_ids' => $params['department_ids']]);
        }
        $builder->andWhere('hc.reason_type IN ({reason_type:array})',
            ['reason_type' => [HrHcModel::REASON_TYPE_RECRUIT, HrHcModel::REASON_TYPE_RESIGNATION]]);
        if (!empty($params['approval_completion_time_s'])){
            $builder->andWhere(
                "hc.approval_completion_time >= CONVERT_TZ('{$params['approval_completion_time_s']}', '{$this->timezone}', '+00:00' )");
        }
        if (!empty($params['approval_completion_time_e'])){
            $builder->andWhere(
                "hc.approval_completion_time < CONVERT_TZ('{$params['approval_completion_time_e']}', '{$this->timezone}', '+00:00' )");
        }
        $builder->orderBy('hc.hc_id desc');
        $builder->columns([
            'hc.hc_id',
            'hc.priority_id',
            'hc.job_id',
            'hr_jd.job_name',
            'hc.job_title',
            'hc.department_id',
            'hc.worknode_id',
            'hc.reason_type',
            'hc.hire_type',
            'hc.hire_times',
            'hc.demandnumber',
            'hc.surplusnumber',
            'hc.state_code',
            'hc.submitter_id',
            "CONVERT_TZ(hc.createtime, '+00:00', '{$this->timezone}' ) AS createtime",
            "CONVERT_TZ(hc.approval_completion_time, '+00:00', '{$this->timezone}' ) AS approval_completion_time",
        ]);
        $list = $builder->getQuery()->execute()->toArray();
        if (empty($list)){
            return [];
        }
        $hcStateList = (new HcRepository())->getHcStateList();
        $hcStateList = array_column($hcStateList, 'state_value', 'state_code');
        $positionList = array_column((new HrJobTitleRepository())->getJobTitleListFromCache(['id','job_name']), 'job_name', 'id');
        $departmentDataList = array_column((new SysDepartmentRepository())->getDepartmentListFromCache(['id','name']), 'name', 'id');
        $worknodeData = array_column((new SysStoreRepository())->getStoreListFromCache(['id','name','sorting_no']), null, 'id');
        $worknodeData['-1'] = ['name'=>enums::HEAD_OFFICE,'sorting_no'=>'','id'=>'-1'];
        $submitter_staff_ids = array_values(array_unique(array_column($list, "submitter_id"))); //提交人ID
        if(!empty($submitter_staff_ids)){
            $staffIds = getIdsStr($submitter_staff_ids);
            $staffList = (new SysListRepository())->getStaffList([
                "ids" => $staffIds,
            ]);
        }
        $staffData = isset($staffList) ? array_column($staffList, null, 'staff_info_id') :[];
        
        foreach ($list as $k=>$v){
            $list[$k]['priority_text'] = !empty($v['priority_id']) ? $this->formatPriority($v['priority_id']) : '';
            $list[$k]['state_text'] = !empty($hcStateList[$v['state_code']]) ? $this->getTranslation()->_($hcStateList[$v['state_code']]) : '';
            $list[$k]['job_title_name'] = !empty($v['job_title']) ? sprintf("(%s) %s", $v['job_title'], $positionList[$v['job_title']]) : '';
            $list[$k]['department_name'] = $departmentDataList[$v['department_id']] ?? '';
            $list[$k]['worknode_name'] = $worknodeData[$v['worknode_id']]["name"] ?? '';
            $list[$k]['reason_type_txt'] = str_replace(["招聘", "Recruitment"], ["新增", "Add"], $this->getTranslation()->_("hc_reason_type_" . $v['reason_type']));
            $list[$k]['hire_type_text'] = $v['hire_type'] ? $this->getTranslation()->_('hire_type_' . $v['hire_type']) : '';
            $list[$k]['demand_surplus'] = $v['surplusnumber'].'/'.$v['demandnumber'];
            $list[$k]['submitter_name'] = !empty($v['submitter_id']) ? $staffData[$v['submitter_id']]["name"].'('.$v['submitter_id'].')' : '';
        }
        return $list ?? [];
    }
    
    public function formatHcExcelData($data)
    {
        $_data = [];
        foreach ($data as $v){
            if ($v['hire_type'] == 1){
                $hire_times = '';
            }else{
                $hire_times = $v['hire_times'] ?? '';
            }
            $_data[]               = [
                $v['hc_id'] ?? '',
                $v['priority_text'] ?? '',
                $v['job_name'] ?? '',
                $v['job_title_name'] ?? '',
                $v['department_name'] ?? '',
                $v['worknode_name'] ?? '',
                $v['reason_type_txt'] ?? '',
                $v['hire_type_text'] ?? '',
                $hire_times,
                $v['demand_surplus'] ?? '',
                $v['state_text'] ?? '',
                $v['createtime'] ?? '',
                $v['approval_completion_time'] ?? '',
                $v['submitter_name'] ?? '',
            ];
        }
        return $_data ?? [];
    }
}
