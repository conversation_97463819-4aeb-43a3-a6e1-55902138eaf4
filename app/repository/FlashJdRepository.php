<?php

namespace FlashExpress\bi\App\Repository;

use FlashExpress\bi\App\library\enumsMy;
use FlashExpress\bi\App\library\enumsTh;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\HrJobTitleByModel;
use FlashExpress\bi\App\Models\backyard\HrJdModel;
use FlashExpress\bi\App\Models\backyard\HrJobDepartmentRelationModel;
use FlashExpress\bi\App\Models\backyard\HrJobGroupModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\SysDepartmentModel;
use FlashExpress\bi\App\Models\fle\StaffInfoModel;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Server\StaffServer;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Server\SysServer;

class FlashJdRepository extends BaseRepository
{
    public function __construct()
    {
        parent::__construct();
        $this->db = $this->getDI()->get('db');//主库（写）
        $this->db_rby = $this->getDI()->get('db_rby');//从库（读）
    }

    /**
     * 校验数据重复
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function checkJdAddOrUpdate($paramIn = [])
    {
        $jobId      = $paramIn['job_id'];
        $jobName    = $paramIn['job_name'];
        $jdCheckSql = "SELECT * FROM `hr_jd` WHERE job_name = '" . $jobName . "' AND state =1";
        if ($jobId) {
            $jdCheckSql .= " AND job_id <> " . $jobId;
        }
        $data       = $this->getDI()->get('db_rby')->query($jdCheckSql);
        $hrJdData   = $data->fetch(\Phalcon\Db::FETCH_ASSOC);
        $returnData = $hrJdData;
        return $returnData;
    }

    /**
     * JD数据修改
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function jdUpdate($paramIn = [])
    {
        //[1]参数定义
        $jobName          = $paramIn['job_name'];
        $jobId            = $paramIn['job_id'];
        $jobDescription   = $paramIn['job_description'];
        $departmentId     = $paramIn['department_id'];
        $reportSuperiorId = $paramIn['report_superior_id'];
        $submitterId      = $paramIn['submitter_id'];
        $submitterName    = $paramIn['submitter_name'];
        $positionId       = $paramIn['position_id'];
        $type             = $paramIn['type'];

        //[2]修改语句拼装
        $returnData = $this->db->updateAsDict(
            'hr_jd',
            [
                'job_name'           => $jobName,
                'type'               => $type,
                'job_description'    => $jobDescription,
                'department_id'      => $departmentId,
                'report_superior_id' => $reportSuperiorId,
                'position_id'        => $positionId,
                'submitter_id'       => $submitterId,
                'submitter_name'     => $submitterName,
            ],
            'job_id = ' . $jobId
        );
        return $jobId;
    }

    /**
     * JD数据添加
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function jdAdd($insetData = [])
    {
        $this->db->begin();
        $data = $this->db->insertAsDict(
            'hr_jd', $insetData
        );
        if (!$data) {
            $this->db->rollback();
            return false;
        }
        $id = $this->db->lastInsertId();
        $this->db->commit();
        return $id;
    }


    /**
     * 获取是bike jd_type 的职位ID
     * @return array
     */
    public function getBikeJobIds(): array
    {
        if (isCountry('th')) {
            return [
                enums::$job_title['Bike Courier'],
                enums::$job_title['messenger'],
                enums::$job_title['shop_bike'],
                enums::$job_title['network_bulky_bike_courier'],
                enums::$job_title['tricycle_courier'],
            ];
        }
        return [
            enums::$job_title['Bike Courier'],
            enums::$job_title['messenger'],
            enums::$job_title['shop_bike'],
            enums::$job_title['network_bulky_bike_courier'],
        ];
    }


    /**
     * JD详情
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function jdDetail($paramIn, $type = 0)
    {
        //[1]参数定义
        $jobId = $paramIn['job_id'] ?? -1;

        //获取职组列表(只一级职组）
        $job_group =  HrJobGroupModel::find([
            'pid=:pid: and is_del=:is_del:',
            'bind' => ['pid' => 0,'is_del' => 0],
        ])->toArray();
        if($job_group){
            $job_group_map = array_column($job_group,'name','id');
        }
        $detailData = HrJdModel::findFirst([
            'state=1 and job_id=:job_id:',
            'bind' => ['job_id' => $jobId],
        ]);
        if(empty($detailData)) return [];
        $returnData = $detailData->toArray();
        $add_hour = $this->getDI()['config']['application']['add_hour'];
        $returnData['job_group_name'] = $job_group_map[$returnData['job_group_id']] ?? '';
        $returnData['created_at'] = date('Y-m-d H:i:s',strtotime($returnData['created_at']) + $add_hour*3600);
        $returnData['updated_at'] = date('Y-m-d H:i:s',strtotime($returnData['created_at']) + $add_hour*3600);

        /*
         * 由于简历附件用到已经废弃的type类型，故临时定义一套规则标识type字段 （江奇需求）
         *  1、JD-类型有值
            2、JD-类型没有值-找职位
            ==职位-包含van->van
            ==职位-包含bike->bike
            ==职位-既包含van，又包含bike->非快递员
            3、找不到职位变非快递员
         */
        if(empty($returnData['type'])){
            $jd_type = '3';//默认为非快递员
            $job_list =  HrJobDepartmentRelationModel::find([
                'jd_id = :jd_id:',
                'bind'=>['jd_id' => $jobId ],
            ])->toArray();

            if(!empty($job_list)){
                //获取该jdID 关联的所有职位ID
                $job_ids = array_column($job_list,'job_id');
                $bike_job_ids = $this->getBikeJobIds(); //bike（摩托）类型 职位
                $van_job_ids = ['110','516']; //van（皮卡）类型 职位
                $tricycle_job_ids = ['1000']; //Tricycle（三轮车）类型 职位
                $car_job_ids = ['1199'];
                $hub_driver_job_ids = ['1413'];
                $truck_job_ids = ['1194','1675']; // truck 职位 快递员 卡车
                $pickup_driver_ids = [enums::$job_title['pickup_driver']];

                //新增
                if (isCountry('MY')) {
                    $van_job_ids = array_merge($van_job_ids, [enumsMy::JOB_TITLE_FLEET_DRIVER,enumsMy::JOB_TITLE_BDC_DRIVER]);
                }

                //配置EVCourier[1930]的jd_type = 2
                if (isCountry()) {
                    $van_job_ids =  array_merge($van_job_ids,[enumsTh::JOB_TITLE_EV_COURIER,enumsTh::JOB_TITLE_VAN_COURIER_PROJECT]);
                }

                if(
                    array_intersect($bike_job_ids,$job_ids) && array_intersect($van_job_ids,$job_ids)
                    ||
                    strtolower(env('country_code')) == 'ph' && array_intersect($bike_job_ids,$job_ids) && array_intersect($van_job_ids,$job_ids) && array_intersect($tricycle_job_ids, $job_ids)
                ){
                    //一个jd 即关联了van 职位也关联了bike职位 => 非快递员 类型
                }elseif(array_intersect($bike_job_ids,$job_ids)){
                    //只关联了bike职位 => bike(摩托车）类型
                    $jd_type = '1';
                }elseif(array_intersect($van_job_ids,$job_ids)){
                    //只关联了van职位 => van(皮卡）类型
                    $jd_type = '2';
                }elseif(array_intersect($tricycle_job_ids, $job_ids) && strtolower(env('country_code')) == 'ph') {
                    // 关联tricycle职位 =》 tricycle(三轮车类型)

                    $jd_type = '4';
                }elseif(array_intersect($truck_job_ids, $job_ids) && strtolower(env('country_code')) == 'ph') {
                    // truck 职位 ==> 卡车

                    $jd_type = '6';
                } elseif (array_intersect($car_job_ids, $job_ids) && strtolower(env('country_code')) == 'my') {
                    //只关联了car职位 => car类型
                    $jd_type = '5';
                } elseif (array_intersect($hub_driver_job_ids, $job_ids) && strtolower(env('country_code')) == 'my') {
                    //只关联了hub driver职位 => hub driver 类型
                    $jd_type = '7';
                } elseif (array_intersect($pickup_driver_ids, $job_ids) && isCountry('TH')) {
                    $jd_type = '2';
                }
            }
            $returnData['type'] = $jd_type;
        }


        return $returnData;
    }

    /**
     * JD列表
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getJdList($paramIn = [])
    {
        return ;
        //[1]参数定义
        $staffId       = $paramIn['staff_id'];
        $noPage        = $paramIn['no_page'];
        $pageSize      = $noPage ? $paramIn['page_size'] : 100000;
        $pageNum       = $paramIn['page_num'];
        $jobName       = $paramIn['job_name'];
        $departmentId  = $paramIn['department_id'];
        $submitterName = $paramIn['submitter_name'];
        $timeStart     = $paramIn['time_start'];
        $timeEnd       = $paramIn['time_end'];
        $type          = $paramIn['type'];
        $returnData    = [
            'dataList'   => [],
            'pagination' => [
                'count'     => 0,
                'pageCount' => 0,
                'pageNum'   => $pageNum,
                'pageSize'  => $pageSize,
            ],
        ];

        //[2]数据查询
        $listSql      = "SELECT 
                         hr_jd.job_id,
                         hr_jd.type,
                         hr_jd.job_name,
                         hr_jd.job_description,
                         hr_jd.department_id,
                         hr_jd.position_id,
                         hr_jd.report_superior_id,
                         hr_jd.submitter_id,
                         hr_jd.submitter_name,
                         hr_jd.state,
                         CONVERT_TZ(hr_jd.created_at, '+00:00', '" . $this->timezone . "' ) AS created_at,
                         CONVERT_TZ(hr_jd.updated_at, '+00:00', '" . $this->timezone . "' ) AS updated_at,
                         hr_report_superior.report_name";
        $listCountSql = "SELECT count(job_id) as cou ";
        $listSqlMain  = " FROM `hr_jd` left join hr_report_superior on hr_jd.report_superior_id =hr_report_superior.id  WHERE hr_jd.state =1 ";
        if (!empty($jobName)) {
            $jobName     = str_replace("'", '', $jobName);
            $listSqlMain .= "And hr_jd.job_name like '%" . $jobName . "%' ";
        }
        if ($departmentId) {
            if (is_array($departmentId)) {
                $listSqlMain .= "And hr_jd.department_id in (" . implode(",", $departmentId) . ") ";
            } else {
                $listSqlMain .= "And hr_jd.department_id =" . $departmentId . " ";
            }
        }
        if ($type) {
            $listSqlMain .= "And hr_jd.type =" . $type . " ";
        }
        if (!empty($submitterName)) {
            $submitterName       = str_replace("'", '', $submitterName);
            $listSqlMain .= "And hr_jd.submitter_name like '%" . $submitterName . "%' ";
        }
        if (!empty($timeStart)) {
            $timeStart   = date("Y-m-d 00:00:00", strtotime($timeStart));
            $listSqlMain .= "and hr_jd.created_at>=CONVERT_TZ(\"" . $timeStart . "\", \"" . $this->timezone . "\", \"+00:00\")";
        }
        if (!empty($timeEnd)) {
            $timeEnd     = date("Y-m-d 23:59:59", strtotime($timeEnd));
            $listSqlMain .= "and hr_jd.created_at<=CONVERT_TZ(\"" . $timeEnd . "\", \"" . $this->timezone . "\", \"+00:00\")";
        }
        $listCountSql = $listCountSql . $listSqlMain;
        $dataObj      = $this->getDI()->get('db_rby')->query($listCountSql);
        $data         = $dataObj->fetch(\Phalcon\Db::FETCH_ASSOC);
        if (!empty($data)) {
            $returnData['pagination']['count']     = intval($data['cou']);
            $returnData['pagination']['pageCount'] = ceil($data['cou'] / $pageSize);
        }

        $listSql .= $listSqlMain . "  order by hr_jd.created_at desc limit " . ($pageNum - 1) * $pageSize . "," . $pageSize;
        $dataObj = $this->getDI()->get('db_rby')->query($listSql);
        $data    = $dataObj->fetchAll(\Phalcon\Db::FETCH_ASSOC);

        //查询部门数据
        $departmentData = (new SysListRepository())->getDepartmentList(1);
        $departmentData = array_column($departmentData, 'name', 'id');
        //处理数据
        if (!empty($data)) {
            $repo = new SysListRepository();
            foreach ($data as $k => $v) {
                if ($v["department_id"]) {
                    $departmentHierarchyIds             = $repo->getDepartmentTree($v["department_id"]);
                    $data[$k]["departmentHierarchyIds"] = $departmentHierarchyIds;
                    $data[$k]['department_name']        = $departmentData[$v['department_id']] ?? '';
                } else {
                    $data[$k]['departmentHierarchyIds'] = [];
                    $data[$k]['department_name']        = "";
                }

                if ($v['type'] == 1) {
                    //摩托车
                    $data[$k]['type_name'] = $this->getTranslation()->_('3014');
                } elseif ($v['type'] == 2) {
                    //皮卡车
                    $data[$k]['type_name'] = $this->getTranslation()->_('3015');
                } elseif ($v['type'] == 3) {
                    //非快递员
                    $data[$k]['type_name'] = $this->getTranslation()->_('3016');
                }
            }
        }
        $returnData['dataList'] = $data;
        return $returnData;
    }

    public function getJdListNew(array $paramIn){
        $noPage        = $paramIn['no_page'];
        $pageNum       = $paramIn['page_num']>1 ? $paramIn['page_num'] : 1;
        $pageSize      = !$noPage ? $paramIn['page_size'] : 10000;
        $pageNum       = !$noPage ? $pageNum : 1;
        $offset        = ($pageNum - 1) * $pageSize;
        $jobName       = $paramIn['job_name'];
        $group_id          = $paramIn['group_id'];

        $department_id   = $paramIn['department_id'];
        $job_id          = $paramIn['job_id'];
        $returnData    = [
            'dataList'   => [],
            'pagination' => [
                'count'     => 0,
                'pageCount' => 0,
                'pageNum'   => $pageNum,
                'pageSize'  => $pageSize,
            ],
        ];
        //todo 获取职位关联列表
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['main'=>HrJobDepartmentRelationModel::class]);
        $builder->leftjoin(HrJdModel::class, 'main.jd_id=detail.job_id', 'detail');

        $builder->columns('main.id,main.department_id,main.job_id,main.group_id,main.jd_id,main.created_at,main.updated_at,detail.job_name as jd_name');

        //添加筛选条件
        if($jobName){
            //职位名称
            $builder->andWhere('detail.job_name like :job_name:', ['job_name' => "%{$jobName}%"]);
        }

        if($group_id){
            //职组ID
            $builder->andWhere('main.group_id = :group_id:', ['group_id' => $group_id]);
        }
//        if($department_id){
//            //部门ID
//            $builder->andWhere('main.department_id = :department_id:', ['department_id' => $department_id]);
//        }
        if (!empty($paramIn['department_id'])) {
            $deptIds = SysServer::getDepartmentConditionByParams($paramIn);
            if (!empty($deptIds)) {
                $builder->inWhere('main.department_id', $deptIds);
            }
        }
        
        if($job_id){
            //职位ID
            $builder->andWhere('main.job_id = :job_id:', ['job_id' => $job_id]);
        }



        $count = $builder->getQuery()->execute()->count();
    
        if ($count > enums::EXPORT_MAX_NUM_LIMIT && $noPage ) {
            throw new ValidationException($this->getTranslation()->_('export_down_num_error'));
        }
        
        if ($count > 0) {

            $returnData['pagination']['count']     = $count;
            $returnData['pagination']['pageCount'] = ceil($count / $pageSize);

            $builder->orderBy('main.updated_at desc');
            $builder->limit($pageSize, $offset);

            $data_list = $builder->getQuery()->execute()->toArray();
//            echo $this
//                ->di->get(
//                    'profiler'
//                )->getLastProfile()->getSQLStatement();exit();
            //拼装数据
            $data_list = $this->buildJobBindList($data_list);

        }
        $returnData['dataList'] = $data_list ?? [];

        return $returnData;

    }



    /**
     * 处理 职位关联列表字段
     * @param $job_bind_list
     */
    private function buildJobBindList($job_bind_list)
    {

        //职位列表map
        $job_list = HrJobTitleByModel::find()->toArray();
        if (!empty($job_list)) {
            $job_id_name_map = array_column($job_list, 'job_name', 'id');
        }

//        //JD列表map
//        $jd_list = HrJdModel::find()->toArray();
//        if (!empty($jd_list)) {
//            $jd_id_name_map = array_column($jd_list, 'job_name', 'job_id');
//        }


        //部门列表map
        $department_list = SysDepartmentModel::find()->toArray();
        if (!empty($department_list)) {
            $department_id_name_map = array_column($department_list, 'name', 'id');
        }


        //职组列表map
        $jobgroup_list = HrJobGroupModel::find()->toArray();
        if (!empty($jobgroup_list)) {
            $jobgroup_id_name_map = array_column($jobgroup_list, 'name', 'id');
        }
        $add_hour = $this->getDI()['config']['application']['add_hour'];

        foreach ($job_bind_list as &$item) {

            $item['job_name']             = $job_id_name_map[$item['job_id']] ?? ''; //职位名称
            $item['department_name']      = $department_id_name_map[$item['department_id']] ?? '';//部门
            $item['group_name']           = $jobgroup_id_name_map[$item['group_id']] ?? ''; //职组名称
            $item['jd_name']              = $item['jd_name'] ?? ''; //JD名称
            $item['created_at']           = date('Y-m-d H:i:s', strtotime($item['created_at']) + $add_hour * 3600);
            $item['updated_at']           = date('Y-m-d H:i:s', strtotime($item['updated_at']) + $add_hour * 3600);


        }

        return $job_bind_list;


    }

    /**
     * 获取职位列表筛选项
     * @return mixed
     */
    public function getJobOption()
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('id,job_name as name');
        $builder->from(HrJobTitleByModel::class);
        $list = $builder->getQuery()->execute()->toArray();
        return $list ? $list : [];
    }

    /**
     * 岗位名称搜索
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function jobSearch($paramIn = [])
    {
        $jobName       = isset($paramIn['job_name']) ? $paramIn['job_name'] : '';
        $hcType        = isset($paramIn['hc_type']) ? $paramIn['hc_type'] : '';
        $submitterName = isset($paramIn['submitter_name']) ? $paramIn['submitter_name'] : '';
        $noPage        = $paramIn['no_page'];
        $pageSize      = $noPage ? $paramIn['page_size'] : 100000;
        $pageNum       = $paramIn['page_num'];
        $jobName       = addcslashes($jobName, "`\`");
        $submitterName = addcslashes($submitterName, "`\`");

        //[2]数据查询
        if (!empty($submitterName) || $submitterName == '0') {
            $submitterName              = str_replace("'", '', $submitterName);
            $auditListSql = "
                            --
                             SELECT submitter_name,submitter_id as staff_id
                            FROM `hr_jd` WHERE submitter_name like '%" . $submitterName . "%'
                            or  submitter_id like '%" . $submitterName . "%'";
            $auditListSql .= " AND state =1 group by submitter_name order by updated_at desc";
        }

        if (!empty($jobName) || $jobName == '0') {
            $jobName              = str_replace("'", '', $jobName);
            $auditListSql = " 
                            --
                            SELECT job_id,job_name,job_description,department_id,position_id,report_superior_id
                            FROM `hr_jd` WHERE (job_name like '%" . $jobName . "%' or job_id = '" . $jobName . "')";
            if (!empty($hcType)) {
                $auditListSql .= " order by CONVERT(job_name USING gbk) asc limit " . ($pageNum - 1) * $pageSize . "," . $pageSize;
            } else {
                $auditListSql .= " AND state =1 order by CONVERT(job_name USING gbk) asc limit " . ($pageNum - 1) * $pageSize . "," . $pageSize;
            }
        }
        $data       = $this->getDI()->get('db_rby')->query($auditListSql);
        $returnData = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        return $returnData;
    }

    /**
     * @description: 岗位名称检索 目前只在 jd 列表使用
     * @Access  public
     * @Param   request
     * @Return  array
     * <AUTHOR> L.J
     * @time       : 2021/6/30 17:50
     */
    public function jobListSearch($paramIn = [])
    {
        $jobName  = isset($paramIn['job_name']) ? $paramIn['job_name'] : '';
        $hcType   = isset($paramIn['hc_type']) ? $paramIn['hc_type'] : '';
        $noPage   = $paramIn['no_page'];
        $pageSize = $noPage ? $paramIn['page_size'] : 50000;
        $pageNum  = $paramIn['page_num'];
        $jobName  = addcslashes($jobName, "`\`");


        $jobName      = str_replace("'", '', $jobName);
        $auditListSql = " 
                            --
                            SELECT detail.job_id,detail.job_name,'' AS job_description,'' AS department_id,'' AS position_id, '' AS report_superior_id
                            FROM `hr_job_department_relation` AS main
                            LEFT JOIN `hr_jd` AS detail ON main.jd_id=detail.job_id WHERE detail.job_name like '%" . $jobName . "%'";
        if (!empty($hcType)) {
            $auditListSql .= " GROUP BY detail.job_id order by CONVERT(detail.job_name USING gbk) asc limit " . ($pageNum - 1) * $pageSize . "," . $pageSize;
        } else {
            $auditListSql .= " AND detail.state =1 GROUP BY detail.job_id order by CONVERT(detail.job_name USING gbk) asc limit " . ($pageNum - 1) * $pageSize . "," . $pageSize;
        }

        $data       = $this->getDI()->get('db_rby')->query($auditListSql);
        $returnData = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        return $returnData;
    }

    /**
     * 岗位删除
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function jobDel($paramIn = [])
    {
        $jobId = $paramIn['job_id'];
        $this->db->begin();
        $data = $this->db->updateAsDict(
            'hr_jd',
            [
                'state' => 2,
            ],
            'job_id = ' . $jobId
        );
        if (!$data) {
            $this->db->rollback();
            return false;
        }
        $this->db->commit();
    }

    /**
     * 获取HC和JD关联信息
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function jdHcDetail($paramIn = [])
    {
        $jobId   = $paramIn['job_id'];
        $jdHcSql = "SELECT * FROM `hr_hc` WHERE job_id =" . $jobId;
        $data    = $this->getDI()->get('db_rby')->query($jdHcSql);
        $jdData  = $data->fetch(\Phalcon\Db::FETCH_ASSOC);
        return $jdData;
    }

    /**
     *
     * 给backyard 提供的 所有jd列表接口
     * @param $paramIn
     * @return array
     */
    public function jdListForBy($paramIn)
    {
        $staff_id      = $paramIn['staff_id'];
        $noPage        = $paramIn['no_page'] ?? false;
        $pageSize      = $noPage ? $paramIn['page_size'] : 10000;
        $pageNum       = $paramIn['page_num'] ?? 1;

        //[1]查询申请人部门
        //获取jd列表
        $data = HrJdModel::find([
            'conditions' => 'state = 1 ',
            'order' => 'created_at desc',
            'columns' => 'job_id,job_name,department_id,sort',
            'limit' => $pageSize,
            'offset' => ($pageNum - 1) * $pageSize
        ])->toArray();

        //处理数据
        if (!empty($data)) {
            //查询部门数据
            $departmentSql  = "SELECT id,`name` FROM `sys_department` where deleted = 0";
            $departmentObj  = $this->getDI()->get('db_fle')->query($departmentSql);
            $departmentData = $departmentObj->fetchAll(\Phalcon\Db::FETCH_ASSOC);

            $department = array_column($departmentData, 'name', 'id');
            foreach ($data as &$v) {
                $v['department_name'] = isset($department[$v['department_id']]) ? $department[$v['department_id']] : '';
            }
        }
        return $data;
    }

    /**
     * 获取部门id
     * @param $staff_id
     * @return array
     */
    public function getDepartmentList($staff_id): array
    {
        //[1]查询申请人部门
        $staffInfo = StaffInfoModel::findFirst([
            'conditions' => 'id = :staff_info_id:',
            'bind' => [
                'staff_info_id' => $staff_id
            ]
        ]);

        if (empty($staffInfo)) {
            return [];
        }

        //查询是否为CEO或CEO的助理
        $CAssList = SysDepartmentModel::findFirst([
            'conditions' => '(manager_id = :manager_id: or assistant_id = :assistant_id:) and type = 5 and deleted = 0',
            'bind' => [
                'manager_id'    => $staff_id,
                'assistant_id'  => $staff_id
            ],
            'columns' => 'id,name'
        ]);
        $role_ids = (new StaffRepository())->getStaffRoleIds($staff_id);

        //获取CLevel负责部门
        $CLevelDeptList = SysDepartmentModel::find([
            'conditions' => 'type = 4 and deleted = 0',
            'columns' => "id,name"
        ])->toArray();

        //拥有全部权限的人
        //(1) 7525 国际人力经理(35) 开放这个角色的可选JD和可选部门的限制，可以选择全部的JD和部门
        //(2) CEO + CEO助理s
        //(3) 属于CEO部门的人
        if (!empty($CAssList) || in_array(35, $role_ids) || isset($staffInfo->department_id) && $staffInfo->department_id == 999) {
            $deptList = SysDepartmentModel::find([
                'conditions' => 'deleted = 0',
                'columns' => "id,name"
            ])->toArray();
            $returnIds = array_column($deptList, 'id');
        } else {

            //拥有部分权限的人
            //(1)C-level(除CEO或CEO的助理外)
            $CList = SysDepartmentModel::find([
                'conditions' => '(manager_id = :manager_id: or assistant_id = :assistant_id:) and type = 4 and deleted = 0',
                'bind' => [
                    'manager_id'    => $staff_id,
                    'assistant_id'  => $staff_id
                ],
                'columns' => "id,name"
            ])->toArray();

            $CList          = array_column($CList, 'id');
            $CLevelDeptList = array_column($CLevelDeptList, 'id');
            if (!empty($CList) || in_array($staffInfo->department_id, $CLevelDeptList)) { //获取group_boss_id
                if (!empty($CList) && in_array($staffInfo->department_id, $CLevelDeptList)) {
                    $deptArr = array_merge($CList, [$staffInfo->department_id]);
                } else if (!empty($CList)) {
                    $deptArr = $CList;
                } else {
                    $deptArr = [$staffInfo->department_id];
                }

                $CDeptList = SysDepartmentModel::find([
                    'conditions' => "(group_boss_id in({group_boss_id:array}) or id in({dept_ids:array})) and deleted = 0",
                    'bind' => [
                        'group_boss_id'    => $deptArr,
                        'dept_ids'         => $deptArr,
                    ]
                ]);
                if (!empty($CDeptList)) {
                    $CDeptListArr = $CDeptList->toArray();
                    $CDeptListIds = array_column($CDeptListArr, 'id');
                } else {
                    $CDeptListIds = [];
                }
            } else {
                $CDeptListIds = [];
            }

            //GM
            $GMList = SysDepartmentModel::find([
                'conditions' => '(manager_id = :manager_id: or assistant_id = :assistant_id:) and type = 1 and deleted = 0',
                'bind' => [
                    'manager_id'    => $staff_id,
                    'assistant_id'  => $staff_id
                ],
                'columns' => "id,name,ancestry_v3"
            ])->toArray();
            $CGMDeptList = SysDepartmentModel::find([
                'conditions' => 'type = 1 and deleted = 0',
                'columns' => "id,name,ancestry_v3"
            ])->toArray();
            $GMList      = array_column($GMList, 'id');
            $CGMDeptList = array_column($CGMDeptList, 'id');

            if (!empty($GMList) || in_array($staffInfo->department_id, $CGMDeptList)) {
                if (!empty($GMList) && in_array($staffInfo->department_id, $CGMDeptList)) {
                    $deptArr = array_merge($GMList, [$staffInfo->department_id]);
                } else if (!empty($GMList)) {
                    $deptArr = $GMList;
                } else {
                    $deptArr = [$staffInfo->department_id];
                }
                //获取所有子部门
                $depIds  = SysDepartmentModel::find([
                    'conditions' => 'company_id in({company_id:array}) and deleted = 0',
                    'bind' => [
                        'company_id' => $deptArr,
                    ],
                    'columns' => "id,name,ancestry_v3"
                ])->toArray();
                $GMListIds = array_column($depIds, 'id');
            } else {
                $GMListIds = [];
            }
            //取并集
            $returnIds = array_merge($GMListIds, $CDeptListIds);
        }

        //部门负责人 & 部门负责人的助理
        $deptManagerList = SysDepartmentModel::find([
            'conditions' => '(manager_id = :manager_id: or assistant_id = :assistant_id:) and type IN(2,3) and deleted = 0',
            'bind' => [
                'manager_id'    => $staff_id,
                'assistant_id'  => $staff_id
            ]
        ]);
        if (!empty($deptManagerList)) {
            $deptListArr = $deptManagerList->toArray();
            $deptListIds = array_column($deptListArr, 'id');
        } else {
            $deptListIds = [];
        }

        if(!empty($staffInfo->department_id)) {
            $deptListIds = array_merge($deptListIds, [$staffInfo->department_id]);
        }

        if (!empty($deptListIds)) {
            $departmentSet = [];

            foreach ($deptListIds as $deptId) {
                $data = SysDepartmentModel::findFirst([
                    'conditions' => "id = :department_id:",
                    'bind'       => [
                        'department_id' => $deptId
                    ],
                    'columns' => "id,level,ancestry_v3,type"
                ]);
                if (!(isset($data->ancestry_v3) && $data->ancestry_v3)) {
                    //记录错误数据
                    $this->getDI()->get('logger')->write_log("getDepartmentList deptId:" . $deptId,'info');
                    continue;
                }
                $depArr = explode('/', $data->ancestry_v3);
                if (isset($data->type) && $data->type == 3) { //直属部门

                    //获取部门链中CLevel以下部门
                    //例如：999/333/146/147/148/149 ==> [999,222,333,444] ==> [146,147,148,149]
                    //==> 146
                    $depAncestryChain  = array_diff($depArr, array_merge($CLevelDeptList, [999]));
                    $tmpDept = array_shift($depAncestryChain);
    
                    $this->getDI()->get('logger')->write_log("getDepartmentList depAncestryChain:" . json_encode($depAncestryChain),'info');
                    
                    //找出部门链中的一级部门
                    $levelOneDepartment = SysDepartmentModel::findFirst([
                        'conditions' => "id = :department_id:",
                        'bind'       => [
                            'department_id'   => $tmpDept,
                        ],
                        'columns' => "id,name,ancestry_v3"
                    ]);

                    //获取一级部门的部门链
                    $depAncestryChain  = $levelOneDepartment->ancestry_v3 ?? '';
                } else { //非直属部门

                    //找出部门链中的一级部门
                    $levelOneDepartment = SysDepartmentModel::findFirst([
                        'conditions' => "id in ({departmentIds:array}) and level = 1",
                        'bind'       => [
                            'departmentIds'   => $depArr,
                        ],
                        'columns' => "id,name,ancestry_v3"
                    ]);

                    //获取一级部门的部门链
                    $depAncestryChain  = $levelOneDepartment->ancestry_v3 ?? '';
                }
                //获取所有子部门
                $tmpDepartmentSet = SysDepartmentModel::find([
                    'conditions' => "ancestry_v3 like :chain_child: or ancestry_v3 = :chain:",
                    'bind'       => [
                        'chain_child'   => $depAncestryChain . '/%',
                        'chain'         => $depAncestryChain
                    ],
                    'columns' => "id"
                ])->toArray();

                $this->getDI()->get('logger')->write_log("getDepartmentList tmpDepartmentSet:" . json_encode($tmpDepartmentSet),'info');
                
                $departmentSet = array_merge($departmentSet, array_column($tmpDepartmentSet, 'id'));
            }
        }

        // 如果当前用户是WFM部门，则把Network Operations部门合并进来
        $department_repository = new DepartmentRepository();

        $envModel = new SettingEnvServer();
        $departId = $envModel->getSetVal('dept_network_operations_id');
	    //Network Bulky Operations
	    $bulky_departId = $envModel->getSetVal('dept_network_bulky_operations_id');
	    
        $departWorkforceId = $envModel->getSetVal('dept_workforce_management_id');
        
        // 获取WFM部门及其子部门
        $wfm_department_list = $department_repository->getDepartmentListById((int)$departWorkforceId, true);

        if (!empty($staffInfo->department_id) && in_array($staffInfo->department_id, $wfm_department_list)) {
            // 获取Network Operations部门及其子部门
            $network_operations_ids = $department_repository->getDepartmentListById((int)$departId, true);
	        //获取Network Bulky Operations部门及其子部门列表
	        $network_bulky_operations_department_ids = $department_repository->getDepartmentListById((int)$bulky_departId, true);
	        
            $departmentSet = array_merge($departmentSet ?? [], $network_operations_ids ?? []);
	        $departmentSet = array_merge($departmentSet ?? [], $network_bulky_operations_department_ids ?? []);
        }

        
        $departmentSet = array_unique(array_merge($departmentSet ?? [], $returnIds));
       
        $this->getDI()->get('logger')->write_log("getDepartmentList departmentSet:" . json_encode($departmentSet),'info');
        
        $departmentSet = array_values($departmentSet);
        return $departmentSet;
    }


}
