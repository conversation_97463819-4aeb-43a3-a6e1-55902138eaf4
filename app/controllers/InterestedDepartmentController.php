<?php

namespace FlashExpress\bi\App\Controllers;

use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\Server\InterestedDepartmentServer;
use WebGeeker\Validation\Validation;
use Exception;

class InterestedDepartmentController extends Controllers\ControllerBase
{
    protected $server;
    protected $paramIn;
    
    public function initialize()
    {
        parent::initialize();
        $this->paramIn = $this->request->getPost();
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
    }
    
    public function onConstruct()
    {
        parent::onConstruct();
    }

    /**
     * 获取学校列表
     */
    public function getListAction()
    {
        $params['keyword'] = $this->request->get('keyword', 'trim');
        $params['pageSize'] = $this->request->get('pageSize', 'trim');
        $params['pageNum']  = $this->request->get('pageNum', 'trim');
    
        $validation = [];
        if (!empty($params['keyword'])) {
            $validation['staff_info_id'] = 'StrLenGeLe:0,200|>>>:' . $this->t->_('input_param_nonstandard', ['field' => 'keyword']);
        }

        $this->validateCheck($params, $validation);

        //更新权限
        $result = (new InterestedDepartmentServer())->getList($params);
        $this->jsonReturn($result);
    }

    /**
     * 更新学校列表
     */
    public function saveAction()
    {
        $params['id'] = $this->request->get('id', 'int');
        $params['name'] = $this->request->get('name', 'trim');

        $validation['name'] = 'Required|StrLenGeLe:1,200|>>>:' . $this->t->_('input_param_nonstandard', ['field' => 'name']);

        $this->validateCheck($params, $validation);

        //更新权限
        if ((new InterestedDepartmentServer())->save($params)) {
            $this->jsonReturn($this->checkReturn(1));
        }

        throw new Exception('school inseart Error');
    }
}