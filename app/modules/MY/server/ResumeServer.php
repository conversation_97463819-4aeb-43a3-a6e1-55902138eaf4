<?php

namespace FlashExpress\bi\App\Modules\MY\Server;

use app\models\manage\HrOperateLogs;

use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\enumsMy;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\HrOfferDefaultSalaryModel;
use FlashExpress\bi\App\Models\backyard\HrQuestionsModel;
use FlashExpress\bi\App\Models\backyard\HrResumeModel;
use FlashExpress\bi\App\Models\backyard\HrStaffAnnexInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffItemsModel;
use FlashExpress\bi\App\Models\backyard\SysManagePieceModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Repository\BlacklistRepository;
use FlashExpress\bi\App\Repository\DepartmentRepository;
use FlashExpress\bi\App\Repository\EntryRepository;
use FlashExpress\bi\App\Repository\ResumeRepository;

use FlashExpress\bi\App\Server\LogServer;
use FlashExpress\bi\App\Server\OutsourcingBlackListServer;
use FlashExpress\bi\App\Server\ResumeServer as BaseResumeServer;
use FlashExpress\bi\App\Server\ResumeVerificationServer;
use FlashExpress\bi\App\Server\SettingEnvServer;
use Phalcon\Db\Column;
use Phalcon\DiInterface;
use FlashExpress\bi\App\Modules\MY\library\enums as myEnums;

class ResumeServer extends BaseResumeServer
{

    // 区域定义
    public $areas = [
        1 => 'N',
        2 => 'C',
        3 => 'S',
        4 => 'EC',
        5 => 'EM',
    ];

    public function __construct($lang = 'en')
    {
        parent::__construct($lang);

    }


    /**
     * 简历-完善状态
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function uploadPerfect($resumeInfo = [], $source = '')
    {
        $resumeId = $resumeInfo['id'] ?? 0;

        if (!$resumeId) {
            throw new ValidationException(self::$t->_('resume_info_non_existent'));
        }
        $resume_data = $this->getResumeBaseInfo($resumeId);
        if (empty($resume_data)){
            throw new ValidationException(self::$t->_('resume_err_1'));
        }
        

        //检测黑名单
        $isBalcklist = (new BlacklistRepository())->blacklistCheck([
            "resume_id" => $resumeId,
        ]);

        if ($isBalcklist > 0) {
            throw new ValidationException(self::$t->_('7500'));
        }
        (new OutsourcingBlackListServer())->check($resume_data['credentials_num'] ?? '','winhr',true,$this->lang);

        /* 获取相关附件详情 */
        $annexData            = (new ResumeRepository())->annexInfo($resumeId);


        //region 业务判断必须上传的选项
        //1  /resume/resumeInfo接口 job_category字段 /1摩托车快递员 2皮卡车快递员 3非快递员  4 其他 5自驾车快递员
        //2  /resume/annexInfo 接口     resumeInfo字段下   sex  性别    military_service兵役   criminal_record犯罪记录     car_owner是否是车主


        $ebilityInfo = (new ResumeRepository())->ebilityInfo($resumeId);
        $import_img  = [];
        if (isset($resumeInfo['jd_type'])) {
            $_type = $resumeInfo['jd_type'];
            //必填项目
            $import_img['file_type'][1] = 1;//半身照必填
            $import_img['file_type'][2] = 1;//身份证反面
            //$import_img['file_type'][3] = 1;//身份证反面
            $import_img['file_type'][4] = 1;//户籍照第一页面
            $import_img['file_type'][5] = 1;//户籍照应聘者本人信息
            $import_img['file_type'][7] = 1;//成绩单

            $import_img['file_type'][10] = 1;//驾驶证正面
            $import_img['file_type'][11] = 1;//驾驶证反面
            $import_img['file_type'][12] = 1;//车辆登记
            $import_img['file_type'][14] = 0;//车辆使用授权//错误定义 但就这么写的

            //Van(皮卡车）类型快递员，且驾照类型是 GDL驾照类型的  GDL驾照类型正面必填
            if($_type == '2' && $resumeInfo['driving_license_type'] == 5){
                $import_img['file_type'][20] = 1;
            }


            //马来需求-不需要户籍照这俩附件（江奇）https://l8bx01gcjr.feishu.cn/docs/doccn9fC2Z40nhH75FclcJecRUb#
            $import_img['file_type'][4] = 0;//户籍照第一页面
            $import_img['file_type'][5] = 0;//户籍照应聘者本人信息

            $import_img['file_type'][17] = 0;//残疾人证明-正面
            $import_img['file_type'][18] = 0;//残疾人证明-反面

            if ((new ResumeVerificationServer())->checkDeformityByCvid($resumeId)) {
                $import_img['file_type'][17] = 1;//残疾人证明-正面
                $import_img['file_type'][18] = 1;//残疾人证明-反面
            }


            //聂梦轩需求：公司车辆不需要验证车辆使用授权
            if (!empty($ebilityInfo) &&  $ebilityInfo['car_owner'] == enums::$car_own_type['borrow_car']) {
                $import_img['file_type'][14] = 1;
            }

            switch ($_type) {
                //1摩托车快递员
                case '1':
                    $import_img['file_type'][7] = 0;//成绩单
                    break;
                //2皮卡车快递员
                case '2':
                case '5':
                case '7':
                    $import_img['file_type'][7] = 0;//成绩单
                    break;
                //3非快递员
                case '3':
                    $import_img['file_type'][10] = 0;//驾驶证正面
                    $import_img['file_type'][11] = 0;//驾驶证反面
                    $import_img['file_type'][12] = 0;//车辆登记
                    $import_img['file_type'][14] = 0;//车辆使用授权
                    break;
                //4 其他
                case '4':
                    break;
            }

            //调整附件验证-删除成绩报告单
            if ($resumeInfo['form_type'] == HrQuestionsModel::TYPE_EXTENSION_NETWORK_FIRST_LINE_JD) {
                $import_img['file_type'][4]  = 0;//户籍照（户籍第一页）
                $import_img['file_type'][5]  = 0;//户籍照（应聘者本人信息页）
                $import_img['file_type'][7]  = 0;//成绩报告单 MY PH TH删除
                $import_img['file_type'][12] = 0;//车辆登记
            }

            //特殊职位不检查驾驶证反面
            if (in_array($resumeInfo['job_id'], [
                enumsMy::JD_VAN,
                enumsMy::JD_BIKE,
                enumsMy::JD_CAR,
                enumsMy::JD_DCO,
                enumsMy::JD_BDC_DRIVER,
                enumsMy::JD_FLEET_DRIVER,
            ])) {
                $import_img['file_type'][11] = 0;//驾驶证反面
            }

            foreach ($annexData as $key => $item) {
                if(isset($import_img['file_type'][$item['file_type']])){
                    $import_img['file_type'][$item['file_type']] = 0;
                }
            }

            if (array_sum($import_img['file_type']) > 0) {
                $this->getDI()->get('logger')->write_log("uploadPerfect:".json_encode(['resume_id'=>json_encode($resumeId)])."，参数： ". json_encode($import_img), 'info');
                return ['code' => 0, 'msg' => self::$t->_('4188'), 'data' => $import_img];
            }
        }

        return ['code' => 1, 'msg' => 'success','data' => []];
    }

    /**
     * 快递员职位默认薪资
     * @param $resumeId
     * @param $hc_id
     * @param string $work_time
     * @param int $project_num
     * @return array
     */
    public function courierSalary($resumeId, $hc_id, $work_time = '', $project_num = 0 , $company = '')
    {
        $hc_info = $this->getHcInfo($hc_id);

        $jobTitle      = $hc_info['job_title'];
        $province_code = $hc_info['province_code'];
        $worknode_id   = $hc_info['worknode_id'];
        $department_id = $hc_info['department_id'];
        $hire_type     = $hc_info['hire_type'] ?? 0;

        $departmentRepository = new DepartmentRepository();

        $selectedStoreIds = SettingEnvServer::getSetVal('selected_sp_store_ids');
        $selectedStoreIds = explode(',', $selectedStoreIds);
        $msStoreIds = SettingEnvServer::getSetVal('ms_store_ids');
        $msStoreIds = explode(',', $msStoreIds);

        $hubDepartmentId = SettingEnvServer::getSetVal('dept_os_my_hub_management_id');
        $networkDepartmentId = SettingEnvServer::getSetVal('dept_network_management_id');

        $resumeRepository = new ResumeRepository();
        $resumeInfo = $resumeRepository->resumeInfo($resumeId);

        // 由配置读取改为数据流读取
        $HrOfferDefaultSalary = HrOfferDefaultSalaryModel::find(
            [
                'conditions' => "deleted = :deleted:",
                'bind' => [
                    'deleted'          => 0,
                ],
                'columns' => ['province_code','base_salary','position_allowance']
            ]
        )->toArray();
        $map = [];
        if ($HrOfferDefaultSalary){
            foreach ($HrOfferDefaultSalary as $k=>$v){
                $map[$v["province_code"]]["base"] = $v["base_salary"];
                $map[$v["province_code"]]["allowance"] = json_decode($v["position_allowance"],true);
            }
        }else{
            $this->getDI()->get('logger')->write_log("courierSalary-HrOfferDefaultSalary is empty" , 'error');
        }
        $vanCourierHasGDL = (new OfferServer())->checkVanCourierIsHasGDL($resumeInfo['credentials_num'] , trim($company ?? ''));

        $result = [
            'money' => $map['TH00']['base'],
            'rental' => $map['TH00']['allowance'][$jobTitle] ?? 0,
            'food' => 4500,
            'mobile_allowance' => 0,
            'attendance_allowance' => 0,
            'fuel_allowance' => 0,
            'car_allowance' => 0,
            'vehicle_allowance' => 0,
            'site_allowance' => 0,
            'gdl_allowance' => 0,
            'internship_salary' => 4500, //实习期工资
        ];
        if (isset($map[$province_code])) {
            $result['money'] = $map[$province_code]['base'];
            $result['rental'] = $map[$province_code]['allowance'][$jobTitle] ?? 0;
        }

        if (in_array($jobTitle, [
            enums::$job_title['Bike Courier'],
            enums::$job_title['Van Courier'],
            enums::$job_title['Car Courier'],
            enums::$job_title['Branch Supervisor'],
            enums::$job_title['DC Officer'],
        ])) {
            $result['mobile_allowance'] = 5000;
        }

        if (in_array($jobTitle, [
            enums::$job_title['Bike Courier']
        ])) {
            $result['money'] = 150000;
        }

        if (in_array($jobTitle, [
            enums::$job_title['Car Courier'] ,
            enums::$job_title['Van Courier'],
            myEnums::$job_title['hub_driver']
        ])) {
            $result['money'] = 0;
            $result['vehicle_allowance'] = 0;
            $result['car_allowance'] = 0;
            $result['gdl_allowance'] = 0;
        }

        if (in_array($jobTitle, [
            myEnums::$job_title['sales_officer'],
            myEnums::$job_title['sales_supervisor'],
            myEnums::$job_title['sales_specialist'],
        ])) {
            $result['sales_travelling_allowance'] = 30000;
        }

        if (in_array($jobTitle, [
            enums::$job_title['Car Courier']
        ])) {
            if (isset($resumeInfo['car_type']) && $resumeInfo['car_type'] == enums::CAR_TYPE_4) {
                $result['money'] = 170000;

                $sysstore = SysStoreModel::findFirst([
                    'conditions' => ' id = :id:',
                    'bind' => ['id' => $worknode_id]
                ]);
                if ($sysstore) {
                    $sysstore = $sysstore->toArray();
                    if ($sysstore['manage_piece']) {
                        $piece = SysManagePieceModel::findFirst([
                            'conditions' => ' id = :id: ',
                            'bind' => [
                                'id' => $sysstore['manage_piece']
                            ]
                        ]);
                        if ($piece) {
                            $piece = $piece->toArray();
                            if($piece['name'] && strpos($piece['name'],'C') === 0) {
                                $result['car_allowance'] = 100000;
                            } else {
                                $result['car_allowance'] = 70000;
                            }
                        }
                    }
                }

            }

            if (isset($resumeInfo['car_type']) && $resumeInfo['car_type'] == enums::CAR_TYPE_5) {
                $result['money'] = 170000;
                $result['car_allowance'] = 100000;
            }
        }

        if (in_array($jobTitle, [
            enums::$job_title['Van Courier']
        ])) {
            //Van Project-Van
            if (isset($resumeInfo['car_type']) && $resumeInfo['car_type'] == enums::CAR_TYPE_1) {
                $result['money'] = 170000;
                $result['gdl_allowance'] = 0;
            }
            //Panel Van
            if (isset($resumeInfo['car_type']) && $resumeInfo['car_type'] == enums::CAR_TYPE_2) {
                $result['money'] = 170000;
                $result['vehicle_allowance'] = 190000;
                $result['gdl_allowance'] = 0;
            }
            //Window Van
            if (isset($resumeInfo['car_type']) && $resumeInfo['car_type'] == enums::CAR_TYPE_3) {
                $result['money'] = 170000;
                $result['vehicle_allowance'] = 170000;
            }
            //Van Project-Lorry
            if (isset($resumeInfo['car_type']) && $resumeInfo['car_type'] == enums::CAR_TYPE_9) {
                $result['money'] = 150000;
                $result['vehicle_allowance'] = 0;
                $result['gdl_allowance'] = 0;
                $result['mobile_allowance'] = 0;
            }
        }

        if (in_array($jobTitle, [
            enums::$job_title['DC Officer']
        ])) {
            $result['attendance_allowance'] = 15000;
        }

        //区域津贴

        if($jobTitle == myEnums::$job_title['hub_operator'] && $department_id == myEnums::$dept_map['hub_operations'] && in_array($worknode_id,myEnums::$site_allowance_storeid) ){
            $result['site_allowance'] = 30000;
        }

        //my network快递员薪资默认值
        //https://flashexpress.feishu.cn/wiki/JwQ9wWeCRitECXkmOpQcy5Fjn8b
        if (in_array($department_id, $departmentRepository->getDepartmentListById($networkDepartmentId))) {
            if ($jobTitle == enums::$job_title['Bike Courier']) {
                $result['money']            = 170000;
                $result['fuel_allowance']   = 0;
                $result['mobile_allowance'] = 0;
            }

            if ($jobTitle == enums::$job_title['DC_officer']) {
                $result['money']            = 170000;
                $result['mobile_allowance'] = 5000;
                $result['fuel_allowance']   = 600;
            }

            //Car Courier(Car)
            if (in_array($jobTitle, [enums::$job_title['Car Courier']]) && $resumeInfo['car_type'] == enums::CAR_TYPE_4) {
                $result['car_allowance']    = 0;
                $result['money']            = 170000;
                $result['mobile_allowance'] = 0;
            }

            //Courier(MPV)
            if (in_array($jobTitle, [enums::$job_title['Car Courier']]) && $resumeInfo['car_type'] == enums::CAR_TYPE_5) {
                $result['car_allowance']    = 0;
                $result['money']            = 170000;
                $result['mobile_allowance'] = 0;
            }

            if (in_array($jobTitle, [
                enums::$job_title['Van Courier']
            ])) {
                //Van Courier(Window)
                if (in_array($resumeInfo['car_type'], [enums::CAR_TYPE_3])) {
                    $result['money']             = 170000;
                    $result['mobile_allowance']  = 0;
                    $result['gdl_allowance']     = 0;
                //Panel Van
                } else if (in_array($resumeInfo['car_type'], [enums::CAR_TYPE_2])) {
                    $result['money']             = 170000;
                    $result['mobile_allowance']  = 0;
                    $result['gdl_allowance']     = 0;
                //Van Project-Van
                } else if (in_array($resumeInfo['car_type'], [enums::CAR_TYPE_1])) {
                    $result['money']            = 170000;
                    $result['mobile_allowance'] = 0;
                    $result['gdl_allowance']    =  $vanCourierHasGDL ? 800 : 0;
                //Van Project-Lorry
                }else if (in_array($resumeInfo['car_type'], [enums::CAR_TYPE_9])){
                    $result['money']             = 170000;
                    $result['mobile_allowance']  = 0;
                    $result['gdl_allowance']     =$vanCourierHasGDL ? 800 : 0;
                }
                //处理货车补贴 vehicle_allowance
                $storeInfo = (new SysStoreModel())->getSysStoreInfo($worknode_id);

                $result['vehicle_allowance'] = 0;

                if (!in_array($hire_type,HrStaffInfoModel::$agentTypeTogether)) {
                    if (isset($storeInfo['sorting_no']) && strtoupper($storeInfo['sorting_no']) == 'C') {
                        $result['vehicle_allowance'] = 6000;
                    } else {
                        $result['vehicle_allowance'] = 4000;
                    }
                }
            }

            //BDC Driver 配置
            if ($jobTitle == enumsMy::JOB_TITLE_BDC_DRIVER) {
                $result['rental']           = 0;
                $result['food']             = 0;
                $result['money']            = 200000;
                $result['gdl_allowance']    = 800;
                $result['mobile_allowance'] = 5000;
            }

        } else if (in_array($department_id, $departmentRepository->getDepartmentListById($hubDepartmentId))) {
            if (in_array($jobTitle, [enums::$job_title['hub_operator'], enums::$job_title['onsite_staff'], myEnums::$job_title['hub_driver']])) {
                $result['fuel_allowance'] = 1200;
                $result['money'] = 250000;
                $result['gdl_allowance'] = 800;
                $result['mobile_allowance'] = 5000;
            }
            if ($jobTitle == myEnums::$job_title['fleet_driver']) {
                $result['gdl_allowance'] = 800;
            }
            if (in_array($worknode_id, $msStoreIds)) {

                if (in_array($jobTitle, [enums::$job_title['hub_operator'], myEnums::$job_title['hub_team_leader'], myEnums::$job_title['hub_driver']])) {
                    $result['site_allowance'] = 1200;
                }
            }
        }

        // 客户端要这个遍历用 要求trial_salary和money 一致
        $result['trial_salary'] = $result['money'];
        $return['salary'] = $result;

        $return['in_salary_range'] = 0;
        $resumeApprove = $this->getLastSalaryApproveData($resumeId);
        if ($resumeApprove && $resumeApprove['in_salary_range']) {

            $return['in_salary_range'] = $resumeApprove['in_salary_range'];
        }
        return $return;

    }

    /**
     * 根据网点获取对应 所属区域
     * @return mixed
     */
    public function getSortingNoByStoreInfo($storeInfo)
    {
        // 获取区域
        return $storeInfo['store_sorting_no'] ?? '';
    }

    /**
     * ai 审核身份证
     * @param $paramIn
     * @return array
     */
    public function aiIdCardAudit($paramIn) {
        $staff_info_id = $paramIn['staff_id'];
        $file_url = $paramIn['file_url'];
        $id_card_audit_param = [
            'staff_id' => $staff_info_id,
            'file_url' => $file_url,
        ];

        //马来原逻辑不变，新增逻辑：除马来国籍外其他国籍走人工审核逻辑
        $hr_staff_item = HrStaffItemsModel::findFirst([
            'conditions' => "staff_info_id = :staff_info_id: and item = 'NATIONALITY'",
            'bind' => [
                'staff_info_id' => $staff_info_id,
            ]
        ]);
        $hr_staff_item = empty($hr_staff_item) ? [] : $hr_staff_item->toArray();
        $nationality = $hr_staff_item['value']  ?? 0;

        if($nationality == enums::IS_MY_NATIONALITY) {
            $apiClient = new ApiClient('by_rpc', '', 'AiIdCardAudit', $this->lang);
            $apiClient->setParams($id_card_audit_param);
            $result = $apiClient->execute();
            $this->getDI()->get('logger')->write_log("aiIdCardAudit 参数:" . json_encode($id_card_audit_param) . '结果：' . json_encode($result), 'info');
            if($result['result']['code'] == 0) {
                return $this->checkReturn(-3, $result['result']['msg']);
            }
            //0识别失败1识别成功
            if($result['result']['data']['code']== 1) {
                //识别成功
                $result['result']['data']['result']['staff_id'] = $staff_info_id;
                $submit_param = $result['result']['data']['result'];
                $submit_apiClient = new ApiClient('by_rpc', '', 'AiIdCardSubmit', $this->lang);
                $submit_apiClient->setParams($submit_param);
                $submit_result = $submit_apiClient->execute();
                $this->getDI()->get('logger')->write_log("aiIdCardAudit AiIdCardSubmit参数:" . json_encode($submit_param) . '结果：' . json_encode($submit_result), 'info');
//                    //1 审核成功 2 审核失败
//                    if($result['result']['data']['result']['status'] == 2) {
//                        $result['result']['data']['result']['msg'] = $this->getTranslation()->_('id_card_ai_audit_error');
//                    }

                //记录审核日志
//                $identity_annex_info = HrStaffAnnexInfoModel::findFirst([
//                    'conditions' => 'staff_info_id = :staff_info_id: and type = :type:',
//                    'bind' => [
//                        'staff_info_id' => $staff_info_id,
//                        'type' => HrStaffAnnexInfoModel::TYPE_ID_CARD,
//                    ]
//                ]);
//                //记录审核前状态
//                $audit_before_state = $identity_annex_info->audit_state ?? 0;
//                $audit_log = [
//                    'staff_info_id' => $staff_info_id,
//                    'audit_id' => 10000,
//                    'audit_name' => 'AI审核',
//                    'audit_before_state' => $audit_before_state,
//                    'audit_after_state' => $result['result']['data']['result']['status'],
//                ];
//                $audit_log_result = $this->getDI()->get('db')->insertAsDict('staff_identity_annex_audit_log', $audit_log);
//                if( !$audit_log_result ){
//                    $msg = "identity annex audit log insert fail:".var_export($audit_log,true).PHP_EOL;
//                    $this->getDI()->get("logger")->write_log($msg, 'info');
//                }

            }
        } else {
            $identity_annex_info = HrStaffAnnexInfoModel::findFirst([
                'conditions' => 'staff_info_id = :staff_info_id: and type = :type:',
                'bind' => [
                    'staff_info_id' => $staff_info_id,
                    'type' => HrStaffAnnexInfoModel::TYPE_ID_CARD,
                ]
            ]);
            if(empty($identity_annex_info)) {
                $identity_annex_info = new HrStaffAnnexInfoModel();
                $identity_annex_info->staff_info_id = $staff_info_id;
            }
            $identity_annex_info->audit_state = 0;
            $identity_annex_info->ai_audit_state = 2;
            $identity_annex_info->annex_path_front = $file_url;
            $identity_annex_info->ai_audit_state_date = date('Y-m-d H:i:s');
            if($identity_annex_info->save() !== true) {
                $this->getDI()->get('logger')->write_log('aiIdCardAudit 非马来国籍 保存失败，参数：'.json_encode($id_card_audit_param),'error');
                return $this->checkReturn(-3, 'error');
            } else {
                $this->getDI()->get('logger')->write_log('aiIdCardAudit 非马来国籍 保存成功，参数：'.json_encode($id_card_audit_param),'info');
                $result['result']['data'] = [
                    'code' => 1,
                    'msg' => '',
                    'result' => [
                        'status' => 2,
                        'upload_count' => 4,
                        'file_url' => $file_url,
                        'msg' => $this->getTranslation()->_('id_card_upload_success'),
                        'staff_info_id' => $staff_info_id
                    ]
                ];
            }
        }
        return $this->checkReturn($result['result']);
    }

    /**
     * By修改简历基本信息
     * @Access  public
     * @throws ValidationException
     */
    public function updateInfoBy($paramIn = [])
    {

        //获取简历 id
        $entry     = new EntryRepository();
        $entryInfo = $entry->getStaffIdInfo($paramIn);
        if (empty($entryInfo['resume_id'])) {
            throw new ValidationException($this->getTranslation()->_('4012'));
        }

        $resumeId = $entryInfo['resume_id'];

        $resumeArr = [
            'resume_id' => $resumeId,
        ];
        //校验上牌地点
        $resumeInfoArr = self::resumeInfo($resumeArr);
        $type          = $resumeInfoArr['data']['job_category'];
        if (in_array($type, [1, 2])) {
            if (empty($this->processingDefault($paramIn, 'place_cards'))) {
                throw new ValidationException($this->getTranslation()->_('4172'));
            }
        }

        $resumeParam['register_country']     = $this->processingDefault($paramIn, 'register_country', 2);  //户口所在地国家
        $resumeParam['register_house_num']   = $this->processingDefault($paramIn, 'register_house_num');  //户口所在地门牌号
        $resumeParam['register_village_num'] = $this->processingDefault($paramIn, 'register_village_num');  //户口所在地村号
        $resumeParam['register_village']     = $this->processingDefault($paramIn, 'register_village');  //户口所在地村庄
        $resumeParam['register_alley']       = $this->processingDefault($paramIn, 'register_alley');  //户口所在地巷子
        $resumeParam['register_street']      = $this->processingDefault($paramIn, 'register_street');   //户口所在地街道
        $resumeParam['register_government']  = $this->processingDefault($paramIn, 'register_government'); //户口所在地府
        $resumeParam['register_city']        = $this->processingDefault($paramIn, 'register_city');  //户口所在地市
        $resumeParam['register_town']        = $this->processingDefault($paramIn, 'register_town');  //户口所在地-镇
        $resumeParam['register_postcodes']   = $this->processingDefault($paramIn, 'register_postcodes');   //户口所在地邮编
        $resumeParam['fit']                  = $this->processingDefault($paramIn, 'fit', 2);  //居住地是否和户口所在地一致? 1=一致,0=不一致
        $resumeParam['permit_number']        = $this->processingDefault($paramIn, 'permit_number');  //工作证号
        $resumeParam['recruiter_id']         = $this->processingDefault($paramIn, 'recruiter_id', 2);


        $resumeParam['residence_country']     = $this->processingDefault($paramIn, 'residence_country', 2);  //居住地 国家
        $resumeParam['residence_government']  = $this->processingDefault($paramIn, 'residence_government'); //居住地 府
        $resumeParam['residence_city']        = $this->processingDefault($paramIn, 'residence_city');  //居住地 市
        $resumeParam['residence_postcodes']   = $this->processingDefault($paramIn, 'residence_postcodes');   //居住地邮编
        $resumeParam['residence_town']        = $this->processingDefault($paramIn, 'residence_town');  //户口所在地-镇
        $resumeParam['residence_detail_address'] = $this->processingDefault($paramIn, 'residence_detail_address');//居住地 详情
        $resumeParam['fund_num']              = $this->processingDefault($paramIn, 'fund_num');//公积金号码
        $resumeParam['cert_place']            = $this->processingDefault($paramIn, 'cert_place');//发证地
        $resumeParam['religion']              = $this->processingDefault($paramIn, 'religion');//宗教
        $resumeParam['expiration_date']       = $this->processingDefault($paramIn, 'expiration_date');//失效日期
        $resumeParam['race']                  = $this->processingDefault($paramIn, 'race');//种族
        $resumeParam['nickname']              = $this->processingDefault($paramIn, 'nickname');//昵称
        
        // 校验邮箱
        $paramIn['email'] = nameSpecialCharsRepalce($paramIn['email'] ?? '');
        if(empty($paramIn['email']) || !filter_var($paramIn['email'],FILTER_VALIDATE_EMAIL)){
            throw new ValidationException($this->getTranslation()->_('4109'));
        }
        $resumeParam['email']                 = $paramIn['email'];
        if (!empty($paramIn['staff_id'])) {
            $syncData = [
                'staff_info_id'        => $paramIn['staff_id'],
                'personal_email'       => $paramIn['email'],
            ];
            $ac = new ApiClient('hr_rpc', '', 'update_staff_info', $this->lang);
            $ac->setParams($syncData);
            $result = $ac->execute();
            $this->logger->write_log("updateInfoBy-同步hris返回信息 " . json_encode($result), 'info');
            if (!isset($result['result']['code']) || $result['result']['code'] != 1) {
                $this->getDI()->get('logger')->write_log("updateInfoBy-同步hris失败：" . json_encode($result));
            }
        }


        if (isset($paramIn['register_detail_address'])) {
            $resumeParam['register_detail_address'] = $this->processingDefault($paramIn, 'register_detail_address');
        }

        $resumeParam['id'] = $resumeId;
        $resumeId          = (new ResumeRepository())->updateResumeInfo($resumeParam);
        //添加日志
        (new LogServer())->addLog([
            'module_id'   => $resumeId,
            'module_type' =>  enums::$log_module_type['base_info'],
            'action'      => enums::$log_option['modify'],
            'data_after'  => $resumeParam,
        ]);

        // h5端过来的请求中会有这些字段信息
        if (isset($paramIn['place_cards'])) {
            $ebilityParam['place_cards'] = $this->processingDefault($paramIn, 'place_cards');
        }
        if (isset($paramIn['car_engine_number'])) {
            $ebilityParam['car_engine_number'] = $this->processingDefault($paramIn, 'car_engine_number');
        }
        if (isset($paramIn['car_owner'])) {
            $ebilityParam['car_owner'] = $this->processingDefault($paramIn, 'car_owner');
        }
        if (isset($paramIn['car_number'])) {
            $ebilityParam['car_number'] = $this->processingDefault($paramIn, 'car_number');
        }
        if (isset($paramIn['driver_number'])) {
            $ebilityParam['driver_number'] = $this->processingDefault($paramIn, 'driver_number');
        }
        if (isset($paramIn['car_engine_number'])) {
            $ebilityParam['car_engine_number'] = $this->processingDefault($paramIn, 'car_engine_number');
        }
        if (isset($paramIn['driving_license_type'])) {
            $ebilityParam['driving_license_type'] = $this->processingDefault($paramIn, 'driving_license_type');
        }
        if (isset($paramIn['computer_other'])) {
            $ebilityParam['computer_other'] = $this->processingDefault($paramIn, 'computer_other');
        }

        if (isset($ebilityParam)) {
            $ebilityParam['resume_id'] = $resumeId;
            $ebilityParam['is_log']    = 1;
            $ebilityParam['is_not_check_black']    = $paramIn['is_not_check_black'] ?? false;
            //插入经济与能力
            self::createEbility($ebilityParam);
        }
        $returnData['data'] = ['id' => $resumeId];
        return $this->checkReturn($returnData);
    }

    /**
     * 附件翻译
     * @return array
     */
    public function annexListLang(): array
    {
        $data = [
            'file_type1'  => [
                'title' => $this->getTranslation()->_('4151'),
                'text'  => $this->getTranslation()->_('4165'),
                'url'   => 'https://fle-staging-asset-internal.oss-ap-southeast-1.aliyuncs.com/backyardUpload/1559812569-2afe9c73999d4c4984878588370d4b7b.jpg',
            ],
            'file_type2'  => [
                'title' => $this->getTranslation()->_('4152'),
                'text'  => $this->getTranslation()->_('4152'),
                'url'   => 'https://fle-staging-asset-internal.oss-ap-southeast-1.aliyuncs.com/backyardUpload/1559812697-156b387c531943c09abb21662454aa7a.jpg',
            ],
            'file_type3'  => [
                'title' => $this->getTranslation()->_('4153'),
                'text'  => $this->getTranslation()->_('4153'),
                'url'   => 'https://fle-staging-asset-internal.oss-ap-southeast-1.aliyuncs.com/backyardUpload/1559812759-635cc86cb9bd445488242ba82259f2ef.jpg',
            ],
            'file_type4'  => [
                'title' => $this->getTranslation()->_('4154'),
                'text'  => $this->getTranslation()->_('4154'),
                'url'   => 'https://tc-static-asset-internal.flashexpress.com/workOrder/1715766335-0dfe926d811b4a24b79873c7ac6f190a.jpeg',
            ],
            'file_type5'  => [
                'title' => $this->getTranslation()->_('4155'),
                'text'  => $this->getTranslation()->_('4155_text'),
                'url'   => 'https://tc-static-asset-internal.flashexpress.com/workOrder/1715766335-0dfe926d811b4a24b79873c7ac6f190a.jpeg',
            ],
            'file_type6'  => [
                'title' => $this->getTranslation()->_('4156'),
                'text'  => $this->getTranslation()->_('4156'),
                'url'   => 'https://fle-staging-asset-internal.oss-ap-southeast-1.aliyuncs.com/backyardUpload/1559813010-291dae54378a41deab10942444094e47.jpg',
            ],
            'file_type7'  => [
                'title' => $this->getTranslation()->_('4157'),
                'text'  => $this->getTranslation()->_('4157'),
                'url'   => 'https://fle-staging-asset-internal.oss-ap-southeast-1.aliyuncs.com/backyardUpload/1559813062-9dca1378ba3644acabf8e812331a6e54.jpg',
            ],
            'file_type8'  => [
                'title' => $this->getTranslation()->_('4158'),
                'text'  => $this->getTranslation()->_('4158'),
                'url'   => 'https://fle-staging-asset-internal.oss-ap-southeast-1.aliyuncs.com/backyardUpload/1559813180-bd02cab70a7341dca0fa85814f4729f6.jpg',
            ],
            'file_type9'  => [
                'title' => $this->getTranslation()->_('4159'),
                'text'  => $this->getTranslation()->_('4165'),
                'url'   => 'https://fle-staging-asset-internal.oss-ap-southeast-1.aliyuncs.com/backyardUpload/1559812569-2afe9c73999d4c4984878588370d4b7b.jpg',
            ],
            'file_type10' => [
                'title' => $this->getTranslation()->_('4160'),
                'text'  => $this->getTranslation()->_('4160'),
                'url'   => 'https://tc-static-asset-internal.flashexpress.my/workOrder/1739348682-68f0727b6cae457cac1a9cff651a23b4.png',
            ],
            'file_type11' => [
                'title' => $this->getTranslation()->_('4161'),
                'text'  => $this->getTranslation()->_('4161'),
                'url'   => 'https://tc-static-asset-internal.flashexpress.my/workOrder/1739348682-68f0727b6cae457cac1a9cff651a23b4.png',
            ],
            'file_type12' => [
                'title' => $this->getTranslation()->_('4162'),
                'text'  => $this->getTranslation()->_('4162_text'),
                'url'   => 'https://fle-staging-asset-internal.oss-ap-southeast-1.aliyuncs.com/backyardUpload/1559813300-3742977730f9477085f70dea85281257.jpg',
            ],
            'file_type13' => [ //车辆照片
                'title' => $this->getTranslation()->_('4163'),
                'text'  => $this->getTranslation()->_('4163'),
                'url'   => 'https://tc-static-asset-internal.flashexpress.my/workOrder/1739348712-ced3b4736c404700b18c56b32bd1ded9.png',
            ],
            'file_type14' => [
                'title' => $this->getTranslation()->_('4164'),
                'text'  => $this->getTranslation()->_('4165'),
                'url'   => 'https://fle-staging-asset-internal.oss-ap-southeast-1.aliyuncs.com/backyardUpload/1559813336-a507472359014a9a930cbd331e4ca994.jpg',
            ],
            'file_type16' => [
                'title' => $this->getTranslation()->_('4166'),
                'text'  => $this->getTranslation()->_('4167'),
                'url'   => '',
            ],
            'file_type17' => [
                'title' => $this->getTranslation()->_('4168'),//残疾人证-正面
                'text'  => $this->getTranslation()->_('4168'),
                'url'   => 'https://fle-staging-asset-internal.oss-ap-southeast-1.aliyuncs.com/workOrder/1604903436-ea372fe9aa4b4552a4f324713a2cc181.png',
            ],
            'file_type18' => [
                'title' => $this->getTranslation()->_('4169'),//残疾人证-反面
                'text'  => $this->getTranslation()->_('4169'),
                'url'   => 'https://sai.flashexpress.com/workOrder/1604975937-7acc98e3a9cf472c808e8e5906171605.png',
            ],
            'file_type19' => [
                'title' => $this->getTranslation()->_('annex_4170'),//最高学历证书
                'text'  => $this->getTranslation()->_('annex_4170'),
                'url'   => 'https://tc-static-asset-internal.flashexpress.my/workOrder/1739348667-362ad135458a4ed4af8d77c1b792cceb.png',
            ],
            'file_type20' => [
                'title' => $this->getTranslation()->_('annex_4171'),//GDL驾照正面(马来新增）
                'text'  => $this->getTranslation()->_('annex_4171'),
                'url'   => 'https://tc-static-asset-internal.flashexpress.my/workOrder/1739348818-a106475361254ac0ac1f47b993222c36.png',
            ],
            'file_type21' => [
                'title' => 'OR',//GDL驾照正面(马来新增）
                'text'  => 'OR',
                'url'   => '',
            ],
            'file_type22' => [
                'title' => $this->getTranslation()->_('annex_commitment'),//GDL驾照正面(马来新增）
                'text'  => $this->getTranslation()->_('annex_commitment'),
                'url'   => '',
            ],
            // 社保卡
            'file_type27' => [
                'title' => $this->getTranslation()->_('annex_27'),
                'text'  => $this->getTranslation()->_('annex_27'),
                'url'   => 'https://fex-ph-asset-pro.oss-ap-southeast-1.aliyuncs.com/workOrder/1681181273-c2ca7afe2b94458b8cd5dc13c360ca8d.jpeg',
            ],
            // 医保卡
            'file_type24' => [
                'title' => $this->getTranslation()->_('annex_24'),
                'text'  => $this->getTranslation()->_('annex_24'),
                'url'   => 'https://fex-ph-asset-pro.oss-ap-southeast-1.aliyuncs.com/workOrder/1681181327-ae2057fb52e24448841e4e36a3374353.jpeg',
            ],
            // 公积金卡
            'file_type25' => [
                'title' => $this->getTranslation()->_('annex_25'),
                'text'  => $this->getTranslation()->_('annex_25'),
                'url'   => 'https://fex-ph-asset-pro.oss-ap-southeast-1.aliyuncs.com/workOrder/1681181306-6d53b2faa35f4e9aa232408975bc7f6e.jpeg',
            ],
            // 税卡
            'file_type26' => [
                'title' => $this->getTranslation()->_('annex_26'),
                'text'  => $this->getTranslation()->_('annex_26'),
                'url'   => 'https://fex-ph-asset-pro.oss-ap-southeast-1.aliyuncs.com/workOrder/1681181339-c8344fd186a649b4b28eb9331bac016e.jpeg',
            ],
            // 薪资证明
            'file_type28' => [
                'title' => $this->getTranslation()->_('annex_28'),
                'text'  => $this->getTranslation()->_('annex_common_28'),
                'url'   => 'https://tc-static-asset-internal.flashexpress.com/workOrder/1688549019-5cd2c913a0044ffeba54b9a5d0386db1.jpeg',
            ],
            // 银行卡
            'file_type29' => [
                'title' => $this->getTranslation()->_('annex_29'),
                'text'  => $this->getTranslation()->_('annex_common_29'),
                'url'   => '',
            ],
        ];
        return $data;
    }
}