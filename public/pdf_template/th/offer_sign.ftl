<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <title>FLASH EXPRESS SOFTWARE (PH) CO. LTD.,INC-offer</title>
    <style>
        @page {
            size: 210mm 297mm;

            @bottom-center {
                content: element(footer);
            }
        }

        div.footer {
            display: block;
            text-align: center;
            position: running(footer);
            width: 100%;
        }

        body {
            margin: 0;
        }

        .box {
            padding: 0 5mm;
            box-sizing: border-box;
            font-size: 3mm;
            font-family: Arial Unicode MS, thailand, SimSun;

        }
        .box-th {
            line-height: 1.9;
            padding: 0 5mm;
            box-sizing: border-box;
            font-size: 3mm;
            font-family: Arial Unicode MS, thailand, SimSun;
        }

        .title {
            border: 0;
            width: 100%;
            margin: 5mm;
        }
        .slot {
            font-style: normal;
            font-size: 3mm;
            /* font-family: Arial Unicode MS, thailand, SimSun; */
        }
        .word-h {
            margin-bottom: 0mm;
        }

        .title-h strong {
            font-size: large;
            border-bottom: 1px solid #333;
        }

        .word-span {
            margin-bottom: 1mm;
        }

        .word-span div {
            margin-bottom: 1mm;
        }


        .word-div {
            margin-left: 32mm;
        }
        .word-s {
            margin-left: 56mm;
        }

        .sign {
            width: 100%;
        }

        .sign tr td {
            margin-left: 10mm;
        }

        .border-b {
            width: 40mm;
            display: inline-block;
            border-bottom: 1px dashed #333;
        }
        .border-t {
            width: 40mm;
            display: inline-block;
            border-bottom: 1px solid #333;
        }

        .word-i {
            font-size: xx-small;
            margin-left: 14mm;
            margin-bottom: 1mm;
        }

        .footer span {
            display: inline-table;
            width: 100%;
            text-align: center;
            font-size: 2.5mm;
        }
        .word-p span {
            font-family: "TH SarabunPSK";
        }
    </style>
</head>

<body>
<#if country_is_th == 0 >
   <div class="box">
    <#else>
    <div class="box-th">
</#if>

    <!-- 页头 -->
    <table class="title" border="0">
        <tr>
            <td style="text-align: left">
                <strong>${company_name}</strong>
            </td>
            <td style="width: 30mm">
                <img
                        style="width: 30mm; height: auto; object-fit: cover"
                        src="${company_logo_url_base64}"
                        alt=""
                />
            </td>
        </tr>
    </table>
    <div class="title-h" style="text-align: center; margin-bottom: 5mm">
        <strong>Offer Letter</strong>
    </div>
    <div class="word-h">Date:&nbsp;<span class="slot">${apply_sign_date}</span></div>
    <div class="word-h">Dear&nbsp;<span class="slot">${candidate_name}</span></div>
    <div class="word-span">
        <span>Thank you for your interest in joining ${company_name} After
          thorough reviews of your application and interview results,we would
          like to offer you the position in our Company as per following:</span>
    </div>
    <div class="word-span">
        <div>
            <span> Job Position:&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="word-div">${job_title_name}</span></span>
        </div>
        <div>
            <span> Job Grade:&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="word-div">${job_title_grade}</span></span>
        </div>
        <div>
            <span> Department: &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="word-div">${dept_name}</span></span>
        </div>
        <div>
            <span> Company Name:</span>
            <span class="word-div">${company_name}</span>
        </div>
        <div>
            <span> Remuneration:&nbsp;&nbsp;&nbsp;</span>
            <span class="word-div">-Basic Salary: <i class="slot">${money}</i></span>
            <span>${company} per month before tax (During the probation)</span>
        </div>
        <div>
            <span class="word-s">and after probation increase to <i class="slot">${trial_salary}</i></span>
            <span>${company} per month before tax;</span>
        </div>

        <#if country_is_th == 0>
        <div>
            <span class="word-s">- Housing Allowance<sup>1</sup>:<i class="slot">${renting}</i></span>
            <span>${company} per month before tax;</span>
        </div>
    </#if>

    <#if position != "" >
    <div>
        <span class="word-s">- Position Allowance:<i class="slot">${position}</i></span>
        <span>${company} per month before tax;</span>
    </div>
</#if>

<#if xinghuo_allowance != "" >
<div>
    <span class="word-s">- Firestarter Award<sup>${xinghuo_allowance_prefix_num}</sup>:<i class="slot">${xinghuo_allowance}</i></span>
    <span>${company} per month before tax;</span>
</div>
</#if>

<#if exp != "" >
    <div>
        <span class="word-s">- Experience Allowance:<i class="slot">${exp}</i></span>
        <span>${company} per month before tax;</span>
    </div>
</#if>

<div>
    <span>Working Days:&nbsp;&nbsp;&nbsp;</span>
    <span class="word-div"><i class="slot">${work_days}</i>&nbsp;working days per week</span>
</div>
</div>
<#if country_is_th == 0 >
<div class="word-span">
        <span
        >Benefits provided by Company policy,including but not limited to the
          following:</span
        >
    <ul>
        <li>WorkVisa;</li>
        <li>Annual Paid Leave (PerCompanyPolicy);</li>
        <li>Group Insurance;</li>
        <li>Family visiting flight tickets allowance;</li>
        <li>
            Laptop allowance;Employee needs to prepare laptop by themselves.
        </li>
    </ul>
</div>
</#if>
<div class="word-span">
    <span>Other Benefits:&nbsp;&nbsp;</span>
    <span class="word-div">Per Company Policy</span>
</div>
<div class="word-span">
    <span>Starting Date:&nbsp;&nbsp;&nbsp;&nbsp;</span>
    <span class="word-div">${work_date}</span>
</div>
<div class="word-span">
        <span
        >We look forward to receiving your acceptance of this offer,and we are
          confident that you will perform well in this position. Should you
          agree,please indicate your acceptance of this offer by signing this
          letter.</span
        >
</div>
<table
        class="sign"
        border="0"
        style="border-collapse: separate; border-spacing: 5mm 2mm"
>
    <tr>
        <td><strong>Yours faithfully,</strong></td>
        <td><strong>Accepted by:</strong></td>
    </tr>
    <tr>
        <td>
            <strong>For &amp; on behalf of ${company_name}</strong>
        </td>
        <td></td>
    </tr>
    <#if offer_type == 2 || offer_type == 4 || offer_type == 6 || offer_type == 8 || offer_type == 10 || offer_type == 12>
    <tr>

        <td style="vertical-align: bottom">
            <img
                    style="
                width: 30mm;
                height: auto;
                object-fit: cover;
                border-bottom: 1px solid #333;
                padding: 0 10mm;
              "
                    src="data:image/png;base64,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"
                    alt=""
            />
        </td>
        <td style="vertical-align: bottom">
            <span class="border-t"></span>
        </td>
    </tr>
    <#else>
    <tr>

        <td style="vertical-align: bottom">
            <img
                    style="
                width: 30mm;
                height: auto;
                object-fit: cover;
                border-bottom: 1px solid #333;
                padding: 0 10mm;
              "
                    src="data:image/png;base64,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"
                    alt=""
            />
        </td>
        <td style="vertical-align: bottom">
            <span class="border-t"></span>
        </td>
    </tr>
</#if>
<tr>
    <td style="font-size: 2.5mm">
        <span style="display: inline-table; width: 100%; margin-top: 0mm">${approver_name}</span>
        <span style="display: inline-table; width: 100%; margin-top: 0mm">${approver_job_title}</span>
    </td>
    <td style="font-size: 2.5mm">
        <span style="display: inline-table; width: 100%; margin-top: 0mm">${candidate_call_name}&nbsp;${candidate_name}</span>
        <span style="display: inline-table; width: 100%; margin-top: 0mm">${job_title_name}</span>
    </td>
</tr>
<tr>
    <td><!--审批人by审批通过日期-->
        Date
        <span class="border-b" style="border-bottom: 1px dashed #333">${approver_approve_pass_date}</span>
    </td>
    <td><!--候选人签字日期地方，不需要填写内容-->
        Date
        <span class="border-b" style="border-bottom: 1px dashed #333"></span>
    </td>
</tr>
</table>

<div class="word-span">
        <span>
          Note: Upon receiving an acceptance (signed copy) of this offer from
          you, we shall communicate further for your joining details.
        </span>
</div>
<table>
    <tr>
        <td>
            <div style="width: 6mm; height: 6mm; border: 1px solid #333"></div>
        </td>
        <td>
            <div class="word-p">
              <span style="font-size: 2.8mm">
                ข้าพเจ้ายืนยันว่าข้อมูลที่ข้าพเจ้าให้ไว้กับบริษัทเป็นความจริงทุกประการและยินยอมให้บริษัททําการตรวจสอบประวัติของข้าพเจ้าก่อนเข้าทํางานด้วยความเต็มใจในกรณีที่บริษัทตรวจสอบภายหลังพบว่าข้อมูลที่ข้าพเจ้า
              </span>
                <br />
                <span style="font-size: 2.8mm">
                ให้ไว้เป็นเท็จมีการปกปิดข้อมูลหรือไม่ตรงตามความจริงข้าพเจ้ายินยอมให้บริษัทยกเลิกสัญญาจ้างโดยไม่ต้องบอกกล่าวล่วงหน้าในการนี้ข้าพเจ้าไม่ติดใจเรียกร้องค่าเสียหายใดๆต่อบริษัททั้งทางแพ่งทางอาญาและตาม
              </span>
                <br />
                <span style="font-size: 2.8mm">
                กฎหมายแรงงาน.
                 <#if country_is_th == 0>
                  <i class="slot">ทั้งนี้หากบริษัทฯไม่สามารถดำเนินการขอใบอนุญาตทำงานของข้าพเจ้าได้เนื่องจากวีซ่ามีปัญหาข้าพเจ้ายินยอมให้บริษัทยกเลิกข้อเสนอการจ้างงานฉบับนี้ได้ในทันที</i>
                  </#if>
                </span>
            </div>
            <div>
              <span style="font-size: 2.8mm">
                I hereby declare that every statement given by me is true and
                correct, is done voluntarily. I authorize the company to
                investigate and collect any information relevant to my
                background check. After any investigation, I also agree that if
                any false declaration is made by me, I agree not to make any
                claims against the Company in Civil, Criminal and Labor Laws,and
                my Contract of Service or this offer letter may be terminated
                forthwith without any further notice.
              </span>
                <#if country_is_th == 0>
                <span style="font-size: 2.8mm">
                I also agree that in the event if the company is not able to
                process my work permit because of issues arising from my current
                visa, this offer will be invalidated.
              </span>
            </#if>
            </div>
        </td>
    </tr>
</table>
<#if country_is_th == 0>
<div class="word-i">
    <i>1:Not provided if accommodation is already provided</i>
</div>
</#if>

<#if xinghuo_allowance != "" >
<div class="word-i">
    <i>
        ${xinghuo_allowance_prefix_num}: Subject to Company Policy–Unpaid leave, absence from work (unpaid),
        out of based location, WFH due to home-visits and etc</i>
</div>
</#if>

</div>
<div class="footer">
      <span>
        ${company_address}
      </span>
    <span>T: ${company_phone} W:${company_web_url}</span>
</div>
</body>
</html>
