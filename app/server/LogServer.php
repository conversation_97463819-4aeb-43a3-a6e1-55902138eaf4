<?php

namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Models\backyard\HrLogModel;
use FlashExpress\bi\App\Models\backyard\HrResumeAiScoreModel;
use FlashExpress\bi\App\Models\backyard\HrResumeFilterModel;
use  FlashExpress\bi\App\Repository\SysListRepository;
use  FlashExpress\bi\App\Repository\LogRepository;

class LogServer extends BaseServer
{
    protected $other_params = [];

    public function setParams($params)
    {
        $this->other_params = $params;
    }

    protected $re;
    public $actionArr;
    public $interviewStatusArr;
    public $moduleTypeArr;
    public $sysList;
    public $log;
    public $staff;

    public function __construct()
    {
        parent::__construct();
        $this->moduleTypeArr = [
            1 => $this->getTranslation()->_('6100'),   //面试
            2 => $this->getTranslation()->_('6101'),   //offer
            3 => $this->getTranslation()->_('6102'),   //入职
            4 => $this->getTranslation()->_('6103'),   //面试反馈
            5 => $this->getTranslation()->_('6118'),   //简历
            6 => $this->getTranslation()->_('6121'),   //基本信息
            7 => $this->getTranslation()->_('6122'),   //家庭信息
            8 => $this->getTranslation()->_('6123'),   //经历与能力
            9 => $this->getTranslation()->_('6124'),   //相关问卷
            10 => $this->getTranslation()->_('6125'),   //附件信息
            11 => $this->getTranslation()->_('6125'),   //培训
            12 => $this->getTranslation()->_('8358'),   //黑名单
            13 => $this->getTranslation()->_('9001'),   //薪资审批
            15 => $this->getTranslation()->_('offer_sign'),   //薪资审批
            16 => $this->getTranslation()->_('9006'),   //办理入职信息
            17 => $this->getTranslation()->_('annex_commitment_text'),   //承诺书
            18 => $this->getTranslation()->_('hc_id'),   //hc id
            19 => $this->getTranslation()->_('recruiter_id'),   //招聘负责人
            21 => $this->getTranslation()->_('winhr_identity_confirm_same'),//(确认)身份证照片与本人相同
            22 => $this->getTranslation()->_('winhr_identity_confirm_different'),//(确认)身份证照片与本人不同
            23 => $this->getTranslation()->_('winhr_identity_confirm_same_update'),//(修改)身份证确认状态为“已通过”
            24 => $this->getTranslation()->_('winhr_identity_confirm_different_update'),//(修改)身份证确认状态为“未通过”
            25 => $this->getTranslation()->_('by_identity_confirm_same'),//身份证照片与本人相同（到岗确认）
            26 => $this->getTranslation()->_('by_identity_confirm_different'),//身份证照片与本人不同（到岗确认）
        ];
        $this->interviewStatusArr = [
            1 => $this->getTranslation()->_('6104'),   //进入下一步
            2 => $this->getTranslation()->_('6105'),   //不通过
            3 => $this->getTranslation()->_('6106'),   //终试通过
            4 => $this->getTranslation()->_('6107'),   //修改内容
            5 => $this->getTranslation()->_('8110'),   //待确认
            6 => $this->getTranslation()->_('8111'),   //待培训
            7 => $this->getTranslation()->_('8112'),   //培训中
            8 => $this->getTranslation()->_('8113'),   //培训通过
            9 => $this->getTranslation()->_('8114'),   //培训未通过
            10 => $this->getTranslation()->_('4809'),  //已入职
            11 => $this->getTranslation()->_('4905'),  //未入职
            12 => $this->getTranslation()->_('8356'),  // 已到岗
            13 => $this->getTranslation()->_('4307'),  // 未到岗
            14 => $this->getTranslation()->_('filter_result_pass'),  //筛选结果为通过
            15 => $this->getTranslation()->_('filter_result_not_pass'),  //筛选结果为不通过
            16 => $this->getTranslation()->_('edit_offer_work_time'),  //修改（offer)预计到岗日期
        ];
        $this->actionArr = [
            1 => $this->getTranslation()->_('6108'),   // 创建
            2 => $this->getTranslation()->_('8360'),   // 修改
            3 => $this->getTranslation()->_('6110'),   // 删除
            4 => $this->getTranslation()->_('6111'),   // 取消
            5 => $this->getTranslation()->_('6112'),   // 查看
            6 => $this->getTranslation()->_('6113'),   // 预约
            7 => $this->getTranslation()->_('6114'),   // 填写
            8 => $this->getTranslation()->_('6115'),   // 发送
            9 => $this->getTranslation()->_('6116'),   // 办理
            10 => $this->getTranslation()->_('6117'),  // 反馈
            11 => $this->getTranslation()->_('8351'),  // 参加培训
            12 => $this->getTranslation()->_('8352'),  // 不参加培训
            13 => $this->getTranslation()->_('8354'),  // 通过培训
            14 => $this->getTranslation()->_('8355'),  // 不通过培训
            15 => $this->getTranslation()->_('8357'),  // 到岗确认
            16 => $this->getTranslation()->_('8359'),  // 添加黑名单
            17 => $this->getTranslation()->_('9002'),  // 申请
            18 => $this->getTranslation()->_('9003'),  //  撤销
            19 => $this->getTranslation()->_('9004'),  //  驳回
            20 => $this->getTranslation()->_('9005'),  //  提交
            21 => $this->getTranslation()->_('approve_passed'),  //  审核通过
            22 => $this->getTranslation()->_('approve_rejected'),  //  审核驳回
            23 => $this->getTranslation()->_('sign'),  //  签署
            24 => $this->getTranslation()->_('9007'),  //  关联
            25 => $this->getTranslation()->_('9008'),  //  编辑
            26 => $this->getTranslation()->_('recommend'),  //推荐
            27 => $this->getTranslation()->_('cancel_recommend'),  //撤回推荐
            28 => $this->getTranslation()->_('6106'),  //  终试通过
            29 => $this->getTranslation()->_('interview_back'),  // 取消本轮面试
            30 => $this->getTranslation()->_('confirm'),  //确认
        ];
    }

    /**
     * 日志添加
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function addLog($paramIn = [])
    {
        $this->log = new LogRepository();

        try {
            $moduleId = $paramIn['module_id'] ?? 0;
            $moduleType = $paramIn['module_type'] ?? 0;
            $moduleLevel = $paramIn['module_level'] ?? 0;
            $moduleStatus = $paramIn['module_status'] ?? 0;
            $action = $paramIn['action'] ?? 0;
            $dataAfter = $paramIn['data_after'] ??'';

            $userIp = $_SERVER["REMOTE_ADDR"]??'';
            if (!empty($this->other_params['deal_staff_info_id'])) {
                $staffInfoId = $this->other_params['deal_staff_info_id'];
            } elseif (!empty($paramIn['staff_id'])) {
                $staffInfoId = $paramIn['staff_id'];
            } else {
                $staffInfoId = $this->userInfo['id'] ? $this->userInfo['id'] : 0;
            }

            if(!$staffInfoId){
                return [];
            }

            //获取更新前后数据比较结果
            $dataDiff = $this->log->getDataDiff([
                'data_after' => $dataAfter,
                'action' => $action,
                'module_id' => $moduleId,
                'module_type' => $moduleType,
            ]);
            $dataBefore = $dataDiff['data_before'] ?? '';
            $dataAfter = $dataDiff['data_after'] ?? '';

            if(!empty($this->other_params['hc_log_remark'])){
                $dataAfter = json_decode($dataAfter,true);
                $dataAfter['task_remark'] = $this->other_params['hc_log_remark'];
                $dataAfter = json_encode($dataAfter);
            }

            if (
                $paramIn['action'] == enums::$log_option['cancel_recommend']
                && $paramIn['module_type'] == enums::$log_module_type['resume']
                && !empty($paramIn['cancel_info'])
            ) {
                $dataAfter = json_encode($paramIn['cancel_info']);
            }

            if (
                $paramIn['action'] == enums::$log_option['modify']
                && $paramIn['module_type'] == enums::$log_module_type['resume_score']
                && !empty($paramIn['data'])
            ) {
                $dataAfter = json_encode($paramIn['data']);
            }

            $paramLog = [
                'staff_info_id' => $staffInfoId,
                'module_id' => $moduleId,
                'module_type' => $moduleType,
                'module_level' => $moduleLevel,
                'module_status' => $moduleStatus,
                'action' => $action,
                'user_ip' => $userIp,
                'data_before' => $dataBefore,
                'data_after' => $dataAfter
            ];

            return $this->log->addLog($paramLog);
        }catch (\Exception $e){
            $this->wLog('添加日志异常',$e->getMessage(),'Log');
        }
    }

    /**
     * 批量日志添加
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function batchAddLog($paramIn = [])
    {
        $this->log = new LogRepository();

        try {
            $moduleIds    = $paramIn['module_ids'] ?? 0;
            $moduleType   = $paramIn['module_type'] ?? 0;
            $moduleLevel  = $paramIn['module_level'] ?? 0;
            $moduleStatus = $paramIn['module_status'] ?? 0;
            $action       = $paramIn['action'] ?? 0;
            $staffInfoId  = $paramIn['staff_info_id']
                ? $paramIn['staff_info_id']
                : ($this->userInfo['id'] ? $this->userInfo['id'] : 0);
            $userIp       = $_SERVER["REMOTE_ADDR"] ?? '';

            if(!$staffInfoId){
                return [];
            }

            //获取更新前后数据比较结果
            /*$dataDiff = $this->log->getDataDiff([
                'data_after' => $dataAfter,
                'action' => $action,
                'module_id' => $moduleId,
                'module_type' => $moduleType,
            ]);
            $dataBefore = $dataDiff['data_before'] ?? '';
            $dataAfter  = $dataDiff['data_after'] ?? '';*/

            $paramLogs = [];
            foreach ($moduleIds as $v) {
                $paramLogs[] = [
                    'staff_info_id' => $staffInfoId,
                    'module_id'     => $v,
                    'module_type'   => $moduleType,
                    'module_level'  => $moduleLevel,
                    'module_status' => $moduleStatus,
                    'action'        => $action,
                    'user_ip'       => $userIp,
                    'data_before'   => [],
                    'data_after'    => []
                ];
            }
            $this->log->batch_insert('hr_log', $paramLogs);
        }catch (\Exception $e){
            $this->wLog('添加日志异常', $e->getMessage(),'Log');
        }
    }

    /**
     * 日志信息
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function infoLog($paramIn = [])
    {
        $this->log = new LogRepository();

        $moduleId = $paramIn['module_id'] ? $paramIn['module_id'] : 0;
        $moduleType = $paramIn['module_type'] ? $paramIn['module_type'] : 0;
        $data = $this->log->infoLog([
            'module_id' => $moduleId,
            'module_type' => $moduleType,
        ]);

        $staffIds = array_column($data,'staff_info_id');

        $staffList = (new StaffServer())->getStaffListByIds($staffIds,['staff_info_id','name','nick_name','mobile']);
        $staffList = array_column($staffList,null,'staff_info_id');

        foreach ($data as $k => $v) {
            $staffInfo = $staffList[$v['staff_info_id']] ?? [];

            if (!$v['module_level']) {
                $module_level = '';
            } else {
                $module_level = $v['module_level'];
            }
            if (!$v['module_status']) {
                $module_status = '';
            } else {
                $module_status = ',' . $this->interviewStatusArr[$v['module_status']];
            }

            $staffName = StaffServer::getStaffNameView($staffInfo,4,$v['staff_info_id'] ?? 0);

            if ($v['action'] == 4 && $v['module_type'] == 1){
                // 取消面试 终止面试
                $data[$k]['interview_log'] = $this->getTranslation()->_('4605') . ' ' . $staffName . ' ' . $this->actionArr[$v['action']] . $this->moduleTypeArr[$v['module_type']] . $module_level . $this->getTranslation()->_('termination_of_interview');
            }elseif ($v['action'] == 29 && $v['module_type'] == 1){
                // 取消面试 取消本轮面试
                $data[$k]['interview_log'] = $this->getTranslation()->_('4605') . ' ' . $staffName . ' ' . $this->actionArr[$v['action']] . $this->moduleTypeArr[$v['module_type']] . $module_level . $this->getTranslation()->_('interview_back');
            }else{
                $data[$k]['interview_log'] = $this->getTranslation()->_('4605') . ' ' . $staffName . ' ' . $this->actionArr[$v['action']] . $this->moduleTypeArr[$v['module_type']] . $module_level . $module_status;
            }

            if (
                $v['action'] == enums::$log_option['cancel_recommend']
                && $v['module_type'] == enums::$log_module_type['resume']
            ) {
                $cancelInfo = !empty($v['data_after']) ? json_decode($v['data_after'],true) : [];

                if (!empty($cancelInfo['cancel_type'])) {
                    $cancel_type_str = $this->getTranslation()->_(HrResumeFilterModel::$cancel_type_list[$cancelInfo['cancel_type']] ?? '');

                    if ($cancel_type_str) {
                        $data[$k]['interview_log'] .= '('.$cancel_type_str.')';
                    }
                }
            }

            //简历分数修改
            if (
                $v['module_type'] == enums::$log_module_type['resume_score'] &&
                $v['action'] == enums::$log_option['modify']
            ) {
                $aftInfo = !empty($v['data_after']) ? json_decode($v['data_after'], true) : [];

                $keyName = $scoreName = '';
                if (!empty($aftInfo['key']) && isset($aftInfo['old_score']) && isset($aftInfo['new_score'])) {
                    $keyName = $this->getTranslation()->_('resume_ai_score_title');
                    $keyName .= '-'.$this->getTranslation()->_(HrResumeAiScoreModel::$score_key_list[$aftInfo['key']] ?? '');

                    $unit      = $this->getTranslation()->_('unit_fen');
                    $scoreName = $aftInfo['old_score'].$unit.' > '.$aftInfo['new_score'].$unit;
                }

                $data[$k]['interview_log'] .= $keyName.' '.$scoreName;
            }

            unset($data[$k]['data_after']);
        }
        return $data;
    }

    /**
     * 获取操作列表
     */
    public function getAction()
    {
        return $this->actionArr;
    }

    /**
     * 获取状态列表
     */
    public function getStatus()
    {
        return $this->interviewStatusArr;
    }

    /**
     * 获取日志信息
     * @param $conditions
     * @param $binds
     * @return mixed
     */
    public function getLogOneInfo($conditions, $binds)
    {
        $result =  HrLogModel::findFirst([
            'columns' => 'id, staff_info_id',
            'conditions' => $conditions,
            'bind' => $binds
        ]);

        return !empty($result) ? $result->toArray() : [];
    }
}
