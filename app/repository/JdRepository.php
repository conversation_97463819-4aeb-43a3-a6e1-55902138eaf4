<?php

namespace FlashExpress\bi\App\Repository;

use FlashExpress\bi\App\Models\backyard\HrJdModel;

/**
 * 岗位信息仓储类
 */
class JdRepository extends BaseRepository
{
    /**
     * 根据岗位名称获取岗位信息
     * @param $name
     * @return array 岗位信息
     */
    public function getInfoByName($name): array
    {
        if (empty($name)) {
            return [];
        }

        $info = (new HrJdModel())->findFirst([
            'conditions' => 'name = :name: and state = :state:',
            'bind' => [
                'name' => $name,
                'state' => HrJdModel::state_delete_no
            ]
        ]);

        return empty($info) ? [] : $info->toArray();
    }
}