<?php

use FlashExpress\bi\App\library\Excel;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\Models\backyard\HrhcModel;
use FlashExpress\bi\App\Models\backyard\WinhrExcelTaskModel;
use FlashExpress\bi\App\Repository\HcRepository;
use FlashExpress\bi\App\Repository\InterviewRepository;
use FlashExpress\bi\App\Server\DownLoadTaskService;
use FlashExpress\bi\App\Server\HcServer;
use FlashExpress\bi\App\Server\BaseServer;
use FlashExpress\bi\App\Server\InterviewServer;
use FlashExpress\bi\App\Repository\OfferRepository;
use FlashExpress\bi\App\Server\EntryToolsServer;

/**
 * Created by PhpStorm.
 * User: nick
 * Date: 2020/5/19
 * Time: 下午4:42
 */
class HcTask extends BaseTask
{

    public $state_code_2 = 2;//招聘中
    public $state_code_4 = 4;//已废弃
    public $state_code_9 = 9;//审批超时


    public function check_stateAction()
    {
        //查询数据
        $select_sql = "SELECT hc_id FROM hr_hc WHERE state_code = 2 and (surplusnumber <= 0 or surplusnumber > demandnumber);";
        $errorData  = $this->getDI()->get('db')->query($select_sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        if (!empty($errorData)) {
            $this->getDI()->get('logger')->write_log(["hc状态异常" => $errorData]);
        }
    }


    //每天 凌晨1点以后 泰国时间 凌晨12点之后 跑一次 7天前未入职offer http://193x782t53.imwork.net:29667/zentao/story-view-5475.html
    public function cancel_hcAction()
    {
        $entryToolsServer = new EntryToolsServer();
        $offerRepository  = new OfferRepository();

        $workTime = date('Y-m-d 00:00:00', strtotime('-1 day'));

        $data = $offerRepository->getExpirationData($workTime);

        if (empty($data)) {
            echo 'data empty!';
            $this->logger->write_log("取消offer任务无需操作员工 ", 'info');
            exit;
        }

        foreach ($data as $v) {
            $entryToolsServer->cancelEntryHc($v);
        }

        echo 'success';
        exit();
    }

    /**
     * 给招聘中的hc数据添加招聘负责人
     * 手动执行一次即可
     */
    public function addHcManagerAction($argv = [])
    {
        if (empty($argv) && env('country_code') != 'TH') {
            echo '其他国家暂未开放';
        }
        //获取招聘中、审批中hc数据
        $hc_list = \FlashExpress\bi\App\Models\HcModel::find([
            'columns'    => 'hc_id,department_id',
            'conditions' => 'state_code in(1,2) and  deleted = 1',
        ])->toArray();
        if (empty($hc_list)) {
            echo 'no data';
            die;
        }
        echo '开始处理'.PHP_EOL;
        foreach ($hc_list as $item) {
            try {
                $manager_staff_id = \FlashExpress\bi\App\Server\HcServer::getHcManagerByDeptId($item['department_id']);
                if ($manager_staff_id) {
                    $result = $this->getDI()->get('db')->updateAsDict(
                        'hr_hc',
                        [
                            'manager_staff_id' => $manager_staff_id,
                        ],
                        'hc_id = '.$item['hc_id']
                    );
                }
            } catch (Exception $e) {
                echo $e->getMessage().PHP_EOL;
            }
        }

        echo '处理完毕'.PHP_EOL;
    }


    /**
     * @description:更新hc过期脚本 原来是 go  0 1 * * * ?
     *
     * @param null
     *
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2021/12/13 14:51
     */

    public function overdueHcAction()
    {
        //获取今天的日期
        $now_date = date('Y-m-d');
        //修改 hc 状态
        $db           = $this->getDI()->get('db');
        $db->updateAsDict("hr_hc", [
            "state_code" => HrhcModel::STATE_TIMEOUT, //修改为超时
        ], [
            "conditions" => " state_code = ? and expirationdate < ?  ",
            "bind"       => [
                HrhcModel::STATE_RECRUITING,
                $now_date,
            ],
        ]);
        return true;
    }


    /**
     * @description: 这里是 根据网点统计 hc  0 *\/30 * * * ?
     *
     * @param null
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2021/12/13 17:17
     */

    public function statisticalStoreidAction()
    {
        set_time_limit(0);             // 设置脚本最大执行时间 为0 永不过期
        ini_set('memory_limit', '-1'); //内存
        try {
            //查询是否执行过
            //获取日期  以前就是获取的当时日期
            $db              = $this->getDI()->get('db');
            $stat_date       = date('Y-m-d');
            $statisticalData = \FlashExpress\bi\App\Models\backyard\HrStatisticalStoreidModel::findFirst([
                'conditions' => "stat_date = :stat_date:",
                'bind'       => ['stat_date' => $stat_date],
                'columns'    => ['id'],
            ]);
            if (!empty($statisticalData)) {
                //存在则删除
                $sql        = "delete from `hr_statistical_storeid` where `stat_date` = ? ";
                $del_result = $db->execute($sql, [$stat_date]);
                if (empty($del_result)) {
                    throw new Exception('删除 失败 !');
                } else {
                    $this->info('删除hr_statistical_storeid  成功 !  日期 stat_date=>'.$stat_date, true);
                }
            }

            //查询数据
            $select_sql = "SELECT
			hr_hc.worknode_id AS store_id,
       		'{$stat_date}' AS stat_date,
			sum(hr_hc.demandnumber) as hc_demandnumber,
			sum(hr_hc.surplusnumber) as hc_surplusnumber,
			(sum(hr_hc.demandnumber)-sum(hr_hc.surplusnumber)) as hc_finishnumber,
            IFNULL(sys_province.manage_geography_code,'-1') AS area_code
		FROM
			`hr_hc` AS hr_hc
		LEFT JOIN `sys_store` as sys_store ON sys_store.id = hr_hc.worknode_id
        LEFT JOIN `sys_province` AS sys_province ON sys_store.province_code = sys_province.code
		WHERE
			state_code = :state_code
		GROUP BY
			worknode_id";

            $list_data = $db->query($select_sql,
                ["state_code" => $this->state_code_2])->fetchAll(\Phalcon\Db::FETCH_ASSOC);
            if (!empty($list_data)) {
                //插入
                $result = (new HcRepository())->batch_insert('hr_statistical_storeid', $list_data);
                if ($result) {
                    $this->info('插入 成功 !', true);
                } else {
                    throw new Exception('插入 失败 !');
                }
            }

        } catch (Exception $e) {
            $this->error("hc statisticalStoreidAction 执行失败  异常信息"
                .$e->getMessage()
                ." File: "
                .$e->getFile()
                ." Line: ".$e->getLine()
                ." Code: ".$e->getCode(), true);
        }
        $this->info('hc statisticalStoreidAction 执行完毕!', true);
    }

    /**
     * @description:根据部门统计 hc
     *
     * @param null
     *
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2021/12/13 18:46
     */

    public function statisticalDepartmentidAction()
    {
        set_time_limit(0);             // 设置脚本最大执行时间 为0 永不过期
        ini_set('memory_limit', '-1'); //内存
        try {
            //查询是否执行过
            //获取日期  以前就是获取的当时日期
            $db              = $this->getDI()->get('db');
            $stat_date       = date('Y-m-d');
            $statisticalData = \FlashExpress\bi\App\Models\backyard\HrStatisticalDepartmentidModel::findFirst([
                'conditions' => "stat_date = :stat_date:",
                'bind'       => ['stat_date' => $stat_date],
                'columns'    => ['id'],
            ]);
            if (!empty($statisticalData)) {
                //存在则删除
                $sql        = "delete from `hr_statistical_departmentid` where `stat_date` = ? ";
                $del_result = $db->execute($sql, [$stat_date]);
                if (empty($del_result)) {
                    throw new Exception('删除 失败 !');
                } else {
                    $this->info('删除 hr_statistical_departmentid  成功 !  日期 stat_date=>'.$stat_date, true);
                }
            }
            $db = $this->getDI()->get('db');
            //查询数据
            $select_sql = "SELECT
			department_id AS department_id,
       		'{$stat_date}' AS stat_date,
			sum(demandnumber) as hc_demandnumber,
			sum(surplusnumber) as hc_surplusnumber,
			(sum(demandnumber)-sum(surplusnumber)) as hc_finishnumber
		FROM
			hr_hc
		WHERE
			state_code = :state_code
		GROUP BY
			department_id";

            $list_data = $db->query($select_sql,
                ["state_code" => $this->state_code_2])->fetchAll(\Phalcon\Db::FETCH_ASSOC);

            //数据为空判断
            if (!empty($list_data)) {
                //插入
                $result = (new HcRepository())->batch_insert('hr_statistical_departmentid', $list_data);
                if ($result) {
                    $this->info('插入 成功 !', true);
                } else {
                    throw new Exception('插入 失败 !');
                }
            }
        } catch (Exception $e) {
            $this->error("hc statisticalDepartmentidAction 执行失败  异常信息"
                .$e->getMessage()
                ." File: "
                .$e->getFile()
                ." Line: ".$e->getLine()
                ." Code: ".$e->getCode(), true);
        }
        $this->info('hc statisticalDepartmentidAction 执行完毕!', true);
    }

    /**
     * 每天凌晨3点执行（0 0 3 * * ?)
     *
     * 当转岗HC审批状态为“已同意”，且状态为”招聘中“时，
     * 如果最终审批通过时间超过30天自动置-0已废弃
     *
     * https://l8bx01gcjr.feishu.cn/docs/doccnfdgjRUY0xifJa9tNaJz08c
     */
    public function voidTransferHcAction()
    {
        $current_time = strtotime(gmdate('Y-m-d H:i:s'));
        $left_30days  = date("Y-m-d 23:59:59", strtotime("-30 days ", $current_time));

        echo "脚本启动...".PHP_EOL;
        //获取数据条件
        $cond = "reason_type = 2 and state_code=2 and approval_state_code = 6 and approval_completion_time <= '{$left_30days}'";

        //查询数据
        $select_sql   = "SELECT hc_id FROM hr_hc WHERE {$cond} ";
        $need_void_hc = $this->getDI()->get('db')->query($select_sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);

        $hc_ids = implode(',', array_column($need_void_hc, 'hc_id'));

        if (empty($need_void_hc)) {
            $this->getDI()->get('logger')->write_log("voidTransHcAction脚本：今日无待处理数据", 'info');
            die('今日无待处理数据');
        }
        $log_info = "今日执行作废hc共计".count($need_void_hc)."条，作废hcid内容:{$hc_ids},作废条件：{$select_sql}";

        echo $log_info.PHP_EOL;
        $this->getDI()->get('logger')->write_log("voidTransHcAction脚本：{$log_info}", 'info');

        //执行作废更新
        $res = $this->getDI()->get('db')->updateAsDict(
            'hr_hc',
            [
                "state_code" => $this->state_code_4,
            ],
            "hc_id in({$hc_ids})"
        );

        if ($res) {
            $this->getDI()->get('logger')->write_log("voidTransHcAction脚本：执行成功！", 'info');
            echo "voidTransHcAction脚本:执行成功！".PHP_EOL;
        } else {
            $this->getDI()->get('logger')->write_log("voidTransHcAction脚本：执行失败！", 'info');
            echo "voidTransHcAction脚本:执行失败！".PHP_EOL;
        }
    }


    /**
     * @description:
     * @param null
     * @return:
     * @author: L.J
     * @time: 2023/2/13 16:31
     */
    public function store_hc_dow_excelAction($input)
    {
        //获取任务参数
        $input = !isset($input[0]) ? (new DownLoadTaskService())->getExcelTaskArgv() : $input;
        //TODO 整理参数
        ini_set('memory_limit', '-1');
        set_time_limit(0);      // 设置脚本最大执行时间 为0 永不过期

        $params = isset($input[0]) ? json_decode(base64_decode($input[0]), true) : [];
        $taskId = $input[1] ?? '';
        $this->info('store_hc_dow_excelAction start '.' params :'.json_encode($params,
                JSON_UNESCAPED_UNICODE).' taskId=>'.$taskId);
        $this->lang = $params['lang'];
        $excelTask  = WinhrExcelTaskModel::findFirst($taskId);
        if (!$excelTask) {
            throw new \Exception('没有找到相关记录!');
        }
        $excelTask          = $excelTask->toArray();
        $file_name          = $excelTask['file_name'];
        $HcServer           = new HcServer();
        $HcServer->userInfo = $params['userinfo'];
        //获取网点HC列表
        $returnArr = $HcServer->getStoreHcList($params);

        $data  = $returnArr['data']['dataList'];

        $_data = [];
        foreach ($data as $k => $v) {
            $areaCode          = $v['area_code'] ?? '';
            $storeName         = $v['store_name'] ?? '';
            $storeId           = $v['store_id'] ?? '';
            $jobName           = $v['job_name'] ?? '';
            $hcBudget          = $v['hc_budget'] ?? '';
            $employeeInService = $v['employee_in_service'] ?? '';
            $hcPending         = $v['hc_pending'] ?? '';
            $hcDemand          = $v['hc_demand'] ?? '';
            $hcApproval        = $v['hc_approval'] ?? '';
            $hcRequest         = $v['hc_request'] ?? '';
            $hcP1              = $v['hc_p1'] ?? '';
            $hcP2              = $v['hc_p2'] ?? '';
            $hcP3              = $v['hc_p3'] ?? '';
            $hcP4              = $v['hc_p4'] ?? '';
            $_data[$k]         = [
                $areaCode,
                $storeName,
                $storeId,
                $jobName,
                $hcBudget,
                $employeeInService,
                $hcPending,
                $hcDemand,
                $hcApproval,
                $hcRequest,
                $hcP1,
                $hcP2,
                $hcP3,
                $hcP4,
            ];
        }
        $t = $this->getTranslation();

        $field     = [
            $t->_('8000'),//区域
            $t->_('8001'),//网点名称
            $t->_('branch_store_id'),//网点名称
            $t->_('8002'),//岗位
            $t->_('8003'),//预算人数（本月）
            $t->_('8004'),//当前在职人数
            $t->_('8005'),//当前待入职人数
            $t->_('8006'),//当前可招人数
            $t->_('8007'),//审核通过后在招聘中的人数
            $t->_('8008'),//还需提交的人数
            $t->_('8009'),//HC P1人数
            $t->_('8010'),//HC P2人数
            $t->_('8011'),//HC P3人数
            $t->_('8012'),//HC P4人数
        ];
        $header    = $field;
        $data      = $_data;
        $file_path = Excel::exportExcel($header, $data, $file_name);
        $fileUrl   = $file_path ? $file_path['object_url'] : '';
        $res       = (new DownLoadTaskService())->updateTask($taskId, $fileUrl);
        $this->info('store_hc_dow_excelAction end '.$res, true);
    }


    /**
     * 导出hc列表
     * @param $input
     * @throws BusinessException
     */
    public function listHcExportAction($input)
    {
        //获取任务参数
        $input   = !isset($input[0]) ? (new DownLoadTaskService())->getExcelTaskArgv() : $input;
        $params  = isset($input[0]) ? json_decode(base64_decode($input[0]), true) : [];
        $task_id = $input[1] ?? '';

        //记录执行日志
        $this->info('listHcExportStart ' . ' params :' . json_encode($params,
                JSON_UNESCAPED_UNICODE) . ' taskId=>' . $task_id);

        $this->lang = $params['lang'];
        if (in_array($this->lang, ['zh', 'zh-CN'])) {
            $this->lang = 'zh-CN';
        }

        //查询任务
        $excel_task = WinhrExcelTaskModel::findFirst($task_id);

        if (!$excel_task) {
            throw new BusinessException('没有找到相关记录!');
        }

        //获取task信息
        $excel_task = $excel_task->toArray();
        $file_name  = $excel_task['file_name'];
        $server_obj = new HcServer();

        //注意给实例附语言，不要传递
        $server_obj->lang     = $this->lang;
        $server_obj->userInfo = $params['userinfo'];
        $t                    = $this->getTranslation();
        BaseServer::$t        = $t;

        //获取数据
        $return_arr = $server_obj->getHcList($params);
        $data       = $return_arr['data']['dataList'] ?? [];

        //导出是数据集合
        $_data = [];

        foreach ($data as $k => $v) {
            $hc_id                     = $v['hc_id'] ? $v['hc_id'] : '';
            $job_name                  = $v['job_name'] ? $v['job_name'] : '';
            $department_name           = $v['department_name'] ? $v['department_name'] : '';
            $worknode_name             = $v['worknode_name'] ? $v['worknode_name'] : '';
            $worknode_id               = $v['worknode_id'] ? : '';
            $_areaName                 = $v["sorting_no"] ?? "";
            $reason_type               = $v["reason_type_txt"] ?? "";
            $expirationdate            = $v['expirationdate'] ? $v['expirationdate'] : '';
            $createtime                = $v['createtime'] ? $v['createtime'] : '';
            $createName                = $v['submitter_id'] ? ('(' . $v['submitter_id'] . ')' . $v['submitter_name']) : '';
            $demandSurplus             = $v['demandnumber'] ? ($v['surplusnumber'] . '/' . $v['demandnumber']) : '';
            $surplusnumber             = $v['surplusnumber'] ?? '';  //剩余数 ，注意也判断
            $demandnumber              = $v['demandnumber'] ?? '';   //总需求
            $stateValue                = $v['state_value'] ? $v['state_value'] : '';
            $priority_name             = $v['priority_name'] ? $v['priority_name'] : '';
            $job_id                    = $v['job_id'] ? $v['job_id'] : '';
            $submitter_department_name = $v['submitter_department_name'] ? $v['submitter_department_name'] : '';
            $submitter_job_title       = $v['submitter_job_title'] ? $v['submitter_job_title'] : '';
            $approval_state_code_name  = $v['approval_state_code_name'] ? $v['approval_state_code_name'] : '';
            $approval_stage_name       = $v['approval_stage_name'] ? $v['approval_stage_name'] : '';
            $approval_completion_time  = $v['approval_completion_time'] ? $v['approval_completion_time'] : '';
            $hire_type_text            = $v['hire_type_text'];
            $hire_times_text           = $v['hire_times_text'];
            $jobTitleId                = $v['job_title_id'] ? $v['job_title_id'] : '';
            $jobTitleName              = $v['job_title_proper_name'] ? $v['job_title_proper_name'] : '';

            $manager_staff_name = '';
            if (!empty($v['manager_staff_id'])) {
                $manager_staff_name = '(' . $v['manager_staff_id'] . ')';
            }

            if (!empty($v['manager_staff_name'])) {
                $manager_staff_name .= $v['manager_staff_name'];
            }

            $working_day_rest_type_text = '';
            if (!empty($v['working_day_rest_type'])) {
                $working_day_rest_type = explode(',', $v['working_day_rest_type']);

                array_walk($working_day_rest_type, function (&$val) {
                    $val = $this->getTranslation()->_('working_day_rest_type_' . $val);
                });

                $working_day_rest_type_text = implode(',', $working_day_rest_type);
            }

            $_data[$k] = [
                $hc_id,
                $priority_name,
                $job_id,
                $job_name,
                $jobTitleId,
                $jobTitleName,
                $department_name,
                $worknode_name,
                $worknode_id,
                $_areaName,
                $reason_type,
                $working_day_rest_type_text,
                $expirationdate,
                $createtime,
                $createName,
                $submitter_department_name,
                $submitter_job_title,
                $approval_state_code_name,
                $approval_stage_name,
                $approval_completion_time,
                $hire_type_text,
                $hire_times_text,
                $demandSurplus,
                $surplusnumber,
                $demandnumber,
                $stateValue,
                $manager_staff_name,
            ];
        }

        $field = [
            'HCID',
            $t->_('4032'),//优先级
            $t->_('4037'),//JDID
            $t->_('7001'),
            $t->_('11008'),//职位 ID
            $t->_('position_name'),//职位名称
            $t->_('7002'),
            $t->_('7003'),
            $t->_('branch_store_id'),
            $t->_('7031'),                 //所属区域
            $t->_('7017'),                 //用人原因
            $t->_('working_day_rest_type'),//工作天数&轮休规则
            $t->_('7004'),
            $t->_('7005'),
            $t->_('7006'),
            $t->_('4033'),       //创建人部门
            $t->_('4034'),       //创建人职位
            $t->_('4035'),       //审批状态
            $t->_('4036'),       //审批阶段
            $t->_('8600'),       //审批完成时间
            $t->_('hire_type'),  // 雇佣类型
            $t->_('hire_times'), // 雇佣期间
            $t->_('7007'),
            $t->_('surplusnumber_text'), //剩余数
            $t->_('demandnumber_text'),  //总需求数
            $t->_('7008'),
            $t->_('hc_staff_manager'), //招聘负责人
        ];

        $file_path = Excel::exportExcel($field, $_data, $file_name);
        $fileUrl   = $file_path ? $file_path['object_url'] : '';
        $res       = (new DownLoadTaskService())->updateTask($task_id, $fileUrl);

        $this->info('store_hc_dow_excelAction end ' . $res, true);

        echo PHP_EOL . '任务 ' . $task_id . ' success ';
    }

    public function handleDiscrepancyHcAction()
    {
        $log = 'handleDiscrepancyHcAction , 系统所在国家: ' . strtoupper(env('country_code',
                'TH')) . PHP_EOL;
        $log .= '开始时间: ' . date('Y-m-d H:i:i:s') . PHP_EOL;
        $server = new \FlashExpress\bi\App\Server\EntryServer();
        $re = (new HcServer())->handleDiscrepancyHc();
        if ($re){
            $log .= '数据已处理' . PHP_EOL;
        }else{
            $log .= '数据处理失败' . PHP_EOL;
        }
        $log .= '结束时间: ' . date('Y-m-d H:i:i:s');
        $this->getDI()->get('logger')->write_log($log, 'info');
        exit($log);
    }

    /**
     * hc审批通过消息通知
     * @return void
     * @throws BusinessException
     */
    public function approvalAgreeNoticeAction()
    {
        $log = 'approvalAgreeNoticeAction , 系统所在国家: ' . strtoupper(env('country_code',
                'TH')) . PHP_EOL;
        $log .= '开始时间: ' . date('Y-m-d H:i:i:s') . PHP_EOL;
        $data = (new HcServer())->approvalAgreeNotice();
        if ($data){
            $log .= '已执行完成'. PHP_EOL;
        }else{
            $log .= '无处理数据' . PHP_EOL;
        }
        $this->getDI()->get('logger')->write_log($log, 'info');
        exit($log);
    }
}

