<?php

namespace FlashExpress\bi\App\Modules\LA\Server;

use App\Modules\La\library\Enums\enums;
use FlashExpress\bi\App\Server\ResumeServer as GlobalServer;

class ResumeServer extends GlobalServer
{

    public $areas = [
        1 => 'N',
        2 => 'C',
        3 => 'S'
    ];

    /**
     * 根据网点获取对应 所属区域
     * @return mixed
     */
    public function getSortingNoByStoreInfo($storeInfo)
    {
        $sortingNo = $storeInfo['store_sorting_no'] ?? '';
        // 获取区域
        return in_array($sortingNo, $this->areas) ? $sortingNo : '';
    }

    /**
     * 附件翻译
     * @return array
     */
    public function annexListLang(): array
    {
        $data = [
            'file_type1'  => [
                'title' => $this->getTranslation()->_('4151'),
                'text'  => $this->getTranslation()->_('4165'),
                'url'   => 'https://fle-staging-asset-internal.oss-ap-southeast-1.aliyuncs.com/backyardUpload/1559812569-2afe9c73999d4c4984878588370d4b7b.jpg',
            ],
            'file_type2'  => [
                'title' => $this->getTranslation()->_('4152'),
                'text'  => $this->getTranslation()->_('4152'),
                'url'   => 'https://fle-staging-asset-internal.oss-ap-southeast-1.aliyuncs.com/backyardUpload/1559812697-156b387c531943c09abb21662454aa7a.jpg',
            ],
            'file_type3'  => [
                'title' => $this->getTranslation()->_('4153'),
                'text'  => $this->getTranslation()->_('4153'),
                'url'   => 'https://fle-staging-asset-internal.oss-ap-southeast-1.aliyuncs.com/backyardUpload/1559812759-635cc86cb9bd445488242ba82259f2ef.jpg',
            ],
            'file_type4'  => [
                'title' => $this->getTranslation()->_('4154'),
                'text'  => $this->getTranslation()->_('4154'),
                'url'   => 'https://tc-static-asset-internal.flashexpress.com/workOrder/1715766335-0dfe926d811b4a24b79873c7ac6f190a.jpeg',
            ],
            'file_type5'  => [
                'title' => $this->getTranslation()->_('4155'),
                'text'  => $this->getTranslation()->_('4155_text'),
                'url'   => 'https://tc-static-asset-internal.flashexpress.com/workOrder/1715766335-0dfe926d811b4a24b79873c7ac6f190a.jpeg',
            ],
            'file_type6'  => [ //兵役服役证明
                'title' => $this->getTranslation()->_('4156'),
                'text'  => $this->getTranslation()->_('4156'),
                'url'   => 'https://fle-staging-asset-internal.oss-ap-southeast-1.aliyuncs.com/backyardUpload/1559813010-291dae54378a41deab10942444094e47.jpg',
            ],
            'file_type7'  => [ //成绩报告单
                'title' => $this->getTranslation()->_('4157'),
                'text'  => $this->getTranslation()->_('4157'),
                'url'   => 'https://fle-staging-asset-internal.oss-ap-southeast-1.aliyuncs.com/backyardUpload/1559813062-9dca1378ba3644acabf8e812331a6e54.jpg',
            ],
            'file_type8'  => [
                'title' => $this->getTranslation()->_('nocrime_certification'),
                'text'  => $this->getTranslation()->_('nocrime_certification'),
                'url'   => 'https://tc-static-asset-internal.flashexpress.la/workOrder/1739348905-cf4433714bd34fefb6828436efc6c25a.png',
            ],
            'file_type9'  => [
                'title' => $this->getTranslation()->_('4159'),
                'text'  => $this->getTranslation()->_('4165'),
                'url'   => 'https://fle-staging-asset-internal.oss-ap-southeast-1.aliyuncs.com/backyardUpload/1559812569-2afe9c73999d4c4984878588370d4b7b.jpg',
            ],
            'file_type10' => [ //驾驶证正面
                'title' => $this->getTranslation()->_('4160'),
                'text'  => $this->getTranslation()->_('4160'),
                'url'   => 'https://fle-staging-asset-internal.oss-ap-southeast-1.aliyuncs.com/backyardUpload/1559813180-bd02cab70a7341dca0fa85814f4729f6.jpg',
            ],
            'file_type11' => [ //驾驶证反面
                'title' => $this->getTranslation()->_('4161'),
                'text'  => $this->getTranslation()->_('4161'),
                'url'   => 'https://fle-staging-asset-internal.oss-ap-southeast-1.aliyuncs.com/backyardUpload/1559813235-54a62f382b2e4c1c82a2a323b4fe53ec.jpg',
            ],
            'file_type12' => [
                'title' => $this->getTranslation()->_('4162'),
                'text'  => $this->getTranslation()->_('4162_text'),
                'url'   => 'https://fle-staging-asset-internal.oss-ap-southeast-1.aliyuncs.com/backyardUpload/1559813300-3742977730f9477085f70dea85281257.jpg',
            ],
            'file_type13' => [ //车辆照片
                'title' => $this->getTranslation()->_('4163'),
                'text'  => $this->getTranslation()->_('4163'),
                'url'   => 'https://tc-static-asset-internal.flashexpress.la/workOrder/1739348923-3b0ccdeb37ce4ea9bfa6979032970739.png',
            ],
            'file_type14' => [
                'title' => $this->getTranslation()->_('4164'),
                'text'  => $this->getTranslation()->_('4165'),
                'url'   => 'https://fle-staging-asset-internal.oss-ap-southeast-1.aliyuncs.com/backyardUpload/1559813336-a507472359014a9a930cbd331e4ca994.jpg',
            ],
            'file_type16' => [
                'title' => $this->getTranslation()->_('4166'),
                'text'  => $this->getTranslation()->_('4167'),
                'url'   => '',
            ],
            'file_type17' => [
                'title' => $this->getTranslation()->_('4168'),//残疾人证-正面
                'text'  => $this->getTranslation()->_('4168'),
                'url'   => 'https://fle-staging-asset-internal.oss-ap-southeast-1.aliyuncs.com/workOrder/1604903436-ea372fe9aa4b4552a4f324713a2cc181.png',
            ],
            'file_type18' => [
                'title' => $this->getTranslation()->_('4169'),//残疾人证-反面
                'text'  => $this->getTranslation()->_('4169'),
                'url'   => 'https://sai.flashexpress.com/workOrder/1604975937-7acc98e3a9cf472c808e8e5906171605.png',
            ],
            'file_type19' => [
                'title' => $this->getTranslation()->_('annex_4170'),//最高学历证书
                'text'  => $this->getTranslation()->_('annex_4170'),
                'url'   => '',
            ],
            'file_type20' => [
                'title' => $this->getTranslation()->_('annex_4171'),//GDL驾照正面(马来新增）
                'text'  => $this->getTranslation()->_('annex_4171'),
                'url'   => '',
            ],
            'file_type21' => [
                'title' => 'OR',//GDL驾照正面(马来新增）
                'text'  => 'OR',
                'url'   => '',
            ],
            'file_type22' => [
                'title' => $this->getTranslation()->_('annex_commitment'),//GDL驾照正面(马来新增）
                'text'  => $this->getTranslation()->_('annex_commitment'),
                'url'   => '',
            ],
            // 社保卡
            'file_type27' => [
                'title' => $this->getTranslation()->_('annex_27'),
                'text'  => $this->getTranslation()->_('annex_27'),
                'url'   => 'https://fex-ph-asset-pro.oss-ap-southeast-1.aliyuncs.com/workOrder/1681181273-c2ca7afe2b94458b8cd5dc13c360ca8d.jpeg',
            ],
            // 医保卡
            'file_type24' => [
                'title' => $this->getTranslation()->_('annex_24'),
                'text'  => $this->getTranslation()->_('annex_24'),
                'url'   => 'https://fex-ph-asset-pro.oss-ap-southeast-1.aliyuncs.com/workOrder/1681181327-ae2057fb52e24448841e4e36a3374353.jpeg',
            ],
            // 公积金卡
            'file_type25' => [
                'title' => $this->getTranslation()->_('annex_25'),
                'text'  => $this->getTranslation()->_('annex_25'),
                'url'   => 'https://fex-ph-asset-pro.oss-ap-southeast-1.aliyuncs.com/workOrder/1681181306-6d53b2faa35f4e9aa232408975bc7f6e.jpeg',
            ],
            // 税卡
            'file_type26' => [
                'title' => $this->getTranslation()->_('annex_26'),
                'text'  => $this->getTranslation()->_('annex_26'),
                'url'   => 'https://fex-ph-asset-pro.oss-ap-southeast-1.aliyuncs.com/workOrder/1681181339-c8344fd186a649b4b28eb9331bac016e.jpeg',
            ],
            // 薪资证明
            'file_type28' => [
                'title' => $this->getTranslation()->_('annex_28'),
                'text'  => $this->getTranslation()->_('annex_common_28'),
                'url'   => 'https://tc-static-asset-internal.flashexpress.com/workOrder/1688549019-5cd2c913a0044ffeba54b9a5d0386db1.jpeg',
            ],
            // 银行卡
            'file_type29' => [
                'title' => $this->getTranslation()->_('annex_29'),
                'text'  => $this->getTranslation()->_('annex_common_29'),
                'url'   => '',
            ],
        ];
        return $data;
    }
}