<?php

namespace FlashExpress\bi\App\Controllers;

use FlashExpress\bi\App\library\Tools;
use \FlashExpress\bi\App\Models\backyard\WinhrExcelTaskModel;
use FlashExpress\bi\App\Server\DownLoadTaskService;
use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\SysDepartmentModel;
use FlashExpress\bi\App\Server\HcServer;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Excel;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use Exception;
use FlashExpress\bi\App\Server\InterviewServer;
use FlashExpress\bi\App\Server\SysListServer;

class HcController extends Controllers\ControllerBase
{
    public $department_id;
    public $paramIn;

    public function initialize()
    {
        parent::initialize();
        $this->department_id = 7;
        $this->paramIn = $this->request->getPost();
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
    }

    public function onConstruct()
    {
        parent::onConstruct();
    }


    /**
     * Hc列表+搜索
     * @Access  public
     * @Param   request
     * @Return  jsonData
     * @Permission(action='Hc.listHc')
     */
    public function listHcAction()
    {
        $serverObj = new HcServer();
        $paramIn = $this->paramIn;
        $paramIn['userinfo'] = $this->userinfo;

        /* 检查自己权限 */
        $rolePermissions = $serverObj->checkRolePermissions($this->userinfo);
        if ($rolePermissions['hcBase'] != 1) {
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('4009')));
        }
        //获取hc 作废和修改权限作为参数给到列表获取数据使用
        $paramIn['hcNullify'] = $rolePermissions['hcNullify'];//该字段，前端没使用，废弃字段
        $paramIn['hcUpdate'] = $rolePermissions['hcUpdate'];
        //读取列表数据
        $returnArr = $serverObj->getHcList($paramIn);
        if (empty($returnArr['data'])) {
            $returnArr['data']['dataList'] = [];
            $returnArr['data']['pagination'] = [
                "count"     => 0,
                "pageCount" => 0,
                "pageNum"   => intval($paramIn['page_num']),
                "pageSize"  => $paramIn['page_size'],
            ];
            return $this->jsonReturn($returnArr);
        }

        //设置负责人工号权限字段
        $returnArr['data']['permission']['hcSetManager'] = $rolePermissions['hcSetManager'];
        $returnArr['data']['permission'] = $rolePermissions;

        return $this->jsonReturn($returnArr);
    }

    /**
     *  Hc 负责人设置
     */
    public function saveHcManagerAction()
    {
        $paramIn = $this->paramIn;
        $paramIn['userinfo'] = $this->userinfo;
        $validations = [
            'hc_id' => "Required|Int",
            'manager_staff_id' => "Required",   // 2是身份证，1是护照
        ];
        $this->validateCheck($paramIn, $validations);
        /* 检查自己权限 */
        $rolePermissions = (new HcServer())->checkRolePermissions($this->userinfo);
        if ($rolePermissions['hcSetManager'] != 1) {
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('4009')));
        }
        $res = (new HcServer())->saveHcManager($paramIn);

        return $this->jsonReturn($this->checkReturn(1));
    }

    /**
     * Hc列表导出
     * @Access  public
     * @Param   request
     * @Return  jsonData
     * @Permission(action='Hc.listHcExport')
     */
    public function listHcExportAction()
    {
        $serverObj = new HcServer();

        $paramIn             = $this->paramIn;
        $paramIn['userinfo'] = $this->userinfo;
        $paramIn['export']   = enums::IS_EXPORT_YES;
        $paramIn['lang']     = $this->lang;//语言

        /* 检查自己权限 */
        $rolePermissions = $serverObj->checkRolePermissions($this->userinfo);
        if ($rolePermissions['hcBase'] != 1) {
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('4009')));
        }

        //获取网点HC列表
        $returnCount = (new HcServer())->getHcList(array_merge($paramIn,['is_count' => 1]));

        if (!empty($returnCount['count'] ) && $returnCount['count'] > enums::EXPORT_SYNC_MAX_NUM_LIMIT) {
            throw new ValidationException($this->t->_('asyn_download_max_num', ['num' => enums::EXPORT_SYNC_MAX_NUM_LIMIT]));
        }

        //获取文件名
        $file_name   = 'listHc' . '_' . $this->lang . '_' . date('Ymd-His') . '.xlsx';
        $file_name   = str_replace(' ', '_', $file_name);
        $action_name = 'hc' . WinhrExcelTaskModel::ACTION_NAME_SPACE_SEPARATOR . 'listHcExport';

        //入 task 表 走队列导出
        $data = (new DownLoadTaskService())->insertTask($this->userinfo['id'], $action_name, $paramIn, $file_name);

        return $this->jsonReturn($data);
    }

    /**
     * 我的Hc列表+搜索
     * @Access  public
     * @Param   request
     * @Return  jsonData
     * @Permission(action='Hc.mylistHc')
     */
    public function mylistHcAction()
    {
        $serverObj = new HcServer();

        $paramIn = $this->paramIn;
        $paramIn['userinfo'] = $this->userinfo;

        /* 检查自己是否有权限 人事=7 */
        $rolePermissions = $serverObj->checkRolePermissions($this->userinfo);
        if ($rolePermissions['hcBase'] != 1) {
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('4008')));
        }

        $returnArr = $serverObj->myHcList($paramIn);
        return $this->jsonReturn($returnArr);
    }

    /**
     * Hc创建
     * @Access  public
     * @Param   request
     * @Return  jsonData
     * @throws ValidationException
     */
    public function addHcAction()
    {
        $serverObj = new HcServer();

        $paramIn = $this->paramIn;
        $paramIn['userinfo'] = $this->userinfo;

        /* 检查自己权限 */
        $rolePermissions = $serverObj->checkRolePermissions($this->userinfo);
        if ($rolePermissions['hcBase'] != 1) {
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('4009')));
        }

        $validations = [
            "department_id" => "Required|Int|>>>:" . $this->getTranslation()->_('3003'),
            "worknode_id" => "Required|Str|>>>:" . $this->getTranslation()->_('4019'),
            "expirationdate" => "Required|Date",
            "demandnumber" => "Required|IntGeLe:1,999|>>>:" . $this->getTranslation()->_('4020'),
            "reason_type" => "Required|IntIn:1,2,3",//用人原因 1-招聘 2-转岗 3-离职
            "job_title_id" => "Required|Int|>>>:" . $this->getTranslation()->_('3010'),
            "leave_staffs[*].staff_info_id" => "Int",
            "working_day_rest_type" => "Str|>>>:Working Day Rest Type Error"
        ];

        // 雇佣类型
        $hire_type = (new SysListServer())->getHireType();
        $hire_type_arr = array_column($hire_type,"key");
        $validations['hire_type'] = "Required|IntIn:".implode(',',$hire_type_arr);

        if (isset($paramIn['reason_type']) && $paramIn['reason_type'] == 3) {
            $validations = array_merge($validations, ["reason" => "Required|>>>:" . $this->getTranslation()->_('miss_args')]);
        }
        if (isset($paramIn['hire_type']) && in_array($paramIn['hire_type'], [3, 4])) {
            $validations['hire_times'] = "Required|IntGeLe:1,365";
        }
        if (isset($paramIn['hire_type']) && in_array($paramIn['hire_type'], [2])) {
            $validations['hire_times'] = "Required|IntGeLe:1,12";
        }

        $this->validateCheck($paramIn, $validations);

        //泰国 非一线 语言能力
        if (isCountry('TH') && empty($paramIn['language_ability']) &&  !(new InterviewServer())->isFirstLineJob($paramIn['department_id'], $paramIn['job_title_id'])) {
            throw new ValidationException('Need Language Ability');
        }

        $returnArr = $serverObj->addHc($paramIn);
        return $this->jsonReturn($returnArr);
    }

    /**
     * hc 申请 离职员工列表
     */
    public function getHcStaffLeaveListAction()
    {
        $serverObj = new HcServer();

        $paramIn = $this->paramIn;
        $paramIn['userinfo'] = $this->userinfo;

        /* 检查自己权限 */
        $rolePermissions = $serverObj->checkRolePermissions($this->userinfo);
        if ($rolePermissions['hcBase'] != 1) {
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('4009')));
        }

        $validations = [
            "department_id" => "Required|Int|>>>:" . $this->getTranslation()->_('3003'),
            "store_id" => "Required|Str",
            "job_title_id" => "Required|Int|>>>:" . $this->getTranslation()->_('3010'),
        ];
        $this->validateCheck($paramIn, $validations);
        $returnArr = $serverObj->getHcStaffLeaveList($paramIn);
        return $this->jsonReturn($returnArr);
    }

    /**
     * Hc状态修改
     * @Access  public
     * @Param   request
     * @Return  jsonData
     * @Permission(action='Hc.modifyHc')
     */
    public function modifyHcAction()
    {
        $serverObj = new HcServer();

        //获取参数
        $paramIn = $this->paramIn;
        $paramIn['userinfo'] = $this->userinfo;

        //校验参数
        $validations = [
            "hc_id" => "Required|Int",
            "state_code" => "Required|IntIn:1,2,3,4,5,6,7,8",
            "new_demand_number" => "IntGe:1|>>>:".$this->getTranslation()->_('hc_new_demand_number_error')
        ];
        $this->validateCheck($paramIn, $validations);

        /* 检查自己权限 */
        $rolePermissions = $serverObj->checkRolePermissions($this->userinfo);
        //主逻辑
        if ($paramIn['is_agree'] == 1 || $paramIn['is_agree'] == 2) {
            //审批流状态-同意or不同意

            /* 校验审批权限 */
            if ($rolePermissions['hcApproval'] != 1) {
                return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('4008')));
            }
            $returnArr = $serverObj->modifyHc($paramIn);

        } else if ($paramIn['state_code'] == 4) {
            //hc状态-作废
            //审批权限
            $paramIn['hcNullify'] = $rolePermissions['hcNullify'];
            $paramIn['hcUpdate'] = $rolePermissions['hcUpdate'];

            $returnArr = $serverObj->nullifyHc($paramIn);

        } else {
            $returnArr = self::checkReturn(-3, $this->getTranslation()->_('4008'));
        }
        return $this->jsonReturn($returnArr);
        
    }

    /**
     * Hc状态列表
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function stateListAction()
    {
        $serverObj = new HcServer();

        $paramIn = $this->paramIn;
        $returnArr = $serverObj->statelList($paramIn);
        return $this->jsonReturn($returnArr);
    }

    /**
     * Hc我的审批列表
     * @Access  public
     * @Param   request
     * @Return  jsonData
     * @Permission(action='Hc.myApprovalList')
     */
    public function myApprovalListAction()
    {
        $serverObj = new HcServer();

        $paramIn = $this->paramIn;
        $paramIn['userinfo'] = $this->userinfo;

        $returnArr = $serverObj->myApprovalList($paramIn);
        return $this->jsonReturn($returnArr);
    }

    /**
     * Hc详情
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function hcInfoAction()
    {
        $serverObj = new HcServer();

        $paramIn = $this->paramIn;
        $paramIn['userinfo'] = $this->userinfo;
        $validations = [
            "hc_id" => "Required|Int",
        ];
        $this->validateCheck($paramIn, $validations);
        $returnArr = $serverObj->hcInfo($paramIn);
        return $this->jsonReturn($returnArr);
    }

    /**
     * Hc详情
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function hcInfoBaseAction()
    {
        $serverObj = new HcServer();

        $paramIn = $this->paramIn;
        $paramIn['userinfo'] = $this->userinfo;
        $validations = [
            "hc_id" => "Required|Int",
        ];
        $this->validateCheck($paramIn, $validations);
        $returnArr = $serverObj->hcInfoBase($paramIn);
        return $this->jsonReturn($returnArr);
    }

    /**
     * 面试官列表
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function interviewerListAction()
    {
        $serverObj = new HcServer();

        $paramIn = $this->paramIn;
        $returnArr = $serverObj->interviewerList($paramIn);
        return $this->jsonReturn($returnArr);
    }

    /**
     * 网点列表
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function worknodeListAction()
    {
        $serverObj = new HcServer();

        $paramIn = $this->paramIn;
        $returnArr = $serverObj->worknodeList($paramIn);
        return $this->jsonReturn($returnArr);
    }

    /**
     * 登陆者网点信息
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function currentWorknodeAction()
    {
        $userinfo = [
            'id' => $this->userinfo['id'],
            'name' => $this->userinfo['name'],
            'store_id' => $this->userinfo['store_id'] ?? -1,
            'store_name' => $this->userinfo['store_name'] ?? 'Head Office',
            'department_id' => $this->userinfo['department_id'],
            'department_name' => $this->userinfo['department_name'],
        ];

        //当前登录人的部门
        $staffInfo = HrStaffInfoModel::findFirst([
            'conditions' => "staff_info_id = :id:",
            'bind' => ['id' => $userinfo['id']],
            'columns' => ['node_department_id', 'id']
        ]);
        if (!empty($staffInfo)) {
            $deptId = $staffInfo->node_department_id ?? 0;
        } else {
            $deptId = 0;
        }
        //判断是否显示预算人数 申请人所在部门为共享 部门，则不显示预算人数
        $is_show_hc = (new HcServer())->isShowHc($deptId);
        $userinfo = array_merge($userinfo, ['is_show_hc' => $is_show_hc]);

        $this->jsonReturn(self::checkReturn(['data' => $userinfo]));
    }

    /**
     * 超管修改HC
     * @Access  public
     * @Param   request
     * @Return  jsonData
     * @Permission(action='Hc.updateHc')
     */
    public function updateHcAction()
    {
        $serverObj = new HcServer();

        //获取参数
        $paramIn = $this->paramIn;
        $paramIn['userinfo'] = $this->userinfo;

        //校验参数
        $validations = [
            "hc_id" => "Required|Int",
            //"state_code" => "Required|IntIn:1,2,3,4,5,6,7,8,9",
        ];

        if (isset($paramIn['hire_type']) && $paramIn['hire_type']) {
            // 雇佣类型
            $hire_type = (new SysListServer())->getHireType();
            $hire_type_arr = array_column($hire_type,"key");
            $validations['hire_type'] = "Required|IntIn:".implode(',',$hire_type_arr);
        }

        if (in_array($paramIn['hire_type'], [3, 4])) {
            $validations['hire_times'] = "Required|IntGeLe:1,365";
        } elseif (in_array($paramIn['hire_type'], [2])) {

            $validations['hire_times'] = "Required|IntGeLe:1,12";
        }
        $this->validateCheck($paramIn, $validations);

        /* 检查自己权限 */
        //hc超级账号
        $superAdministratorIds = env('superAdministratorIds', "");
        $superAdministratorIdsArr = explode(',', $superAdministratorIds);
        if (!in_array($this->userInfo['id'], $superAdministratorIdsArr)) {
            $rolePermissions = $serverObj->checkRolePermissions($this->userinfo);
            //修改权限
            $paramIn['hcUpdate'] = $rolePermissions['hcUpdate'];
        } else {
            //修改权限
            $paramIn['hcUpdate'] = 1;
        }

        $returnArr = $serverObj->updateHc($paramIn);
        return $this->jsonReturn($returnArr);
    }

    /**
     * 辅助接口---通过接口修改hc主状态 审批阶段 审批状态 审批人
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function modifyHcHelpersAction()
    {
        $serverObj = new HcServer();

        //获取参数
        $paramIn = $this->paramIn;
        $paramIn['userinfo'] = $this->userinfo;

        //校验参数
        $validations = [
            //当前hc主状态
            "hc_state_code" => "Required|IntIn:1,2,3,4,5,6,7,8,9",
            //当前hc审批阶段
            "hc_approval_stage" => "Required|IntIn:1,2,3,4,5,6,7,8,9",
            //当前hc审批状态
            "hc_approval_state_code" => "Required|IntIn:1,2,3,4,5,6,7,8,9",
            //当前hc审批人
            "hc_approval_staff_id" => "Required|Int",

            //要改为的hc主状态
            "modify_hc_state_code" => "Required|IntIn:1,2,3,4,5,6,7,8,9",
            //要改为的hc审批阶段
            "modify_hc_approval_stage" => "Required|IntIn:1,2,3,4,5,6,7,8,9",
            //要改为的hc审批状态
            "modify_hc_approval_state_code" => "Required|IntIn:1,2,3,4,5,6,7,8,9",
            //要改为的hc审批人
            "modify_hc_approval_staff_id" => "Required|Int",
        ];
        $this->validateCheck($paramIn, $validations);
        $returnArr = $serverObj->modifyHcHelpers($paramIn);
        return $this->jsonReturn($returnArr);
    }


    /**
     * 网点HC统计列表
     * @Access  public
     * @Param   request
     * @Return  jsonData
     * @Permission(action='Hc.listStoreHc')
     */
    public function listStoreHcAction()
    {
        $paramIn = $this->paramIn;
        $paramIn['userinfo'] = $this->userinfo;

        //获取网点HC列表
        $returnArr = (new HcServer())->getStoreHcList($paramIn);

        return $this->jsonReturn($returnArr);
    }

    /**
     * 网点HC统计列表
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function ExportStoreHcAction()
    {
        $paramIn = $this->paramIn;
        $paramIn['userinfo'] = $this->userinfo;
        $paramIn['export'] = 1;
        $paramIn['lang'] = $this->lang;//语言

        //获取网点HC列表
        $returnCount = (new HcServer())->getStoreHcList(array_merge($paramIn,['is_count' => 1]));

        if (!empty($returnCount['count'] ) && $returnCount['count'] > enums::EXPORT_SYNC_MAX_NUM_LIMIT) {
            throw new ValidationException($this->t->_('asyn_download_max_num', ['num' => enums::EXPORT_SYNC_MAX_NUM_LIMIT]));
        }

        //获取文件名
        $file_name   = 'listStoreHc'.'_'.$this->locale.'_'.date('Ymd-His').'.xlsx';
        $file_name   = str_replace(' ', '_', $file_name);
        $action_name = 'hc'.WinhrExcelTaskModel::ACTION_NAME_SPACE_SEPARATOR.'store_hc_dow_excel';

        //入 task 表 走队列导出
        $data = (new DownLoadTaskService())->insertTask( $this->userinfo['id'], $action_name, $paramIn,$file_name);

        return $this->jsonReturn($data);

    }

    /**
     * 网点HC待入职列表
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function listPendingByStoreAction()
    {
        $paramIn = $this->paramIn;
        $paramIn['userinfo'] = $this->userinfo;

        //获取网点HC列表
        $returnArr = (new HcServer())->getPendingByStore($paramIn);

        return $this->jsonReturn($returnArr);
    }

    /**
     * 网点HC审批通过并且在招聘中的人数
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function listApprovalByStoreAction()
    {
        $paramIn = $this->paramIn;
        $paramIn['userinfo'] = $this->userinfo;

        //获取网点HC列表
        $returnArr = (new HcServer())->getApprovalByStore($paramIn);

        return $this->jsonReturn($returnArr);
    }

    /**
     * 修改hc需求数
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function modifyDemandnumberAction()
    {
        $serverObj = new HcServer();

        $paramIn = $this->paramIn;
        $paramIn['userinfo'] = $this->userinfo;

        //校验参数
        $validations = [
            "hc_id" => "Required",
            "demandnumber" => "Required|Int",
        ];
        $this->validateCheck($paramIn, $validations);

        //获取网点HC列表
        $returnArr = $serverObj->modifyDemandnumber($paramIn);
        return $this->jsonReturn($returnArr);
    }

    /**
     * 修改hc优先级
     * @Access  public
     * @Param   request
     * @Return  jsonData
     * @Permission(action='Hc.modifyPriority')
     */
    public function modifyPriorityAction()
    {
        $serverObj = new HcServer();

        $paramIn = $this->paramIn;
        $staffInfoId = $this->userinfo['id'];
        $paramIn['staff_id'] = $this->userinfo['id'];

        //校验参数
        $validations = [
            "hc_id"       => "Required",
            "priority_id" => "Required|Int",
        ];
        $this->validateCheck($paramIn, $validations);

        $staffInfo = (new \FlashExpress\bi\App\Repository\StaffRepository())->getStaffInfoById($staffInfoId);
        //昕哲需求关闭 Area Manager、Regional Manager更改权限
        //5830 【HRIS】优化“修改优先级”按钮的权限
        //http://193x782t53.imwork.net:29667/zentao/story-view-5830.html
        if (in_array($staffInfo['job_title'], [
            enums::$job_title['District Manager'],
            enums::$job_title['Regional Manager'],
        ])) {
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('2107')));
        }

        //获取网点HC列表
        $returnArr = $serverObj->modifyPriority($paramIn);
        return $this->jsonReturn($returnArr);
    }

    /**
     * 修改hc Budget
     * @Access  public
     * @Param   request
     * @Return  jsonData
     * @Permission(action='Hc.modifyHcBudget')
     */
    public function modifyHcBudgetAction()
    {
        $serverObj = new HcServer();

        $paramIn = $this->paramIn;
        $paramIn['userinfo'] = $this->userinfo;

        //校验参数
        $validations = [
            "store_id" => "Required",
            "jd_id" => "Required|Int",
            "budget" => "Required|Int|IntGeLe:0,999|>>>:" . $this->getTranslation()->_('4020')
        ];
        $this->validateCheck($paramIn, $validations);
        //获取网点HC列表
        $returnArr = $serverObj->modifyHcBudget($paramIn);
        return $this->jsonReturn($returnArr);
    }


    /**
     * 导入hc Budget
     * @Access  public
     * @Param   request
     * @Return  jsonData
     * @Permission(action='Hc.importHcBudget')
     */
    public function importHcBudgetAction()
    {
        $serverObj = new HcServer();
        if (!$this->request->hasFiles()) {
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('miss_args')));
        }
        $files = $this->request->getUploadedFiles();
        $file = $files[0];
        $extension = $file->getExtension();
        if ($extension !== "xlsx") {
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('miss_args')));
        }

        $temp = $file->getTempName();
        //获取网点HC列表
        $flag = $serverObj->importHcBudget($temp);
        if ($flag === true) {
            return $this->jsonReturn(self::checkReturn(1));
        } else {
            $this->getDI()->get('logger')->write_log("importHcBudgetAction 导入失败:" . $flag, 'info');
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('4205')));
        }
    }

    /**
     * 校验hc是否重复，如果重复返回重复列表
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function checkRepeatHcAction()
    {
        $serverObj = new HcServer();

        $paramIn = $this->paramIn;
        $paramIn['userinfo'] = $this->userinfo;

        /* 检查自己权限 */
        $rolePermissions = $serverObj->checkRolePermissions($this->userinfo);
        if ($rolePermissions['hcBase'] != 1) {
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('4009')));
        }

        $validations = [
            "department_id" => "Required|Int|>>>:" . $this->getTranslation()->_('3003'),
            "worknode_id" => "Required|Str|>>>:" . $this->getTranslation()->_('4019'),
            "job_id" => "Required|Int|>>>:" . $this->getTranslation()->_('3010'),
            "reason_type" => "Required|IntIn:1,2,3",//用人原因 1-招聘 2-转岗 3-离职
        ];

        $this->validateCheck($paramIn, $validations);

        $returnArr = $serverObj->checkRepeatHcRequestList($paramIn);
        return $this->jsonReturn($returnArr);
    }

    /**
     * 批量导出待审批hc
     */
    public function exportHcAction()
    {
        $serverObj = Tools::reBuildCountryInstance(new \FlashExpress\bi\App\Server\HcServer());
    
        $paramIn = $this->paramIn;
        $paramIn['userinfo'] = $this->userinfo;
    
        /* 检查自己权限 */
        $rolePermissions = $serverObj->checkRolePermissions($this->userinfo);
        if ($rolePermissions['hcBase'] != 1) {
            $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('4009')));
        }
    
        $return_data = $serverObj->exportApproveHc($this->userinfo['id']);
        $returnData = [
            'code' => 1,
            'msg' => 'ok',
            'data' => [
                'file_url' => $return_data['export_url'],
            ],
        ];
        $this->jsonReturn($returnData);
    }

    /**
     * 批量上传审批hc
     *
     */
    public function uploadHcAction()
    {
        $serverObj = Tools::reBuildCountryInstance(new \FlashExpress\bi\App\Server\HcServer());
        $excel_file = $this->request->getUploadedFiles();
        if (empty($excel_file)) {
            $this->jsonReturn(1, 'Excel File Empty', []);
        }
        $extension = $excel_file[0]->getExtension();
        if ($extension !== "xlsx") {
            $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('8015')));
        }
        if ($serverObj->getApprovalTask()) {
            $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('8020')));
        }
        $config = ['path' => ''];
        $excel = new \Vtiful\Kernel\Excel($config);
        // 读取文件
        $excel_data = $excel->openFile($excel_file[0]->getTempName())
            ->openSheet()
            ->setSkipRows(0)
            ->setType([
            ])
            ->getSheetData();
        if (empty($excel_data)){
            $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('8402')));
        }
        $count = count($excel_data);
        if ($count > 1000) {
            $this->jsonReturn(self::checkReturn(-3, '单次不能超过1000条数据'));
        }
        $this->getDI()->get('logger')->write_log("uploadHcAction-step6:", 'info');

        if (count($excel_data[0]) != 10) {
            throw new ValidationException($this->getTranslation()->_('file_num_error'));
        }

        $res = $serverObj->dealAgentApproval($this->userInfo['id'], $excel_data);
        $this->jsonReturn($res);
    }

    /**
     * hc批量审批
     */
    public function uploadHcAuditAction()
    {

        $paramIn = $this->paramIn;
        $validations = [
            'file_url' => "Required|StrLenGe:1|>>>:file_url param error",
        ];
        $this->validateCheck($paramIn, $validations);
        $paramIn['staff_info'] = $this->userInfo;
        (new HcServer())->uploadHcAuditImportUseLock($paramIn);

        return $this->jsonReturn([
            'code' => 1,
            'msg' => 'ok',
            'data' => []
        ]);
    }

    /**
     * 查询当前审批是否完成
     * */

    public function isApproveDoneAction()
    {
        $paramIn = $this->paramIn;
        if (!isset($paramIn['task_id']) || empty($paramIn['task_id'])) {

            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('4008')));
        }

        $res = (new HcServer())->getApprovalResult($paramIn['task_id']);
        return $this->jsonReturn([
            'code' => 1,
            'msg' => 'ok',
            'data' => $res
        ]);
    }

    /**
     * 导出失败
     * */

    public function exportFailHcAction()
    {
        $paramIn = $this->paramIn;
        if (!isset($paramIn['task_id']) || empty($paramIn['task_id'])) {

            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('4008')));
        }

        $res = (new HcServer())->exportHc($paramIn['task_id']);
        return $this->jsonReturn([
            'code' => 1,
            'msg' => 'ok',
            'data' => $res
        ]);
    }

    /**
     * 审批模版
     * */

    public function exportTemplateAction()
    {
        return $this->jsonReturn([
            'code' => 1,
            'msg' => 'ok',
            'data' => ['export_url' => 'https://tc-static-asset-internal.flashexpress.com/workOrder/1726224809-04cc1b14147f4a58949d084dcaf30576.xlsx']
        ]);
    }

    /**
     * hc批量修改优先级
     */
    public function dealHcPriorityAction()
    {
        $paramIn = $this->paramIn;
        $validations = [
            'file_url' => "Required|StrLenGe:1|>>>:file_url param error",
        ];
        $this->validateCheck($paramIn, $validations);
        $paramIn['staff_id'] = $this->userInfo['id'];
        (new HcServer())->BatchPriorityImportUseLock($paramIn);

        return $this->jsonReturn([
            'code' => 1,
            'msg' => 'ok',
            'data' => []
        ]);
    }

    /**
     * hc批量创建
     */
    public function hcAddBatchAction()
    {
        $paramIn = $this->paramIn;
        $validations = [
            'file_url' => "Required|StrLenGe:1|>>>:file_url param error",
        ];
        $this->validateCheck($paramIn, $validations);
        $paramIn['staff_info'] = $this->userInfo;
        (new HcServer())->batchInsertHcImportUseLock($paramIn);

        return $this->jsonReturn([
            'code' => 1,
            'msg' => 'ok',
            'data' => []
        ]);
    }

    /**
     * hc批量作废
     */
    public function hcNullifyBatchAction()
    {
        $paramIn = $this->paramIn;
        $validations = [
            'file_url' => "Required|StrLenGe:1|>>>:file_url param error",
        ];
        $this->validateCheck($paramIn, $validations);
        $paramIn['staff_info'] = $this->userInfo;
        (new HcServer())->batchNullifyHcImportUseLock($paramIn);

        return $this->jsonReturn([
            'code' => 1,
            'msg' => 'ok',
            'data' => []
        ]);
    }

    /**
     * hc批量作废
     */
    public function hcUpdateBatchAction()
    {
        $paramIn = $this->paramIn;
        $validations = [
            'file_url' => "Required|StrLenGe:1|>>>:file_url param error",
        ];
        $this->validateCheck($paramIn, $validations);
        $paramIn['staff_info'] = $this->userInfo;
        (new HcServer())->hcUpdateBatchImportUseLock($paramIn);

        return $this->jsonReturn([
            'code' => 1,
            'msg' => 'ok',
            'data' => []
        ]);
    }


    //获取变更记录
    public function getHcLogListAction(){
        $paramIn     = $this->paramIn;
        $validations = [
            'hc_id' => 'Required|Int',
        ];

        $this->validateCheck($paramIn, $validations);
        $data = (new HcServer())->getHcLog($paramIn);
        $this->jsonReturn($data);

    }

    /**
     * 我的HC 导入结果列表
     * @Permission(action='Hc.importList')
     */
    public function importListAction()
    {
        $paramIn = $this->paramIn;

        $paramIn['staff_id'] = $this->userInfo['id'];

        $result = (new HcServer())->importList($paramIn);
        return $this->jsonReturn([
            'code' => 1,
            'msg'  => 'ok',
            'data' => $result,
        ]);
    }

    /**
     * 我的HC审批 导入结果列表
     * @Permission(action='Hc.importAuditList')
     */
    public function importAuditListAction()
    {
        $paramIn = $this->paramIn;

        $paramIn['staff_id'] = $this->userInfo['id'];

        $result = (new HcServer())->importAuditList($paramIn);

        return $this->jsonReturn([
            'code' => 1,
            'msg'  => 'ok',
            'data' => $result,
        ]);
    }

    /**
     * 我的HC审批 导入结果列表
     */
    public function deleteImportTaskAction()
    {
        $paramIn     = $this->paramIn;
        $validations = [
            'id' => "Required|IntGt:0|>>>:id not empty",  // 导入任务id
        ];
        $this->validateCheck($paramIn, $validations);

        $paramIn['staff_id'] = $this->userInfo['id'];

        $result = (new HcServer())->deleteImportTaskUseLock($paramIn);

        return $this->jsonReturn([
            'code' => 1,
            'msg'  => 'ok',
            'data' => $result,
        ]);
    }

    /**
     * 获取hc关联的jd信息
     * @return void
     * @throws ValidationException
     */
    public function getRelationInfoByHcIdAction()
    {
        $hcId = $this->request->get('hc_id', 'trim');

        if (empty($hcId)) {
            throw new ValidationException($this->t->_('operation_data_not_exist'));
        }

        $data = (new HcServer())->getRelationInfoByHcId($hcId);

        $this->jsonReturn($this->checkReturn(['data' => $data]));
    }
}
