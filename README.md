WinHR API开发规范
==========
==========

###  开发工具 & 环境

* IDE: PhpStorm
* 环境搭建：[mac 环境包]


### 目录结构

```

├── app/                        # 开发目录
│   ├── asset                       # 
│   ├── common                      # 公共方法
│   ├── config                      # 系统配置
│   ├── controllers                 # 控制器 接口的大门 (多国家默认入口逻辑)
│   ├── core                        # base类
│   ├── library                     # 三方类库的具体实现
│   ├── messages                    # 语言包
│   ├── models                      # model文件
│   │   ├── backyard                    # backyard 库
│   │   ├── bi                          # bi 库
│   │   └── fle                         # fle 库
│   ├── modules                     # 多国家模块
│   │   ├── La                          # 老挝的业务逻辑
│   │   │   ├── controllers                 # 控制器
│   │   │   ├── language                    # 语言包  目前共用的最外层messages
│   │   │   ├── library                     # 三方类库的具体实现
│   │   │   ├── models                      # 
│   │   │   ├── server                      # 逻辑层
│   │   │   └── tasks                       # 任务脚本
│   │   ├── My                          # 马来西亚的业务逻辑
│   │   │   ├── controllers                 # 控制器
│   │   │   ├── language                    # 语言包  目前共用的最外层messages
│   │   │   ├── library                     # 三方类库的具体实现
│   │   │   ├── models                      # 
│   │   │   ├── server                      # 逻辑层
│   │   │   └── tasks                       # 任务脚本
│   ├── plugins                     # 插件 
│   ├── repository                  # 数据库操作层
│   ├── runtimes                    # 运行日志相关 
│   ├── server                      # 多国家公用逻辑层 ，通用性的db查询 都放到repository里方便维护
│   ├── tasks                       # 多国家公用任务脚本
│   ├── traits                      # 特性
│   ├── uconfig                     # 又是一些配置信息 20210-8-31后 不要再加了  新增的可以放到 enums 里
│   ├── util                        # 工具 
│   ├── views                       # 视图文件 
│   └── cli.php                     # 任务脚本注册文件
│
├── public/                    # ★★★★★ 入口
│   ├── index.php              # 入口文件
│   
│
├── vendor/                     # 第三方扩展包 (安装 composer)，不更改这里的任何代码，若欲修改请利用 Extensions
│      
│
└── .env                        # 配置环境 (生产环境已排除)

```

### 注意事项必须执行
1. 所有时间用UTC 时区，试用不同国家和市场
2. 所有文件上oss适应分布式部署
3. 所有try代码上分校验错误，和系统错误
4. 所有sql，要经过自己测试是否用到索引
5. 翻译问题足够重视，给到产品或是翻译同学使用，避免修改错误的情况
6. 上线按照流程操作，样例sql，代码逻辑说明，脚本任务
7. 一个功能一个分支 feature/名字/分之名称
8. 导出功能重复点击+锁
9. 一个项目只有一个svc/call 不能添加 svc/aaa   svc/bbbb
10. task 任务 名字一律小写 aaa_ccccAction 可以这样 不能 AaaaCccccAction
11. 按照MVC 模式开发。
12. Sql不能拼接。必须使用insertAsDict、updateAsDict、delete 不允许execute 可参考Phalcon数据库抽象层
13. 任何数据要处理为空的情况
14. 设计异步导出的，三步 1 异步任务编写 2 任务参数入库 3 task与导出成对出现
15. 数据库表必须有主键
16. hris-schemas项目的release命名规范：release-日期-两位整数(根据同日期两位整数从01开始叠加).sql  例子：release-v20210823-01.sql
17. sql注释语法 : 从两个连续横杠 --  序列到行尾。 在MySQL中， - （两个连续横杠 ）注释样式要求第二个破折号后跟至少一个空格或控制字符（例如空格，制表符，换行符等）
18. svc 只使用 library 目录下 ApiClient 类
19. 日志等级需要正确使用，多记录日志，方便debug，除非你认为用不到
20. 参数绑定的方式有 :: 和 ? 两种，看看前人是怎么用的。
21. SQL中使用in进行范围查询时，in的元素控制在500以为，否则进行分批处理。
22. 进行循环操作时，循环体内部定义的变量给默认值 或 置空
23. 不要在循环里实例化对象
24. 不要在构造函数里实例化对象
25. 代码格式化，提交的代码必须要格式化，除非有其他人也在同时修改这个文件
26. 在使用绑定参数进行数据库查询的时候，如果对数组使用了array_unique  ，则要加上 array_values 重置索引
27. 新建的表、新增连表 要将SQL样例写到hris-schemas项目中
28. 项目负责人在代码review时，要在生产环境执行SQL，查看索引使用情况
29. 查询fle、bi库时候，除leader同意的均需要使用接口调用
30. by用户侧提交接口 使用 UseLock加原子锁，静态数据使用FromCache 缓存
31. 新增接口、老接口重构的 controller层不再使用try catch，验证逻辑提示统一使用 throw方式向上层抛
32. 上线完成后，开发人员需要确认SQL和脚本 是否已生效 项目负责人张贴执行截图
33. 不要用SQL去兼容历史数据，要进行历史数据的清洗，保证SQL的性能
34. 循环开启事务，begin  commit/rollback 要成对出现，避免事务嵌套导致数据不提交。

### 严格遵守PSR规范

[PSR](https://www.php-fig.org/psr/)
[PSR中文版本](https://psr.phphub.org/)

### 基本代码规范

* 类的属性命名小写开头的驼峰式 ($camelCase)
* 类的方法命名小写开头的驼峰式 (getUserInfo)
* 方法参数命名使用下划线分隔式 ($user_id)
* 关键字使用顺序

### 代码开发规范

* === 所有方法都必须完善基本注释 ===
* 若对 .env 文件的字段有增删改，请记录至 .env.example 中
* Controllers 层只关注用户逻辑，业务逻辑由Server模块负责
* 所有加了缓存的方法必须在方法名标识 (例如 getCacheList)
* 只要与预期不相同的错误都要用 throw new Exception() 抛出，每个方法的作用域中尽量只用唯一的 return
* 补齐每次查询中要挑选的字段 select() ，可有效加快查询速度
* 当方法的业务逻辑复杂，有一定程度的消耗资源，务必加上缓存来减少 SQL 查询次数
* 基于名副其实原则，不要尝试命名长度小于 3 个字的变量名
* 接口不应返回前端不采用的数据，尽可能减少数据量可显著降低接口数据传输时间
* 数据库查询返回结果被后续逻辑使用时需要加非空判断
* 命名原则与功能一定要与团队统一，不要自己造轮子，尤其是数据分页的方法、街口字段名、数据字段名等
* 基于高内聚低耦合最高指导方针开发模块，遵照 OCP、SRP、DIP、LKP、LSP、ISP、CARP 原则
* 数据库的任何变更都需记录在 schemas 中
* controller 层使用try catch 必须在文件首部 use Exception

* === 假如撰写必须要注释一个月后才能知道自己写的都是啥方法就重构它 ===


### 代码风格规范

* 禁止把 && 及 || 充当 if else 来用
* 三元运算符 ?: 少量使用，只能用在长度不超过 70 字的变量赋值与 return
* 统一代码风格，提交的代码需要通过 friendsofphp/php-cs-fixer 检查，可在不分系统，所有的 IDE 上配置 ( 例如 VIM )

```
说明文档: https://mlocati.github.io/php-cs-fixer-configurator/
安装: composer global require friendsofphp/php-cs-fixer
IDE配置: 百度
配置参数: php-cs-fixer fix $file --config=$path/.php_cs --using-cache=no

```


#### 普通函数和类中的函数注释

```
/**
 * 获取头像地址
 *
 * <AUTHOR> <<EMAIL>>
 *
 * @param string  $imageName  图片文件名
 * @param integer $size 	   大小
 *
 * @return string
 */
function getAvatarUrl($imageName, $size = 80)
{
    return sprintf(SITE_URL . '/service/images/cropped_%s/'.$imageName, $size);
}

```
* 顺序按照author、param、return来放，**区块间空行**。

#### 程序段落注释

* 段落注释和逻辑注释使用如下方式

```
/**
 * 1 如果$_GET['do']等于buy,则购买条码
 */
if($_GET['do'] == 'buy')
{
    // 1.1 验证用户提交变量是否合法
    if($_POST['strCodeNum'])
    {
        
    }
    // 1.2 验证用户提交的码是否可以购买
    
    // 1.3 ..................
} // end if

/**
 * 2 如果$_GET['do']等于list,显示用户选择的条码
 */
if($_GET['do'] == 'list')
{
    // 2.1 验证用户提交变量是否合法
    if($_POST['strCodeNum'])
    {
        
    }
    // 2.2 验证用户提交的码是否可以购买
    
    // 2.3 ..................
} // end if
```

### 延伸阅读
[php安全编码规范](https://l8bx01gcjr.feishu.cn/docs/doccnvODX6NPGd8zygZRhiZzwah#Rx5GAB)
[composer 自动载入 autoload 的使用详解](https://my.oschina.net/sallency/blog/893518)
[PSR-12 基本代码规范](https://www.php-fig.org/psr/psr-12/)
[【阿里技术】这一团糟的代码，真的是我写的？！](https://yq.aliyun.com/articles/718155?utm_content=g_1000076022)
[php注释规范](https://gist.github.com/angusty/5efaae783bc45a0dff391d2362febb22)
[Phalcon 3.4中文手册](https://www.kancloud.cn/jaya1992/phalcon_doc_zh)

### 其它 参考

* 与 DB 相关的 SQL 语句排查可以利用此方法返回: echo $this->di->get('profiler')->getLastProfile()->getSQLStatement();
* 执行计划任务: php cli.php test  testaaa  arg1 arg2 
