<?php


namespace FlashExpress\bi\App\Modules\MY\Server;

use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\RedisListEnums;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\StaffSalaryEpfSocsoModel;
use FlashExpress\bi\App\Server\NoticeServer as GlobalBaseServer;

/**
 * 注意
 * 改类只用与发送消息
 */
class NoticeServer extends GlobalBaseServer
{
    /**
     * 完善车辆信息 消息标题内容
     * @return array
     */
    public function getVehicleMessageTitleAndContent(): array
    {
        $default_lang = getCountryDefaultLang();

        return [
            'title' => $this->getTranslation($default_lang)->_('vehicle_information_filling_title_my_v21270'),
            'content' => $this->getTranslation($default_lang)->_('vehicle_information_filling_content_my_v21270',
                ['vehicle_info_path' => $this->getFillingVehicleInformationPath()]),
        ];
    }

    /**
     * 入职填写TP3
     */
    public function sendTP3msg()
    {
        //无底薪不发送
        if (in_array($this->params['hire_type'],HrStaffInfoModel::$agentTypeTogether)) {
            return;
        }
        $msg_title         = 'Reminder: TP3 Submission';
        $msg_content       = '';
        $add_message_param = [
            'staff_info_ids_str' => $this->params['new_staff_info_id'],
            'staff_users'        => [$this->params['new_staff_info_id']],
            'message_title'      => $msg_title,
            'message_content'    => $msg_content,
            'add_userid'         => $this->params['admin_staff_id'],
            'category'           => 45,
            'id'                 => $this->params['tp3_msg_id'],
        ];
        if (date('d') >= 24) {
            $this->redisLib->lpush(RedisListEnums::TP3_WRITE_MESSAGE,
                json_encode(['params' => $add_message_param, 'lang' => $this->params['lang']]));
            return;
        }
        $apiClient         = new ApiClient('bi_rpc', '', 'add_kit_message', $this->params['lang']);
        $apiClient->setParams($add_message_param);
        $apiClient->execute();
    }

    /**
     * 公积金号码填写提醒
     */
    public function sendEpfNoMsg()
    {
        if (!($this->params['working_country'] == 3 && $this->params['formal'] == 1)) {
            return;
        }
        //无底薪不发送
        if (in_array($this->params['hire_type'],HrStaffInfoModel::$agentTypeTogether)) {
            return;
        }
        $msg_title         = $this->getTranslation($this->params['lang'])->_('epf_no_notice');
        $add_message_param = [
            'staff_info_ids_str' => $this->params['new_staff_info_id'],
            'staff_users'        => [$this->params['new_staff_info_id']],
            'message_title'      => $msg_title,
            'message_content'    => '',
            'add_userid'         => $this->params['admin_staff_id'],
            'category'           => 110,
            'id'                 => $this->params['epf_no_msg_id'],
        ];
        $apiClient         = new ApiClient('bi_rpc', '', 'add_kit_message', $this->params['lang']);
        $apiClient->setParams($add_message_param);
        $apiClient->execute();
    }
}
