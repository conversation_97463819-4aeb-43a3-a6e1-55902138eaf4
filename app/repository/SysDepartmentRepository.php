<?php

namespace FlashExpress\bi\App\Repository;

use App\Library\Validation\BuddyException;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\HrHcLogModel;
use FlashExpress\bi\App\Models\backyard\HrhcModel;
use FlashExpress\bi\App\Models\backyard\HrJdModel;
use FlashExpress\bi\App\Models\backyard\HrJobTitleModel;
use FlashExpress\bi\App\Models\backyard\HrMyApprovalModel;
use FlashExpress\bi\App\Models\backyard\HrStateModel;
use FlashExpress\bi\App\Models\backyard\SysDepartmentModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Server\MessageQueueServer;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Server\StaffServer;

class SysDepartmentRepository extends BaseRepository
{

    public function getDepartmentList(array $columns, array $condition = [], array $bind = [])
    {
        $query_info['columns'] = '*';
        if ($columns) {
            $query_info['columns'] = implode(' , ', $columns);
        }
        if ($condition) {
            $query_info['conditions'] = implode(' AND ', $condition);
        }

        if ($bind) {
            $query_info['bind'] = $bind;
        }

        return SysDepartmentModel::find($query_info)->toArray();
    }

    /**
     * 获取部门数据单条信息
     * @param array $columns
     * @param array $condition
     * @param array $bind
     * @return array
     */
    public function getDepartmentInfo(array $columns, array $condition = [], array $bind = [])
    {
        $query_info['columns'] = '*';
        if ($columns) {
            $query_info['columns'] = implode(' , ', $columns);
        }
        if ($condition) {
            $query_info['conditions'] = implode(' AND ', $condition);
        }

        if ($bind) {
            $query_info['bind'] = $bind;
        }

        $info =  SysDepartmentModel::findFirst($query_info);

        return $info ? $info->toArray() : [];
    }

    /**
     * 获取部门数据单条信息通过ID
     * @param array $columns
     * @param array $condition
     * @param array $bind
     * @return array
     */
    public function getDepartmentInfoById($id, $columnsArr = [])
    {
        if (!$id) {
            return [];
        }

        return $this->getDepartmentInfo($columnsArr, ['id = :id:'], ['id' => $id]);
    }



}
