/*
[version]1.01[version]
[updateDate]2021-07-06 00:00:00[updateDate]
*/
--
-- Table structure for table `assets_goods`
--

-- ysql -umy_ard_dev_rw -pBuhxyob_ey8x -h192.168.0.229 --default-character-set=utf8 my-backyard < /Users/<USER>/pycode/init/sql_b/backyard_pro_tables-20210706.sql

CREATE TABLE `approve_modify_mileage` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `serial_no` varchar(32) DEFAULT NULL COMMENT '序列号',
  `origin_id` bigint(20) DEFAULT NULL COMMENT '原记录编号',
  `staff_info_id` int(11) unsigned DEFAULT '0' COMMENT '申请人工号',
  `mileage_date` date DEFAULT NULL COMMENT '里程日期',
  `origin_start_kilometres` bigint(20) DEFAULT NULL COMMENT '原上班公里数(m)',
  `start_kilometres` bigint(20) unsigned DEFAULT NULL COMMENT '上班公里数(m)',
  `origin_end_kilometres` bigint(20) DEFAULT NULL COMMENT '原下班公里数(m)',
  `end_kilometres` bigint(20) unsigned DEFAULT NULL COMMENT '下班公里数(m)',
  `status` tinyint(4) DEFAULT '0' COMMENT '状态 1 待审核 2 通过',
  `approve_user` int(11) DEFAULT '0' COMMENT '审批人',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_origin_id` (`origin_id`),
  UNIQUE KEY `idx_id_date` (`staff_info_id`,`mileage_date`)
) ENGINE=InnoDB AUTO_INCREMENT=6587 DEFAULT CHARSET=utf8mb4 COMMENT='修改里程表数审批';


--
-- Table structure for table `assets_goods`
--

-- mysql -umy_ard_dev_rw -pBuhxyob_ey8x -h192.168.0.229 --default-character-set=utf8mb4 my-backyard < /Users/<USER>/pycode/init/sql_b/backyard_pro_tables-20210706.sql

CREATE TABLE `assets_goods` (
  `id` int(11) NOT NULL,
  `bar_code` varchar(20) DEFAULT '' COMMENT 'barcode',
  `goods_name_en` varchar(100) DEFAULT '' COMMENT '资产名称（英文）',
  `goods_name_th` varchar(100) DEFAULT '' COMMENT '资产名称（泰文）',
  `goods_name_zh` varchar(100) DEFAULT '' COMMENT '资产名称（中文）',
  `type` tinyint(2) DEFAULT '0' COMMENT '1.DC/SP 2.HUB 3.shop',
  `deleted` tinyint(1) DEFAULT '0' COMMENT '1.已删除 0.未删除',
  `category` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '  1 可持有中的可申请资产',
  `sku_bar_code` varchar(255) NOT NULL DEFAULT '' COMMENT '资产型号 bar_code 列表',
  `is_public` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '1 公共资产 0个人资产',
  `is_batch` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否是批量类型资产 0 默认否 1 批量类型',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `udpated_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_bar_code` (`bar_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='网点可申请资产表';


--
-- Table structure for table `assets_handover`
--



CREATE TABLE `assets_handover` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `staff_id` int(10) DEFAULT NULL COMMENT '员工id',
  `state` tinyint(2) DEFAULT '1' COMMENT '审批状态；1=未上传，2=待审核，3=不通过，4=已通过',
  `approver_staff_id` int(10) DEFAULT NULL COMMENT '审批者id',
  `reason` text COMMENT '原因',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `createtime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_staff_id` (`staff_id`) USING BTREE COMMENT 'staff_id索引'
) ENGINE=InnoDB AUTO_INCREMENT=28668 DEFAULT CHARSET=utf8mb4;


--
-- Table structure for table `assets_info`
--



CREATE TABLE `assets_info` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `staff_info_id` int(11) NOT NULL DEFAULT '0' COMMENT '员工ID',
  `order_id` int(11) NOT NULL DEFAULT '0' COMMENT '订单号',
  `assets_goods_id` int(11) NOT NULL DEFAULT '0' COMMENT '申请资产ID',
  `store_id` varchar(20) NOT NULL DEFAULT '' COMMENT '申请网点ID',
  `handover_type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '交接方式：1.邮寄',
  `express` varchar(50) NOT NULL DEFAULT '' COMMENT '承运商',
  `express_sn` varchar(50) NOT NULL DEFAULT '' COMMENT '运单号',
  `state` tinyint(1) NOT NULL DEFAULT '0' COMMENT '主库状态：1.已出库，0.待出库',
  `out_time` datetime DEFAULT NULL COMMENT '出库时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `bar_code` varchar(20) NOT NULL DEFAULT '' COMMENT '申请资产编码',
  `sn_code` varchar(20) NOT NULL DEFAULT '' COMMENT 'sn 编码',
  `transfer_at` datetime DEFAULT NULL COMMENT '转移时间',
  `asset_code` varchar(20) DEFAULT '' COMMENT '资产编号',
  `transfer_state` tinyint(3) DEFAULT '0' COMMENT '转移状态：0.未转移，1.已转移，2.待确认',
  `transfer_staff_id` int(11) DEFAULT '0' COMMENT '交接人',
  `transfer_type` tinyint(3) DEFAULT '0' COMMENT '交接方式：1.当面交接，2.邮寄',
  `pno` varchar(20) DEFAULT '' COMMENT 'pno运单号',
  `operate_status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '1 使用中 2 待接收 3 转交中 4 已报修 5 已挂失 6 已报废 7 已停用 ,8已入库,9报修待接收,10多余待接收，11离职待接收',
  PRIMARY KEY (`id`),
  KEY `order_id_index` (`order_id`),
  KEY `staff_info_id_index` (`staff_info_id`),
  KEY `transfer_staff_index` (`transfer_staff_id`)
) ENGINE=InnoDB AUTO_INCREMENT=316387 DEFAULT CHARSET=utf8mb4 COMMENT='资产管理详情表';


--
-- Table structure for table `assets_info_log`
--



CREATE TABLE `assets_info_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `assets_id` int(11) NOT NULL DEFAULT '0' COMMENT '资产ID',
  `staff_info_id` int(11) NOT NULL DEFAULT '0' COMMENT '转交人',
  `transfer_staff_id` int(11) NOT NULL DEFAULT '0' COMMENT '接收人',
  `transfer_type` tinyint(3) NOT NULL DEFAULT '0' COMMENT '交接方式：1.当面，2.邮寄',
  `pno` varchar(50) DEFAULT NULL COMMENT '运单号',
  `transfer_state` tinyint(3) NOT NULL DEFAULT '0' COMMENT '转移状态：1.已交接，2.待确认，3.撤销',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `remark` varchar(250) NOT NULL DEFAULT '' COMMENT '不接受财产备注',
  PRIMARY KEY (`id`),
  KEY `idx_staff_info_id` (`staff_info_id`),
  KEY `idx_assets_id` (`assets_id`)
) ENGINE=InnoDB AUTO_INCREMENT=467680 DEFAULT CHARSET=utf8mb4 COMMENT='资产转交日志';


--
-- Table structure for table `assets_inventory`
--



CREATE TABLE `assets_inventory` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `assets_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT 'assets_info 表 主键ID 盘点 个人资产 公共资产',
  `staff_info_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '员工ID 盘点 批量资产',
  `sys_store_id` varchar(20) NOT NULL DEFAULT '' COMMENT '网点ID',
  `goods_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '商品ID assets_goods 表 主键 盘点 批量资产',
  `bar_code` varchar(20) DEFAULT '' COMMENT 'bar_code',
  `is_public` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否是公共资产 1公共资产 0个人资产',
  `is_batch` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否是批量资产 1 批量资产 0 非批量资产',
  `assets_name` varchar(50) DEFAULT '' COMMENT '资产名称',
  `assets_code` varchar(50) NOT NULL DEFAULT '' COMMENT '资产编码',
  `sn_code` varchar(50) NOT NULL DEFAULT '' COMMENT 'sn编码',
  `assets_type` varchar(100) NOT NULL DEFAULT '' COMMENT '资产型号',
  `assets_brand` varchar(100) NOT NULL DEFAULT '' COMMENT '资产品牌',
  `status` tinyint(3) unsigned DEFAULT '0' COMMENT '状态 1 使用中 4 已报修 5 已挂失 6 已报废 7 已停用 ',
  `inventory` tinyint(3) unsigned DEFAULT '0' COMMENT '1 正确 2 删除 3编辑 4 添加',
  `reason` text NOT NULL COMMENT '删除原因',
  `useing_count` int(10) unsigned DEFAULT '0' COMMENT '使用中',
  `repair_count` int(10) unsigned DEFAULT '0' COMMENT '以报修',
  `scrapped_count` int(10) unsigned DEFAULT '0' COMMENT '已报废',
  `disable_count` int(10) unsigned DEFAULT '0' COMMENT '已停用',
  `lose_count` int(10) unsigned DEFAULT '0' COMMENT '已挂失',
  `operator_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '操作人ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '编辑时间',
  PRIMARY KEY (`id`),
  KEY `idx_staff_id` (`staff_info_id`),
  KEY `idx_assets_id` (`assets_id`),
  KEY `idx_goods_id` (`goods_id`)
) ENGINE=InnoDB AUTO_INCREMENT=17615 DEFAULT CHARSET=utf8mb4 COMMENT='资产盘库表';


--
-- Table structure for table `assets_inventory_stores`
--



CREATE TABLE `assets_inventory_stores` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `sys_store_id` varchar(20) NOT NULL DEFAULT '' COMMENT '网点ID',
  `is_switch` tinyint(4) NOT NULL DEFAULT '0' COMMENT '1 打开 0 关闭',
  `operator_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '编辑人id',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '编辑时间',
  PRIMARY KEY (`id`),
  KEY `idx_store_id` (`sys_store_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1196 DEFAULT CHARSET=utf8mb4 COMMENT='资产盘库 网点开关';


--
-- Table structure for table `assets_operate_log`
--



CREATE TABLE `assets_operate_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `assets_info_id` int(11) NOT NULL DEFAULT '0' COMMENT 'assets_info表主键ID',
  `staff_info_id` int(11) NOT NULL DEFAULT '0' COMMENT '员工ID',
  `assets_goods_id` int(11) NOT NULL DEFAULT '0' COMMENT '资产ID',
  `operator_id` int(11) NOT NULL DEFAULT '0' COMMENT '操作人ID',
  `operate_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  `operate_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1 使用中 4 已报修 5 已挂失',
  `remarks` varchar(300) NOT NULL DEFAULT '' COMMENT '备注信息',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 （这样默认是UTC时区）',
  PRIMARY KEY (`id`),
  KEY `idx_assets_info_id` (`assets_info_id`)
) ENGINE=InnoDB AUTO_INCREMENT=21480 DEFAULT CHARSET=utf8mb4 COMMENT='资产操作日志表';


--
-- Table structure for table `assets_order`
--



CREATE TABLE `assets_order` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `order_union_id` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '订单编号',
  `organization_id` varchar(20) NOT NULL DEFAULT '' COMMENT '网点编号',
  `staff_info_id` int(11) NOT NULL DEFAULT '0' COMMENT '申请人',
  `approve_user` int(11) DEFAULT '0' COMMENT '审批人',
  `status` tinyint(3) NOT NULL DEFAULT '0' COMMENT '状态：0.默认，1.待审批，2.已同意，3.已驳回，4.已撤销',
  `wf_role` varchar(20) DEFAULT '' COMMENT '关联流程',
  `reject_reason` varchar(500) DEFAULT '' COMMENT '驳回原因',
  `reason` varchar(500) NOT NULL DEFAULT '',
  `shipping_user` varchar(255) NOT NULL DEFAULT '' COMMENT '收货人姓名',
  `consignee_phone` char(20) DEFAULT '' COMMENT '联系方式',
  `consignee_address` varchar(255) DEFAULT '' COMMENT '详细地址',
  `province_code` char(50) DEFAULT '' COMMENT '省',
  `city_code` varchar(50) DEFAULT '' COMMENT '市',
  `district_code` varchar(50) DEFAULT '' COMMENT '区',
  `postal_code` char(10) DEFAULT '' COMMENT '邮编',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `order_union_id_index` (`order_union_id`),
  KEY `staff_info_id_index` (`staff_info_id`),
  KEY `wf_role_index` (`wf_role`)
) ENGINE=InnoDB AUTO_INCREMENT=55200 DEFAULT CHARSET=utf8mb4 COMMENT='资产订单表';


--
-- Table structure for table `assets_order_detail`
--



CREATE TABLE `assets_order_detail` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `order_id` int(11) NOT NULL DEFAULT '0' COMMENT '订单表ID',
  `goods_id` int(11) NOT NULL DEFAULT '0' COMMENT '条形码',
  `recomment_num` int(11) DEFAULT '0' COMMENT '申请数量',
  `approval_num` int(11) DEFAULT '0' COMMENT '审批数量',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后修改时间',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `order_id` (`order_id`,`goods_id`),
  KEY `idx_goods` (`goods_id`)
) ENGINE=InnoDB AUTO_INCREMENT=147791 DEFAULT CHARSET=utf8mb4 COMMENT='资产订单详情表';


--
-- Table structure for table `assets_repair`
--



CREATE TABLE `assets_repair` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `goods_name_th` varchar(100) NOT NULL COMMENT '资产名称（泰文）',
  `goods_name_zh` varchar(100) NOT NULL COMMENT '资产名称（中文）',
  `bar_code` varchar(20) NOT NULL COMMENT 'bar_code',
  `is_public` tinyint(1) NOT NULL COMMENT '1 公共资产 0个人资产',
  `is_receive` tinyint(1) NOT NULL COMMENT '总部是否接收 0：不接收，1：接收',
  `consignee_address` text NOT NULL COMMENT '邮寄地址',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `type` tinyint(4) NOT NULL DEFAULT '9' COMMENT '类别:  9保修待接收   10离职待接收  11多余待接收 ',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unq_bar_code_type_is_public` (`bar_code`,`type`,`is_public`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=225 DEFAULT CHARSET=utf8mb4 COMMENT='维修清单管理表';


--
-- Table structure for table `attendance_data_log`
--



CREATE TABLE `attendance_data_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `leave_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '请假类型',
  `leave_start_time` date DEFAULT NULL COMMENT '请假开始时间',
  `leave_end_time` date DEFAULT NULL COMMENT '请假结束时间',
  `leave_day` varchar(3) NOT NULL DEFAULT '' COMMENT '请假天数',
  `staff_info_id` int(11) NOT NULL DEFAULT '0' COMMENT '员工ID',
  `attendance_type` tinyint(2) NOT NULL DEFAULT '0' COMMENT '打卡类型',
  `attendance_date` date DEFAULT NULL COMMENT '出勤日期',
  `attendance_time` datetime DEFAULT NULL COMMENT '补卡时间',
  `ot_date` date DEFAULT NULL COMMENT 'ot日期',
  `ot_start_time` datetime DEFAULT NULL COMMENT 'ot开始时间',
  `ot_type` tinyint(2) NOT NULL DEFAULT '0' COMMENT 'ot类型',
  `duration` float(2,0) DEFAULT NULL COMMENT 'ot时长',
  `log_type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '日志类型',
  `create_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `update_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '操作时间',
  `operator` int(11) NOT NULL DEFAULT '0',
  `operator_name` varchar(100) NOT NULL DEFAULT '' COMMENT '操作人',
  `image_path` text,
  `kilometres` varchar(100) NOT NULL DEFAULT '',
  `reason` text,
  `leave_start_type` tinyint(2) NOT NULL DEFAULT '0' COMMENT '请假开始类型',
  `leave_end_type` tinyint(2) NOT NULL DEFAULT '0' COMMENT '请假结束类型',
  PRIMARY KEY (`id`),
  KEY `attendance_data_log_staff_info_id_index` (`staff_info_id`),
  KEY `attendance_data_log_update_at_index` (`update_at`)
) ENGINE=InnoDB AUTO_INCREMENT=16254 DEFAULT CHARSET=utf8;


--
-- Table structure for table `audit_apply`
--



CREATE TABLE `audit_apply` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `serial_no` varchar(32) DEFAULT NULL COMMENT '审批编号',
  `biz_type` tinyint(4) DEFAULT NULL COMMENT '业务数据类型 1-补卡 2-请假 3-加班 4-离职',
  `biz_value` varchar(100) DEFAULT NULL COMMENT '业务数据ID',
  `flow_id` char(19) DEFAULT NULL COMMENT '流程ID',
  `current_flow_node_id` int(11) DEFAULT NULL COMMENT '当前流程节点ID',
  `state` tinyint(4) DEFAULT NULL COMMENT '当前状态，1待审批，2已同意，3已驳回 4已撤销 5已超时',
  `submitter_id` char(20) DEFAULT '' COMMENT '申请提交人ID',
  `summary` text COMMENT '申请概要 json数据',
  `final_approver` varchar(20) DEFAULT NULL COMMENT '最终审批人id',
  `final_approval_time` timestamp NULL DEFAULT NULL COMMENT '最终审批时间',
  `reject_reason` varchar(1000) DEFAULT NULL COMMENT '驳回原因',
  `cancel_reason` varchar(1000) DEFAULT NULL COMMENT '撤销原因',
  `deleted` tinyint(3) DEFAULT '0' COMMENT '是否删除 0-否 1-是',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_show_detail` (`biz_value`,`biz_type`) USING BTREE,
  KEY `idx_list` (`submitter_id`,`created_at`,`final_approval_time`) USING BTREE,
  KEY `idx_serial_no` (`serial_no`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1747280 DEFAULT CHARSET=utf8mb4 COMMENT='审批列表-我的申请表';


--
-- Table structure for table `audit_approval`
--



CREATE TABLE `audit_approval` (
  `id` bigint(15) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `biz_type` int(4) DEFAULT NULL COMMENT '业务数据类型 1-补卡 2-请假 3-加班 4-离职',
  `biz_value` varchar(100) DEFAULT NULL COMMENT '业务数据ID',
  `flow_node_id` int(10) DEFAULT NULL COMMENT '节点ID',
  `submitter_id` varchar(20) DEFAULT NULL COMMENT '申请用户ID',
  `approval_id` varchar(20) DEFAULT '' COMMENT '审批人ID',
  `state` tinyint(3) DEFAULT NULL COMMENT '状态集(1:待审批 2:同意 3:驳回，4:撤销 5:超时）',
  `summary` text COMMENT '申请概要 json数据',
  `audit_time` timestamp NULL DEFAULT NULL COMMENT '审批时间',
  `deleted` tinyint(3) DEFAULT '0' COMMENT '是否删除 0-否 1-是',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_show_detail` (`biz_type`,`biz_value`) USING BTREE,
  KEY `idx_list` (`approval_id`,`created_at`,`audit_time`),
  KEY `idx_flow_node_id` (`flow_node_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2817338 DEFAULT CHARSET=utf8mb4 COMMENT='审批列表-我的审批已完成表';


--
-- Table structure for table `audit_cc`
--



CREATE TABLE `audit_cc` (
  `id` bigint(15) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `biz_type` int(4) NOT NULL COMMENT '业务数据类型 1-补卡 2-请假 3-加班 4-离职',
  `biz_value` varchar(100) NOT NULL COMMENT '业务数据ID',
  `flow_node_id` int(10) DEFAULT NULL COMMENT '节点ID',
  `submitter_id` int(20) DEFAULT NULL COMMENT '申请用户ID',
  `cc_staff_id` int(20) NOT NULL COMMENT '审批人ID',
  `state` tinyint(3) DEFAULT NULL COMMENT '状态集(1:待审批 2:同意 3:驳回，4:撤销 5:超时）',
  `summary` text COMMENT '申请概要 json数据',
  `is_read` tinyint(3) DEFAULT '1' COMMENT '是否已读 1-未读 2-已读',
  `deleted` tinyint(3) DEFAULT '0' COMMENT '是否删除 0-否 1-是',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_show_detail` (`biz_type`,`biz_value`) USING BTREE,
  KEY `idx_list` (`cc_staff_id`,`created_at`)
) ENGINE=InnoDB AUTO_INCREMENT=25553 DEFAULT CHARSET=utf8mb4 COMMENT='审批列表-我的审批已完成表';


--
-- Table structure for table `audit_log`
--



CREATE TABLE `audit_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `request_id` int(11) DEFAULT NULL COMMENT '工作流申请ID',
  `flow_id` char(19) DEFAULT NULL COMMENT '工作流ID',
  `flow_node_id` int(11) DEFAULT NULL COMMENT '工作流节点ID',
  `staff_id` varchar(20) DEFAULT NULL COMMENT '审批人ID',
  `staff_name` varchar(100) DEFAULT NULL COMMENT '审批人姓名',
  `staff_department` varchar(100) DEFAULT NULL COMMENT '审批人部门名称',
  `staff_job_title_name` varchar(100) DEFAULT NULL COMMENT '审批人职位名称',
  `audit_action` tinyint(4) DEFAULT NULL COMMENT '审批结果，1同意，2驳回',
  `audit_info` varchar(1000) DEFAULT NULL COMMENT '审批备注',
  `audit_at` datetime DEFAULT NULL COMMENT '审批时间',
  PRIMARY KEY (`id`),
  KEY `idx_request_id` (`request_id`),
  KEY `idx_flow_id` (`flow_id`)
) ENGINE=InnoDB AUTO_INCREMENT=4431216 DEFAULT CHARSET=utf8mb4 COMMENT='工作流审核日志';


--
-- Table structure for table `audit_permission`
--



CREATE TABLE `audit_permission` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `company_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '公司id 废弃',
  `sys_department_id` varchar(10) DEFAULT NULL COMMENT '部门Id 废弃',
  `node_department_id` varchar(10) DEFAULT NULL COMMENT '子部门Id 废弃',
  `ancestry_1` varchar(10) DEFAULT NULL COMMENT '组织id 一级 对应 sys_department 的 ancestry_v3 字段的第一层级 跟产品沟通 按组织类型type 划分 不同意',
  `ancestry_2` varchar(10) DEFAULT NULL COMMENT '组织id 一级 对应 sys_department 的 ancestry_v3 字段的第一层级 跟产品沟通 按组织类型type 划分 不同意',
  `ancestry_3` varchar(10) DEFAULT NULL COMMENT '组织id 一级 对应 sys_department 的 ancestry_v3 字段的第一层级 跟产品沟通 按组织类型type 划分 不同意',
  `ancestry_4` varchar(10) DEFAULT NULL COMMENT '组织id 一级 对应 sys_department 的 ancestry_v3 字段的第一层级 跟产品沟通 按组织类型type 划分 不同意',
  `ancestry_5` varchar(10) DEFAULT NULL COMMENT '组织id 一级 对应 sys_department 的 ancestry_v3 字段的第一层级 跟产品沟通 按组织类型type 划分 不同意',
  `job_title_id` varchar(10) DEFAULT NULL COMMENT '职位Id',
  `job_title_grade` varchar(10) DEFAULT NULL COMMENT '职级Id',
  `job_title_level` varchar(10) DEFAULT NULL COMMENT '职等Id',
  `permission_value` text COMMENT '对应权限的值 怎么存都行',
  `module_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '对应模块id 1 加班申请类型',
  `version` varchar(10) DEFAULT NULL COMMENT '版本号',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=82 DEFAULT CHARSET=utf8mb4 COMMENT='各个业务模块申请 权限表';


--
-- Table structure for table `backyard_img`
--



CREATE TABLE `backyard_img` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '工单表自增ID',
  `origin_id` varchar(32) NOT NULL COMMENT '上传图片关联外键',
  `oss_bucket_type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '上传图片类型(1:工单详情,2:工单回复)',
  `bucket_name` varchar(63) DEFAULT NULL COMMENT 'oss bucketName',
  `object_key` varchar(100) DEFAULT NULL COMMENT 'oss 对象 key 值',
  `deleted` tinyint(3) NOT NULL DEFAULT '0' COMMENT '逻辑删除 1:已逻辑删除 0：未逻辑删除',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `rotate_angle` int(10) DEFAULT '0' COMMENT '图片旋转角度：0、90、180、270',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='oss图片';


--
-- Table structure for table `business_trip`
--



CREATE TABLE `business_trip` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `serial_no` varchar(32) DEFAULT NULL COMMENT '序列号',
  `reason_application` varchar(50) DEFAULT '' COMMENT '出差理由',
  `traffic_tools` tinyint(4) DEFAULT '0' COMMENT '交通工具 1飞机 2火车 3汽车 4其他',
  `other_traffic_name` varchar(20) DEFAULT '' COMMENT '其他交通工具名称',
  `oneway_or_roundtrip` tinyint(4) DEFAULT '0' COMMENT '单程1往返2',
  `departure_city` varchar(50) DEFAULT '' COMMENT '出发城市',
  `destination_city` varchar(50) DEFAULT '' COMMENT '目的城市',
  `start_time` date DEFAULT NULL COMMENT '开始时间',
  `end_time` date DEFAULT NULL COMMENT '结束时间',
  `days_num` int(11) DEFAULT '1' COMMENT '出差天数',
  `remark` varchar(500) DEFAULT '' COMMENT '备注',
  `reason` varchar(500) DEFAULT '' COMMENT '驳回原因',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '审批状态 1 待审核 2 审核通过 3 驳回 4 撤销',
  `is_push` tinyint(4) DEFAULT '0' COMMENT '推送',
  `apply_user` int(11) DEFAULT '0' COMMENT '申请人',
  `approve_user` int(11) DEFAULT '0' COMMENT '审批人',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `workflow_role` varchar(50) NOT NULL DEFAULT '' COMMENT '审批role',
  `business_trip_type` tinyint(2) NOT NULL DEFAULT '1' COMMENT '出差类型1普通出差2黄牌项目出差',
  `reason_application_type` tinyint(2) DEFAULT '0' COMMENT '出差理由类型1办理黄牌2考驾照；出差类型=2',
  `car_no` varchar(50) DEFAULT NULL COMMENT '车牌号，黄牌出差项目必填',
  PRIMARY KEY (`id`),
  KEY `apply_user` (`apply_user`),
  KEY `approve_user` (`approve_user`)
) ENGINE=InnoDB AUTO_INCREMENT=44405 DEFAULT CHARSET=utf8mb4 COMMENT='出差记录表';


--
-- Table structure for table `business_trip_img`
--



CREATE TABLE `business_trip_img` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '编号',
  `business_trip_id` bigint(10) unsigned DEFAULT '0' COMMENT '出差记录编号',
  `img_path` varchar(255) DEFAULT '' COMMENT '图片路径',
  `deleted` tinyint(4) DEFAULT '0',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `business_trip_id` (`business_trip_id`)
) ENGINE=InnoDB AUTO_INCREMENT=7880 DEFAULT CHARSET=utf8mb4 COMMENT='出差申请图片表';


--
-- Table structure for table `ceo_mail_problem_category`
--



CREATE TABLE `ceo_mail_problem_category` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `category_name_zh` varchar(255) NOT NULL DEFAULT '' COMMENT '问题分类中文名称',
  `category_name_en` varchar(255) NOT NULL DEFAULT '' COMMENT '问题分类英文名称',
  `category_name_th` varchar(255) NOT NULL DEFAULT '' COMMENT '问题分类泰文名称',
  `category_desc_zh` varchar(255) NOT NULL DEFAULT '' COMMENT '分类描述中文',
  `category_desc_en` varchar(255) NOT NULL DEFAULT '' COMMENT '分类描述英文',
  `category_desc_th` varchar(255) NOT NULL DEFAULT '' COMMENT '分类描述泰文',
  `parent_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '父分类ID',
  `related_department_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '关联该类问题的部门id',
  `related_department_name` varchar(255) NOT NULL DEFAULT '' COMMENT '关联该类问题的部门名称',
  `related_tab_name` varchar(32) NOT NULL DEFAULT '' COMMENT '关联的tab名称，FBI  模块展示',
  `remark` varchar(255) NOT NULL DEFAULT '' COMMENT '其他备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '0 时区',
  `display_status` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '分类展示状态:0-不展示;1-展示',
  `display_channel` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '展示渠道: 1-问题反馈; 2-ceo信箱',
  `sort_index` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '排序索引, 大数靠前',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=68 DEFAULT CHARSET=utf8mb4 COMMENT='ceo信箱 - 问题分类表';


--
-- Table structure for table `ceo_mail_problem_transfer_record`
--



CREATE TABLE `ceo_mail_problem_transfer_record` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `problem_no` varchar(32) NOT NULL DEFAULT '' COMMENT '问题工单号',
  `mail_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '员工反馈的问题id:mail_to_ceo主键',
  `transfer_category_id` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '移交后的问题分类id',
  `leave_message` varchar(1000) NOT NULL DEFAULT '' COMMENT '移交时的留言',
  `transfer_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '移交时间, 泰国时间 (东7区)',
  `transfer_department_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '移交后的部门id',
  `transfer_department_name` varchar(128) NOT NULL DEFAULT '' COMMENT '移交后的部门名称',
  `transfer_interval_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '距离上一次操作[系统回复/系统移交/员工反馈]的间隔时长，单位 秒',
  `staff_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '操作者工号ID',
  `staff_name` varchar(128) NOT NULL DEFAULT '' COMMENT '操作者姓名',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '0时区',
  PRIMARY KEY (`id`),
  KEY `idx_problem_no` (`problem_no`)
) ENGINE=InnoDB AUTO_INCREMENT=502 DEFAULT CHARSET=utf8mb4 COMMENT='ceo信箱 - 问题工单移交记录';


--
-- Table structure for table `ceo_mail_staff_problem_order`
--



CREATE TABLE `ceo_mail_staff_problem_order` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `problem_no` varchar(32) NOT NULL DEFAULT '' COMMENT '问题工单号',
  `staff_id` int(10) NOT NULL COMMENT '员工工号',
  `staff_name` varchar(128) NOT NULL DEFAULT '' COMMENT '员工姓名',
  `staff_mobile` varchar(64) NOT NULL DEFAULT '' COMMENT '员工手机号',
  `avatar_url` varchar(255) NOT NULL DEFAULT '' COMMENT '员工头像',
  `problem_category` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '员工侧,问题分类id',
  `problem_desc` varchar(1024) NOT NULL DEFAULT '' COMMENT '问题描述',
  `problem_image` varchar(1024) NOT NULL DEFAULT '' COMMENT '问题图片',
  `department_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '员工所在部门id',
  `department_name` varchar(255) NOT NULL DEFAULT '' COMMENT '员工所在部门名称',
  `create_time` datetime DEFAULT NULL COMMENT '问题工单创建时间, 泰国时间',
  `first_reply_staff_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '首次回复员工反馈的平台账号id',
  `first_reply_time` datetime DEFAULT NULL COMMENT '首次回复员工反馈的时间, 泰国时区',
  `last_deal_staff_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '最后处理员工反馈的平台账号id，回复后移交',
  `last_deal_time` datetime DEFAULT NULL COMMENT '最后处理员工反馈的时间, 泰国时间，回复或移交',
  `last_reply_staff_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '最后回复员工反馈的系统账号id',
  `last_reply_time` datetime DEFAULT NULL COMMENT '最后回复员工反馈的时间, 泰国时间',
  `problem_status` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '问题工单状态: 0-未回复; 1- 已回复; 2已完成; 3 已超时',
  `is_evaluate` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否评价: 0 未评价; 1 已评价',
  `staff_score` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '员工评分: 1,2,3,4,5',
  `staff_evaluate` varchar(1000) NOT NULL DEFAULT '' COMMENT '员工评价',
  `staff_evaluate_remark` varchar(512) NOT NULL DEFAULT '' COMMENT '员工评价备注',
  `evaluate_time` datetime DEFAULT NULL COMMENT '员工评价时间, 泰国时区',
  `all_interval_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '工单处理完毕的间隔时长, 单位秒',
  `first_reply_interval_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '问题工单首次反馈到系统首次回复的间隔时长，单位 秒',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '数据变更时间, 0 时区',
  `is_read_sys_reply` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '系统回复是否已读: 0-未读; 1- 已读',
  `latest_feedback_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '最新提交反馈消息的ID，关联mail_to_ceo表主键',
  `latest_feedback_time` datetime NOT NULL COMMENT '最新提交反馈信息的时间, 泰国时间',
  `sys_category_id` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '系统侧: 问题分类id, 若问题工单有移交, 那么该字段存储移交后对应的分类id',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `idx_problem_no` (`problem_no`),
  KEY `idx_staff_problem_status` (`staff_id`,`problem_status`,`is_read_sys_reply`) USING BTREE,
  KEY `idx_category` (`problem_category`),
  KEY `idx_last_reply_status` (`problem_status`,`last_reply_time`) USING BTREE,
  KEY `idx_sys_category_ids` (`sys_category_id`)
) ENGINE=InnoDB AUTO_INCREMENT=13710 DEFAULT CHARSET=utf8mb4 COMMENT='Ceo信箱 - 员工问题工单';


--
-- Table structure for table `certificate_code`
--



CREATE TABLE `certificate_code` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `staff_info_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '员工工号',
  `type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '证明文件的类型 1-工资条（文件名固定本表不存）2-在职证明 3-就业证明（离职证明）',
  `code` varchar(64) NOT NULL DEFAULT '' COMMENT '文件对应编码',
  `date_at` date NOT NULL DEFAULT '0000-00-00' COMMENT '申请日期 员工申请证明相关的日期 ',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_staff_info_id` (`staff_info_id`)
) ENGINE=InnoDB AUTO_INCREMENT=31231 DEFAULT CHARSET=utf8mb4 COMMENT='证明下载pdf生成code保存记录表';


--
-- Table structure for table `claimer_approve`
--



CREATE TABLE `claimer_approve` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `pickup_claims_ticket_id` varchar(30) NOT NULL DEFAULT '' COMMENT 'pickup_claims_ticket_id',
  `serial_no` varchar(30) NOT NULL DEFAULT '' COMMENT '审批ID',
  `organization_id` varchar(20) NOT NULL DEFAULT '' COMMENT '申请网点',
  `operator_id` int(11) NOT NULL DEFAULT '0' COMMENT '操作人ID',
  `client_id` varchar(25) NOT NULL DEFAULT '' COMMENT '申请人ID',
  `pno` varchar(20) NOT NULL DEFAULT '' COMMENT 'pno',
  `approval_at` datetime NOT NULL DEFAULT '1970-01-01 00:00:00' COMMENT '申请审批的时间',
  `state` tinyint(4) NOT NULL DEFAULT '0' COMMENT '1 待审批 2 已同意 3 已驳回 4 已撤销',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_serial_no` (`serial_no`),
  KEY `idx_pno` (`pno`)
) ENGINE=InnoDB AUTO_INCREMENT=350 DEFAULT CHARSET=utf8mb4 COMMENT='网点理赔审批表';


--
-- Table structure for table `communication_resume_log`
--



CREATE TABLE `communication_resume_log` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `resume_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '简历ID',
  `submiter_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '提交人ID',
  `status` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '1 沟通失败 2 沟通成功',
  `content` varchar(500) NOT NULL DEFAULT '' COMMENT '沟通内容',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_resume_id` (`resume_id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COMMENT='简历沟通记录表';


--
-- Table structure for table `cs_ticket_list`
--



CREATE TABLE `cs_ticket_list` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `staff_id` int(11) NOT NULL DEFAULT '0' COMMENT '提交人工号',
  `staff_name` varchar(50) NOT NULL DEFAULT '' COMMENT '提交人name',
  `dm_staff_id` int(11) NOT NULL DEFAULT '0' COMMENT 'dm',
  `order_id` int(11) NOT NULL DEFAULT '0' COMMENT 'cs工单id',
  `order_no` varchar(20) NOT NULL DEFAULT '' COMMENT 'cs工单no',
  `store_id` varchar(30) NOT NULL DEFAULT '' COMMENT '工单来源id',
  `speed_level` int(4) NOT NULL DEFAULT '0' COMMENT '紧急程度 1紧急 2一般',
  `order_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'cs工单类型/ 11催单-18投诉',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '工单提交时间',
  `updated_at` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00' ON UPDATE CURRENT_TIMESTAMP COMMENT '工单最后回复时间',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '当前工单状态 1代处理，2已读,3已处理，4已关闭',
  PRIMARY KEY (`id`),
  KEY `idx_dm_staff_id` (`dm_staff_id`) USING BTREE,
  KEY `idx_order_id_no` (`order_id`,`order_no`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=4823 DEFAULT CHARSET=utf8mb4;


--
-- Table structure for table `dictionary`
--



CREATE TABLE `dictionary` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `pid` int(11) DEFAULT '0' COMMENT '所属大类',
  `name` varchar(100) DEFAULT '' COMMENT '中文',
  `name_th` varchar(100) DEFAULT '' COMMENT '泰语',
  `name_en` varchar(100) DEFAULT '' COMMENT '英语',
  `remark` varchar(100) DEFAULT '' COMMENT '备注',
  `status` tinyint(4) DEFAULT '1' COMMENT '1：激活',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=358 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='数据字典';


--
-- Table structure for table `dinner_record`
--



CREATE TABLE `dinner_record` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `date_at` date NOT NULL DEFAULT '0000-00-00' COMMENT '申请日期',
  `name` varchar(20) NOT NULL DEFAULT '' COMMENT '姓名',
  `department` varchar(20) NOT NULL DEFAULT '' COMMENT '部门',
  `remark` varchar(80) NOT NULL DEFAULT '' COMMENT '备注',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx` (`name`,`date_at`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COMMENT='flash 订餐记录';


--
-- Table structure for table `door_event`
--



CREATE TABLE `door_event` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `day` date DEFAULT NULL COMMENT '数据属于哪一天的',
  `page` int(10) unsigned DEFAULT NULL COMMENT '数据属于那一页',
  `index` tinyint(3) unsigned DEFAULT NULL COMMENT '数据属于哪一行',
  `event_type` varchar(300) DEFAULT NULL COMMENT '事件类型',
  `controller_name` varchar(300) DEFAULT NULL COMMENT '门禁种类 标识 in 或者out',
  `hub_prefix` varchar(30) DEFAULT NULL COMMENT '从门禁名称格式化得到的hub名称',
  `in_or_out` tinyint(1) unsigned DEFAULT NULL COMMENT '格式化后的 进或出 0.in 1.out',
  `staff_info_id` varchar(20) DEFAULT NULL COMMENT '快递员ID',
  `card_holder` varchar(300) DEFAULT NULL COMMENT '快递员名称',
  `event_time` datetime DEFAULT NULL COMMENT '事件发生时间',
  `access_control_point` varchar(300) DEFAULT NULL COMMENT '门禁控制点名称',
  `event_source` varchar(300) DEFAULT NULL COMMENT '事件发生源',
  `linkage` varchar(100) DEFAULT NULL COMMENT '连接',
  `created_at` datetime(3) DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间,也是同步时间',
  `updated_at` datetime(3) DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '修改时间',
  PRIMARY KEY (`id`),
  KEY `idx_event_time` (`event_time`),
  KEY `idx_hub_prefix` (`hub_prefix`),
  KEY `idx_staff_info_id_day` (`staff_info_id`,`day`)
) ENGINE=InnoDB AUTO_INCREMENT=3016408 DEFAULT CHARSET=utf8mb4;


--
-- Table structure for table `emergency_record`
--



CREATE TABLE `emergency_record` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `staff_info_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '紧急事故发生工号',
  `phone` varchar(20) NOT NULL DEFAULT '' COMMENT '联系电话',
  `date_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '发生时间 泰国时间',
  `type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '1 发生事故 有受伤者 2 关于汽车/叉车 3 高处坠落 4 被触电 5 被撞击，被夹，被拉 6 遭受都刺激性化学品 7 其他',
  `personal_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0 没选默认 1 无受伤者 2 有受伤者 3 送医院 4 未送医院 5 请假 6 未请假',
  `num` int(11) NOT NULL DEFAULT '0' COMMENT '财务 数量',
  `worth` int(11) NOT NULL DEFAULT '0' COMMENT '价值 单位 分',
  `content` text COMMENT '事故描述',
  `img_url` varchar(512) NOT NULL DEFAULT '' COMMENT '事故图片地址',
  `operator` int(11) NOT NULL DEFAULT '0' COMMENT '提交人工号',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_staff_info_id` (`staff_info_id`)
) ENGINE=InnoDB AUTO_INCREMENT=53 DEFAULT CHARSET=utf8mb4 COMMENT='紧急事故记录表';


--
-- Table structure for table `file_oss_url`
--



CREATE TABLE `file_oss_url` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `staff_info_id` int(10) unsigned DEFAULT NULL COMMENT '员工工号',
  `file_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '文件类型 1 工资条pdf 2在职证明 3 离职证明（就业证明）4 薪资证明（不同于工资条）5 油费报销（发票）6油费报销（签名）7油费报销（申请单pdf）',
  `origin_id` varchar(20) DEFAULT NULL COMMENT '关联其他数据表编号',
  `date_at` date NOT NULL DEFAULT '1970-01-01' COMMENT '日期 针对日期维度的文件',
  `month` char(7) NOT NULL DEFAULT '' COMMENT '月份 针对每月一个类型的文件',
  `path` varchar(100) DEFAULT NULL COMMENT '附带oss文件夹空间的后缀 xxx/aaa.jpg',
  `bucket` varchar(63) DEFAULT NULL COMMENT '所属bucket fle-staging-asset-internal',
  `lang` varchar(10) NOT NULL DEFAULT '' COMMENT '对应pdf 的 语言环境 1 泰语 2 中文 3 英文',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_staff_type` (`staff_info_id`,`file_type`)
) ENGINE=InnoDB AUTO_INCREMENT=282148 DEFAULT CHARSET=utf8mb4 COMMENT='文件 上传oss 保存对应路径 ';


--
-- Table structure for table `fleet_audit`
--



CREATE TABLE `fleet_audit` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `serial_no` varchar(32) DEFAULT NULL COMMENT '序列号',
  `car_type` int(3) NOT NULL COMMENT '车辆类型',
  `capacity` int(10) NOT NULL COMMENT '装载量',
  `expected_date` datetime(3) NOT NULL COMMENT '期望到达日期',
  `plan_date` datetime(3) DEFAULT NULL COMMENT '单向-计划到达时间',
  `plan_back_date` datetime(3) DEFAULT NULL COMMENT '双向-计划到达时间',
  `start_store` varchar(10) NOT NULL COMMENT '出发网点',
  `end_store` varchar(10) NOT NULL COMMENT '目的网点',
  `region` varchar(255) DEFAULT NULL COMMENT '区域',
  `reason` varchar(500) NOT NULL COMMENT '申请理由',
  `reject_reason` varchar(500) DEFAULT NULL COMMENT '拒绝理由',
  `reject_type` int(3) unsigned DEFAULT NULL COMMENT '驳回原因类型',
  `line_id` varchar(32) DEFAULT NULL COMMENT '单边线路id',
  `line_back_id` varchar(32) DEFAULT NULL COMMENT '双边线路id',
  `single_line` tinyint(3) DEFAULT NULL COMMENT '是否为单一线路 1-是  2-不是',
  `system_quote` int(11) unsigned DEFAULT '0' COMMENT '系统报价',
  `running_mileage` varchar(10) DEFAULT NULL COMMENT '运行里程',
  `abnormal_cost` int(11) DEFAULT '0' COMMENT '异常费用 ',
  `abnormal_cost_back` int(11) DEFAULT '0' COMMENT '返程异常费用',
  `final_cost_back` int(11) DEFAULT '0' COMMENT '返程总费用',
  `final_cost` int(11) unsigned DEFAULT '0' COMMENT '最终费用',
  `submitter_id` int(10) unsigned NOT NULL COMMENT '申请人id',
  `status` tinyint(3) NOT NULL COMMENT '审批状态 1-待审批 2-同意  3-撤销 4-驳回 5-超时',
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  `updated_at` datetime(3) DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
  `final_approver` int(10) DEFAULT '0' COMMENT '最终审批人id',
  `final_approval_time` datetime DEFAULT NULL COMMENT '最终审批时间',
  `audit_type` tinyint(3) unsigned DEFAULT NULL COMMENT '申请类型',
  `driver_id` int(10) unsigned DEFAULT NULL COMMENT '审批后司机工号',
  `fd_courier_id` int(10) unsigned DEFAULT NULL COMMENT 'FD工号',
  `fd_courier_name` varchar(50) DEFAULT NULL COMMENT 'FD姓名',
  `fd_courier_phone` varchar(20) DEFAULT NULL COMMENT 'FD电话',
  `fd_courier_plate` varchar(20) DEFAULT NULL COMMENT 'FD车牌号',
  `province_code` varchar(4) DEFAULT NULL COMMENT '车牌省份code',
  `wf_role` varchar(20) DEFAULT '' COMMENT '关联流程',
  `approve_time` datetime DEFAULT NULL COMMENT 'BK审批通过时间',
  `ms_first_approver` int(10) DEFAULT NULL COMMENT 'ms首次审批人id',
  `ms_first_approval_time` datetime(3) DEFAULT NULL COMMENT 'ms首次审批时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `serial_num_index` (`serial_no`) USING HASH COMMENT '序列号索引',
  KEY `idx_expected_date` (`expected_date`),
  KEY `idx_store` (`start_store`,`end_store`) USING BTREE COMMENT '始发网点、目的网点',
  KEY `idx_plan_date` (`plan_date`),
  KEY `idx_status_created_at` (`status`,`created_at`)
) ENGINE=InnoDB AUTO_INCREMENT=296893 DEFAULT CHARSET=utf8mb4;


--
-- Table structure for table `fleet_audit_record`
--



CREATE TABLE `fleet_audit_record` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `audit_id` bigint(20) DEFAULT NULL COMMENT '加班车审批表主键ID',
  `status` tinyint(3) DEFAULT NULL COMMENT '审批操作 2-同意  3-驳回',
  `operator_id` int(10) DEFAULT NULL COMMENT '操作人工号',
  `operator_name` varchar(50) DEFAULT NULL COMMENT '操作人名称',
  `created_at` datetime(3) DEFAULT CURRENT_TIMESTAMP(3),
  PRIMARY KEY (`id`),
  KEY `idx_audit_id` (`audit_id`)
) ENGINE=InnoDB AUTO_INCREMENT=18542 DEFAULT CHARSET=utf8mb4 COMMENT='加班车ms审批变更记录表';


--
-- Table structure for table `freight_discount`
--



CREATE TABLE `freight_discount` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `serial_no` varchar(32) DEFAULT NULL COMMENT '序列号',
  `submitter_id` int(10) DEFAULT NULL COMMENT '申请人id',
  `costomer_id` varchar(30) DEFAULT NULL COMMENT '客户id',
  `costomer_name` varchar(50) DEFAULT NULL COMMENT '客户名称',
  `costomer_mobile` varchar(50) DEFAULT NULL COMMENT '客户手机号',
  `costomer_type` tinyint(3) DEFAULT NULL COMMENT '客户类型 1-KA 2-小C',
  `costomer_created_at` datetime DEFAULT NULL COMMENT '客户账户创建时间',
  `costomer_parcel_count` int(6) DEFAULT NULL COMMENT '客户前30天每天发件量',
  `costomer_estimate_parcel_count` int(6) DEFAULT NULL COMMENT '客户预估发件量',
  `price_type` tinyint(4) DEFAULT NULL COMMENT '价格类型',
  `current_disc` int(4) DEFAULT '0' COMMENT '当前折扣率',
  `request_disc` int(4) DEFAULT '0' COMMENT '申请折扣率',
  `disc_start_date` datetime DEFAULT NULL COMMENT '折扣开始日期',
  `disc_end_date` datetime DEFAULT NULL COMMENT '折扣结束日期',
  `valid_days` int(4) DEFAULT '0' COMMENT '有效时长',
  `remark` text COMMENT '备注',
  `apply_reason_type` varchar(100) DEFAULT NULL COMMENT '申请原因类型',
  `proportion` varchar(500) DEFAULT NULL COMMENT '占比',
  `state` tinyint(3) NOT NULL DEFAULT '1' COMMENT '审批状态 1-待审批 2-已同意 3-已驳回 4-已撤销 5-已超时',
  `reject_reason` varchar(500) DEFAULT NULL COMMENT '驳回原因',
  `sync_state` tinyint(3) DEFAULT '1' COMMENT '同步ms状态 1-待同步 2-已同步',
  `coupon_sync_state` tinyint(3) DEFAULT '1' COMMENT '同步coupon状态 1-待同步 2-已同步',
  `is_valid` tinyint(3) DEFAULT '1' COMMENT '是否有效 1-有效 2-无效',
  `wf_role` varchar(20) DEFAULT NULL COMMENT '审批流',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `price_rule_category` tinyint(3) DEFAULT '1' COMMENT '折扣申请类型 1 通用  2水果',
  `current_cod_poundage_rate_str` varchar(20) DEFAULT '0' COMMENT 'cod 手续费费率(当前)',
  `request_cod_poundage_rate_str` varchar(20) DEFAULT '0' COMMENT 'cod 手续费费率(申请)',
  `current_return_discount_rate` int(4) DEFAULT '0' COMMENT '退费折扣(当前)',
  `request_return_discount_rate` int(4) DEFAULT '0' COMMENT '退费折扣(申请)',
  `current_credit_term` int(4) DEFAULT '0' COMMENT '信用期限(当前)',
  `request_credit_term` int(4) DEFAULT '0' COMMENT '信用期限(申请)',
  `channel` tinyint(3) DEFAULT '0' COMMENT '数据来源(1:Sales/PMD部门申请营销产品 2:Shop部门申请营销产品页)',
  `discount_effective_date` date NOT NULL DEFAULT '1970-01-01' COMMENT '折扣生效日期',
  `settlement_category` int(11) DEFAULT NULL COMMENT '结算类型',
  PRIMARY KEY (`id`),
  KEY `idx_costomer_id` (`costomer_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=10678 DEFAULT CHARSET=utf8mb4;


--
-- Table structure for table `freight_discount_coupon`
--



CREATE TABLE `freight_discount_coupon` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `pid` int(11) NOT NULL COMMENT '关联freight_discount表主键',
  `coupon_type` int(4) DEFAULT NULL COMMENT '优惠券类型',
  `coupon_name` varchar(255) DEFAULT NULL COMMENT '优惠券名字',
  `coupon_days_type` int(4) DEFAULT NULL COMMENT '优惠券天数类型',
  `coupon_valid_days` int(4) DEFAULT NULL COMMENT '优惠券有效天数',
  `coupon_num` int(6) DEFAULT NULL COMMENT '优惠券数量',
  `coupon_start_date` datetime DEFAULT NULL COMMENT '优惠券开始有效日期',
  `coupon_end_date` datetime DEFAULT NULL COMMENT '优惠券终止有效日期',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_pid` (`pid`,`coupon_type`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1521 DEFAULT CHARSET=utf8mb4 COMMENT='营销产品申请-优惠券';


--
-- Table structure for table `freight_discount_expire_remind`
--



CREATE TABLE `freight_discount_expire_remind` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `remind_staff_info_id` int(11) NOT NULL COMMENT '收信人员工id',
  `related_id` int(11) NOT NULL,
  `client_id` varchar(32) DEFAULT NULL COMMENT '客户ID',
  `customer_type_category` tinyint(3) unsigned DEFAULT NULL COMMENT '客户类型',
  `customer_name` varchar(50) DEFAULT NULL COMMENT '客户名称',
  `customer_mobile` varchar(50) DEFAULT NULL COMMENT '客户手机号',
  `price_type` tinyint(4) DEFAULT NULL COMMENT '价格类型',
  `price_rule_category` tinyint(4) unsigned DEFAULT NULL COMMENT '折扣类型',
  `current_disc` int(10) DEFAULT NULL COMMENT '当前折扣率',
  `request_disc` int(10) DEFAULT NULL COMMENT '申请折扣率',
  `disc_start_date` datetime(3) DEFAULT NULL COMMENT '折扣开始日期',
  `disc_end_date` datetime(3) DEFAULT NULL COMMENT '折扣结束日期',
  `valid_dates` int(4) DEFAULT '0' COMMENT '有效时长',
  `state` tinyint(3) NOT NULL DEFAULT '1' COMMENT '状态 1-生效中 2-已过期 3-已取消 4-已删除',
  `channel` int(2) DEFAULT '0' COMMENT '数据来源(0:network 1:Sales/PMD部门 2:Shop部门)',
  `staff_info_id` int(10) unsigned DEFAULT NULL COMMENT '收信人员工id',
  `created_at` datetime(3) DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  `updated_at` datetime(3) DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=13153 DEFAULT CHARSET=utf8mb4 COMMENT='客户优惠过期提醒';


--
-- Table structure for table `fuel_approve`
--



CREATE TABLE `fuel_approve` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `staff_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '用户ID',
  `serial_no` varchar(30) NOT NULL DEFAULT '' COMMENT '编号',
  `start_drive_time` datetime NOT NULL DEFAULT '1970-01-01 00:00:00' COMMENT '开始用车时间',
  `start_drive_lat` decimal(11,8) NOT NULL DEFAULT '0.00000000' COMMENT '开始用车地点经度',
  `start_drive_lng` decimal(11,8) NOT NULL DEFAULT '0.00000000' COMMENT '开始用车地点纬度',
  `start_drive_place` varchar(100) NOT NULL DEFAULT '' COMMENT '开始用车地点',
  `start_drive_mileage` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '开始用车时里程数',
  `start_drive_mileage_img` varchar(500) NOT NULL DEFAULT '' COMMENT '开始用车里程数图片',
  `start_drive_place_img` varchar(500) NOT NULL DEFAULT '' COMMENT '开始用车地点图片',
  `end_drive_time` datetime NOT NULL DEFAULT '1970-01-01 00:00:00' COMMENT '结束用车时间',
  `end_drive_lat` decimal(11,8) NOT NULL DEFAULT '0.00000000' COMMENT '结束用车地点经度',
  `end_drive_lng` decimal(11,8) NOT NULL DEFAULT '0.00000000' COMMENT '结束用车地点纬度',
  `end_drive_place` varchar(100) NOT NULL DEFAULT '' COMMENT '结束用车地点',
  `end_drive_mileage` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '结束用车时里程数',
  `end_drive_mileage_img` varchar(500) NOT NULL DEFAULT '' COMMENT '结束用车里程数图片',
  `end_drive_place_img` varchar(500) NOT NULL DEFAULT '' COMMENT '结束用车地点图片',
  `drive_reason` varchar(250) NOT NULL DEFAULT '' COMMENT '用车原因',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '数据状态 0 未填写结束用车数据 1 待审批 2 审批完成',
  `input_state` tinyint(1) unsigned DEFAULT '0' COMMENT 'WRS 审核状态：0、未审核，1、审核中，2、通过，3、模糊，4，虚假',
  `input_by` varchar(10) DEFAULT '' COMMENT 'WRS 审核人（补单员）',
  `input_id` int(10) unsigned DEFAULT NULL COMMENT 'WRS 审核人id（补单员）',
  `input_at` datetime DEFAULT NULL COMMENT 'WRS 审核时间',
  `ft_id` bigint(10) unsigned DEFAULT NULL COMMENT 'WRS 油表审核任务表 id',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `staff_id_key` (`staff_id`),
  KEY `idx_start_time` (`start_drive_time`)
) ENGINE=InnoDB AUTO_INCREMENT=113 DEFAULT CHARSET=utf8mb4 COMMENT='油费补贴表';


--
-- Table structure for table `git_code_count`
--



CREATE TABLE `git_code_count` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `project` varchar(255) DEFAULT NULL COMMENT '项目',
  `name` varchar(255) DEFAULT NULL COMMENT '姓名',
  `added` int(10) DEFAULT NULL COMMENT '新增条数',
  `removed` int(10) DEFAULT NULL COMMENT '删除条数',
  `total` int(10) DEFAULT NULL COMMENT '总条数',
  `start_date` datetime DEFAULT NULL COMMENT '开始时间',
  `end_date` datetime DEFAULT NULL COMMENT '结束时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间1',
  PRIMARY KEY (`id`),
  KEY `idx_updated_at` (`updated_at`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=63 DEFAULT CHARSET=utf8mb4;


--
-- Table structure for table `hc_approval_detail`
--



CREATE TABLE `hc_approval_detail` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `hc_task_id` int(11) DEFAULT NULL COMMENT '审批id',
  `export_time` datetime DEFAULT NULL COMMENT '导出时间',
  `hc_id` int(11) DEFAULT NULL,
  `branch_id` varchar(100) DEFAULT NULL,
  `job_name` varchar(255) DEFAULT NULL,
  `reason` varchar(500) DEFAULT NULL,
  `demand_num` int(4) DEFAULT NULL,
  `priority` int(10) DEFAULT NULL,
  `approval_result` varchar(250) DEFAULT NULL COMMENT '审批结果',
  `cause_rejection` varchar(250) DEFAULT NULL COMMENT '驳回原因',
  `state` tinyint(1) DEFAULT '1' COMMENT '1未处理2审批通过3驳回',
  `error_message` varchar(255) DEFAULT NULL COMMENT '错误原因',
  `create_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `update_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_task_id` (`hc_task_id`)
) ENGINE=InnoDB AUTO_INCREMENT=4743 DEFAULT CHARSET=utf8mb4;


--
-- Table structure for table `hc_approval_task`
--



CREATE TABLE `hc_approval_task` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `is_deal` tinyint(1) DEFAULT '0',
  `staff_id` int(11) NOT NULL COMMENT '审批人',
  `create_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `update_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `total_num` int(11) DEFAULT '0',
  `success_num` int(11) DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `idx_key` (`create_at`,`staff_id`,`is_deal`)
) ENGINE=InnoDB AUTO_INCREMENT=35 DEFAULT CHARSET=utf8mb4;


--
-- Table structure for table `hc_priority_record`
--



CREATE TABLE `hc_priority_record` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `staff_id` int(11) DEFAULT NULL COMMENT '操作人',
  `hc_id` int(11) DEFAULT NULL,
  `is_success` tinyint(1) DEFAULT '0' COMMENT '1成功2失败',
  `error_message` varchar(255) DEFAULT NULL COMMENT '失败原因',
  `priority_id` int(11) DEFAULT NULL,
  `create_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'CURRENT_TIMESTAMP',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2032 DEFAULT CHARSET=utf8mb4;


--
-- Table structure for table `headquarters_address`
--



CREATE TABLE `headquarters_address` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `office_name` varchar(100) NOT NULL DEFAULT '',
  `province_name` varchar(50) NOT NULL DEFAULT '' COMMENT '省份名称',
  `province_code` varchar(4) NOT NULL DEFAULT '' COMMENT '省份code',
  `city_name` varchar(50) NOT NULL DEFAULT '' COMMENT '城市名称',
  `city_code` varchar(6) NOT NULL DEFAULT '' COMMENT '城市code',
  `district_name` varchar(50) NOT NULL DEFAULT '' COMMENT '区域名称',
  `district_code` varchar(8) NOT NULL DEFAULT '' COMMENT '区域code',
  `address` varchar(255) NOT NULL DEFAULT '' COMMENT '详细地址',
  `postal_code` varchar(50) NOT NULL DEFAULT '',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4;


--
-- Table structure for table `headquarters_attendance_range`
--



CREATE TABLE `headquarters_attendance_range` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `longitude` decimal(16,8) NOT NULL DEFAULT '0.00000000' COMMENT '经度',
  `latitude` decimal(16,8) NOT NULL DEFAULT '0.00000000' COMMENT '纬度',
  `attendance_range` int(11) NOT NULL DEFAULT '0' COMMENT '打卡范围,单位:米',
  `deleted` tinyint(4) NOT NULL DEFAULT '0',
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updated_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COMMENT='总部打卡距离配置表';


--
-- Table structure for table `hr_annex`
--



CREATE TABLE `hr_annex` (
  `id` int(32) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `oss_bucket_key` int(11) NOT NULL COMMENT '上传文件关联外键',
  `oss_bucket_type` varchar(50) NOT NULL COMMENT '上传文件类型',
  `file_content_type` varchar(100) DEFAULT NULL COMMENT '文件类型',
  `bucket_name` varchar(63) NOT NULL COMMENT 'oss bucketName',
  `object_key` varchar(1000) DEFAULT NULL COMMENT 'oss 对象 key 值',
  `original_name` varchar(200) NOT NULL COMMENT '上传图片原始名称',
  `deleted` tinyint(3) NOT NULL DEFAULT '0' COMMENT '逻辑删除 1:已逻辑删除 0：未逻辑删除',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `rotate_angle` int(10) DEFAULT '0' COMMENT '图片旋转角度：0、90、180、270',
  `file_size` varchar(20) DEFAULT NULL COMMENT '文件大小',
  `file_type` int(10) DEFAULT '0' COMMENT '0=其他，1=本人半身照，2=身份证正面，3=身份证反面，4=户籍照第一页，5=户籍照应聘者本人信息页，6=兵役服役证明，7=成绩报告单，8=清白证明书，9=补充附件，10=驾驶证正面，11=驾驶证反面，12=车辆登记薄，13=车辆照片，14=车辆使用授权，15=签名，16=个人简历, 17=残疾证正面，18=残疾证反面',
  `type` int(10) DEFAULT '1' COMMENT '1=简历附件，2=hc附件',
  PRIMARY KEY (`id`),
  KEY `idx_file_type` (`file_type`),
  KEY `idx_deleted` (`deleted`),
  KEY `idx_oss_bucket_key` (`oss_bucket_key`) USING BTREE,
  KEY `idx_type` (`type`,`oss_bucket_key`,`deleted`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=887444 DEFAULT CHARSET=utf8mb4 COMMENT='简历附件';


--
-- Table structure for table `hr_approval`
--



CREATE TABLE `hr_approval` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `hc_id` int(10) DEFAULT NULL COMMENT '关联hc表主键',
  `submitter_id` int(10) DEFAULT NULL COMMENT '提交者id',
  `reason` text COMMENT '原因',
  `state_code` int(10) DEFAULT NULL COMMENT '状态',
  `updatetime` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `createtime` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `position_id` varchar(50) DEFAULT NULL COMMENT '职位id',
  `staff_flag` int(2) DEFAULT '1' COMMENT '类型 1 以职位为准 2 以员工id为准',
  `staff_ids` text COMMENT '有权限的员工ids',
  PRIMARY KEY (`id`),
  KEY `hc_id` (`hc_id`),
  KEY `submitter_id` (`submitter_id`),
  KEY `state_code` (`state_code`)
) ENGINE=InnoDB AUTO_INCREMENT=176001 DEFAULT CHARSET=utf8mb4;


--
-- Table structure for table `hr_blacklist`
--



CREATE TABLE `hr_blacklist` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `identity` varchar(50) DEFAULT NULL COMMENT '证件号（身份证/护照）',
  `name` varchar(50) DEFAULT NULL COMMENT '姓名',
  `mobile_area_code` varchar(5) DEFAULT NULL COMMENT '电话区号',
  `mobile` varchar(20) DEFAULT NULL COMMENT '电话',
  `submitter_staff_id` int(11) DEFAULT NULL COMMENT '提交人id',
  `type` tinyint(2) DEFAULT '1' COMMENT '类型（1=直接录入，2=离职-开除，3=离职-个人原因，4=取消Offer，5=面试淘汰，6=取消面试）',
  `remark` text COMMENT '备注',
  `remove_staff_id` int(11) DEFAULT NULL COMMENT '移除人id',
  `remove_date` datetime DEFAULT NULL COMMENT '移除时间',
  `remove_remark` text COMMENT '移除说明',
  `status` tinyint(2) DEFAULT '1' COMMENT '状态（1=生效中，2=已移除）',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `identity` (`identity`) USING BTREE,
  KEY `type` (`type`) USING BTREE,
  KEY `remove_date` (`remove_date`) USING BTREE,
  KEY `updated_at` (`updated_at`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=18013 DEFAULT CHARSET=utf8mb4 COMMENT='黑名单列表';


--
-- Table structure for table `hr_department_store_bp`
--



CREATE TABLE `hr_department_store_bp` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `department_id` int(10) DEFAULT NULL COMMENT '一级部门id',
  `department_name` varchar(255) DEFAULT NULL COMMENT '部门名称',
  `store_id` varchar(10) DEFAULT NULL COMMENT '网点id',
  `store_name` varchar(255) DEFAULT NULL COMMENT '网点名称',
  `hrbp` int(10) DEFAULT NULL COMMENT 'hrbp员工id',
  `hr_service` int(10) DEFAULT NULL COMMENT 'hr_service员工id',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_department_store` (`department_id`,`store_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1273 DEFAULT CHARSET=utf8mb4 COMMENT='部门网点对应的bp信息';


--
-- Table structure for table `hr_economy_ability`
--



CREATE TABLE `hr_economy_ability` (
  `id` int(10) NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `education_level` int(10) DEFAULT NULL COMMENT '最高学历 1 初中及以下 2 高中 3 专科 4 本科 5 硕士及以上',
  `graduate_school` varchar(500) DEFAULT NULL COMMENT '毕业学校',
  `major` varchar(500) DEFAULT NULL COMMENT '专业',
  `graduate_time` varchar(50) DEFAULT NULL COMMENT '毕业时间',
  `word` int(4) DEFAULT NULL COMMENT 'word能力 1=差,2=一般,3=好，4=很好',
  `excel` int(4) DEFAULT NULL COMMENT 'excel能力 1=差,2=一般,3=好，4=很好',
  `computer_other` text COMMENT '电脑能力-其他，多个自定义能力',
  `bool_relation` int(4) DEFAULT NULL COMMENT '是否有亲戚在本公司 1=有，2=没有',
  `recommender` varchar(500) DEFAULT NULL COMMENT '推荐人',
  `car_owner` int(4) DEFAULT NULL COMMENT '车主 1=本人自有车,2=借用车辆',
  `car_number` varchar(500) DEFAULT NULL COMMENT '车牌号',
  `driver_number` varchar(500) DEFAULT NULL COMMENT '驾驶号',
  `driving_license_type` tinyint(3) unsigned DEFAULT '0' COMMENT '驾照类型：1-公共驾驶执照, 2-私人驾驶执照',
  `place_cards` varchar(500) DEFAULT NULL COMMENT '上牌地点',
  `work_experience` varchar(1000) DEFAULT NULL,
  `languages` text,
  `relationship` varchar(1000) DEFAULT NULL,
  `education_spare` varchar(500) DEFAULT NULL,
  `resume_id` int(10) DEFAULT NULL COMMENT 'resume_id',
  `car_engine_number` varchar(16) DEFAULT '' COMMENT '发动机号',
  PRIMARY KEY (`id`),
  KEY `resume_id` (`resume_id`)
) ENGINE=InnoDB AUTO_INCREMENT=78032 DEFAULT CHARSET=utf8mb4 COMMENT='经济与能力';


--
-- Table structure for table `hr_education`
--



CREATE TABLE `hr_education` (
  `education_code` int(10) NOT NULL COMMENT '状态码',
  `education_value` varchar(255) DEFAULT NULL COMMENT '状态值',
  `is_enable` int(2) DEFAULT '1' COMMENT '是否启用：1=启用，2=不启用',
  PRIMARY KEY (`education_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


--
-- Table structure for table `hr_entry`
--



CREATE TABLE `hr_entry` (
  `entry_id` int(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `interview_offer_id` int(11) DEFAULT NULL COMMENT 'offerId',
  `hc_id` int(11) DEFAULT NULL COMMENT '用人计划id',
  `resume_id` int(11) DEFAULT NULL COMMENT '简历id',
  `interview_id` int(11) DEFAULT NULL COMMENT '面试id',
  `status` int(1) DEFAULT '2' COMMENT '状态1=已⼊职，2=\n待⼊职，3=未⼊职',
  `staff_id` int(10) DEFAULT NULL COMMENT '员工号',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` int(1) DEFAULT '0' COMMENT '是否删除，1=已删除，0=未删除',
  `entry_date` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '入职日期',
  `operate_date` datetime DEFAULT NULL COMMENT '办理时间',
  `record_entry` text COMMENT '办理⼊职的信息',
  PRIMARY KEY (`entry_id`),
  KEY `interview_offer_id` (`interview_offer_id`),
  KEY `hc_id` (`hc_id`),
  KEY `resume_id` (`resume_id`),
  KEY `interview_id` (`interview_id`),
  KEY `idx_staff_id` (`staff_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=44945 DEFAULT CHARSET=utf8mb4;


--
-- Table structure for table `hr_family`
--



CREATE TABLE `hr_family` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `dad_call_name` varchar(50) DEFAULT NULL COMMENT '父亲称呼',
  `dad_first_name` varchar(250) DEFAULT NULL COMMENT '父亲名',
  `dad_last_name` varchar(250) DEFAULT NULL COMMENT '父亲姓',
  `dad_age` int(10) DEFAULT NULL COMMENT '父亲年龄',
  `dad_job_name` varchar(250) DEFAULT NULL COMMENT '父亲职业名称',
  `dad_mobile_area_code` char(5) DEFAULT NULL COMMENT '父亲电话区域码',
  `dad_mobile` char(20) DEFAULT NULL COMMENT '父亲电话',
  `dad_alive` int(4) DEFAULT NULL COMMENT '父亲是否活着 1=健在,0=已去世',
  `mum_call_name` varchar(50) DEFAULT NULL COMMENT '母亲称呼',
  `mum_first_name` varchar(250) DEFAULT NULL COMMENT '母亲名',
  `mum_last_name` varchar(250) DEFAULT NULL COMMENT '母亲姓',
  `mum_age` int(10) DEFAULT NULL COMMENT '母亲年龄',
  `mum_job_name` varchar(250) DEFAULT NULL COMMENT '母亲职业名称',
  `mum_mobile_area_code` char(5) DEFAULT NULL COMMENT '母亲电话区域码',
  `mum_mobile` char(20) DEFAULT NULL COMMENT '母亲电话',
  `mum_alive` int(4) DEFAULT NULL COMMENT '母亲是否活着 1=健在，0=已去世',
  `marital` int(10) DEFAULT NULL COMMENT '婚姻状况 1=未婚 2=已婚 3=离婚 4=丧偶 5=分居',
  `spouse_call_name` varchar(50) DEFAULT NULL COMMENT '配偶称呼',
  `spouse_first_name` varchar(250) DEFAULT NULL COMMENT '配偶名',
  `spouse_last_name` varchar(250) DEFAULT NULL COMMENT '配偶姓',
  `spouse_job_name` varchar(250) DEFAULT NULL COMMENT '配偶职业',
  `spouse_mobile_area_code` char(5) DEFAULT NULL,
  `spouse_mobile` varchar(250) DEFAULT NULL COMMENT '配偶电话',
  `child_num` int(10) DEFAULT NULL COMMENT '子女数量',
  `child_list` varchar(1000) DEFAULT NULL COMMENT '子女称呼',
  `relationship` int(10) DEFAULT NULL COMMENT '亲属关系 1=父亲 2=母亲 3=配偶 4=子女 5=亲戚 6=朋友',
  `relationship_call_name` varchar(50) DEFAULT NULL COMMENT '亲属称呼',
  `relationship_first_name` varchar(250) DEFAULT NULL COMMENT '亲属名',
  `relationship_last_name` varchar(250) DEFAULT NULL COMMENT '亲属姓',
  `relationship_mobile_area_code` char(5) DEFAULT NULL COMMENT '亲属电话区域码',
  `relationship_mobile` varchar(250) DEFAULT NULL COMMENT '亲属电话',
  `resume_id` int(10) DEFAULT NULL COMMENT '简历id',
  PRIMARY KEY (`id`),
  KEY `resume_id` (`resume_id`)
) ENGINE=InnoDB AUTO_INCREMENT=75063 DEFAULT CHARSET=utf8mb4 COMMENT='家庭信息';


--
-- Table structure for table `hr_hc`
--



CREATE TABLE `hr_hc` (
  `hc_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `serial_no` varchar(32) DEFAULT NULL COMMENT '序列号',
  `job_id` int(10) DEFAULT NULL COMMENT 'jd表id',
  `department_id` int(10) DEFAULT NULL COMMENT '部门id',
  `type` int(2) DEFAULT '1' COMMENT '类型：1=快递员工，2=总部员工',
  `worknode_id` varchar(30) DEFAULT NULL COMMENT '工作网点id',
  `expirationdate` datetime DEFAULT NULL COMMENT '截止日期',
  `demandnumber` int(4) DEFAULT NULL COMMENT '需求人数',
  `surplusnumber` int(4) DEFAULT '0' COMMENT '剩余人数',
  `interviewer` varchar(255) DEFAULT NULL COMMENT '面试官',
  `remarks` text COMMENT '其他要求',
  `reason` text COMMENT '用人原因',
  `submitter_id` int(10) DEFAULT NULL COMMENT '提交人id',
  `country_code` varchar(2) DEFAULT NULL COMMENT '国家编码',
  `province_code` varchar(4) DEFAULT NULL COMMENT '网点一级行政区划',
  `city_code` varchar(6) DEFAULT NULL COMMENT '网点二级行政区划',
  `district_code` varchar(8) DEFAULT NULL COMMENT '网点三级行政区划',
  `state_code` int(10) DEFAULT '1' COMMENT '状态表code状态表code,1=审批中，2=招聘中，3=已完成，4=已作废，5=已拒绝，6=已同意，7=待审批，8=申请，9=过期',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `createtime` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `priority_id` int(2) DEFAULT NULL COMMENT '优先级',
  `approval_stage` tinyint(4) DEFAULT '0' COMMENT '审批阶段',
  `approval_state_code` tinyint(4) DEFAULT '0' COMMENT '审批状态,4撤销 5 驳回 6同意 7待审批 8申请 9超时',
  `workflow_role` varchar(255) DEFAULT NULL COMMENT '审批流角色',
  `approval_completion_time` datetime DEFAULT NULL COMMENT '审批完成时间',
  `reason_type` tinyint(1) DEFAULT '1' COMMENT '用人原因类型，1招聘，2转岗， 3离职',
  `hire_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '雇佣类型1 正式员工 2 月薪制特殊合同工  3 日薪制特殊合同工 4 时薪制特殊合同工  5  实习生员工',
  `hire_times` int(11) NOT NULL DEFAULT '0' COMMENT '雇佣天数/月数',
  `deleted` tinyint(3) DEFAULT '1' COMMENT '是否删除 1-未删除 2-已删除',
  `job_title` int(5) DEFAULT NULL COMMENT '申请职位',
  `request_info` varchar(255) DEFAULT NULL COMMENT 'hc预算剩余、总数',
  PRIMARY KEY (`hc_id`),
  KEY `job_id` (`job_id`),
  KEY `department_id` (`department_id`),
  KEY `province_code` (`province_code`),
  KEY `expirationdate` (`expirationdate`),
  KEY `idx_state_code` (`state_code`,`approval_state_code`) USING BTREE,
  KEY `idx_job_title` (`job_title`) USING BTREE,
  KEY `idx_created_at` (`createtime`)
) ENGINE=InnoDB AUTO_INCREMENT=58866 DEFAULT CHARSET=utf8mb4;


--
-- Table structure for table `hr_hc_budget`
--



CREATE TABLE `hr_hc_budget` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `budget_month` varchar(100) DEFAULT NULL COMMENT '预算月份',
  `dept_id` int(11) DEFAULT NULL COMMENT '用人部门id',
  `dept_name` varchar(100) DEFAULT NULL,
  `applicant_id` int(11) DEFAULT NULL COMMENT '申请人id',
  `applicant_name` varchar(50) DEFAULT NULL,
  `applicant_dept_id` int(11) DEFAULT NULL COMMENT '申请人部门id',
  `applicant_dept_name` varchar(100) DEFAULT NULL COMMENT '申请人部门名称',
  `status` tinyint(2) DEFAULT NULL COMMENT '审核状态 1待审批 2审批通过 3驳回 4撤销',
  `remarks` text COMMENT '备注说明',
  `annex` text COMMENT '附件内容',
  `summary` text,
  `item_file` text,
  `is_batch` tinyint(1) DEFAULT '0' COMMENT '是否批量上传0否1是',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=944 DEFAULT CHARSET=utf8mb4 COMMENT='Hc 预算申请表';


--
-- Table structure for table `hr_hc_budget_item`
--



CREATE TABLE `hr_hc_budget_item` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `budget_id` int(11) DEFAULT NULL COMMENT 'hc 预算id',
  `dept_id` int(10) DEFAULT NULL COMMENT '直属部门id',
  `dept_name` varchar(100) DEFAULT NULL,
  `job_title_id` int(11) DEFAULT NULL COMMENT '职位id',
  `job_title_name` varchar(100) DEFAULT NULL,
  `store_id` varchar(100) DEFAULT NULL COMMENT '网点id',
  `store_name` varchar(100) DEFAULT NULL,
  `budget_count` int(10) DEFAULT NULL COMMENT '预算人数',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `on_job_count` int(10) unsigned DEFAULT '0' COMMENT '在职人数',
  `to_be_hired_count` int(10) unsigned DEFAULT '0' COMMENT '待入职人数',
  `hiring_count` int(10) unsigned DEFAULT '0' COMMENT '招聘中人数',
  `adopt_count` int(10) DEFAULT '0' COMMENT '已提交新增招聘人数',
  `cur_month_budget` int(10) DEFAULT '0' COMMENT '当月预算人数',
  `cur_month_planed` int(10) DEFAULT '0' COMMENT '当月计划人数',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=27003 DEFAULT CHARSET=utf8mb4 COMMENT='Hc 预算详情表';


--
-- Table structure for table `hr_hc_leave_staff`
--



CREATE TABLE `hr_hc_leave_staff` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `hc_id` int(11) NOT NULL COMMENT 'hr_hc表id',
  `staff_info_id` int(11) DEFAULT NULL COMMENT '员工id',
  `snapshot` varchar(500) DEFAULT NULL COMMENT '员工信息快照',
  `create_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=398 DEFAULT CHARSET=utf8mb4 COMMENT='Hc申请所选离职员工表';


--
-- Table structure for table `hr_interview`
--



CREATE TABLE `hr_interview` (
  `interview_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `resume_id` int(11) DEFAULT NULL COMMENT '简历ID',
  `hc_id` int(11) DEFAULT NULL COMMENT 'HCID',
  `job_id` int(11) DEFAULT NULL COMMENT '岗位ID',
  `state` int(2) NOT NULL DEFAULT '1' COMMENT '状态 \r\n1未沟通 \r\n5待面试  \r\n10面试中 \r\n20代发OFFER \r\n25已发offer \r\n30已拒绝 \r\n31已取消 \r\n32已删除',
  `cancel_type` int(11) DEFAULT NULL COMMENT '取消原因类型',
  `cancel_reason` text COMMENT '取消原因',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `send_time` datetime DEFAULT NULL COMMENT '发送offer时间',
  PRIMARY KEY (`interview_id`),
  KEY `resume_id` (`resume_id`,`hc_id`),
  KEY `job_id` (`job_id`),
  KEY `idx_hc_id` (`hc_id`),
  KEY `idx_state` (`state`)
) ENGINE=InnoDB AUTO_INCREMENT=48269 DEFAULT CHARSET=utf8mb4;


--
-- Table structure for table `hr_interview_file`
--



CREATE TABLE `hr_interview_file` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `interview_info_id` int(11) DEFAULT NULL COMMENT '面试InfoID',
  `type` int(1) DEFAULT '1' COMMENT '类型：1、面试信息附件，2、反馈附件，3、简历评价附件',
  `file_path` varchar(300) DEFAULT NULL COMMENT '文件路径',
  `file_name` varchar(300) DEFAULT NULL COMMENT '文件名称',
  `file_size` varchar(255) DEFAULT NULL COMMENT '文件大小',
  `is_del` int(11) DEFAULT '2' COMMENT '状态 是否被删除 1是 2否',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_interview_info_id` (`interview_info_id`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_updated_at` (`updated_at`),
  KEY `idx_type` (`type`)
) ENGINE=InnoDB AUTO_INCREMENT=12516 DEFAULT CHARSET=utf8mb4;


--
-- Table structure for table `hr_interview_info`
--



CREATE TABLE `hr_interview_info` (
  `interview_info_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `interview_id` int(11) DEFAULT NULL COMMENT '面试ID',
  `hc_id` int(11) DEFAULT NULL COMMENT 'HCID',
  `level` int(11) DEFAULT '1' COMMENT '面试轮次',
  `evaluate` varchar(1000) DEFAULT NULL COMMENT '评价内容',
  `currency` int(1) DEFAULT '1' COMMENT '币种 1泰币 2人民币',
  `money` int(10) DEFAULT '0' COMMENT '建议薪资',
  `state` int(1) DEFAULT '2' COMMENT '状态 1终试通过 2 进入下一轮 3 不通过',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `out_reason` text COMMENT '淘汰原因',
  `out_type` tinyint(2) DEFAULT '0' COMMENT '淘汰类型',
  PRIMARY KEY (`interview_info_id`),
  KEY `interview_id` (`interview_id`),
  KEY `idx_hc_id` (`hc_id`)
) ENGINE=InnoDB AUTO_INCREMENT=57118 DEFAULT CHARSET=utf8mb4;


--
-- Table structure for table `hr_interview_log`
--



CREATE TABLE `hr_interview_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `staff_info_id` int(11) DEFAULT NULL COMMENT '操作人ID',
  `interview_id` int(11) DEFAULT NULL COMMENT '面试ID',
  `interview_log` varchar(300) DEFAULT NULL COMMENT '面试操作详情',
  `status` int(1) DEFAULT '1' COMMENT '状态 1开启  2关闭',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `module_type` int(3) DEFAULT NULL COMMENT '1=面试，2=offer，3=入职',
  `module_status` varchar(3) DEFAULT NULL COMMENT '1=进入下一步，2=不通过，3=终试通过，4=修改内容',
  `module_action` int(3) DEFAULT NULL COMMENT '1=创建，2=修改，3=删除，4=取消，5=查看，6=预约，7=填写，8=发送，9=办理，10=反馈',
  `module_level` varchar(3) DEFAULT '0' COMMENT '轮次',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5797 DEFAULT CHARSET=utf8mb4;


--
-- Table structure for table `hr_interview_offer`
--



CREATE TABLE `hr_interview_offer` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `interview_id` int(11) DEFAULT NULL COMMENT '面试ID',
  `resume_id` int(11) DEFAULT NULL COMMENT '简历ID',
  `submitter_id` int(11) NOT NULL COMMENT '提交人ID',
  `hc_id` int(11) DEFAULT NULL COMMENT 'HCID',
  `currency` int(1) DEFAULT '1' COMMENT '币种 1泰币 2人民币',
  `money` int(11) DEFAULT '0' COMMENT '薪资 以分单位',
  `trial_salary` int(11) DEFAULT '0' COMMENT '试用期通过薪资',
  `other` text COMMENT '其他',
  `status` int(1) DEFAULT NULL COMMENT '状态 1发送 2取消发送',
  `work_time` timestamp NULL DEFAULT NULL COMMENT '到岗时间',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `cancel_type` int(11) DEFAULT '0' COMMENT '取消类型',
  `cancel_reason` text COMMENT '取消原因',
  `position_id` int(5) DEFAULT NULL COMMENT '职位id',
  `exp` int(11) DEFAULT '0' COMMENT '工作经验',
  `food` int(11) DEFAULT '0' COMMENT '餐补',
  `position` int(11) DEFAULT '0' COMMENT '岗位津贴',
  `rental` int(11) DEFAULT '0' COMMENT '租车费',
  `fuel` int(11) DEFAULT '0' COMMENT '每日油费补贴',
  `staff_job_type` tinyint(2) DEFAULT '1' COMMENT '员工属性类型，(1正式，2临时，3实习，4一年期)',
  `hire_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '雇佣类型1 正式员工 2 月薪制特殊合同工  3 日薪制特殊合同工 4 时薪制特殊合同工  5  实习生员工',
  `equipment_cost` int(10) DEFAULT '0' COMMENT '设备费',
  `train_time` datetime DEFAULT NULL COMMENT '培训时间',
  `computer` int(11) DEFAULT '0' COMMENT '电脑补贴',
  `renting` int(11) DEFAULT '0' COMMENT '租房津贴',
  `dangerously` int(11) DEFAULT '0' COMMENT '危险区域津贴',
  `island_allowance` int(11) DEFAULT '0' COMMENT '海岛补助',
  `gasoline_allowance` int(11) DEFAULT '0' COMMENT '销售油补',
  `shift_id` int(11) DEFAULT '0' COMMENT '班次id',
  `job_title_grade` tinyint(2) unsigned DEFAULT NULL COMMENT '职级',
  PRIMARY KEY (`id`),
  KEY `resume_id` (`resume_id`),
  KEY `idx_interview_id` (`interview_id`),
  KEY `idx_hc_id` (`hc_id`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_updated_at` (`updated_at`),
  KEY `idx_status` (`status`),
  KEY `idx_work_time` (`work_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=49515 DEFAULT CHARSET=utf8mb4;


--
-- Table structure for table `hr_interview_subscribe`
--



CREATE TABLE `hr_interview_subscribe` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `interview_id` int(11) DEFAULT NULL COMMENT '面试ID',
  `interviewer_id` int(11) DEFAULT NULL COMMENT '面试官ID',
  `hc_id` int(11) DEFAULT NULL COMMENT 'HCID',
  `level` int(11) DEFAULT '1' COMMENT '预约轮次',
  `interview_time` datetime DEFAULT NULL COMMENT '预约面试时间',
  `shop_id` varchar(255) DEFAULT NULL COMMENT '网点ID',
  `country_code` varchar(2) DEFAULT NULL COMMENT '国家编码',
  `country_name` varchar(255) DEFAULT NULL COMMENT '国家名称',
  `province_code` varchar(4) DEFAULT NULL COMMENT '一级行政区划编码',
  `province_name` varchar(255) DEFAULT NULL COMMENT '省份名称',
  `city_code` varchar(6) DEFAULT NULL COMMENT '二级行政区划编码',
  `city_name` varchar(255) DEFAULT NULL COMMENT '城市名称',
  `district_code` varchar(8) DEFAULT NULL COMMENT '三级行政区划编码',
  `district_name` varchar(300) DEFAULT NULL COMMENT '区名称',
  `postal_code` varchar(255) DEFAULT NULL COMMENT '邮编',
  `detail_address` varchar(300) DEFAULT NULL COMMENT '面试详细地址',
  `status` int(1) NOT NULL DEFAULT '1' COMMENT '状态 1未取消 2 已取消 3 面试完成',
  `staff_id` int(11) DEFAULT NULL COMMENT '操作人',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `resume_provider_id` varchar(255) DEFAULT NULL COMMENT '简历提供人id',
  PRIMARY KEY (`id`),
  KEY `interview_id` (`interview_id`),
  KEY `idx_interviewer_id` (`interviewer_id`),
  KEY `idx_hc_id` (`hc_id`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_updated_at` (`updated_at`)
) ENGINE=InnoDB AUTO_INCREMENT=48274 DEFAULT CHARSET=utf8mb4;


--
-- Table structure for table `hr_jd`
--



CREATE TABLE `hr_jd` (
  `job_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `job_name` varchar(100) DEFAULT NULL COMMENT '岗位名称',
  `job_description` text COMMENT '岗位描述',
  `department_id` int(10) DEFAULT NULL COMMENT '部门id',
  `position_id` int(10) DEFAULT NULL COMMENT '职位ID',
  `report_superior_id` int(10) DEFAULT NULL COMMENT '汇报线',
  `submitter_id` int(10) DEFAULT NULL COMMENT '提交人id',
  `submitter_name` varchar(255) DEFAULT NULL COMMENT '提交人名称',
  `state` int(2) NOT NULL DEFAULT '1' COMMENT '状态：1=未删除，2=已删除',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `type` int(11) DEFAULT '0' COMMENT '状态 (1.摩托车,2.皮卡车,3非快递员)',
  `sort` int(11) DEFAULT '100' COMMENT '自定义排序字段',
  `job_group_id` int(4) unsigned NOT NULL DEFAULT '0' COMMENT '职组ID',
  `job_group_child_id` int(4) unsigned NOT NULL DEFAULT '0' COMMENT '子职组ID',
  `created_uid` int(10) DEFAULT NULL COMMENT '创建人',
  `updated_uid` int(10) DEFAULT NULL COMMENT '更新人',
  `created_uname` varchar(100) NOT NULL DEFAULT '' COMMENT '创建人名称',
  `updated_uname` varchar(100) NOT NULL DEFAULT '' COMMENT '更新人名称',
  PRIMARY KEY (`job_id`),
  KEY `created_at` (`created_at`),
  KEY `updated_at` (`updated_at`),
  KEY `idx_type` (`type`),
  KEY `idx_job_group_id` (`job_group_id`) USING BTREE COMMENT '子职组ID'
) ENGINE=InnoDB AUTO_INCREMENT=11242 DEFAULT CHARSET=utf8mb4;


--
-- Table structure for table `hr_job_competency`
--



CREATE TABLE `hr_job_competency` (
  `id` int(4) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL DEFAULT '' COMMENT '胜任力名称',
  `type` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '胜任力类型 1：领导胜任力，2：专业胜任力，3：核心胜任力',
  `is_del` tinyint(1) NOT NULL DEFAULT '0' COMMENT '0正常1删除',
  `description` varchar(500) NOT NULL DEFAULT '' COMMENT '胜任力描述',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=95 DEFAULT CHARSET=utf8mb4 COMMENT='职位管理-胜任力表';


--
-- Table structure for table `hr_job_department_relation`
--



CREATE TABLE `hr_job_department_relation` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `department_id` int(11) NOT NULL DEFAULT '0' COMMENT '部门id',
  `job_id` int(11) NOT NULL COMMENT '职位id',
  `job_level` varchar(100) NOT NULL DEFAULT '' COMMENT '职级 ：F0,F12-F24,value为数字部分',
  `public_job_name` varchar(100) NOT NULL DEFAULT '' COMMENT '公开职位名称',
  `group_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '职组ID',
  `group_child_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '子职组ID',
  `work_place_type` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '工作地点类型 1：网点，2：职能总部，3：业务总部，4：分拨、5：门店',
  `report_job_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '汇报职位ID',
  `jd_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT 'JD(岗位） id',
  `jd_desc` text COMMENT '岗位描述',
  `jd_desc_supply` text COMMENT 'JD描述(补充)',
  `plan_hc_nums` smallint(5) unsigned NOT NULL DEFAULT '0' COMMENT '计划hc 数量',
  `job_requirements_jybj` text NOT NULL COMMENT '职位要求-教育背景',
  `job_requirements_zyjl` text NOT NULL COMMENT '职位要求-专业经历',
  `job_requirements_other` varchar(1000) NOT NULL DEFAULT '' COMMENT '职位要求_其他',
  `job_competency_zy` varchar(2000) NOT NULL DEFAULT '' COMMENT '胜任力要求-专业胜任力，json内容（胜任力ID，level值）',
  `job_competency_ld` varchar(2000) NOT NULL DEFAULT '' COMMENT '胜任力要求-领导胜任力，json内容（胜任力ID，level值）',
  `job_competency_hx` varchar(2000) NOT NULL DEFAULT '' COMMENT '胜任力要求-核心胜任力,，json内容（胜任力ID）',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `created_uid` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '添加人',
  `created_uname` varchar(100) NOT NULL DEFAULT '' COMMENT '创建人名称',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `updated_uid` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '最后操作人',
  `updated_uname` varchar(100) NOT NULL DEFAULT '' COMMENT '数据更新人名称',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uidx_depart_job` (`department_id`,`job_id`) USING BTREE,
  KEY `idx_groupid` (`group_id`) USING BTREE,
  KEY `idx_joblevel` (`job_level`) USING BTREE,
  KEY `idx_jobid` (`job_id`) USING BTREE,
  KEY `idx_jdid` (`jd_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1432 DEFAULT CHARSET=utf8mb4 COMMENT='职位关联表';


--
-- Table structure for table `hr_job_group`
--



CREATE TABLE `hr_job_group` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `pid` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '父ID',
  `name` varchar(100) NOT NULL DEFAULT '' COMMENT '职组名称',
  `functional_competency_ids` varchar(500) NOT NULL DEFAULT '' COMMENT '专业胜任力ids，多个逗号隔开（一级职组才有该字段）',
  `add_userid` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '添加人ID',
  `is_del` tinyint(1) NOT NULL DEFAULT '0' COMMENT '0正常1删除',
  `update_userid` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '最后更新人ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '0时区',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '0时区',
  `created_uname` varchar(100) NOT NULL DEFAULT '' COMMENT '创建人',
  `updated_uname` varchar(100) NOT NULL DEFAULT '' COMMENT '更新人',
  PRIMARY KEY (`id`),
  KEY `pid` (`pid`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=20 DEFAULT CHARSET=utf8mb4 COMMENT='职位管理-职组表';


--
-- Table structure for table `hr_job_log`
--



CREATE TABLE `hr_job_log` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `hr_job_d_r_id` int(11) unsigned NOT NULL COMMENT '关联 ID',
  `type` tinyint(2) DEFAULT '0' COMMENT '类型: 1.新增职位 2.修改职位 3.新建关联 4. 编辑关联 5.解除关联',
  `log_data` varchar(200) NOT NULL DEFAULT '' COMMENT '历史数据',
  `save_data` varchar(200) NOT NULL DEFAULT '' COMMENT '新数据',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `created_uid` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '操作人',
  `staff_info_name` varchar(50) DEFAULT NULL COMMENT '操作人姓名',
  `hr_job_id` int(11) NOT NULL COMMENT '变更职位 ID',
  PRIMARY KEY (`id`),
  KEY `idx_uid` (`created_uid`) COMMENT '操作人id',
  KEY `idx_hrjobid` (`hr_job_id`) USING BTREE COMMENT '职位 id'
) ENGINE=InnoDB AUTO_INCREMENT=116 DEFAULT CHARSET=utf8mb4 COMMENT='职位管理-变更记录';


--
-- Table structure for table `hr_job_title`
--



CREATE TABLE `hr_job_title` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '职位ID',
  `status` int(1) DEFAULT '1' COMMENT '状态 1开启 2关闭',
  `job_name` varchar(255) DEFAULT NULL COMMENT '职位名称',
  `create_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `operator` int(10) DEFAULT NULL COMMENT '最后操作人id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1076 DEFAULT CHARSET=utf8mb4;


--
-- Table structure for table `hr_languages`
--



CREATE TABLE `hr_languages` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `languages` varchar(10) DEFAULT NULL COMMENT '语种 1=英语,2=汉语,3=日语,4=其他语言,5=无',
  `ability` varchar(10) DEFAULT NULL COMMENT '能力 1=差,2=一般,3=好,4=很好',
  `languages_level` tinyint(3) unsigned DEFAULT '0' COMMENT '中文（HSK等级）语种等级 ：1-1级，2-2级，3-3级，4-4级，5-5级，6-6级，7-其他',
  `lang_remarks` varchar(1000) DEFAULT NULL COMMENT '备注',
  `economy_ablity_id` int(10) DEFAULT NULL COMMENT '经济与能力id',
  `resume_id` int(10) DEFAULT NULL COMMENT '简历id',
  PRIMARY KEY (`id`),
  KEY `resume_id` (`resume_id`)
) ENGINE=InnoDB AUTO_INCREMENT=104046 DEFAULT CHARSET=utf8mb4 COMMENT='外语能力表';


--
-- Table structure for table `hr_log`
--



CREATE TABLE `hr_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `staff_info_id` int(11) DEFAULT NULL COMMENT '操作者id',
  `module_id` int(11) DEFAULT NULL COMMENT '模块id',
  `module_type` tinyint(4) DEFAULT NULL COMMENT '模块类型,1=面试，2=offer，3=入职',
  `action` tinyint(4) DEFAULT NULL COMMENT '操作类型,1=创建，2=修改，3=删除，4=取消，5=查看，6=预约，7=填写，8=发送，9=办理，10=反馈',
  `module_level` tinyint(4) DEFAULT NULL COMMENT '轮次',
  `module_status` tinyint(4) DEFAULT NULL COMMENT '1=进入下一步，2=不通过，3=终试通过，4=修改内容',
  `user_ip` varchar(255) DEFAULT NULL COMMENT '用户ip',
  `data_before` text COMMENT '修改前json格式',
  `data_after` text COMMENT '修改后json格式',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_staff_info_id` (`staff_info_id`),
  KEY `idx_module_id` (`action`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_module_type` (`module_type`,`module_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=996062 DEFAULT CHARSET=utf8mb4;


--
-- Table structure for table `hr_myapproval`
--



CREATE TABLE `hr_myapproval` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `hc_id` int(11) DEFAULT NULL,
  `state_code` int(10) DEFAULT NULL COMMENT '状态表code,1=审批中，2=招聘中，3=已完成，4=已作废，5=已拒绝，6=已同意，7=待审批，8=申请',
  `submitter_id` varchar(11) DEFAULT NULL,
  `updatetime` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `createtime` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `staff_ids` varchar(500) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `hc_id` (`hc_id`),
  KEY `updatetime` (`updatetime`),
  KEY `createtime` (`createtime`),
  KEY `idx_state_code` (`state_code`),
  KEY `idx_submitter_id` (`submitter_id`)
) ENGINE=InnoDB AUTO_INCREMENT=157734 DEFAULT CHARSET=utf8mb4;


--
-- Table structure for table `hr_overtime`
--



CREATE TABLE `hr_overtime` (
  `overtime_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '加班id',
  `serial_no` varchar(32) DEFAULT NULL COMMENT '序列号',
  `staff_id` int(11) DEFAULT NULL COMMENT '员工id',
  `type` int(2) DEFAULT NULL COMMENT '加班类型1=工作日，2=节假日加班，3=晚班 4-节假日正常上班',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `reason` text COMMENT '原因',
  `state` int(2) DEFAULT NULL COMMENT '状态：1=待审批，2=已同意，3=驳回 4=撤销',
  `higher_staff_id` varchar(255) DEFAULT '0' COMMENT '上级id',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `duration` float(2,0) DEFAULT NULL COMMENT '时长',
  `reject_reason` text COMMENT '驳回原因',
  `is_push` int(2) DEFAULT '0' COMMENT '是否推送，默认0=未推送，1=已推送',
  `is_anticipate` tinyint(4) NOT NULL DEFAULT '1' COMMENT '0- 非预申请（后补申请） 1- 预申请',
  `date_at` date DEFAULT NULL COMMENT '申请日期',
  `sub_type` tinyint(4) DEFAULT '0' COMMENT '0-默认 1--1倍日薪 2--1.5倍日薪 3--3倍日薪 4--可调休',
  `wf_role` varchar(20) DEFAULT NULL COMMENT '审批流rolename',
  `references` varchar(500) DEFAULT NULL COMMENT '参考数据',
  PRIMARY KEY (`overtime_id`),
  KEY `ids_index` (`staff_id`,`start_time`,`end_time`),
  KEY `idx_date_at` (`date_at`),
  KEY `idx_state_create_time` (`state`,`created_at`)
) ENGINE=InnoDB AUTO_INCREMENT=2120049 DEFAULT CHARSET=utf8mb4;


--
-- Table structure for table `hr_permission_auth`
--



CREATE TABLE `hr_permission_auth` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `pid` int(11) DEFAULT NULL COMMENT '上级id',
  `class` varchar(255) DEFAULT NULL COMMENT '模块code',
  `function` varchar(255) DEFAULT NULL COMMENT '方法code',
  `auth_code` varchar(150) DEFAULT NULL COMMENT '权限code',
  `describe` varchar(255) DEFAULT NULL COMMENT '描述',
  `is_switch` tinyint(1) NOT NULL DEFAULT '0' COMMENT '接口开关;默认0=开启，1=关闭',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_auth_code` (`auth_code`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=162 DEFAULT CHARSET=utf8mb4;


--
-- Table structure for table `hr_permission_relation`
--



CREATE TABLE `hr_permission_relation` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `staff_id` int(11) DEFAULT NULL COMMENT '员工id',
  `auth_id` int(11) DEFAULT NULL COMMENT '权限id',
  `is_switch` tinyint(1) DEFAULT '0' COMMENT '开关;默认0=开启，1=关闭',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_staffid_authid` (`staff_id`,`auth_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=210287 DEFAULT CHARSET=utf8mb4;


--
-- Table structure for table `hr_position_relation`
--



CREATE TABLE `hr_position_relation` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `position_category` int(11) DEFAULT NULL COMMENT '角色',
  `department_id` int(11) DEFAULT NULL COMMENT '部门ID',
  `manger_position_category` int(11) DEFAULT NULL COMMENT '审批上级角色',
  `manger_staff_ids` text COMMENT '审批上级工号',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


--
-- Table structure for table `hr_questions`
--



CREATE TABLE `hr_questions` (
  `id` int(10) NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `no` int(4) DEFAULT NULL COMMENT '题目编号',
  `question_no` varchar(10) DEFAULT NULL COMMENT '题目翻译编号',
  `question` varchar(500) DEFAULT NULL COMMENT '问题',
  `options` varchar(800) DEFAULT NULL COMMENT '选项列表',
  `type` int(4) DEFAULT NULL COMMENT '类型 摩托车:1，皮卡车:2,与你相关的问题:3',
  `forbid_option` int(4) DEFAULT NULL COMMENT '禁止的选项',
  `display` varchar(10) DEFAULT NULL COMMENT '是否显示隐藏',
  `relation` varchar(300) DEFAULT NULL COMMENT '对应关系',
  `question_text` varchar(100) DEFAULT NULL COMMENT '显示文本框,选几显示附属框',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=40 DEFAULT CHARSET=utf8mb4;


--
-- Table structure for table `hr_relationship`
--



CREATE TABLE `hr_relationship` (
  `id` int(10) NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `relate_first_name` varchar(250) DEFAULT NULL COMMENT '亲属名',
  `relate_last_name` varchar(250) DEFAULT NULL COMMENT '亲属姓',
  `relate_deparment` varchar(250) DEFAULT NULL COMMENT '亲属部门',
  `economy_ablity_id` int(10) DEFAULT NULL COMMENT '经济与能力id',
  `resume_id` int(10) DEFAULT NULL COMMENT '简历id',
  `relate_mobile` varchar(30) DEFAULT NULL COMMENT '亲属电话',
  PRIMARY KEY (`id`),
  KEY `resume_id` (`resume_id`),
  KEY `economy_ablity_id` (`economy_ablity_id`)
) ENGINE=InnoDB AUTO_INCREMENT=109 DEFAULT CHARSET=utf8mb4 COMMENT='亲属表';


--
-- Table structure for table `hr_report_superior`
--



CREATE TABLE `hr_report_superior` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `report_name` varchar(255) DEFAULT NULL COMMENT '汇报线名称',
  `position_id` int(11) DEFAULT NULL COMMENT '职位ID',
  `status` int(1) DEFAULT '1' COMMENT '状态 1 开启 2关闭',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COMMENT='审批字典表';


--
-- Table structure for table `hr_resume`
--



CREATE TABLE `hr_resume` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `name` varchar(50) DEFAULT NULL COMMENT '姓名',
  `sex` tinyint(1) DEFAULT NULL COMMENT '性别：1男、2女、3其他',
  `phone_area_code` char(5) DEFAULT NULL COMMENT '电话区码',
  `phone` varchar(30) DEFAULT NULL COMMENT '手机号',
  `email` varchar(50) DEFAULT NULL COMMENT '电子邮箱',
  `date_birth` date DEFAULT NULL,
  `credentials_category` tinyint(2) DEFAULT NULL COMMENT '证件类型 1:身份证 2:护照',
  `credentials_num` varchar(50) DEFAULT NULL COMMENT '证件号',
  `education` tinyint(1) DEFAULT NULL COMMENT '最高学历：1、初中及以下，2、高中，3、专科，4、本科，5、硕士及以上',
  `graduate_school` varchar(110) DEFAULT NULL COMMENT '毕业学校',
  `discipline` varchar(110) DEFAULT NULL COMMENT '所学专业',
  `graduate_time` datetime DEFAULT NULL COMMENT '毕业时间',
  `work_exp` varchar(2100) DEFAULT NULL COMMENT '工作经历',
  `address_id` varchar(10) DEFAULT NULL COMMENT '工作地址',
  `job_id` varchar(10) DEFAULT NULL COMMENT '岗位id',
  `alternative_job_ids` varchar(500) NOT NULL DEFAULT '' COMMENT '备选职位',
  `submitter_id` int(10) DEFAULT NULL COMMENT '录入人员',
  `currency` tinyint(10) DEFAULT NULL COMMENT '币种：1、泰铢，2、人民币',
  `entry_salary` int(10) DEFAULT NULL COMMENT '薪资：单位：分；币种：泰铢、人民币',
  `state_code` tinyint(1) DEFAULT '1' COMMENT '状态：1未沟通、2已沟通',
  `deleted` tinyint(1) DEFAULT '0' COMMENT '逻辑删除 1:已逻辑删除 0：未逻辑删除',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `hr_interview_deleted` tinyint(1) DEFAULT '0' COMMENT '面试管理删除',
  `first_name` varchar(50) DEFAULT '' COMMENT '名',
  `last_name` varchar(50) DEFAULT '' COMMENT '姓',
  `call_name` varchar(50) DEFAULT NULL COMMENT '称呼 1=Mr.,2=Mrs.,3=Miss',
  `family_id` int(10) DEFAULT NULL COMMENT '家庭id',
  `ablity_id` int(10) DEFAULT NULL COMMENT '经济与能力id',
  `job_type` int(10) DEFAULT NULL COMMENT '岗位类别(1正式，2临时，3实习)',
  `expected_arrivaltime` varchar(10) DEFAULT NULL COMMENT '预计到岗时间',
  `nationality` varchar(50) DEFAULT NULL COMMENT '国籍：1泰国，2中国，3马来西亚，99其他',
  `working_country` tinyint(2) unsigned DEFAULT '1' COMMENT '工作所在国家：1泰国、2中国、3马来西亚、4菲律宾、5越南、6老挝、99其他',
  `first_name_en` varchar(200) DEFAULT NULL COMMENT '名英文',
  `last_name_en` varchar(200) DEFAULT NULL COMMENT '姓中文',
  `expiration_date` varchar(50) DEFAULT NULL COMMENT '失效日期',
  `cert_place` varchar(200) DEFAULT NULL COMMENT '发证地',
  `permit_number` varchar(200) DEFAULT NULL COMMENT '工作证号',
  `religion` varchar(200) DEFAULT NULL COMMENT '宗教',
  `line_id` varchar(200) DEFAULT NULL COMMENT 'line_id',
  `register_house_num` varchar(200) DEFAULT NULL COMMENT '户口所在地门牌号',
  `register_village_num` varchar(200) DEFAULT NULL COMMENT '户口所在地村号',
  `register_village` varchar(200) DEFAULT NULL COMMENT '户口所在地村庄',
  `register_alley` varchar(200) DEFAULT NULL COMMENT '户口所在地巷子',
  `register_street` varchar(200) DEFAULT NULL COMMENT '户口所在地街道',
  `register_country` tinyint(3) unsigned DEFAULT '1' COMMENT '户口所在地国家，1：泰国、2：中国、3：马来西亚、4：菲律宾、5：越南、6：老挝、7：印度尼西亚，8：新加坡，9：柬埔寨，10：缅甸，11：文莱，12：东帝汶',
  `register_government` varchar(200) DEFAULT NULL COMMENT '户口所在地府',
  `register_city` varchar(200) DEFAULT NULL COMMENT '户口所在地市',
  `register_town` varchar(200) DEFAULT NULL COMMENT '户口所在地镇',
  `register_postcodes` varchar(50) DEFAULT NULL COMMENT '户口所在地邮编',
  `residence_house_num` varchar(200) DEFAULT NULL COMMENT '居住所在地门牌号',
  `residence_village_num` varchar(200) DEFAULT NULL COMMENT '居住所在地村号',
  `residence_village` varchar(200) DEFAULT NULL COMMENT '居住所在地村庄',
  `residence_alley` varchar(200) DEFAULT NULL COMMENT '居住所在地巷',
  `residence_street` varchar(200) DEFAULT NULL COMMENT '居住所在地街道',
  `residence_country` tinyint(3) unsigned DEFAULT '1' COMMENT '居住地所在地国家，1：泰国、2：中国、3：马来西亚、4：菲律宾、5：越南、6：老挝、7：印度尼西亚，8：新加坡，9：柬埔寨，10：缅甸，11：文莱，12：东帝汶',
  `residence_government` varchar(200) DEFAULT NULL COMMENT '居住所在地府',
  `residence_city` varchar(200) DEFAULT NULL COMMENT '居住所在地市',
  `residence_town` varchar(200) DEFAULT NULL COMMENT '居住所在地镇',
  `residence_postcodes` varchar(50) DEFAULT NULL COMMENT '居住所在地邮编',
  `fit` int(10) DEFAULT NULL COMMENT '居住地是否和户口所在地一致? 1=一致,0=不一致 ',
  `is_perfect` tinyint(1) DEFAULT '2' COMMENT '是否完善，1=完善，2=未完善',
  `source` tinyint(1) DEFAULT '1' COMMENT '来源，1=h5,2=hr',
  `is_out` tinyint(1) DEFAULT '2' COMMENT '状态，1=已淘汰，2=未淘汰',
  `out_type` tinyint(2) DEFAULT '0' COMMENT '淘汰类型',
  `recruit_channel` tinyint(2) DEFAULT NULL COMMENT '招聘渠道1=Facebook，2=DC，3=Referral，4=Walk-in，5=Friend，6=others',
  `store_id` varchar(50) DEFAULT NULL COMMENT '所属网点',
  `buddy_id` varchar(50) DEFAULT NULL COMMENT 'buddy_id',
  `out_reason` text COMMENT '淘汰原因',
  `nickname` varchar(50) DEFAULT '' COMMENT '昵称',
  PRIMARY KEY (`id`),
  KEY `phone` (`phone`),
  KEY `family_id` (`family_id`),
  KEY `ablity_id` (`ablity_id`),
  KEY `job_id` (`job_id`),
  KEY `created_at` (`created_at`),
  KEY `idx_state_code` (`state_code`),
  KEY `idx_store_id` (`store_id`),
  KEY `updated_at` (`updated_at`,`state_code`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=85770 DEFAULT CHARSET=utf8mb4 COMMENT='简历表';


--
-- Table structure for table `hr_resume_buddy`
--



CREATE TABLE `hr_resume_buddy` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `resume_id` int(11) DEFAULT NULL,
  `item` varchar(50) DEFAULT NULL,
  `val` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_resume_id` (`resume_id`,`item`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=10129 DEFAULT CHARSET=utf8mb4 COMMENT='简历关联buddy的一些特殊字段。';


--
-- Table structure for table `hr_resume_education`
--



CREATE TABLE `hr_resume_education` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `resume_id` int(11) NOT NULL COMMENT '简历id',
  `education_level` tinyint(4) DEFAULT NULL COMMENT '学历 1 初中及以下 2 高中 3 专科 4 本科 5 硕士及以上',
  `graduate_school` varchar(255) DEFAULT NULL COMMENT '毕业学校',
  `major` varchar(255) DEFAULT NULL COMMENT '专业',
  `admission_time` char(10) DEFAULT NULL COMMENT '入学时间',
  `graduate_time` char(10) DEFAULT NULL COMMENT '毕业时间',
  `is_delete` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否删除1删除，0未删除',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_resumeId` (`resume_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=48001 DEFAULT CHARSET=utf8mb4 COMMENT='学历表(2020年9月后加的，以前的学历只有一条在表 hr_economy_ability表里)';


--
-- Table structure for table `hr_resume_log`
--



CREATE TABLE `hr_resume_log` (
  `id` int(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `staff_info_id` varchar(20) DEFAULT NULL COMMENT '操作人ID',
  `module` char(50) DEFAULT NULL COMMENT '操作模块',
  `controller` char(50) DEFAULT NULL COMMENT '操作控制器',
  `action` char(50) DEFAULT NULL COMMENT '操作方法',
  `params` text COMMENT '操作参数',
  `IP` int(11) unsigned DEFAULT NULL COMMENT 'IP',
  `remarks` varchar(1000) DEFAULT NULL COMMENT '备注',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `staff_info_id` (`staff_info_id`),
  KEY `created_at` (`created_at`),
  KEY `updated_at` (`updated_at`)
) ENGINE=InnoDB AUTO_INCREMENT=30 DEFAULT CHARSET=utf8mb4 COMMENT='操作日志';


--
-- Table structure for table `hr_shift`
--



CREATE TABLE `hr_shift` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '班次表',
  `type` enum('EARLY','MIDDLE','NIGHT') NOT NULL COMMENT '班次类型',
  `start` varchar(32) NOT NULL DEFAULT '',
  `end` varchar(32) NOT NULL DEFAULT '',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `mark_up` varchar(32) NOT NULL DEFAULT '',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=51 DEFAULT CHARSET=utf8mb4;


--
-- Table structure for table `hr_shift_config_log`
--



CREATE TABLE `hr_shift_config_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `region_id` int(10) DEFAULT NULL COMMENT '大区id',
  `piece_id` int(10) DEFAULT NULL COMMENT '片区id',
  `job_title_id` int(10) DEFAULT NULL COMMENT '职位id',
  `department_id` int(10) DEFAULT NULL COMMENT '部门id',
  `store_id` varchar(10) DEFAULT NULL COMMENT '网点id',
  `before` text COMMENT '变更前',
  `after` text COMMENT '变更后',
  `operater` int(10) DEFAULT NULL COMMENT '操作者',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=28 DEFAULT CHARSET=utf8mb4;


--
-- Table structure for table `hr_shift_department_config`
--



CREATE TABLE `hr_shift_department_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `department_id` int(10) DEFAULT NULL COMMENT '部门id',
  `job_title_id` int(10) DEFAULT NULL COMMENT '职位id',
  `shift_id` int(10) DEFAULT NULL COMMENT '班次id',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `operater` int(10) DEFAULT NULL COMMENT '操作人',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='部门班次配置';


--
-- Table structure for table `hr_shift_region_piece_config`
--



CREATE TABLE `hr_shift_region_piece_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `region_id` int(10) DEFAULT NULL COMMENT '大区id',
  `piece_id` int(10) DEFAULT NULL COMMENT '片区id',
  `job_title_id` int(11) DEFAULT NULL COMMENT '职位id',
  `shift_id` int(11) DEFAULT NULL COMMENT '班次id',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `operater` int(10) DEFAULT NULL COMMENT '操作者',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2225 DEFAULT CHARSET=utf8mb4 COMMENT='大区片区职位班次配置';


--
-- Table structure for table `hr_shift_store_config`
--



CREATE TABLE `hr_shift_store_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `sys_store_id` varchar(10) DEFAULT NULL COMMENT '网点id',
  `job_title_id` int(10) DEFAULT NULL COMMENT '职位id',
  `shift_id` int(11) DEFAULT NULL COMMENT '班次id',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `operater` int(10) DEFAULT NULL COMMENT '最后操作人',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=150 DEFAULT CHARSET=utf8mb4 COMMENT='网点班次表';


--
-- Table structure for table `hr_staff_contract`
--



CREATE TABLE `hr_staff_contract` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `staff_id` int(11) NOT NULL DEFAULT '0' COMMENT '工号',
  `staff_name` varchar(50) NOT NULL DEFAULT '' COMMENT '员工姓名（名+空格 +姓）',
  `mobile` varchar(20) NOT NULL DEFAULT '' COMMENT '手机号',
  `email` varchar(50) NOT NULL DEFAULT '' COMMENT '企业邮箱',
  `personal_email` varchar(50) NOT NULL DEFAULT '' COMMENT '个人邮箱',
  `department_id` int(11) NOT NULL DEFAULT '0' COMMENT '部门ID',
  `job_title_id` int(11) NOT NULL DEFAULT '0' COMMENT '职位ID',
  `store_id` varchar(50) NOT NULL DEFAULT '' COMMENT '网点ID',
  `manage_region` varchar(32) NOT NULL DEFAULT '0' COMMENT '大区ID',
  `manage_piece` varchar(32) NOT NULL DEFAULT '0' COMMENT '片区ID',
  `entry_date` date NOT NULL DEFAULT '1000-01-01' COMMENT '入职时间',
  `resume_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '简历ID（cvid)',
  `nationality` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '国籍（注释看简历表)',
  `contract_name` varchar(100) NOT NULL DEFAULT '' COMMENT '合同名称',
  `contract_path` varchar(200) NOT NULL DEFAULT '' COMMENT 'pdf合同路径',
  `contract_start_date` date NOT NULL DEFAULT '1000-01-01' COMMENT '开始日期',
  `contract_end_date` date NOT NULL DEFAULT '9999-12-31' COMMENT '截止时间',
  `contract_ld_end_date` date NOT NULL DEFAULT '9999-12-31' COMMENT '劳动合同截止日期',
  `contract_ld_no` varchar(50) NOT NULL DEFAULT '' COMMENT '劳动合同编号（只有劳动合同类型的合同存在）',
  `contract_date_is_long` tinyint(1) NOT NULL DEFAULT '2' COMMENT '是否长期合同 1：是（截止时间不限制），2：否',
  `contract_status` smallint(3) NOT NULL DEFAULT '10' COMMENT '合同状态：10-待添加，20-待发送，30-待员工签署，40-待复核，50-待归档，60-已归档，70-已解除，80-待续约，81-已续约，90-已到期，100-未签字，110-反馈处理',
  `contract_is_all_archived` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '合同是否都已归档 ：0-否，1-是',
  `contract_type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '合同类型：1:劳动合同，2：租车合同，3：公司损失扣款同意书，4：保密协议',
  `contract_child_type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '合同类型的子类（如：劳动合同有 普通劳动合同和6天劳动合同等）',
  `contract_is_need` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否需要该合同：1-需要，2-不需要',
  `contract_signature_img` varchar(200) NOT NULL DEFAULT '' COMMENT '电子签图片路径（oss路径）',
  `contract_msg_id` varchar(50) NOT NULL DEFAULT '' COMMENT '消息ID(message_courier表ID)',
  `contract_is_deleted` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否已删除，1：已删除，0：未删除',
  `contract_send_time` datetime DEFAULT NULL COMMENT '合同发送时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_ct_status_staffid` (`contract_status`,`staff_id`) USING BTREE,
  KEY `idx_staff_id` (`staff_id`) USING BTREE,
  KEY `idx_send_time` (`contract_send_time`) USING BTREE,
  KEY `idx_end_date` (`contract_end_date`) USING BTREE,
  KEY `ids_msg_id` (`contract_msg_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=39692 DEFAULT CHARSET=utf8mb4 COMMENT='员工电子合同表';


--
-- Table structure for table `hr_staff_contract_log`
--



CREATE TABLE `hr_staff_contract_log` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `staff_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '操作对象ID',
  `operate_staff_id` int(11) NOT NULL DEFAULT '0' COMMENT '操作人ID',
  `operate_staff_name` varchar(50) NOT NULL COMMENT '操作人姓名',
  `contract_id` int(11) NOT NULL DEFAULT '0' COMMENT '合同ID',
  `contract_type` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '1:劳动合同，2：租车合同，3：公司损失扣款同意书，4：保密协议',
  `action` tinyint(2) unsigned NOT NULL COMMENT '操作：1-添加，2：发送，3：批量发送，4：删除，5：解除，6：下载，7：不需要,8:续约',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_contract_id` (`contract_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=26189 DEFAULT CHARSET=utf8mb4 COMMENT='电子合同操作日志表';


--
-- Table structure for table `hr_staff_outsourcing`
--



CREATE TABLE `hr_staff_outsourcing` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `serial_no` varchar(32) DEFAULT NULL COMMENT '序列号',
  `staff_id` int(10) DEFAULT NULL COMMENT '提交人ID',
  `os_type` tinyint(3) DEFAULT '1' COMMENT '外协类型 1-短期外协 2-长期外协 3-车队外协',
  `job_id` int(11) DEFAULT NULL COMMENT '外协职位名称',
  `department_id` varchar(10) DEFAULT NULL COMMENT '所属部门',
  `store_id` varchar(10) DEFAULT NULL COMMENT '网点ID',
  `employment_date` date DEFAULT NULL COMMENT '雇佣日期',
  `employment_days` int(3) DEFAULT NULL COMMENT '雇佣天数',
  `shift_id` int(10) DEFAULT NULL COMMENT '班次ID',
  `demend_num` int(3) DEFAULT NULL COMMENT '需求人数',
  `final_audit_num` int(3) DEFAULT NULL COMMENT '最终批准人数',
  `reason_type` tinyint(3) DEFAULT '0' COMMENT '申请原因 0-其他原因 1-偏远外协 2-网点爆仓 3-人员离职 4-网点自身车辆不足 5-人效偏高',
  `reason` varchar(500) DEFAULT NULL COMMENT '申请原因',
  `status` tinyint(3) DEFAULT NULL COMMENT '审批状态',
  `reject_reason` varchar(500) DEFAULT NULL COMMENT '驳回原因',
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  `updated_at` datetime(3) DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
  `wf_role` char(10) DEFAULT '' COMMENT '关联流程',
  PRIMARY KEY (`id`),
  UNIQUE KEY `serial_num_index` (`serial_no`) USING HASH COMMENT '序列号索引'
) ENGINE=InnoDB AUTO_INCREMENT=138699 DEFAULT CHARSET=utf8mb4 COMMENT='外协员工申请表';


--
-- Table structure for table `hr_staff_shift`
--



CREATE TABLE `hr_staff_shift` (
  `staff_info_id` int(11) NOT NULL COMMENT '员工ID',
  `start` varchar(32) NOT NULL DEFAULT '' COMMENT '开始时间',
  `end` varchar(32) NOT NULL DEFAULT '' COMMENT '结束时间',
  `name` varchar(32) DEFAULT '' COMMENT '班次名称',
  `shift_type` enum('EARLY','MIDDLE','NIGHT') DEFAULT NULL COMMENT '班次类型',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `mark_up` varchar(32) DEFAULT NULL,
  `shift_id` int(11) DEFAULT NULL COMMENT '班次ID',
  `working_day` varchar(30) DEFAULT NULL COMMENT '员工工作日',
  `effective_date` date DEFAULT NULL COMMENT '生效时间',
  `last_start` varchar(32) DEFAULT NULL COMMENT '上一班次开始时间',
  `last_end` varchar(32) DEFAULT NULL COMMENT '上一班次结束时间',
  `last_shift_type` enum('EARLY','MIDDLE','NIGHT') DEFAULT NULL COMMENT '上一班次类型',
  `last_shift_id` int(11) DEFAULT NULL COMMENT '上一班次id',
  PRIMARY KEY (`staff_info_id`) USING BTREE,
  KEY `start` (`start`,`end`,`shift_type`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='员工班次表';


--
-- Table structure for table `hr_staff_shift_history`
--



CREATE TABLE `hr_staff_shift_history` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `staff_info_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '员工ID',
  `start` varchar(32) NOT NULL DEFAULT '' COMMENT '开始时间',
  `end` varchar(32) NOT NULL DEFAULT '' COMMENT '结束时间',
  `shift_type` varchar(32) NOT NULL DEFAULT '' COMMENT '班次类型',
  `shift_id` int(11) NOT NULL DEFAULT '0' COMMENT '班次ID',
  `shift_day` date NOT NULL DEFAULT '1970-01-01' COMMENT '班次日期',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_staff_day` (`staff_info_id`,`shift_day`)
) ENGINE=InnoDB AUTO_INCREMENT=15293409 DEFAULT CHARSET=utf8mb4 COMMENT='员工班次记录表';


--
-- Table structure for table `hr_staff_shift_preset`
--



CREATE TABLE `hr_staff_shift_preset` (
  `staff_info_id` int(11) NOT NULL COMMENT '员工id',
  `start` varchar(32) NOT NULL COMMENT '开始时间',
  `end` varchar(32) NOT NULL COMMENT '结束时间',
  `shift_type` enum('EARLY','MIDDLE','NIGHT') NOT NULL COMMENT '班次类型',
  `shift_id` int(11) NOT NULL COMMENT '班次id',
  `effective_date` date NOT NULL COMMENT '生效日期',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`staff_info_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='员工班次预设表';


--
-- Table structure for table `hr_staff_work_days`
--



CREATE TABLE `hr_staff_work_days` (
  `staff_info_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '员工工号',
  `month` varchar(10) NOT NULL DEFAULT '' COMMENT '对应月份 2019-04',
  `date_at` date NOT NULL DEFAULT '0000-00-00' COMMENT '申请日期',
  `operator` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '配置人工号',
  `remark` varchar(80) NOT NULL DEFAULT '' COMMENT '备注',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`staff_info_id`,`month`,`date_at`),
  KEY `idx_date_at` (`date_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='员工班次轮休表';


--
-- Table structure for table `hr_staffing`
--



CREATE TABLE `hr_staffing` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `month` varchar(100) DEFAULT NULL COMMENT '月份',
  `dept_id` int(11) DEFAULT NULL COMMENT '部门id',
  `store_id` varchar(20) DEFAULT NULL COMMENT '网点id',
  `job_title_id` int(11) DEFAULT NULL COMMENT '职位id',
  `count` int(11) DEFAULT NULL COMMENT '总数',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1496 DEFAULT CHARSET=utf8mb4 COMMENT='人员编制数据表';


--
-- Table structure for table `hr_state`
--



CREATE TABLE `hr_state` (
  `state_code` int(10) NOT NULL COMMENT '状态码',
  `state_value` varchar(255) DEFAULT NULL COMMENT '状态值',
  `is_enable` int(2) DEFAULT '1' COMMENT '是否启用：1=启用，2=不启用',
  PRIMARY KEY (`state_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


--
-- Table structure for table `hr_statistical_budget_dept`
--



CREATE TABLE `hr_statistical_budget_dept` (
  `budget_month` varchar(20) NOT NULL DEFAULT '' COMMENT '月份',
  `department_id` int(4) NOT NULL DEFAULT '0' COMMENT '所属部门',
  `job_title_id` int(4) NOT NULL DEFAULT '0' COMMENT '职位',
  `hc_new_budget` int(6) DEFAULT '0' COMMENT '新增人数',
  `employee_in_service` int(6) DEFAULT '0' COMMENT '在职人数',
  `hc_pending` int(6) DEFAULT '0' COMMENT '待入职人数',
  `hc_demand` int(6) DEFAULT '0' COMMENT '招聘中人数',
  `hc_request` int(6) DEFAULT '0' COMMENT '当前可提交HC人数',
  `budget` int(6) DEFAULT '0' COMMENT '预算人数',
  `hc_plan` int(6) DEFAULT '0' COMMENT '计划人数',
  `hc_p1` int(6) DEFAULT '0' COMMENT 'p1人数',
  `hc_p2` int(6) DEFAULT '0' COMMENT 'p2人数',
  `hc_p3` int(6) DEFAULT '0' COMMENT 'p3人数',
  `hc_p4` int(6) DEFAULT '0' COMMENT 'p4人数',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`budget_month`,`department_id`,`job_title_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


--
-- Table structure for table `hr_statistical_budget_store`
--



CREATE TABLE `hr_statistical_budget_store` (
  `budget_month` varchar(20) NOT NULL DEFAULT '' COMMENT '月份',
  `department_id` int(4) NOT NULL DEFAULT '0' COMMENT '所属部门',
  `store_id` varchar(30) NOT NULL DEFAULT '' COMMENT '工作地点',
  `job_title_id` int(4) NOT NULL DEFAULT '0' COMMENT '职位',
  `hc_new_budget` int(6) DEFAULT '0' COMMENT '新增人数',
  `hc_plan` int(6) DEFAULT '0' COMMENT '计划人数',
  `employee_in_service` int(6) DEFAULT '0' COMMENT '在职人数',
  `hc_pending` int(6) DEFAULT '0' COMMENT '待入职人数',
  `hc_demand` int(6) DEFAULT '0' COMMENT '招聘中人数',
  `hc_request` int(6) DEFAULT '0' COMMENT '当前可提交HC人数',
  `budget` int(6) DEFAULT '0' COMMENT '预算人数',
  `hc_p1` int(6) DEFAULT '0' COMMENT 'p1人数',
  `hc_p2` int(6) DEFAULT '0' COMMENT 'p2人数',
  `hc_p3` int(6) DEFAULT '0' COMMENT 'p3人数',
  `hc_p4` int(6) DEFAULT '0' COMMENT 'p4人数',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `manage_region` int(10) DEFAULT NULL COMMENT '管理大区',
  `manage_piece` int(10) DEFAULT NULL COMMENT '管理片区',
  PRIMARY KEY (`budget_month`,`department_id`,`job_title_id`,`store_id`),
  KEY `idx_manager_region` (`manage_region`,`manage_piece`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='预算汇总表';


--
-- Table structure for table `hr_statistical_departmentid`
--



CREATE TABLE `hr_statistical_departmentid` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `department_id` int(11) DEFAULT NULL COMMENT '部门id',
  `stat_date` date DEFAULT NULL COMMENT '统计时间',
  `hc_demandnumber` int(4) DEFAULT '0' COMMENT 'hc需求人数',
  `hc_surplusnumber` int(4) DEFAULT '0' COMMENT 'hc剩余人数',
  `hc_finishnumber` int(4) DEFAULT '0' COMMENT 'hc已招人数',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `department_id` (`department_id`) USING BTREE,
  KEY `stat_date` (`stat_date`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1998414 DEFAULT CHARSET=utf8mb4;


--
-- Table structure for table `hr_statistical_hc`
--



CREATE TABLE `hr_statistical_hc` (
  `store_id` varchar(30) NOT NULL COMMENT '网点id',
  `jd_id` int(4) NOT NULL COMMENT 'JD ID',
  `job_title_id` int(4) DEFAULT NULL COMMENT 'jd关联职位',
  `area_code` varchar(255) DEFAULT NULL COMMENT '所属大区code',
  `hc_budget` int(4) DEFAULT '0' COMMENT '预算人数',
  `employee_in_service` int(4) DEFAULT '0' COMMENT '在职人数',
  `hc_pending` int(4) DEFAULT '0' COMMENT '待入职人数',
  `hc_demand` int(4) DEFAULT '0' COMMENT '可招聘人数',
  `hc_approval` int(4) DEFAULT '0' COMMENT '审批通过的人数',
  `hc_request` int(4) DEFAULT '0' COMMENT '还需提交的人数',
  `hc_p1` int(4) DEFAULT '0' COMMENT 'p1人数',
  `hc_p2` int(4) DEFAULT '0' COMMENT 'p2人数',
  `hc_p3` int(4) DEFAULT '0' COMMENT 'p3人数',
  `hc_p4` int(4) DEFAULT '0' COMMENT 'p4人数',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `manage_region` varchar(32) DEFAULT NULL COMMENT '大区id',
  `manage_piece` varchar(32) DEFAULT NULL COMMENT '片区id',
  PRIMARY KEY (`store_id`,`jd_id`),
  UNIQUE KEY `idx_index` (`store_id`,`jd_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='网点HC统计表';


--
-- Table structure for table `hr_statistical_storeid`
--



CREATE TABLE `hr_statistical_storeid` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `store_id` varchar(30) DEFAULT NULL COMMENT '网点id',
  `area_code` varchar(255) DEFAULT NULL COMMENT '所属大区code',
  `stat_date` date DEFAULT NULL COMMENT '统计时间',
  `hc_demandnumber` int(4) DEFAULT '0' COMMENT 'hc需求人数',
  `hc_surplusnumber` int(4) DEFAULT '0' COMMENT 'hc剩余人数',
  `hc_finishnumber` int(4) DEFAULT '0' COMMENT 'hc已招人数',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `store_id` (`store_id`) USING BTREE,
  KEY `stat_date` (`stat_date`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=29350641 DEFAULT CHARSET=utf8mb4;


--
-- Table structure for table `hr_survey_question`
--



CREATE TABLE `hr_survey_question` (
  `id` int(10) NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `answer_key` int(5) DEFAULT NULL COMMENT '问题编号',
  `question_value` varchar(10) DEFAULT NULL COMMENT '答案所选值',
  `question_text` varchar(255) DEFAULT NULL COMMENT '简历内容text文本',
  `question_mobile` varchar(50) DEFAULT NULL COMMENT '文本电话',
  `mobile_id` varchar(250) DEFAULT NULL COMMENT '电话(预约面试只能记录手机号)',
  `type` int(4) DEFAULT NULL COMMENT '类型 1=摩托车,2=皮卡车,3=与你相关的问题',
  `submitter_id` int(10) DEFAULT NULL COMMENT '录入人员',
  `resume_id` int(10) DEFAULT NULL COMMENT '简历id',
  PRIMARY KEY (`id`),
  KEY `resume_id` (`resume_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2232806 DEFAULT CHARSET=utf8mb4 COMMENT='问卷调查答案表';


--
-- Table structure for table `hr_talentpoll`
--



CREATE TABLE `hr_talentpoll` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `name_zh` varchar(100) NOT NULL DEFAULT '' COMMENT '中文姓名',
  `name_en` varchar(100) NOT NULL DEFAULT '' COMMENT '英文名',
  `call_name` tinyint(1) NOT NULL DEFAULT '1' COMMENT '1:先生,2:女士,3:小姐',
  `email` varchar(100) NOT NULL DEFAULT '' COMMENT '邮箱',
  `mobile` varchar(100) NOT NULL DEFAULT '' COMMENT '手机号',
  `department` varchar(20) NOT NULL DEFAULT '' COMMENT '工作部门',
  `current_position` varchar(100) NOT NULL DEFAULT '' COMMENT '当前职位',
  `expected_position` varchar(100) NOT NULL DEFAULT '' COMMENT '期望岗位',
  `resume_source` tinyint(2) unsigned NOT NULL DEFAULT '0' COMMENT '简历来源 1：buddy，2：Facebook，3：DC，4：Referral，5：Walk-in，6：Friend，7：JobBKK，8：JobsDB，9：JobThai，10：JobTopGun，11：Linkedin，12：others',
  `current_salary` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '当前薪水',
  `expected_salary` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '期望薪水',
  `idcard` varchar(100) NOT NULL DEFAULT '' COMMENT '身份证号',
  `is_can_speak_chinese` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '1:会，2：不会',
  `is_have_work_permit` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否有工作签证 1:有，2：无',
  `resume_url` varchar(500) NOT NULL DEFAULT '' COMMENT '简历附件 url',
  `communication_status` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '沟通状态 1：未沟通，2：已沟通',
  `candidate_status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '候选人状态 1：待筛选，2：已通过，3：已淘汰',
  `adder_hr_id` int(8) unsigned NOT NULL DEFAULT '0' COMMENT '添加候选人hr工号',
  `adder_hr_name` varchar(100) NOT NULL DEFAULT '' COMMENT '添加候选人hr姓名',
  `communication_hr_id` mediumint(8) unsigned NOT NULL DEFAULT '0' COMMENT '最后提交沟通记录的操作者工号',
  `communication_hr_name` varchar(100) NOT NULL DEFAULT '' COMMENT '最后提交沟通记录的操作者姓名',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '数据添加时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '数据更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uidx_mobile_email` (`mobile`,`email`) USING BTREE,
  UNIQUE KEY `uidx_email_mobile` (`email`,`mobile`) USING BTREE,
  KEY `idx_name_th` (`name_zh`) USING BTREE,
  KEY `idx_name_en` (`name_en`) USING BTREE,
  KEY `idx_update` (`updated_at`) USING BTREE,
  KEY `idx_resume_source` (`resume_source`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COMMENT='win_hr 人才库表';


--
-- Table structure for table `hr_talentpoll_communication_comment`
--



CREATE TABLE `hr_talentpoll_communication_comment` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '评论/沟通 记录ID',
  `talentpoll_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '候选人表talentpoll id 主键',
  `type` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '1:沟通，2：评论',
  `content` varchar(500) NOT NULL DEFAULT '' COMMENT '沟通内容或评论内容',
  `submit_staff_id` int(8) unsigned NOT NULL DEFAULT '0' COMMENT '提交人工号',
  `submit_staff_name` varchar(100) NOT NULL DEFAULT '' COMMENT '提交人姓名',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_talentpoll_id_type` (`talentpoll_id`,`type`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='沟通/评论数据表';


--
-- Table structure for table `hr_talentpoll_operation_log`
--



CREATE TABLE `hr_talentpoll_operation_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '评论/沟通 记录ID',
  `talentpoll_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '候选人表talentpoll id 主键',
  `operation_key` varchar(500) NOT NULL DEFAULT '' COMMENT '操作内容翻译key',
  `submit_staff_id` int(8) unsigned NOT NULL DEFAULT '0' COMMENT '提交人工号',
  `submit_staff_name` varchar(100) NOT NULL DEFAULT '' COMMENT '提交人姓名',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_talentpoll_id_type` (`talentpoll_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COMMENT='人才库简历操作日志表';


--
-- Table structure for table `hr_training`
--



CREATE TABLE `hr_training` (
  `train_id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `resume_id` int(11) DEFAULT NULL COMMENT '简历id',
  `hc_id` int(11) DEFAULT NULL COMMENT '⽤⼈计划id',
  `train_date` date DEFAULT NULL COMMENT '培训时间',
  `train_place` varchar(32) DEFAULT NULL COMMENT '培训地点',
  `status` tinyint(1) DEFAULT NULL COMMENT '状态：1待确认 2待培训 3培训\n中 4培训通过 5未通过',
  `fail_reason` tinyint(1) DEFAULT NULL COMMENT '未通过原因：1未参加 2\n未通过',
  `remark` text COMMENT '备注',
  `deleted` tinyint(1) DEFAULT '0' COMMENT '删除状态 0 未删除 1已删除',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`train_id`),
  KEY `idx_hc_id` (`hc_id`) USING BTREE,
  KEY `idx_resume_id` (`resume_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=38390 DEFAULT CHARSET=utf8mb4 COMMENT='新员工培训';


--
-- Table structure for table `hr_training_place`
--



CREATE TABLE `hr_training_place` (
  `place_id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `code` varchar(32) DEFAULT NULL COMMENT '编码',
  `name` varchar(255) DEFAULT NULL COMMENT '名称',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_enable` tinyint(2) NOT NULL DEFAULT '1' COMMENT '是否启用1启用0不启用',
  PRIMARY KEY (`place_id`)
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8mb4;


--
-- Table structure for table `hr_work_experience`
--



CREATE TABLE `hr_work_experience` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `company_name` varchar(500) DEFAULT NULL COMMENT '公司名称',
  `position_name` varchar(500) DEFAULT NULL COMMENT '职位',
  `salary` varchar(50) DEFAULT NULL COMMENT '薪资',
  `work_time_start` varchar(50) DEFAULT NULL COMMENT '工作起始时间',
  `work_time_end` varchar(50) DEFAULT NULL COMMENT '工作结束时间',
  `leave_reason` varchar(500) DEFAULT NULL COMMENT '离职原因',
  `witness_1` varchar(250) DEFAULT NULL COMMENT '证明人1',
  `witness_2` varchar(250) DEFAULT NULL COMMENT '证明人2',
  `witness_mobile` varchar(250) DEFAULT NULL COMMENT '证明人电话',
  `economy_ablity_id` int(10) DEFAULT NULL COMMENT '经济与能力表id',
  `resume_id` int(10) DEFAULT NULL COMMENT '简历id',
  PRIMARY KEY (`id`),
  KEY `resume_id` (`resume_id`),
  KEY `economy_ablity_id` (`economy_ablity_id`)
) ENGINE=InnoDB AUTO_INCREMENT=107801 DEFAULT CHARSET=utf8mb4 COMMENT='工作经历表';


--
-- Table structure for table `hr_workflow`
--



CREATE TABLE `hr_workflow` (
  `id` int(11) NOT NULL,
  `workflow_code` varchar(255) DEFAULT NULL COMMENT '审批流code',
  `level` varchar(255) DEFAULT NULL COMMENT '审批级别',
  `pid` varchar(11) DEFAULT NULL COMMENT '上级审批流id',
  `type` varchar(255) DEFAULT NULL COMMENT '1=网点，2=人，3=角色',
  `value` varchar(255) DEFAULT NULL COMMENT '根据type取值',
  `data` varchar(128) NOT NULL DEFAULT '' COMMENT '与审批流相关的json数据',
  `setting` text COMMENT '相关定制配置',
  `createtime` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


--
-- Table structure for table `hr_workflow_binding`
--



CREATE TABLE `hr_workflow_binding` (
  `id` int(11) NOT NULL,
  `staff_id` varchar(500) DEFAULT NULL COMMENT '员工id',
  `role_id` int(11) DEFAULT NULL COMMENT '角色id',
  `type` int(2) DEFAULT NULL COMMENT '类型，1=动态取上级，2=取staffid',
  `remark` varchar(50) DEFAULT '' COMMENT '备注',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


--
-- Table structure for table `hr_workflow_role`
--



CREATE TABLE `hr_workflow_role` (
  `id` int(11) NOT NULL,
  `role_name` varchar(255) DEFAULT NULL COMMENT '角色名称',
  `createtime` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `work_code` varchar(255) DEFAULT NULL COMMENT '审批流code',
  `type` int(2) DEFAULT NULL COMMENT '1=直接取staffid字段，2=取fle取角色',
  `module_type` tinyint(4) DEFAULT '1' COMMENT '1=win_hr.hc,2=backyard.fleet',
  `describe` text,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


--
-- Table structure for table `interior_department`
--



CREATE TABLE `interior_department` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `department_id` int(11) NOT NULL,
  `department_name` varchar(100) NOT NULL DEFAULT '',
  `province_name` varchar(50) NOT NULL DEFAULT '',
  `province_code` char(4) NOT NULL DEFAULT '',
  `city_name` varchar(50) NOT NULL DEFAULT '',
  `city_code` char(6) NOT NULL DEFAULT '',
  `district_name` varchar(50) NOT NULL DEFAULT '',
  `district_code` varchar(8) NOT NULL DEFAULT '',
  `address` varchar(255) NOT NULL DEFAULT '',
  `postal_code` char(50) NOT NULL DEFAULT '',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=92 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT;


--
-- Table structure for table `interior_goods`
--



CREATE TABLE `interior_goods` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `goods_name_zh` varchar(50) NOT NULL DEFAULT '' COMMENT '商品名称（标记并不在前端展示）',
  `goods_name_th` varchar(50) NOT NULL DEFAULT '',
  `goods_name_en` varchar(50) NOT NULL DEFAULT '',
  `goods_cate` int(11) NOT NULL DEFAULT '0' COMMENT '商品类别（具体的查看程序里枚举值）0未定义，1polo衫，2黑色T恤，3外套，4长裤、5flash homeT恤（黑）、6flash homeT恤（黄）、7flash home polo衫、8flash home外套，其他待定',
  `img_path` varchar(255) NOT NULL DEFAULT '' COMMENT '商品主图',
  `info` text COMMENT '详情描述',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '商品状态，0下线，1上线',
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '数据库默认时区时间(默认零时区)',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '数据库默认时区时间(默认零时区)',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_goodsCate` (`goods_cate`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='内部员工商城商品主表';


--
-- Table structure for table `interior_goods_sku`
--



CREATE TABLE `interior_goods_sku` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `goods_id` int(11) NOT NULL DEFAULT '0' COMMENT '商品主表ID',
  `goods_sku_code` char(8) NOT NULL DEFAULT '' COMMENT 'sku 唯一标识',
  `goods_name_en` varchar(100) NOT NULL DEFAULT '',
  `goods_name_th` varchar(100) NOT NULL DEFAULT '',
  `goods_name_zh` varchar(100) NOT NULL DEFAULT '',
  `img_path` varchar(255) NOT NULL DEFAULT '',
  `attr_1` varchar(50) NOT NULL DEFAULT '' COMMENT '商品属性名1',
  `attr_2` varchar(50) NOT NULL DEFAULT '' COMMENT '商品属性名2',
  `unit_en` varchar(50) NOT NULL DEFAULT '',
  `unit_th` varchar(50) NOT NULL DEFAULT '',
  `unit_zh` varchar(50) NOT NULL DEFAULT '',
  `unit_num` int(11) NOT NULL DEFAULT '0',
  `sale_num` int(11) NOT NULL DEFAULT '0' COMMENT '销售量',
  `surplus_num` int(11) NOT NULL DEFAULT '0' COMMENT '剩余库存数量',
  `total_num` int(11) NOT NULL DEFAULT '0' COMMENT '总库存数量（包括原始的和后再增加的）',
  `price` decimal(8,2) NOT NULL DEFAULT '0.00' COMMENT '商品单价(1泰铢/ 一元钱人民币)',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '商品状态，0下线，1上线',
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '数据库默认时区时间(默认零时区)',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '数据库默认时区时间(默认零时区)',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `unq_goodsSkuCode` (`goods_sku_code`) USING BTREE,
  KEY `idx_goodsId` (`goods_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=67 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='内部员工商城商品sku表';


--
-- Table structure for table `interior_goods_sku_log`
--



CREATE TABLE `interior_goods_sku_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `staff_id` int(11) NOT NULL DEFAULT '0' COMMENT '修改者id',
  `goods_id` int(11) NOT NULL DEFAULT '0',
  `goods_sku_id` int(11) NOT NULL DEFAULT '0',
  `from_surplus_num` int(11) NOT NULL DEFAULT '0' COMMENT '库存变更前的数量',
  `to_surplus_num` int(11) NOT NULL DEFAULT '0' COMMENT '库存变更后的数量',
  `current_ch_num` int(11) NOT NULL COMMENT '该处变量数量',
  `remark` varchar(100) NOT NULL DEFAULT '' COMMENT '备注',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '(默认零时区)',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '(默认零时区)',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_goodsSkuId` (`goods_sku_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=282110 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='内部员工商城商品sku库存变动日志表';


--
-- Table structure for table `interior_orders`
--



CREATE TABLE `interior_orders` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_code` char(20) NOT NULL DEFAULT '' COMMENT '订单号',
  `staff_id` int(11) NOT NULL DEFAULT '0' COMMENT '购买者用户ID',
  `staff_name` varchar(50) NOT NULL DEFAULT '',
  `staff_mobile` varchar(20) NOT NULL DEFAULT '',
  `staff_store_id` char(10) NOT NULL DEFAULT '0',
  `node_department_id` int(11) DEFAULT NULL COMMENT '子部门',
  `receive_province_name` varchar(50) NOT NULL DEFAULT '',
  `receive_city_name` varchar(50) NOT NULL DEFAULT '',
  `receive_district_name` char(50) CHARACTER SET utf16 NOT NULL DEFAULT '',
  `receive_address` varchar(350) NOT NULL DEFAULT '',
  `receive_postal_code` varchar(50) NOT NULL DEFAULT '',
  `pay_method` tinyint(4) NOT NULL DEFAULT '1' COMMENT '1工资抵扣',
  `pay_amount` decimal(8,2) NOT NULL DEFAULT '0.00' COMMENT '订单总金额',
  `order_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '订单状态，0未定义(已经下单，待提交)，1待发货，2预定中，3已发货（待收货），4已完成（已签收），5已取消',
  `submit_at` timestamp NULL DEFAULT NULL COMMENT '订单提交时间',
  `send_at` timestamp NULL DEFAULT NULL COMMENT '发货时间',
  `delivery_way` varchar(30) NOT NULL DEFAULT '' COMMENT '配送方式',
  `received_at` timestamp NULL DEFAULT NULL COMMENT '收货时间',
  `is_audited` tinyint(4) DEFAULT '0' COMMENT '审核状态 0未审核，1审核通过，2审核不通过',
  `audited_at` timestamp NULL DEFAULT NULL COMMENT '审核通过时间',
  `audited_desc` varchar(255) NOT NULL DEFAULT '' COMMENT '审核描述',
  `out_sn` char(15) NOT NULL DEFAULT '' COMMENT '出库单号',
  `out_status` tinyint(4) DEFAULT '1' COMMENT '出库单状态，1成功，2失败',
  `fail_num` int(11) DEFAULT '0' COMMENT '出库单失败重试次数',
  `remark` varchar(255) DEFAULT '' COMMENT '备注',
  `node_sn` varchar(255) DEFAULT NULL COMMENT '网点id或者pc_code(下单的时候给scm)',
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '数据库默认时区时间(默认零时区)',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '数据库默认时区时间(默认零时区)',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `unq_orderCode` (`order_code`) USING BTREE,
  KEY `idx_staffId` (`staff_id`) USING BTREE,
  KEY `idx_out_status` (`out_status`)
) ENGINE=InnoDB AUTO_INCREMENT=91685 DEFAULT CHARSET=utf8mb4 COMMENT='内部员工商城商品订单商品表';


--
-- Table structure for table `interior_orders_goods_sku`
--



CREATE TABLE `interior_orders_goods_sku` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_code` char(20) NOT NULL DEFAULT '' COMMENT '订单号',
  `goods_id` int(11) NOT NULL DEFAULT '0',
  `goods_sku_id` int(11) NOT NULL DEFAULT '0',
  `goods_sku_code` char(8) NOT NULL DEFAULT '',
  `goods_name_en` varchar(255) NOT NULL DEFAULT '',
  `goods_name_th` varchar(255) NOT NULL DEFAULT '',
  `goods_name_zh` varchar(255) NOT NULL DEFAULT '',
  `img_path` varchar(255) NOT NULL DEFAULT '',
  `attr_1` varchar(50) NOT NULL DEFAULT '',
  `attr_2` varchar(50) NOT NULL DEFAULT '',
  `unit_en` varchar(50) NOT NULL DEFAULT '',
  `unit_th` varchar(50) NOT NULL DEFAULT '',
  `unit_zh` varchar(50) NOT NULL DEFAULT '',
  `unit_num` int(11) NOT NULL DEFAULT '0',
  `buy_price` decimal(8,2) NOT NULL DEFAULT '0.00' COMMENT '购买时价格单价',
  `buy_num` int(11) NOT NULL DEFAULT '0' COMMENT '购买数量',
  `pay_amount` decimal(8,2) NOT NULL DEFAULT '0.00' COMMENT '购买总金额',
  `current_sku_pre_sale` tinyint(4) NOT NULL DEFAULT '0' COMMENT '当前sku商品是否是预售的（库存不足时下的单）',
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '(默认零时区)',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '(默认零时区)',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `unq_orderCode_goodsSkuId` (`order_code`,`goods_sku_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=131577 DEFAULT CHARSET=utf8mb4 COMMENT='内部员工商城商品订单商品记录表';


--
-- Table structure for table `interior_staffs_shopping_cart`
--



CREATE TABLE `interior_staffs_shopping_cart` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `staff_id` int(11) NOT NULL DEFAULT '0',
  `goods_sku_id` int(11) NOT NULL DEFAULT '0',
  `buy_num` int(11) NOT NULL DEFAULT '0' COMMENT '加入购物车数量',
  `created_price` decimal(8,2) NOT NULL DEFAULT '0.00' COMMENT '加入购物车时价格',
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '(默认零时区)',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '(默认零时区)',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `unq_staffId_goodsSkuId` (`staff_id`,`goods_sku_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=24291 DEFAULT CHARSET=utf8mb4 COMMENT='内部员工商城商品购物车表';


--
-- Table structure for table `inventory`
--



CREATE TABLE `inventory` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `batch_no` varchar(20) DEFAULT NULL COMMENT '批次号',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `store_id` varchar(10) DEFAULT NULL COMMENT '网点ID',
  `category` int(3) DEFAULT NULL COMMENT '网点类型',
  `store_manager_id` int(6) DEFAULT NULL COMMENT '网点负责人id',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_batch_no` (`batch_no`,`store_id`) USING BTREE COMMENT '批次索引'
) ENGINE=InnoDB AUTO_INCREMENT=565 DEFAULT CHARSET=utf8mb4;


--
-- Table structure for table `inventory_asset`
--



CREATE TABLE `inventory_asset` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `pid` int(11) NOT NULL,
  `asset_id` int(4) NOT NULL COMMENT '财产ID',
  `available_cnt` int(8) DEFAULT '0' COMMENT '可用数量',
  `unavailable_cnt` int(8) DEFAULT '0' COMMENT '不可用数量',
  `total_cnt` int(8) DEFAULT '0' COMMENT '总数',
  `last_operator` int(6) DEFAULT NULL COMMENT '最后操作人id',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_pid` (`pid`,`asset_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=28201 DEFAULT CHARSET=utf8mb4;


--
-- Table structure for table `inventory_asset_item`
--



CREATE TABLE `inventory_asset_item` (
  `asset_id` int(4) NOT NULL COMMENT '资产id',
  `name` varchar(40) DEFAULT NULL COMMENT '资产名',
  `status` tinyint(3) DEFAULT '1' COMMENT '盘点状态0-不盘点 1-盘点 ',
  `sort` float(4,1) DEFAULT NULL COMMENT '排序',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`asset_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='盘点资产盘点状态表';


--
-- Table structure for table `inventory_base`
--



CREATE TABLE `inventory_base` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `asset_id` int(10) NOT NULL,
  `store_id` varchar(10) CHARACTER SET utf8 DEFAULT NULL COMMENT '网点编码',
  `asset_name` varchar(255) DEFAULT '' COMMENT '资产名称',
  `reference_cnt` int(10) DEFAULT '0' COMMENT '参考数量',
  `created` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=223 DEFAULT CHARSET=utf8mb4;


--
-- Table structure for table `job_transfer`
--



CREATE TABLE `job_transfer` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `serial_no` varchar(255) DEFAULT NULL COMMENT '编号',
  `staff_id` int(11) DEFAULT NULL COMMENT '员工工号',
  `hc_id` int(11) DEFAULT NULL COMMENT 'hcID；win-hr系统里的hc',
  `workflow_role_name` varchar(255) DEFAULT NULL COMMENT '审批流角色',
  `state` tinyint(2) DEFAULT '1' COMMENT '转岗状态；1=待转岗，2=未转岗，3=已转岗',
  `type` tinyint(2) DEFAULT NULL COMMENT '转岗类型；1=部门内转岗',
  `current_department_id` int(11) DEFAULT NULL COMMENT '当前部门id',
  `current_store_id` varchar(255) DEFAULT NULL COMMENT '当前网点id',
  `current_position_id` int(11) DEFAULT NULL COMMENT '当前职位id',
  `current_role_id` varchar(255) DEFAULT NULL COMMENT '当前角色id',
  `current_manager_id` int(11) DEFAULT NULL COMMENT '当前直线上级id',
  `current_company_id` int(11) DEFAULT NULL COMMENT '当前公司id',
  `current_indirect_manger_id` int(11) DEFAULT NULL COMMENT '当前虚线上级id',
  `after_department_id` int(11) DEFAULT NULL COMMENT '转岗后部门id',
  `after_store_id` varchar(255) DEFAULT NULL COMMENT '转岗后网点id',
  `after_position_id` int(11) DEFAULT NULL COMMENT '转岗后职位id',
  `after_manager_id` int(11) DEFAULT NULL COMMENT '转岗后直线上级id',
  `after_role_ids` varchar(255) DEFAULT NULL COMMENT '转岗后角色id;多个id以逗号分隔',
  `after_company_id` int(11) DEFAULT NULL COMMENT '转岗后公司id',
  `after_date` date DEFAULT NULL COMMENT '转岗日期',
  `reason` text COMMENT '转岗原因',
  `job_transfer_id` int(10) DEFAULT NULL COMMENT '转岗id',
  `job_handover_staff_id` int(11) DEFAULT NULL COMMENT '工作交接人id',
  `is_pay_adjustment` tinyint(1) DEFAULT NULL COMMENT '是否调整薪酬；1=是，2=否',
  `submitter_id` int(11) DEFAULT NULL COMMENT '提交人id',
  `deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除；0=未删除，1=已删除',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `approval_state` varchar(255) DEFAULT NULL COMMENT '审批状态；1=待审批，2=审批通过，3=审批驳回，4=撤销',
  `reject_reason` text COMMENT '驳回原因',
  `car_owner` int(4) DEFAULT NULL COMMENT '车辆归属1=本人自有车,2=公司车辆',
  `rental_car_cteated_at` datetime DEFAULT NULL COMMENT '租车开始时间',
  `before_base_salary` int(10) DEFAULT '0' COMMENT '基本工资',
  `before_exp_allowance` int(10) DEFAULT '0' COMMENT '经验津贴',
  `before_position_allowance` int(10) DEFAULT '0' COMMENT '职位津贴',
  `before_car_rental` int(10) DEFAULT '0' COMMENT '租车津贴',
  `before_trip_payment` int(10) DEFAULT '0' COMMENT '油补',
  `before_notebook_rental` int(10) DEFAULT '0' COMMENT '电脑补贴',
  `before_recommended` int(10) DEFAULT '0' COMMENT '推荐补贴',
  `before_food_allowance` int(10) DEFAULT '0' COMMENT '餐补',
  `before_dangerous_area` int(10) DEFAULT '0' COMMENT '危险地区补贴',
  `before_house_rental` int(10) DEFAULT '0' COMMENT '租房津贴',
  `before_island_allowance` int(10) DEFAULT '0' COMMENT '海岛补助',
  `before_gasoline_allowance` int(10) DEFAULT '0' COMMENT '销售油补',
  `after_base_salary` int(10) DEFAULT '0' COMMENT '基本工资',
  `after_exp_allowance` int(10) DEFAULT '0' COMMENT '经验津贴',
  `after_position_allowance` int(10) DEFAULT '0' COMMENT '职位津贴',
  `after_car_rental` int(10) DEFAULT '0' COMMENT '租车津贴',
  `after_trip_payment` int(10) DEFAULT '0' COMMENT '油补',
  `after_notebook_rental` int(10) DEFAULT '0' COMMENT '电脑补贴',
  `after_recommended` int(10) DEFAULT '0' COMMENT '推荐补贴',
  `after_food_allowance` int(10) DEFAULT '0' COMMENT '餐补',
  `after_dangerous_area` int(10) DEFAULT '0' COMMENT '危险地区补贴',
  `after_house_rental` int(10) DEFAULT '0' COMMENT '租房津贴',
  `after_island_allowance` int(10) DEFAULT '0' COMMENT '海岛补助',
  `after_gasoline_allowance` int(10) DEFAULT '0' COMMENT '销售油补',
  `reject_salary_detail` text COMMENT '驳回薪资详情',
  `hc_expiration_date` datetime DEFAULT NULL COMMENT 'hc截止日期',
  `senior_auditor` varchar(255) DEFAULT NULL COMMENT '高级审核人',
  `data_source` tinyint(3) DEFAULT '1' COMMENT '数据来源 1-by 2-oa',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_staff_id` (`staff_id`) USING BTREE,
  KEY `idx_after_date` (`after_date`) USING BTREE,
  KEY `idx_state` (`state`) USING BTREE,
  KEY `idx_job_tranfer_id` (`job_transfer_id`) USING BTREE,
  KEY `idx_submitter_id` (`submitter_id`)
) ENGINE=InnoDB AUTO_INCREMENT=5591 DEFAULT CHARSET=utf8mb4 COMMENT='转岗表';


--
-- Table structure for table `job_transfer_operate_log`
--



CREATE TABLE `job_transfer_operate_log` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `pid` int(11) DEFAULT NULL COMMENT '转岗id',
  `staf_info_id` int(10) DEFAULT NULL COMMENT '操作工号',
  `staff_name` varchar(60) DEFAULT NULL COMMENT '操作人姓名',
  `department_id` int(5) DEFAULT NULL COMMENT '部门id',
  `department_name` varchar(60) DEFAULT NULL COMMENT '部门名',
  `postion_id` int(5) DEFAULT NULL COMMENT '职位id',
  `position_name` varchar(60) DEFAULT NULL COMMENT '职位名',
  `operate_id` int(11) DEFAULT NULL COMMENT '转岗操作',
  `state` int(11) DEFAULT NULL COMMENT '转岗状态',
  `failure_reason` varchar(5000) DEFAULT NULL COMMENT '失败原因',
  `operate_content` varchar(255) DEFAULT NULL COMMENT '操作内容',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_pid` (`pid`)
) ENGINE=InnoDB AUTO_INCREMENT=4281 DEFAULT CHARSET=utf8mb4;


--
-- Table structure for table `job_transfer_prefix`
--



CREATE TABLE `job_transfer_prefix` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `serial_no` varchar(255) DEFAULT NULL COMMENT '编号',
  `staff_id` int(11) DEFAULT NULL COMMENT '员工工号',
  `state` tinyint(2) DEFAULT '1' COMMENT '转岗状态；1=待转岗，2=未转岗，3=已转岗',
  `type` tinyint(2) DEFAULT NULL COMMENT '转岗类型；1=部门内转岗',
  `current_department_id` int(11) DEFAULT NULL COMMENT '当前部门id',
  `current_store_id` varchar(255) DEFAULT NULL COMMENT '当前网点id',
  `current_position_id` int(11) DEFAULT NULL COMMENT '当前职位id',
  `current_company_id` int(11) DEFAULT NULL COMMENT '当前公司id',
  `after_department_id` int(11) DEFAULT NULL COMMENT '转岗后部门id',
  `after_store_id` varchar(255) DEFAULT NULL COMMENT '转岗后网点id',
  `after_position_id` int(11) DEFAULT NULL COMMENT '转岗后职位id',
  `after_company_id` int(11) DEFAULT NULL COMMENT '转岗后公司id',
  `reason` text COMMENT '转岗原因',
  `submitter_id` int(11) DEFAULT NULL COMMENT '提交人id',
  `job_transfer_id` int(11) DEFAULT NULL COMMENT '转岗id',
  `deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除；0=未删除，1=已删除',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `approval_state` varchar(255) DEFAULT NULL COMMENT '审批状态；1=待审批，2=审批通过，3=审批驳回，4=撤销',
  `reject_reason` text COMMENT '驳回原因',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_staff_id` (`staff_id`) USING BTREE,
  KEY `idx_state` (`state`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1848 DEFAULT CHARSET=utf8mb4 COMMENT='转岗前置表';


--
-- Table structure for table `mail_reply_from_ceo`
--



CREATE TABLE `mail_reply_from_ceo` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `mail_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '关联mail_to_ceo表主键',
  `staff_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '回复员工id',
  `staff_name` varchar(50) NOT NULL DEFAULT '' COMMENT '回复员工',
  `content` text COMMENT 'ceo站内信回复内容',
  `img_url` varchar(512) DEFAULT NULL COMMENT '图片地址：以逗号隔开',
  `file_url` varchar(512) DEFAULT NULL COMMENT '附件地址：以逗号隔开',
  `create_time` datetime DEFAULT NULL COMMENT '回复时间(已转成泰国时区)',
  `problem_no` varchar(32) NOT NULL DEFAULT '' COMMENT '员工提问的问题工单号',
  `reply_interval_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '距离上一次操作[系统回复/系统移交/员工反馈]的间隔时长，单位 秒',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '数据变更时间，0 时区',
  PRIMARY KEY (`id`),
  KEY `idx_mailid` (`mail_id`) USING BTREE,
  KEY `idx_problem_no` (`problem_no`,`staff_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB AUTO_INCREMENT=13192 DEFAULT CHARSET=utf8mb4 COMMENT='员工站内信回复表';


--
-- Table structure for table `mail_to_ceo`
--



CREATE TABLE `mail_to_ceo` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `staff_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '用户id',
  `staff_name` varchar(50) NOT NULL DEFAULT '' COMMENT '姓名',
  `content` text COMMENT 'ceo站内信内容',
  `mobile` varchar(20) NOT NULL DEFAULT '' COMMENT '手机号',
  `type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '问题类型:1=建议；2=工资问题；3=提成问题；4=投诉&举报；5=其他',
  `is_reply` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否回复 0 - 未回复 1- 已回复',
  `create_time` datetime DEFAULT NULL COMMENT '用户提交时间(已转成泰国时区)',
  `is_read` tinyint(4) DEFAULT '0' COMMENT '0=未读；1=已读',
  `img_url` varchar(512) DEFAULT NULL COMMENT '图片地址：以逗号隔开',
  `file_url` varchar(512) DEFAULT NULL COMMENT '附件地址：以逗号隔开',
  `problem_no` varchar(32) NOT NULL DEFAULT '' COMMENT '问题工单号',
  `from_ceo_reply_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '员工提问对应的平台回复id',
  `reply_interval_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '间隔时长: 员工本次反馈距最近次平台回复的间隔时长, 单位秒',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '数据变更时间, 0 时区',
  PRIMARY KEY (`id`),
  KEY `idx_isread_staffid` (`is_read`,`staff_id`) USING BTREE,
  KEY `idx_problem_no_staff` (`problem_no`,`staff_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB AUTO_INCREMENT=16917 DEFAULT CHARSET=utf8mb4 COMMENT='员工给ceo发送站内信表附件表 关联work_order_img oss_bucket_type=3';


--
-- Table structure for table `message_qa_answer_log`
--



CREATE TABLE `message_qa_answer_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `remote_message_id` varchar(32) DEFAULT NULL COMMENT '消息id',
  `question_id` varchar(32) DEFAULT NULL COMMENT '问题id',
  `staff_id` int(11) DEFAULT NULL COMMENT '用户id',
  `answer_content` varchar(255) DEFAULT NULL COMMENT '问题答案',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_r_s` (`remote_message_id`,`staff_id`)
) ENGINE=InnoDB AUTO_INCREMENT=3816706 DEFAULT CHARSET=utf8mb4;


--
-- Table structure for table `message_send_mns_log`
--



CREATE TABLE `message_send_mns_log` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `message_courier_id` varchar(32) NOT NULL COMMENT '终端消息id',
  `mns_message_id` varchar(64) NOT NULL DEFAULT '' COMMENT 'mns成功后返回的message id',
  `send_status` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '发送状态: 0 未发送; 1: 发送中; 2: 发送成功; 3: 发送失败',
  `send_count` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '发送次数',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '消息日志创建时间, 0 时区',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '消息日志更新时间, 0时区',
  PRIMARY KEY (`id`),
  KEY `idx_create_time_status` (`create_time`,`send_status`),
  KEY `idx_message_courier_id` (`message_courier_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='已读消息 - send to mns 日志';


--
-- Table structure for table `oauth_access_tokens`
--



CREATE TABLE `oauth_access_tokens` (
  `access_token` varchar(150) NOT NULL DEFAULT '' COMMENT 'token',
  `expires` timestamp NULL DEFAULT NULL COMMENT '过期时间',
  `scope` varchar(255) DEFAULT NULL COMMENT '授权scope',
  `client_id` varchar(80) NOT NULL DEFAULT '' COMMENT '客户端ID',
  `user_id` varchar(20) DEFAULT NULL,
  `revoked` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否被收回',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`access_token`),
  KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


--
-- Table structure for table `oauth_authorization_codes`
--



CREATE TABLE `oauth_authorization_codes` (
  `authorization_code` varchar(150) NOT NULL DEFAULT '',
  `expires` datetime DEFAULT NULL,
  `redirect_url` varchar(255) DEFAULT NULL,
  `scope` varchar(255) DEFAULT NULL,
  `client_id` varchar(80) NOT NULL DEFAULT '',
  `user_id` int(11) unsigned DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `revoked` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`authorization_code`),
  KEY `client_id` (`client_id`),
  KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


--
-- Table structure for table `oauth_clients`
--



CREATE TABLE `oauth_clients` (
  `client_id` varchar(80) NOT NULL,
  `name` varchar(255) DEFAULT NULL,
  `client_secret` varchar(255) DEFAULT NULL,
  `redirect_uri` varchar(255) DEFAULT NULL,
  `grant_types` varchar(80) DEFAULT NULL,
  `scope` varchar(255) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`client_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


--
-- Table structure for table `oauth_jwt`
--



CREATE TABLE `oauth_jwt` (
  `client_id` varchar(80) NOT NULL DEFAULT '',
  `subject` varchar(80) DEFAULT NULL,
  `public_key` text,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`client_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


--
-- Table structure for table `oauth_refresh_tokens`
--



CREATE TABLE `oauth_refresh_tokens` (
  `refresh_token` varchar(150) NOT NULL DEFAULT '',
  `expires` datetime DEFAULT NULL,
  `scope` varchar(255) DEFAULT NULL,
  `client_id` varchar(80) NOT NULL DEFAULT '',
  `user_id` varchar(20) DEFAULT NULL,
  `revoked` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`refresh_token`),
  KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


--
-- Table structure for table `oauth_scopes`
--



CREATE TABLE `oauth_scopes` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `scope` varchar(80) DEFAULT NULL,
  `is_default` tinyint(1) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `scope` (`scope`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4;


--
-- Table structure for table `penalty_appeal`
--



CREATE TABLE `penalty_appeal` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `staff_id` int(11) NOT NULL DEFAULT '0' COMMENT '申请人staff_id',
  `serial_no` varchar(30) NOT NULL DEFAULT '' COMMENT '编号',
  `store_id` varchar(10) NOT NULL DEFAULT '' COMMENT '申请人所在网点',
  `penalty_reason` varchar(30) NOT NULL DEFAULT '' COMMENT '处罚原因',
  `happen_date` date DEFAULT NULL COMMENT '发生日期',
  `related_info` varchar(30) NOT NULL DEFAULT '' COMMENT '关联信息',
  `penalty_money` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '申诉金额',
  `apply_remark` varchar(500) NOT NULL DEFAULT '' COMMENT '申请备注',
  `image` text,
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '审批状态 1 待审核 2 审核通过 3 驳回 4 撤销',
  `reject_reason` varchar(500) DEFAULT '' COMMENT '驳回原因',
  `process_result` varchar(30) NOT NULL DEFAULT '' COMMENT 'QAQC处理结果',
  `process_remark` varchar(500) NOT NULL DEFAULT '' COMMENT 'QAQC处理备注',
  `penalty_after_money` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '申诉后的金额',
  `approval_notice` varchar(30) NOT NULL DEFAULT '' COMMENT '审批须知',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `penalty_id` int(11) NOT NULL DEFAULT '0' COMMENT 'kit 系统处罚id，与bi系统通信用',
  PRIMARY KEY (`id`),
  KEY `idx_updated_at` (`updated_at`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=3473 DEFAULT CHARSET=utf8mb4 COMMENT='kit处罚申诉表';


--
-- Table structure for table `pp_notify_tpl`
--



CREATE TABLE `pp_notify_tpl` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '模板id',
  `type` enum('system','default') NOT NULL DEFAULT 'default',
  `tpl_name` varchar(20) NOT NULL COMMENT '模板名称',
  `tpl_type` tinyint(1) NOT NULL COMMENT '模板类型 0:邮件;1:信息;2:钉钉;3:微信;',
  `title` varchar(64) DEFAULT NULL COMMENT '标题',
  `content` text NOT NULL COMMENT '模板内容',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态 0:禁用;1:启用;',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `create_id` int(11) NOT NULL DEFAULT '0' COMMENT '创建者ID',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '最后一次编辑时间',
  `update_id` int(11) NOT NULL DEFAULT '0' COMMENT '最后一次编辑者ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8 COMMENT='通知模板';


--
-- Table structure for table `pp_task`
--



CREATE TABLE `pp_task` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `group_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '分组ID',
  `server_ids` varchar(200) NOT NULL DEFAULT '0' COMMENT '服务器id字符串，英文都好隔开',
  `task_name` varchar(50) NOT NULL DEFAULT '' COMMENT '任务名称',
  `description` varchar(200) NOT NULL DEFAULT '' COMMENT '任务描述',
  `cron_spec` varchar(100) NOT NULL DEFAULT '' COMMENT '时间表达式',
  `concurrent` tinyint(4) unsigned NOT NULL DEFAULT '0' COMMENT '是否只允许一个实例',
  `command` text NOT NULL COMMENT '命令详情',
  `timeout` smallint(6) unsigned NOT NULL DEFAULT '0' COMMENT '超时设置 s',
  `execute_times` int(11) NOT NULL DEFAULT '0' COMMENT '累计执行次数',
  `prev_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '上次执行时间',
  `is_notify` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '0-不通知，1-通知',
  `notify_type` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '0-邮件通知，1-信息通知，2-钉钉通知，3-微信通知，',
  `notify_tpl_id` int(11) NOT NULL DEFAULT '0' COMMENT '通知模板id',
  `notify_user_ids` varchar(200) NOT NULL DEFAULT '0' COMMENT '通知用户ID字符串，1,2,3',
  `status` tinyint(4) NOT NULL DEFAULT '2' COMMENT '-1删除，0停用 1启用 2审核中,3不通过',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `create_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建者ID',
  `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '最后一次编辑时间',
  `update_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '最后一次编辑者ID',
  `server_type` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '执行策略：0-同时执行，1-轮询执行',
  `server_id` int(11) DEFAULT NULL COMMENT '服务器id',
  PRIMARY KEY (`id`),
  KEY `idx_group_id` (`group_id`)
) ENGINE=InnoDB AUTO_INCREMENT=61 DEFAULT CHARSET=utf8;


--
-- Table structure for table `pp_task_ban`
--



CREATE TABLE `pp_task_ban` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `code` varchar(64) NOT NULL DEFAULT '0' COMMENT '命令',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  `status` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '0-正常，1-删除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COMMENT='权限和角色关系表';


--
-- Table structure for table `pp_task_group`
--



CREATE TABLE `pp_task_group` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `group_name` varchar(50) NOT NULL DEFAULT '' COMMENT '组名',
  `description` varchar(255) NOT NULL DEFAULT '' COMMENT '说明',
  `create_id` int(11) NOT NULL DEFAULT '0' COMMENT '用户ID',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '修改者Id',
  `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  `status` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '状态：1-正常，0-删除',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`create_id`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8;


--
-- Table structure for table `pp_task_log`
--



CREATE TABLE `pp_task_log` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `task_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '任务ID',
  `server_id` int(11) NOT NULL DEFAULT '-1' COMMENT '服务器ID，-1，异常',
  `server_name` varchar(60) NOT NULL DEFAULT '""' COMMENT '服务器名称',
  `output` mediumtext NOT NULL COMMENT '任务输出',
  `error` text NOT NULL COMMENT '错误信息',
  `status` tinyint(4) NOT NULL COMMENT '状态',
  `process_time` int(11) NOT NULL DEFAULT '0' COMMENT '消耗时间/毫秒',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_task_id` (`task_id`,`create_time`)
) ENGINE=InnoDB AUTO_INCREMENT=59942 DEFAULT CHARSET=utf8;


--
-- Table structure for table `pp_task_server`
--



CREATE TABLE `pp_task_server` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `group_id` int(64) NOT NULL,
  `connection_type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '连接类型 0:SSH;1:Telnet;',
  `server_name` varchar(64) NOT NULL DEFAULT '0' COMMENT '服务器名称',
  `server_account` varchar(32) NOT NULL DEFAULT 'root' COMMENT '账户名称',
  `server_outer_ip` varchar(20) NOT NULL DEFAULT '0' COMMENT '外网IP',
  `server_ip` varchar(20) NOT NULL DEFAULT '0' COMMENT '服务器内网IP',
  `port` int(4) unsigned NOT NULL DEFAULT '22' COMMENT '服务器端口',
  `password` varchar(64) NOT NULL DEFAULT '0' COMMENT '服务器密码',
  `private_key_src` varchar(128) NOT NULL DEFAULT '0' COMMENT '私钥文件地址',
  `public_key_src` varchar(128) NOT NULL DEFAULT '0' COMMENT '公钥地址',
  `type` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '登录类型：0-密码登录，1-私钥登录',
  `detail` varchar(255) NOT NULL DEFAULT '0' COMMENT '备注',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  `status` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '状态：0-正常，1-删除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COMMENT='服务器列表';


--
-- Table structure for table `pp_task_server_group`
--



CREATE TABLE `pp_task_server_group` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `group_name` varchar(50) NOT NULL DEFAULT '0' COMMENT '组名',
  `description` varchar(255) NOT NULL DEFAULT '' COMMENT '说明',
  `status` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '1-正常，0-删除',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  `create_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '用户ID',
  `update_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新id',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`create_id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8;


--
-- Table structure for table `pp_uc_admin`
--



CREATE TABLE `pp_uc_admin` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `login_name` varchar(20) NOT NULL DEFAULT '' COMMENT '用户名',
  `real_name` varchar(32) NOT NULL DEFAULT '0' COMMENT '真实姓名',
  `password` char(32) NOT NULL DEFAULT '' COMMENT '密码',
  `role_ids` varchar(255) NOT NULL DEFAULT '0' COMMENT '角色id字符串，如：2,3,4',
  `phone` varchar(20) NOT NULL DEFAULT '0' COMMENT '手机号码',
  `email` varchar(50) NOT NULL DEFAULT '' COMMENT '邮箱',
  `dingtalk` varchar(64) DEFAULT NULL COMMENT '钉钉',
  `wechat` varchar(64) DEFAULT NULL COMMENT '微信',
  `salt` char(10) NOT NULL DEFAULT '' COMMENT '密码盐',
  `last_login` int(11) NOT NULL DEFAULT '0' COMMENT '最后登录时间',
  `last_ip` char(15) NOT NULL DEFAULT '' COMMENT '最后登录IP',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态，1-正常 0禁用',
  `create_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建者ID',
  `update_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '修改者ID',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_user_name` (`login_name`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COMMENT='管理员表';


--
-- Table structure for table `pp_uc_auth`
--



CREATE TABLE `pp_uc_auth` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `pid` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '上级ID，0为顶级',
  `auth_name` varchar(64) NOT NULL DEFAULT '0' COMMENT '权限名称',
  `auth_url` varchar(255) NOT NULL DEFAULT '0' COMMENT 'URL地址',
  `sort` int(1) unsigned NOT NULL DEFAULT '999' COMMENT '排序，越小越前',
  `icon` varchar(255) NOT NULL,
  `is_show` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否显示，0-隐藏，1-显示',
  `user_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '操作者ID',
  `create_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建者ID',
  `update_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '修改者ID',
  `status` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '状态，1-正常，0-删除',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=66 DEFAULT CHARSET=utf8mb4 COMMENT='权限因子';


--
-- Table structure for table `pp_uc_role`
--



CREATE TABLE `pp_uc_role` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `role_name` varchar(32) NOT NULL DEFAULT '0' COMMENT '角色名称',
  `detail` varchar(255) NOT NULL DEFAULT '0' COMMENT '备注',
  `server_group_ids` varchar(255) NOT NULL DEFAULT '0' COMMENT '服务器分组权限ids,1,2,3',
  `task_group_ids` varchar(255) NOT NULL DEFAULT '0' COMMENT '任务分组权限ids ,1,2,32',
  `create_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建者ID',
  `update_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '修改这ID',
  `status` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '状态1-正常，0-删除',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '添加时间',
  `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COMMENT='角色表';


--
-- Table structure for table `pp_uc_role_auth`
--



CREATE TABLE `pp_uc_role_auth` (
  `role_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '角色ID',
  `auth_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '权限ID',
  PRIMARY KEY (`role_id`,`auth_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='权限和角色关系表';


--
-- Table structure for table `reimbursement_fuel`
--



CREATE TABLE `reimbursement_fuel` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `staff_info_id` int(10) unsigned DEFAULT NULL COMMENT '员工工号',
  `r_no` varchar(20) NOT NULL DEFAULT '' COMMENT '关联oa报销单订单编号',
  `f_no` varchar(30) NOT NULL DEFAULT '' COMMENT '用车记录编号',
  `car_no` varchar(20) NOT NULL DEFAULT '' COMMENT '车牌号',
  `car_owner` varchar(30) NOT NULL DEFAULT '' COMMENT '车主姓名',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `is_delete` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否删除 0 未删除 1 已删除',
  PRIMARY KEY (`id`),
  KEY `idx_staff_info_id` (`staff_info_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='by申请报销关联用车记录表 oa 撤销驳回操作 会删除记录';


--
-- Table structure for table `report_audit`
--



CREATE TABLE `report_audit` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `report_type` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '申请类型(举报类型）：1-调查,2-发警告信',
  `reason` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '举报（违规）原因：1-迟到早退，2-连续旷工3天，3-贪污，4-工作时间或工作地点饮酒，5-吸毒，6-违反公司的命令/通知/规则/纪律/规定，7-通过社会媒体污蔑公司，8-公司设备私人使用、下载竞争对手的APP 如；Lalamove、Grab，9-滥用职权，10-玩忽职守，11-吵架、打架',
  `remark` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '事情描述',
  `reject_reason` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '拒绝理由',
  `submitter_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '申请人id',
  `event_date` date NOT NULL COMMENT '违规事件发生日期（Y-m-d格式)',
  `report_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '被举报人id',
  `status` tinyint(3) NOT NULL DEFAULT '1' COMMENT '审批状态 1-待审批 2-同意 3 驳回 4 撤销 5 超时关闭',
  `warning_status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '警告处理状态：1-未处理，2-已警告，3-不警告',
  `final_approver` int(10) NOT NULL DEFAULT '0' COMMENT '最终审批人id',
  `final_approval_time` datetime DEFAULT NULL COMMENT '最终审批时间',
  `wf_role` varchar(20) NOT NULL DEFAULT '' COMMENT '配置角色',
  `serial_no` varchar(32) NOT NULL DEFAULT '' COMMENT '序列号',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `submitter_id` (`submitter_id`),
  KEY `final_approver` (`final_approver`),
  KEY `report_id` (`report_id`),
  KEY `reason` (`reason`)
) ENGINE=InnoDB AUTO_INCREMENT=7423 DEFAULT CHARSET=utf8mb4 COMMENT='举报申请';


--
-- Table structure for table `report_audit_desc`
--



CREATE TABLE `report_audit_desc` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `report_audit_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '举报申请ID',
  `staff_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '补充人工号',
  `staff_name` varchar(100) NOT NULL DEFAULT '' COMMENT '补充人姓名',
  `content_desc` varchar(1000) NOT NULL DEFAULT '' COMMENT '事情描述-补充',
  `img_list` varchar(2000) NOT NULL DEFAULT '' COMMENT '图片补充说明(多张图片json存储）',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_auditid_staffid` (`report_audit_id`,`staff_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=11087 DEFAULT CHARSET=utf8mb4 COMMENT='举报申请 事情描述-补充信息表';


--
-- Table structure for table `report_audit_log`
--



CREATE TABLE `report_audit_log` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `report_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '举报ID',
  `operator_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '操作人ID',
  `warning_status` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '处理结果，2-已警告，3-不警告',
  `warning_type` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '警告类型 1 口述警告 2 书面警告 3 末次书面警告',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `remark` varchar(500) NOT NULL DEFAULT '' COMMENT '原因',
  PRIMARY KEY (`id`),
  KEY `idx_report_id` (`report_id`)
) ENGINE=InnoDB AUTO_INCREMENT=4677 DEFAULT CHARSET=utf8mb4 COMMENT='举报操作记录';


--
-- Table structure for table `resume_out_log`
--



CREATE TABLE `resume_out_log` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `interview_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '面试ID',
  `resume_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '简历ID',
  `hc_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT 'hcID',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_resume` (`resume_id`),
  KEY `idx_interview` (`interview_id`)
) ENGINE=InnoDB AUTO_INCREMENT=304 DEFAULT CHARSET=utf8mb4 COMMENT='简历淘汰记录表';


--
-- Table structure for table `salary_approve`
--



CREATE TABLE `salary_approve` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '薪资审批主键ID',
  `resume_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '简历ID',
  `serial_no` varchar(30) NOT NULL DEFAULT '' COMMENT '审批编号',
  `submitter_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '提交人ID',
  `job_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '职位ID',
  `workflow_role` varchar(30) NOT NULL DEFAULT '' COMMENT '审批流',
  `basic_salary` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '基本薪资',
  `trial_salary` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '通过试用期后薪资',
  `renting` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '租房津贴',
  `status` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '审批状态1 待审批 2 已同意 3 已驳回 4 已撤销 5 已超时 0-不需要审批',
  `cancel_reason` varchar(250) NOT NULL DEFAULT '' COMMENT '撤销原因',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_resume_id` (`resume_id`),
  KEY `idx_submitter_id` (`submitter_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1609 DEFAULT CHARSET=utf8mb4 COMMENT='薪资申请表';


--
-- Table structure for table `setting_env`
--



CREATE TABLE `setting_env` (
  `code` varchar(32) NOT NULL COMMENT '各种业务对应编码 主键',
  `set_val` text NOT NULL COMMENT '各种业务定制配置',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `remark` varchar(48) NOT NULL DEFAULT '' COMMENT '配置说明备注',
  PRIMARY KEY (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='各种定制配置';


--
-- Table structure for table `staff_apply`
--



CREATE TABLE `staff_apply` (
  `apply_id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '审批ID',
  `staff_info_id` int(10) unsigned DEFAULT NULL COMMENT '员工工号',
  `apply_category` tinyint(3) unsigned DEFAULT '0' COMMENT '1- 请假  2-OT 3- LH  4- 补卡',
  `apply_type` tinyint(4) DEFAULT '0' COMMENT '根据category字段 及联的二级分类',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `duration` float(2,0) DEFAULT NULL COMMENT '时长',
  `lh_plate_number` varchar(50) DEFAULT NULL COMMENT 'LH车牌号',
  `leave_start_time` datetime DEFAULT NULL COMMENT '请假开始时间',
  `leave_start_type` tinyint(1) DEFAULT '1' COMMENT '请假开始时间类型 1上午 2下午',
  `leave_end_time` datetime DEFAULT NULL COMMENT '请假结束时间',
  `leave_end_type` tinyint(1) DEFAULT '1' COMMENT '请假结束时间类型 1上午 2下午',
  `leave_day` decimal(10,1) DEFAULT NULL COMMENT '请假天数',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `reason` text COMMENT '原因',
  `image_path` text COMMENT '图片路径 逗号分隔',
  `version` tinyint(4) NOT NULL DEFAULT '1' COMMENT '1-第一版补申请数据 4.16-4.24 2-第二版补申请 2019年4月16日到5月10日',
  `date_at` date DEFAULT NULL COMMENT '申请日期',
  PRIMARY KEY (`apply_id`),
  KEY `staff_info_id` (`staff_info_id`)
) ENGINE=InnoDB AUTO_INCREMENT=9542 DEFAULT CHARSET=utf8mb4 COMMENT='员工补添申请记录表';


--
-- Table structure for table `staff_attendance_source_data`
--



CREATE TABLE `staff_attendance_source_data` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `staff_id` int(10) unsigned DEFAULT NULL COMMENT '员工工号',
  `date_at` date DEFAULT NULL COMMENT '覆盖数据日期',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `source_data` text COMMENT '海康数据源 json',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  PRIMARY KEY (`id`),
  KEY `idx_date_at` (`date_at`)
) ENGINE=InnoDB AUTO_INCREMENT=1401169 DEFAULT CHARSET=utf8mb4 COMMENT='海康数源数据和格式化后数据 日志表';


--
-- Table structure for table `staff_audit`
--



CREATE TABLE `staff_audit` (
  `audit_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '审批ID',
  `serial_no` varchar(32) DEFAULT NULL COMMENT '序列号',
  `staff_info_id` int(10) unsigned DEFAULT NULL COMMENT '员工工号',
  `manager_id` int(10) unsigned DEFAULT NULL COMMENT '主管ID',
  `audit_type` tinyint(1) unsigned DEFAULT '0' COMMENT '审核类型 1补卡 2请假 3申请LH费',
  `attendance_type` int(1) DEFAULT '1' COMMENT '考勤类型 1上班打卡 2下班打卡',
  `leave_type` int(10) DEFAULT '1' COMMENT '请假类型',
  `approver_id` int(10) DEFAULT NULL COMMENT '审批人',
  `approver_name` varchar(255) DEFAULT NULL COMMENT '审批人名称',
  `attendance_date` date DEFAULT NULL COMMENT '考勤日期',
  `reissue_card_date` datetime DEFAULT NULL COMMENT '补卡时间',
  `leave_start_time` datetime DEFAULT NULL COMMENT '请假开始时间',
  `lh_date` date DEFAULT NULL COMMENT 'LH日期',
  `lh_time` varchar(100) DEFAULT NULL COMMENT 'LH时间',
  `lh_plate_number` varchar(50) DEFAULT NULL COMMENT 'LH车牌号',
  `leave_start_type` tinyint(1) DEFAULT '1' COMMENT '请假开始时间类型 1上午 2下午',
  `leave_end_time` datetime DEFAULT NULL COMMENT '请假结束时间',
  `leave_end_type` tinyint(1) DEFAULT '1' COMMENT '请假结束时间类型 1上午 2下午',
  `leave_day` decimal(10,1) DEFAULT NULL COMMENT '请假天数',
  `status` tinyint(1) DEFAULT '1' COMMENT '补卡状态 1 申请中 2 审核通过 3 驳回 4 撤销',
  `audit_reason` text COMMENT '申请原因',
  `reject_reason` text COMMENT '驳回原因',
  `template_comment` varchar(512) NOT NULL DEFAULT '' COMMENT '对应不同请假类型 需要保存存在差异的定制字段 json map 保存为key 字段翻译key  value 定制value',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `workflow_role` varchar(50) NOT NULL DEFAULT '' COMMENT '审批role',
  PRIMARY KEY (`audit_id`),
  KEY `status` (`status`),
  KEY `attendance` (`attendance_date`),
  KEY `lindex_evel` (`leave_start_time`,`leave_end_time`,`leave_start_type`,`leave_end_type`),
  KEY `idx_leave_end_time` (`leave_end_time`),
  KEY `idx_audit_type` (`audit_type`,`created_at`) USING BTREE,
  KEY `staff_info_id` (`staff_info_id`,`audit_type`,`status`,`lh_date`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3315587 DEFAULT CHARSET=utf8mb4 COMMENT='员工审批表';


--
-- Table structure for table `staff_audit_approval`
--



CREATE TABLE `staff_audit_approval` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `type` int(3) DEFAULT NULL COMMENT '审批类型',
  `level` int(10) unsigned NOT NULL COMMENT '级别',
  `audit_id` int(10) DEFAULT NULL COMMENT '审批id',
  `status` int(10) DEFAULT NULL COMMENT '审批状态',
  `confirm` varchar(500) DEFAULT '' COMMENT '确认内容',
  `is_shown` tinyint(3) DEFAULT '1' COMMENT 'ms是否可见 1-不可见 2-可见',
  `submitter_id` varchar(11) DEFAULT NULL COMMENT '提交人id',
  `updatetime` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
  `createtime` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  `staff_ids` varchar(500) DEFAULT NULL COMMENT '审批人id',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `audit_id_index` (`audit_id`,`type`,`level`) USING BTREE,
  KEY `audit_id` (`audit_id`) USING BTREE,
  KEY `updatetime` (`updatetime`) USING BTREE,
  KEY `createtime` (`createtime`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1041096 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='backyard我的审批表';


--
-- Table structure for table `staff_audit_images`
--



CREATE TABLE `staff_audit_images` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '审批图片ID',
  `audit_id` bigint(10) unsigned DEFAULT NULL COMMENT '审核ID',
  `image_path` varchar(255) DEFAULT NULL COMMENT '图片路径',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_audit_id` (`audit_id`)
) ENGINE=InnoDB AUTO_INCREMENT=333425 DEFAULT CHARSET=utf8mb4 COMMENT='审批图片表';


--
-- Table structure for table `staff_audit_leave_split`
--



CREATE TABLE `staff_audit_leave_split` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `audit_id` bigint(20) unsigned NOT NULL COMMENT '关联staff_audit 请假id',
  `staff_info_id` int(10) unsigned DEFAULT NULL COMMENT '员工工号',
  `date_at` date DEFAULT NULL COMMENT '请假日期',
  `type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '请假日期类型 0-一天 1-上午 2-下午',
  `year_at` smallint(6) NOT NULL DEFAULT '1970' COMMENT '请假 所在年 主要针对年假三月失效规则制定',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_audit_id` (`audit_id`),
  KEY `idx_staff_info_id` (`staff_info_id`),
  KEY `idx_date_at` (`date_at`)
) ENGINE=InnoDB AUTO_INCREMENT=2807720 DEFAULT CHARSET=utf8mb4 COMMENT='员工请假拆分成天';


--
-- Table structure for table `staff_audit_reissue_for_business`
--



CREATE TABLE `staff_audit_reissue_for_business` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '审批ID',
  `serial_no` varchar(32) DEFAULT NULL COMMENT '序列号',
  `staff_info_id` int(10) unsigned DEFAULT NULL COMMENT '员工工号',
  `attendance_date` date DEFAULT NULL COMMENT '考勤日期',
  `start_shift` varchar(32) NOT NULL DEFAULT '' COMMENT '班次开始时间',
  `end_shift` varchar(32) NOT NULL DEFAULT '' COMMENT '班次结束时间',
  `working_day` tinyint(3) unsigned DEFAULT NULL COMMENT '是否是工作日 0:否；1:是',
  `start_time` datetime DEFAULT NULL COMMENT '补卡时间',
  `start_lat` decimal(11,8) DEFAULT NULL COMMENT '上班打卡位置的纬度',
  `start_lng` decimal(11,8) DEFAULT NULL COMMENT '上班打卡位置经度',
  `started_path` varchar(100) DEFAULT NULL COMMENT '上班考勤照片',
  `started_bucket` varchar(63) DEFAULT NULL COMMENT '上班考勤照片所属bucket',
  `start_reason` varchar(512) NOT NULL DEFAULT '' COMMENT '上班申请原因',
  `end_time` datetime DEFAULT NULL COMMENT '补卡时间',
  `end_lat` decimal(11,8) DEFAULT NULL COMMENT '上班打卡位置的纬度',
  `end_lng` decimal(11,8) DEFAULT NULL COMMENT '上班打卡位置经度',
  `end_path` varchar(100) DEFAULT NULL COMMENT '下班考勤照片',
  `end_bucket` varchar(63) DEFAULT NULL COMMENT '下班考勤照片所属bucket',
  `end_reason` varchar(512) NOT NULL DEFAULT '' COMMENT '下班申请原因',
  `reject_reason` text COMMENT '驳回原因',
  `status` tinyint(1) DEFAULT '1' COMMENT '补卡状态 1 申请中 2 审核通过 3 驳回 4 撤销',
  `task_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '跑超时任务字段 因为该数据表创建时间非正式开始审批的创建时间',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `staff_info_id` (`staff_info_id`),
  KEY `attendance` (`attendance_date`),
  KEY `idx_task_time` (`task_time`)
) ENGINE=InnoDB AUTO_INCREMENT=54448 DEFAULT CHARSET=utf8mb4 COMMENT='出差打卡申请表';


--
-- Table structure for table `staff_audit_tool_log`
--



CREATE TABLE `staff_audit_tool_log` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '审批ID',
  `staff_id` int(10) unsigned DEFAULT NULL COMMENT '员工工号',
  `type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '分类 1补卡 2请假 3申请LH费 加班类型 5=工作日，6=节假日加班，7=晚班 8-节假日正常上班',
  `original_type` tinyint(3) unsigned DEFAULT '0' COMMENT '原始状态 1 待审核 2 审核通过 3 驳回',
  `to_status_type` tinyint(3) unsigned DEFAULT '0' COMMENT '修改后状态 1 待审核 2 审核通过 3 驳回 4撤销',
  `original_id` int(11) NOT NULL DEFAULT '0' COMMENT '关联id type 1234-staff_audit 5678-hr_overtime',
  `operator` int(11) DEFAULT '0' COMMENT '操作员工id',
  `operator_name` varchar(80) DEFAULT '' COMMENT '操作人名称',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  PRIMARY KEY (`id`),
  KEY `original_id_index` (`original_id`)
) ENGINE=InnoDB AUTO_INCREMENT=6151830 DEFAULT CHARSET=utf8mb4 COMMENT='员工申请 撤销工具日志表';


--
-- Table structure for table `staff_audit_union`
--



CREATE TABLE `staff_audit_union` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `id_union` varchar(16) NOT NULL COMMENT '数据对应id',
  `staff_id_union` int(10) DEFAULT NULL COMMENT '用户id',
  `type_union` int(3) DEFAULT NULL COMMENT '类型 1补卡 2请假 3LH 4OT 6hc审批7hc申请 9物料 10出差',
  `status_union` int(3) DEFAULT NULL COMMENT '状态集 101申请中 102审核通过 103驳回 104撤销 105超时关闭 201待审批 202招聘中 203已完成 204已作废 205已拒绝 206已同意 207待审批',
  `store_id` varchar(10) NOT NULL COMMENT '网点id',
  `data` text COMMENT '数据集',
  `summary` text COMMENT '申请概要 json数据',
  `table` varchar(64) DEFAULT NULL COMMENT '表名',
  `created_at` datetime(3) DEFAULT NULL COMMENT '创建时间',
  `origin_id` int(11) DEFAULT '0' COMMENT '原始id',
  `approval_id` int(10) DEFAULT '0' COMMENT '审批人id',
  PRIMARY KEY (`id`),
  KEY `idx_created_at` (`created_at`) USING BTREE,
  KEY `idx_id_union` (`id_union`) USING BTREE,
  KEY `idx_union` (`type_union`,`status_union`,`staff_id_union`) USING BTREE,
  KEY `idx_approval_id` (`approval_id`,`type_union`,`created_at`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=30344906 DEFAULT CHARSET=utf8mb4;


--
-- Table structure for table `staff_audit_union_2019_03`
--



CREATE TABLE `staff_audit_union_2019_03` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `id_union` varchar(16) NOT NULL COMMENT '数据对应id',
  `staff_id_union` int(10) DEFAULT NULL COMMENT '用户id',
  `type_union` int(3) DEFAULT NULL COMMENT '类型 1补卡 2请假 3LH 4OT 6hc审批7hc申请 9物料 10出差',
  `status_union` int(3) DEFAULT NULL COMMENT '状态集 101申请中 102审核通过 103驳回 104撤销 105超时关闭 201待审批 202招聘中 203已完成 204已作废 205已拒绝 206已同意 207待审批',
  `store_id` varchar(10) NOT NULL COMMENT '网点id',
  `data` text COMMENT '数据集',
  `summary` text COMMENT '申请概要 json数据',
  `table` varchar(20) DEFAULT NULL COMMENT '表名',
  `created_at` datetime(3) DEFAULT NULL COMMENT '创建时间',
  `origin_id` int(11) DEFAULT '0' COMMENT '原始id',
  `approval_id` int(10) DEFAULT '0' COMMENT '审批人id',
  PRIMARY KEY (`id`),
  KEY `idx_created_at` (`created_at`) USING BTREE,
  KEY `idx_id_union` (`id_union`) USING BTREE,
  KEY `idx_union` (`type_union`,`status_union`,`staff_id_union`) USING BTREE,
  KEY `idx_approval_id` (`approval_id`,`type_union`,`created_at`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1107275 DEFAULT CHARSET=utf8mb4;


--
-- Table structure for table `staff_audit_union_2019_04`
--



CREATE TABLE `staff_audit_union_2019_04` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `id_union` varchar(16) NOT NULL COMMENT '数据对应id',
  `staff_id_union` int(10) DEFAULT NULL COMMENT '用户id',
  `type_union` int(3) DEFAULT NULL COMMENT '类型 1补卡 2请假 3LH 4OT 6hc审批7hc申请 9物料 10出差',
  `status_union` int(3) DEFAULT NULL COMMENT '状态集 101申请中 102审核通过 103驳回 104撤销 105超时关闭 201待审批 202招聘中 203已完成 204已作废 205已拒绝 206已同意 207待审批',
  `store_id` varchar(10) NOT NULL COMMENT '网点id',
  `data` text COMMENT '数据集',
  `summary` text COMMENT '申请概要 json数据',
  `table` varchar(20) DEFAULT NULL COMMENT '表名',
  `created_at` datetime(3) DEFAULT NULL COMMENT '创建时间',
  `origin_id` int(11) DEFAULT '0' COMMENT '原始id',
  `approval_id` int(10) DEFAULT '0' COMMENT '审批人id',
  PRIMARY KEY (`id`),
  KEY `idx_created_at` (`created_at`) USING BTREE,
  KEY `idx_id_union` (`id_union`) USING BTREE,
  KEY `idx_union` (`type_union`,`status_union`,`staff_id_union`) USING BTREE,
  KEY `idx_approval_id` (`approval_id`,`type_union`,`created_at`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=398634 DEFAULT CHARSET=utf8mb4;


--
-- Table structure for table `staff_audit_union_2019_05`
--



CREATE TABLE `staff_audit_union_2019_05` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `id_union` varchar(16) NOT NULL COMMENT '数据对应id',
  `staff_id_union` int(10) DEFAULT NULL COMMENT '用户id',
  `type_union` int(3) DEFAULT NULL COMMENT '类型 1补卡 2请假 3LH 4OT 6hc审批7hc申请 9物料 10出差',
  `status_union` int(3) DEFAULT NULL COMMENT '状态集 101申请中 102审核通过 103驳回 104撤销 105超时关闭 201待审批 202招聘中 203已完成 204已作废 205已拒绝 206已同意 207待审批',
  `store_id` varchar(10) NOT NULL COMMENT '网点id',
  `data` text COMMENT '数据集',
  `summary` text COMMENT '申请概要 json数据',
  `table` varchar(20) DEFAULT NULL COMMENT '表名',
  `created_at` datetime(3) DEFAULT NULL COMMENT '创建时间',
  `origin_id` int(11) DEFAULT '0' COMMENT '原始id',
  `approval_id` int(10) DEFAULT '0' COMMENT '审批人id',
  PRIMARY KEY (`id`),
  KEY `idx_created_at` (`created_at`) USING BTREE,
  KEY `idx_id_union` (`id_union`) USING BTREE,
  KEY `idx_union` (`type_union`,`status_union`,`staff_id_union`) USING BTREE,
  KEY `idx_approval_id` (`approval_id`,`type_union`,`created_at`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3796990 DEFAULT CHARSET=utf8mb4;


--
-- Table structure for table `staff_audit_union_2019_06`
--



CREATE TABLE `staff_audit_union_2019_06` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `id_union` varchar(16) NOT NULL COMMENT '数据对应id',
  `staff_id_union` int(10) DEFAULT NULL COMMENT '用户id',
  `type_union` int(3) DEFAULT NULL COMMENT '类型 1补卡 2请假 3LH 4OT 6hc审批7hc申请 9物料 10出差',
  `status_union` int(3) DEFAULT NULL COMMENT '状态集 101申请中 102审核通过 103驳回 104撤销 105超时关闭 201待审批 202招聘中 203已完成 204已作废 205已拒绝 206已同意 207待审批',
  `store_id` varchar(10) NOT NULL COMMENT '网点id',
  `data` text COMMENT '数据集',
  `summary` text COMMENT '申请概要 json数据',
  `table` varchar(20) DEFAULT NULL COMMENT '表名',
  `created_at` datetime(3) DEFAULT NULL COMMENT '创建时间',
  `origin_id` int(11) DEFAULT '0' COMMENT '原始id',
  `approval_id` int(10) DEFAULT '0' COMMENT '审批人id',
  PRIMARY KEY (`id`),
  KEY `idx_created_at` (`created_at`) USING BTREE,
  KEY `idx_id_union` (`id_union`) USING BTREE,
  KEY `idx_union` (`type_union`,`status_union`,`staff_id_union`) USING BTREE,
  KEY `idx_approval_id` (`approval_id`,`type_union`,`created_at`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=654231 DEFAULT CHARSET=utf8mb4;


--
-- Table structure for table `staff_audit_union_2019_07`
--



CREATE TABLE `staff_audit_union_2019_07` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `id_union` varchar(16) NOT NULL COMMENT '数据对应id',
  `staff_id_union` int(10) DEFAULT NULL COMMENT '用户id',
  `type_union` int(3) DEFAULT NULL COMMENT '类型 1补卡 2请假 3LH 4OT 6hc审批7hc申请 9物料 10出差',
  `status_union` int(3) DEFAULT NULL COMMENT '状态集 101申请中 102审核通过 103驳回 104撤销 105超时关闭 201待审批 202招聘中 203已完成 204已作废 205已拒绝 206已同意 207待审批',
  `store_id` varchar(10) NOT NULL COMMENT '网点id',
  `data` text COMMENT '数据集',
  `summary` text COMMENT '申请概要 json数据',
  `table` varchar(20) DEFAULT NULL COMMENT '表名',
  `created_at` datetime(3) DEFAULT NULL COMMENT '创建时间',
  `origin_id` int(11) DEFAULT '0' COMMENT '原始id',
  `approval_id` int(10) DEFAULT '0' COMMENT '审批人id',
  PRIMARY KEY (`id`),
  KEY `idx_created_at` (`created_at`) USING BTREE,
  KEY `idx_id_union` (`id_union`) USING BTREE,
  KEY `idx_union` (`type_union`,`status_union`,`staff_id_union`) USING BTREE,
  KEY `idx_approval_id` (`approval_id`,`type_union`,`created_at`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=657970 DEFAULT CHARSET=utf8mb4;


--
-- Table structure for table `staff_audit_union_2019_08`
--



CREATE TABLE `staff_audit_union_2019_08` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `id_union` varchar(16) NOT NULL COMMENT '数据对应id',
  `staff_id_union` int(10) DEFAULT NULL COMMENT '用户id',
  `type_union` int(3) DEFAULT NULL COMMENT '类型 1补卡 2请假 3LH 4OT 6hc审批7hc申请 9物料 10出差',
  `status_union` int(3) DEFAULT NULL COMMENT '状态集 101申请中 102审核通过 103驳回 104撤销 105超时关闭 201待审批 202招聘中 203已完成 204已作废 205已拒绝 206已同意 207待审批',
  `store_id` varchar(10) NOT NULL COMMENT '网点id',
  `data` text COMMENT '数据集',
  `summary` text COMMENT '申请概要 json数据',
  `table` varchar(20) DEFAULT NULL COMMENT '表名',
  `created_at` datetime(3) DEFAULT NULL COMMENT '创建时间',
  `origin_id` int(11) DEFAULT '0' COMMENT '原始id',
  `approval_id` int(10) DEFAULT '0' COMMENT '审批人id',
  PRIMARY KEY (`id`),
  KEY `idx_created_at` (`created_at`) USING BTREE,
  KEY `idx_id_union` (`id_union`) USING BTREE,
  KEY `idx_union` (`type_union`,`status_union`,`staff_id_union`) USING BTREE,
  KEY `idx_approval_id` (`approval_id`,`type_union`,`created_at`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=829256 DEFAULT CHARSET=utf8mb4;


--
-- Table structure for table `staff_audit_union_2019_09`
--



CREATE TABLE `staff_audit_union_2019_09` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `id_union` varchar(16) NOT NULL COMMENT '数据对应id',
  `staff_id_union` int(10) DEFAULT NULL COMMENT '用户id',
  `type_union` int(3) DEFAULT NULL COMMENT '类型 1补卡 2请假 3LH 4OT 6hc审批7hc申请 9物料 10出差',
  `status_union` int(3) DEFAULT NULL COMMENT '状态集 101申请中 102审核通过 103驳回 104撤销 105超时关闭 201待审批 202招聘中 203已完成 204已作废 205已拒绝 206已同意 207待审批',
  `store_id` varchar(10) NOT NULL COMMENT '网点id',
  `data` text COMMENT '数据集',
  `summary` text COMMENT '申请概要 json数据',
  `table` varchar(20) DEFAULT NULL COMMENT '表名',
  `created_at` datetime(3) DEFAULT NULL COMMENT '创建时间',
  `origin_id` int(11) DEFAULT '0' COMMENT '原始id',
  `approval_id` int(10) DEFAULT '0' COMMENT '审批人id',
  PRIMARY KEY (`id`),
  KEY `idx_created_at` (`created_at`) USING BTREE,
  KEY `idx_id_union` (`id_union`) USING BTREE,
  KEY `idx_union` (`type_union`,`status_union`,`staff_id_union`) USING BTREE,
  KEY `idx_approval_id` (`approval_id`,`type_union`,`created_at`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1553472 DEFAULT CHARSET=utf8mb4;


--
-- Table structure for table `staff_audit_union_2019_10`
--



CREATE TABLE `staff_audit_union_2019_10` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `id_union` varchar(16) NOT NULL COMMENT '数据对应id',
  `staff_id_union` int(10) DEFAULT NULL COMMENT '用户id',
  `type_union` int(3) DEFAULT NULL COMMENT '类型 1补卡 2请假 3LH 4OT 6hc审批7hc申请 9物料 10出差',
  `status_union` int(3) DEFAULT NULL COMMENT '状态集 101申请中 102审核通过 103驳回 104撤销 105超时关闭 201待审批 202招聘中 203已完成 204已作废 205已拒绝 206已同意 207待审批',
  `store_id` varchar(10) NOT NULL COMMENT '网点id',
  `data` text COMMENT '数据集',
  `summary` text COMMENT '申请概要 json数据',
  `table` varchar(20) DEFAULT NULL COMMENT '表名',
  `created_at` datetime(3) DEFAULT NULL COMMENT '创建时间',
  `origin_id` int(11) DEFAULT '0' COMMENT '原始id',
  `approval_id` int(10) DEFAULT '0' COMMENT '审批人id',
  PRIMARY KEY (`id`),
  KEY `idx_created_at` (`created_at`) USING BTREE,
  KEY `idx_id_union` (`id_union`) USING BTREE,
  KEY `idx_union` (`type_union`,`status_union`,`staff_id_union`) USING BTREE,
  KEY `idx_approval_id` (`approval_id`,`type_union`,`created_at`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2442694 DEFAULT CHARSET=utf8mb4;


--
-- Table structure for table `staff_audit_union_2019_11`
--



CREATE TABLE `staff_audit_union_2019_11` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `id_union` varchar(16) NOT NULL COMMENT '数据对应id',
  `staff_id_union` int(10) DEFAULT NULL COMMENT '用户id',
  `type_union` int(3) DEFAULT NULL COMMENT '类型 1补卡 2请假 3LH 4OT 6hc审批7hc申请 9物料 10出差',
  `status_union` int(3) DEFAULT NULL COMMENT '状态集 101申请中 102审核通过 103驳回 104撤销 105超时关闭 201待审批 202招聘中 203已完成 204已作废 205已拒绝 206已同意 207待审批',
  `store_id` varchar(10) NOT NULL COMMENT '网点id',
  `data` text COMMENT '数据集',
  `summary` text COMMENT '申请概要 json数据',
  `table` varchar(20) DEFAULT NULL COMMENT '表名',
  `created_at` datetime(3) DEFAULT NULL COMMENT '创建时间',
  `origin_id` int(11) DEFAULT '0' COMMENT '原始id',
  `approval_id` int(10) DEFAULT '0' COMMENT '审批人id',
  PRIMARY KEY (`id`),
  KEY `idx_created_at` (`created_at`) USING BTREE,
  KEY `idx_id_union` (`id_union`) USING BTREE,
  KEY `idx_union` (`type_union`,`status_union`,`staff_id_union`) USING BTREE,
  KEY `idx_approval_id` (`approval_id`,`type_union`,`created_at`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1831929 DEFAULT CHARSET=utf8mb4;


--
-- Table structure for table `staff_audit_union_2019_12`
--



CREATE TABLE `staff_audit_union_2019_12` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `id_union` varchar(16) NOT NULL COMMENT '数据对应id',
  `staff_id_union` int(10) DEFAULT NULL COMMENT '用户id',
  `type_union` int(3) DEFAULT NULL COMMENT '类型 1补卡 2请假 3LH 4OT 6hc审批7hc申请 9物料 10出差',
  `status_union` int(3) DEFAULT NULL COMMENT '状态集 101申请中 102审核通过 103驳回 104撤销 105超时关闭 201待审批 202招聘中 203已完成 204已作废 205已拒绝 206已同意 207待审批',
  `store_id` varchar(10) NOT NULL COMMENT '网点id',
  `data` text COMMENT '数据集',
  `summary` text COMMENT '申请概要 json数据',
  `table` varchar(20) DEFAULT NULL COMMENT '表名',
  `created_at` datetime(3) DEFAULT NULL COMMENT '创建时间',
  `origin_id` int(11) DEFAULT '0' COMMENT '原始id',
  `approval_id` int(10) DEFAULT '0' COMMENT '审批人id',
  PRIMARY KEY (`id`),
  KEY `idx_created_at` (`created_at`) USING BTREE,
  KEY `idx_id_union` (`id_union`) USING BTREE,
  KEY `idx_union` (`type_union`,`status_union`,`staff_id_union`) USING BTREE,
  KEY `idx_approval_id` (`approval_id`,`type_union`,`created_at`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2503462 DEFAULT CHARSET=utf8mb4;


--
-- Table structure for table `staff_audit_union_2020_01`
--



CREATE TABLE `staff_audit_union_2020_01` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `id_union` varchar(16) NOT NULL COMMENT '数据对应id',
  `staff_id_union` int(10) DEFAULT NULL COMMENT '用户id',
  `type_union` int(3) DEFAULT NULL COMMENT '类型 1补卡 2请假 3LH 4OT 6hc审批7hc申请 9物料 10出差',
  `status_union` int(3) DEFAULT NULL COMMENT '状态集 101申请中 102审核通过 103驳回 104撤销 105超时关闭 201待审批 202招聘中 203已完成 204已作废 205已拒绝 206已同意 207待审批',
  `store_id` varchar(10) NOT NULL COMMENT '网点id',
  `data` text COMMENT '数据集',
  `summary` text COMMENT '申请概要 json数据',
  `table` varchar(20) DEFAULT NULL COMMENT '表名',
  `created_at` datetime(3) DEFAULT NULL COMMENT '创建时间',
  `origin_id` int(11) DEFAULT '0' COMMENT '原始id',
  `approval_id` int(10) DEFAULT '0' COMMENT '审批人id',
  PRIMARY KEY (`id`),
  KEY `idx_created_at` (`created_at`) USING BTREE,
  KEY `idx_id_union` (`id_union`) USING BTREE,
  KEY `idx_union` (`type_union`,`status_union`,`staff_id_union`) USING BTREE,
  KEY `idx_approval_id` (`approval_id`,`type_union`,`created_at`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3379765 DEFAULT CHARSET=utf8mb4;


--
-- Table structure for table `staff_audit_union_2020_02`
--



CREATE TABLE `staff_audit_union_2020_02` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `id_union` varchar(16) NOT NULL COMMENT '数据对应id',
  `staff_id_union` int(10) DEFAULT NULL COMMENT '用户id',
  `type_union` int(3) DEFAULT NULL COMMENT '类型 1补卡 2请假 3LH 4OT 6hc审批7hc申请 9物料 10出差',
  `status_union` int(3) DEFAULT NULL COMMENT '状态集 101申请中 102审核通过 103驳回 104撤销 105超时关闭 201待审批 202招聘中 203已完成 204已作废 205已拒绝 206已同意 207待审批',
  `store_id` varchar(10) NOT NULL COMMENT '网点id',
  `data` text COMMENT '数据集',
  `summary` text COMMENT '申请概要 json数据',
  `table` varchar(20) DEFAULT NULL COMMENT '表名',
  `created_at` datetime(3) DEFAULT NULL COMMENT '创建时间',
  `origin_id` int(11) DEFAULT '0' COMMENT '原始id',
  `approval_id` int(10) DEFAULT '0' COMMENT '审批人id',
  PRIMARY KEY (`id`),
  KEY `idx_created_at` (`created_at`) USING BTREE,
  KEY `idx_id_union` (`id_union`) USING BTREE,
  KEY `idx_union` (`type_union`,`status_union`,`staff_id_union`) USING BTREE,
  KEY `idx_approval_id` (`approval_id`,`type_union`,`created_at`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=4159222 DEFAULT CHARSET=utf8mb4;


--
-- Table structure for table `staff_audit_union_2020_03`
--



CREATE TABLE `staff_audit_union_2020_03` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `id_union` varchar(16) NOT NULL COMMENT '数据对应id',
  `staff_id_union` int(10) DEFAULT NULL COMMENT '用户id',
  `type_union` int(3) DEFAULT NULL COMMENT '类型 1补卡 2请假 3LH 4OT 6hc审批7hc申请 9物料 10出差',
  `status_union` int(3) DEFAULT NULL COMMENT '状态集 101申请中 102审核通过 103驳回 104撤销 105超时关闭 201待审批 202招聘中 203已完成 204已作废 205已拒绝 206已同意 207待审批',
  `store_id` varchar(10) NOT NULL COMMENT '网点id',
  `data` text COMMENT '数据集',
  `summary` text COMMENT '申请概要 json数据',
  `table` varchar(20) DEFAULT NULL COMMENT '表名',
  `created_at` datetime(3) DEFAULT NULL COMMENT '创建时间',
  `origin_id` int(11) DEFAULT '0' COMMENT '原始id',
  `approval_id` int(10) DEFAULT '0' COMMENT '审批人id',
  PRIMARY KEY (`id`),
  KEY `idx_created_at` (`created_at`) USING BTREE,
  KEY `idx_id_union` (`id_union`) USING BTREE,
  KEY `idx_union` (`type_union`,`status_union`,`staff_id_union`) USING BTREE,
  KEY `idx_approval_id` (`approval_id`,`type_union`,`created_at`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=4613697 DEFAULT CHARSET=utf8mb4;


--
-- Table structure for table `staff_audit_union_2020_04`
--



CREATE TABLE `staff_audit_union_2020_04` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `id_union` varchar(16) NOT NULL COMMENT '数据对应id',
  `staff_id_union` int(10) DEFAULT NULL COMMENT '用户id',
  `type_union` int(3) DEFAULT NULL COMMENT '类型 1补卡 2请假 3LH 4OT 6hc审批7hc申请 9物料 10出差',
  `status_union` int(3) DEFAULT NULL COMMENT '状态集 101申请中 102审核通过 103驳回 104撤销 105超时关闭 201待审批 202招聘中 203已完成 204已作废 205已拒绝 206已同意 207待审批',
  `store_id` varchar(10) NOT NULL COMMENT '网点id',
  `data` text COMMENT '数据集',
  `summary` text COMMENT '申请概要 json数据',
  `table` varchar(20) DEFAULT NULL COMMENT '表名',
  `created_at` datetime(3) DEFAULT NULL COMMENT '创建时间',
  `origin_id` int(11) DEFAULT '0' COMMENT '原始id',
  `approval_id` int(10) DEFAULT '0' COMMENT '审批人id',
  PRIMARY KEY (`id`),
  KEY `idx_created_at` (`created_at`) USING BTREE,
  KEY `idx_id_union` (`id_union`) USING BTREE,
  KEY `idx_union` (`type_union`,`status_union`,`staff_id_union`) USING BTREE,
  KEY `idx_approval_id` (`approval_id`,`type_union`,`created_at`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=4372408 DEFAULT CHARSET=utf8mb4;


--
-- Table structure for table `staff_audit_union_2020_05`
--



CREATE TABLE `staff_audit_union_2020_05` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `id_union` varchar(16) NOT NULL COMMENT '数据对应id',
  `staff_id_union` int(10) DEFAULT NULL COMMENT '用户id',
  `type_union` int(3) DEFAULT NULL COMMENT '类型 1补卡 2请假 3LH 4OT 6hc审批7hc申请 9物料 10出差',
  `status_union` int(3) DEFAULT NULL COMMENT '状态集 101申请中 102审核通过 103驳回 104撤销 105超时关闭 201待审批 202招聘中 203已完成 204已作废 205已拒绝 206已同意 207待审批',
  `store_id` varchar(10) NOT NULL COMMENT '网点id',
  `data` text COMMENT '数据集',
  `summary` text COMMENT '申请概要 json数据',
  `table` varchar(64) DEFAULT NULL COMMENT '表名',
  `created_at` datetime(3) DEFAULT NULL COMMENT '创建时间',
  `origin_id` int(11) DEFAULT '0' COMMENT '原始id',
  `approval_id` int(10) DEFAULT '0' COMMENT '审批人id',
  PRIMARY KEY (`id`),
  KEY `idx_created_at` (`created_at`) USING BTREE,
  KEY `idx_id_union` (`id_union`) USING BTREE,
  KEY `idx_union` (`type_union`,`status_union`,`staff_id_union`) USING BTREE,
  KEY `idx_approval_id` (`approval_id`,`type_union`,`created_at`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=12884494 DEFAULT CHARSET=utf8mb4;


--
-- Table structure for table `staff_audit_union_2020_06`
--



CREATE TABLE `staff_audit_union_2020_06` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `id_union` varchar(16) NOT NULL COMMENT '数据对应id',
  `staff_id_union` int(10) DEFAULT NULL COMMENT '用户id',
  `type_union` int(3) DEFAULT NULL COMMENT '类型 1补卡 2请假 3LH 4OT 6hc审批7hc申请 9物料 10出差',
  `status_union` int(3) DEFAULT NULL COMMENT '状态集 101申请中 102审核通过 103驳回 104撤销 105超时关闭 201待审批 202招聘中 203已完成 204已作废 205已拒绝 206已同意 207待审批',
  `store_id` varchar(10) NOT NULL COMMENT '网点id',
  `data` text COMMENT '数据集',
  `summary` text COMMENT '申请概要 json数据',
  `table` varchar(64) DEFAULT NULL COMMENT '表名',
  `created_at` datetime(3) DEFAULT NULL COMMENT '创建时间',
  `origin_id` int(11) DEFAULT '0' COMMENT '原始id',
  `approval_id` int(10) DEFAULT '0' COMMENT '审批人id',
  PRIMARY KEY (`id`),
  KEY `idx_created_at` (`created_at`) USING BTREE,
  KEY `idx_id_union` (`id_union`) USING BTREE,
  KEY `idx_union` (`type_union`,`status_union`,`staff_id_union`) USING BTREE,
  KEY `idx_approval_id` (`approval_id`,`type_union`,`created_at`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=5934190 DEFAULT CHARSET=utf8mb4;


--
-- Table structure for table `staff_audit_union_2020_07`
--



CREATE TABLE `staff_audit_union_2020_07` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `id_union` varchar(16) NOT NULL COMMENT '数据对应id',
  `staff_id_union` int(10) DEFAULT NULL COMMENT '用户id',
  `type_union` int(3) DEFAULT NULL COMMENT '类型 1补卡 2请假 3LH 4OT 6hc审批7hc申请 9物料 10出差',
  `status_union` int(3) DEFAULT NULL COMMENT '状态集 101申请中 102审核通过 103驳回 104撤销 105超时关闭 201待审批 202招聘中 203已完成 204已作废 205已拒绝 206已同意 207待审批',
  `store_id` varchar(10) NOT NULL COMMENT '网点id',
  `data` text COMMENT '数据集',
  `summary` text COMMENT '申请概要 json数据',
  `table` varchar(64) DEFAULT NULL COMMENT '表名',
  `created_at` datetime(3) DEFAULT NULL COMMENT '创建时间',
  `origin_id` int(11) DEFAULT '0' COMMENT '原始id',
  `approval_id` int(10) DEFAULT '0' COMMENT '审批人id',
  PRIMARY KEY (`id`),
  KEY `idx_created_at` (`created_at`) USING BTREE,
  KEY `idx_id_union` (`id_union`) USING BTREE,
  KEY `idx_union` (`type_union`,`status_union`,`staff_id_union`) USING BTREE,
  KEY `idx_approval_id` (`approval_id`,`type_union`,`created_at`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=4983899 DEFAULT CHARSET=utf8mb4;


--
-- Table structure for table `staff_days_for_leave`
--



CREATE TABLE `staff_days_for_leave` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `leave_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '假期类型 staff_audit表 leave_type字段',
  `days` decimal(10,1) NOT NULL DEFAULT '0.0' COMMENT '类型对应可以请假天数',
  `job_title` int(11) NOT NULL DEFAULT '0' COMMENT '部分类型 针对职位 天数不同 职位id',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=181 DEFAULT CHARSET=utf8mb4 COMMENT='请假天数表（根据不同请假类型 获取对应天数 基础信息表）';


--
-- Table structure for table `staff_days_freeze`
--



CREATE TABLE `staff_days_freeze` (
  `staff_info_id` int(10) unsigned NOT NULL COMMENT '员工ID ms staff_info.id',
  `leave_type` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '假期类型 1 年假',
  `date_at` date NOT NULL DEFAULT '0000-00-00' COMMENT '任务日期',
  `days` decimal(10,3) NOT NULL DEFAULT '0.000' COMMENT '每年1月1号到当天应有假期类型的递增额度',
  `job_title_level` tinyint(2) DEFAULT NULL COMMENT '职等1.Staff/2.Supervisor/3.Manager/4.Executive 用户记录每天职等变化的员工 存到staff_job_level_log',
  `job_title_grade` tinyint(2) DEFAULT NULL COMMENT '职级',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`staff_info_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='每天固化员工按当前职等叠加当天增加的额度';


--
-- Table structure for table `staff_device_info`
--



CREATE TABLE `staff_device_info` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `staff_info_id` int(10) unsigned NOT NULL COMMENT '员工信息ID',
  `device_model` varchar(200) DEFAULT NULL COMMENT '设备型号',
  `equipment_type` varchar(60) DEFAULT NULL COMMENT '服务端 seed 1-kit 2-bs 3-backyard',
  `current_ip` varchar(20) DEFAULT NULL COMMENT '本次登录ip',
  `network_type` varchar(64) DEFAULT NULL COMMENT '运营商类型或者wifi',
  `lat` decimal(11,8) DEFAULT NULL COMMENT '位置的纬度',
  `lng` decimal(11,8) DEFAULT NULL COMMENT '位置经度',
  `current_time` datetime DEFAULT NULL COMMENT '本次登录时间',
  `version` varchar(20) DEFAULT NULL COMMENT '客户端版本',
  `os` varchar(30) DEFAULT NULL COMMENT '本次登录os',
  `client_id` varchar(128) DEFAULT NULL COMMENT '标识app，若同一个发布账号下所以app都被删除，重新安装此值会改变。',
  `device_id` varchar(200) DEFAULT NULL COMMENT '广告标识符，可作为设备唯一标识，但有可能获取不到',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_staff_device` (`staff_info_id`,`equipment_type`,`client_id`)
) ENGINE=InnoDB AUTO_INCREMENT=244751 DEFAULT CHARSET=utf8mb4 COMMENT='员工登录账号表';


--
-- Table structure for table `staff_job_level_log`
--



CREATE TABLE `staff_job_level_log` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `staff_info_id` int(10) unsigned NOT NULL COMMENT '员工ID ms staff_info.id',
  `date_at` date NOT NULL DEFAULT '0000-00-00' COMMENT '职等变更日期',
  `job_title_level` tinyint(2) DEFAULT NULL COMMENT '职等1.Staff/2.Supervisor/3.Manager/4.Executive ',
  `job_title_grade` tinyint(2) DEFAULT NULL COMMENT '职级',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=138852 DEFAULT CHARSET=utf8mb4 COMMENT='每天跑年假额度任务 发现职等变更 出发保存操作';


--
-- Table structure for table `staff_last_year_days`
--



CREATE TABLE `staff_last_year_days` (
  `staff_info_id` int(10) unsigned NOT NULL COMMENT '员工ID ms staff_info.id',
  `year_time` smallint(6) NOT NULL DEFAULT '1970' COMMENT '所在年的 剩余年假',
  `hire_date` datetime DEFAULT NULL COMMENT '入职时间',
  `job_title` varchar(64) DEFAULT NULL COMMENT '职位',
  `job_title_level` tinyint(2) DEFAULT NULL COMMENT '职等1.Staff/2.Supervisor/3.Manager/4.Executive',
  `job_title_grade` tinyint(2) DEFAULT NULL COMMENT '职级',
  `got_days` decimal(10,1) NOT NULL DEFAULT '0.0' COMMENT '应有年假天数',
  `left_days` decimal(10,1) NOT NULL DEFAULT '0.0' COMMENT '剩余年假天数',
  `left_all_days` decimal(10,1) NOT NULL DEFAULT '0.0' COMMENT '截止新年1月1日 去年剩余年假天数总量（显示剩余年假用）这字段不会再变化，新年请假不更新这个字段',
  `split_id` varchar(200) NOT NULL DEFAULT '' COMMENT '对应拆分假期表 撤销操作 还原天数用',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`staff_info_id`,`year_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='每年一月一号泰国时间 凌晨（北京时间 凌晨 1点15）跑去年的年假剩余天数数据';


--
-- Table structure for table `staff_leave_add_log`
--



CREATE TABLE `staff_leave_add_log` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `serial_no` varchar(32) NOT NULL DEFAULT '' COMMENT '序列号',
  `staff_info_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '员工ID',
  `leave_start_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '开始时间',
  `leave_end_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '结束时间',
  `year` year(4) NOT NULL DEFAULT '1970' COMMENT '年份',
  `leave_days` decimal(3,1) NOT NULL DEFAULT '0.0' COMMENT '请假天数',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_staff_leave_time` (`staff_info_id`,`leave_start_time`,`leave_end_time`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COMMENT='员工请探亲假记录表';


--
-- Table structure for table `staff_leave_read`
--



CREATE TABLE `staff_leave_read` (
  `staff_info_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '工号',
  `leave_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '假期类型 staff_audit表 leave_type字段',
  `is_read` tinyint(4) NOT NULL DEFAULT '1' COMMENT '是否选择不再弹窗 0 弹窗  1 已选择不再弹窗',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`staff_info_id`,`leave_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='请假类型说明 是否已读且选择不再弹窗';


--
-- Table structure for table `staff_leave_remaining_days`
--



CREATE TABLE `staff_leave_remaining_days` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `staff_info_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '员工ID',
  `leave_type` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '假期类型',
  `year` year(4) NOT NULL DEFAULT '1970' COMMENT '年份',
  `days` decimal(3,1) NOT NULL DEFAULT '0.0' COMMENT '剩余假期天数',
  `leave_days` decimal(3,1) NOT NULL DEFAULT '0.0' COMMENT '已使用假期天数',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_staff_leave_year` (`staff_info_id`,`leave_type`,`year`)
) ENGINE=InnoDB AUTO_INCREMENT=163 DEFAULT CHARSET=utf8mb4 COMMENT='员工假期天数每年剩余表 实时';


--
-- Table structure for table `staff_mileage_record`
--



CREATE TABLE `staff_mileage_record` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `staff_info_id` int(10) unsigned DEFAULT NULL COMMENT '员工工号',
  `store_id` varchar(10) DEFAULT NULL COMMENT '网点编号',
  `sorting_no` varchar(10) DEFAULT NULL COMMENT '分拣区编号',
  `mileage_date` date DEFAULT NULL COMMENT '里程日期',
  `start_kilometres` bigint(20) DEFAULT NULL COMMENT '上班公里数(m)',
  `started_at` datetime(3) DEFAULT NULL COMMENT '上班汇报时间',
  `end_kilometres` bigint(20) DEFAULT NULL COMMENT '下班公里数(m)',
  `end_at` datetime(3) DEFAULT NULL COMMENT '下班汇报时间',
  `change_car` tinyint(1) DEFAULT '0' COMMENT '是否换车',
  `created_at` datetime(3) DEFAULT CURRENT_TIMESTAMP(3),
  `updated_at` datetime(3) DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
  `prepaid_slip_no` varchar(50) DEFAULT NULL COMMENT '批次号',
  `money` int(11) DEFAULT '0' COMMENT '补贴金额',
  `data_price` int(11) DEFAULT '0' COMMENT '当天的补贴单价',
  `input_state` tinyint(10) DEFAULT '0' COMMENT 'WRS 审核状态：0、未审核，1、审核中，2、通过，3、模糊，4，虚假',
  `input_by` varchar(10) DEFAULT '' COMMENT 'WRS 审核人（补单员）',
  `input_id` int(10) DEFAULT NULL COMMENT 'WRS 审核人id（补单员）',
  `ft_id` bigint(10) DEFAULT NULL COMMENT 'WRS 油表审核任务表 id',
  `create_channel` tinyint(1) DEFAULT '0' COMMENT '创建渠道 0 默认 1，by',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_staff_date` (`staff_info_id`,`mileage_date`),
  KEY `idx_input_id` (`input_id`),
  KEY `idx_ft_id` (`ft_id`),
  KEY `idx_store_mileage_date` (`store_id`,`mileage_date`) USING BTREE,
  KEY `idx_mileage_date` (`mileage_date`),
  KEY `idx_prepaid_slip_no` (`prepaid_slip_no`)
) ENGINE=InnoDB AUTO_INCREMENT=4069519 DEFAULT CHARSET=utf8mb4 COMMENT='快递员里程信息记录表';


--
-- Table structure for table `staff_mileage_record_prepaid`
--



CREATE TABLE `staff_mileage_record_prepaid` (
  `prepaid_no` varchar(50) NOT NULL COMMENT '预付单号',
  `staff_id` int(10) DEFAULT NULL COMMENT '提交人',
  `money` int(10) DEFAULT NULL COMMENT '合计金额',
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  `state` int(2) DEFAULT '0' COMMENT '0未充值，1已充值',
  PRIMARY KEY (`prepaid_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='油卡充值批次';


--
-- Table structure for table `staff_mileage_record_prepaid_info`
--



CREATE TABLE `staff_mileage_record_prepaid_info` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `prepaid_no` varchar(50) NOT NULL COMMENT '预付单号',
  `staff_id` int(10) DEFAULT NULL COMMENT '提交人',
  `mileage` int(10) DEFAULT '0' COMMENT '里程数',
  `oil_number` varchar(50) DEFAULT NULL COMMENT '油卡号',
  `money` int(10) DEFAULT '0' COMMENT '合计金额',
  `state` int(2) DEFAULT '0' COMMENT '充值状态',
  `already_money` int(10) DEFAULT '0' COMMENT '已充值金额',
  `recharge_staff_id` int(11) DEFAULT NULL COMMENT '充值操作者',
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  `recharge_at` datetime DEFAULT NULL COMMENT '实际充值时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `prepaid_no_staff_id` (`prepaid_no`,`staff_id`),
  KEY `idx_po` (`prepaid_no`,`oil_number`),
  KEY `idx_oil_staff_state` (`state`,`staff_id`,`oil_number`)
) ENGINE=InnoDB AUTO_INCREMENT=3457730 DEFAULT CHARSET=utf8mb4 COMMENT='员工油卡批次详情';


--
-- Table structure for table `staff_mobile`
--



CREATE TABLE `staff_mobile` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `staff_id` int(11) unsigned DEFAULT '0' COMMENT '工号',
  `staff_mobile` char(10) DEFAULT '0' COMMENT '手机号',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除 0:未删除；1:已删除；',
  `create_id` int(11) unsigned DEFAULT '0' COMMENT '创建人',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `create_id` (`create_id`)
) ENGINE=InnoDB AUTO_INCREMENT=7905 DEFAULT CHARSET=utf8mb4 COMMENT='员工手机表';


--
-- Table structure for table `staff_mobile_company_notice_log`
--



CREATE TABLE `staff_mobile_company_notice_log` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `staff_info_id` int(10) unsigned NOT NULL COMMENT '员工ID',
  `job_title` varchar(64) DEFAULT NULL COMMENT '职位',
  `send_type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '发送类型1:直发 2:抄送',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `staff_info_id` (`staff_info_id`) USING BTREE,
  KEY `job_title` (`job_title`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=18914 DEFAULT CHARSET=utf8mb4 COMMENT='员工公司号码为空通知记录表';


--
-- Table structure for table `staff_resign`
--



CREATE TABLE `staff_resign` (
  `resign_id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `serial_no` varchar(32) DEFAULT NULL COMMENT '序列号',
  `submitter_id` int(10) DEFAULT NULL COMMENT '提交人 id',
  `hire_date` varchar(20) DEFAULT NULL COMMENT '入职日期',
  `leave_date` varchar(20) DEFAULT NULL COMMENT '离职日期',
  `last_work_date` date DEFAULT NULL COMMENT '最后工作日期',
  `work_handover` int(10) DEFAULT NULL COMMENT '交接人id',
  `goods` varchar(255) DEFAULT NULL COMMENT '交接物品',
  `reason` varchar(255) DEFAULT NULL COMMENT '离职原因',
  `remark` text COMMENT '离职备注',
  `reject_reason` varchar(500) DEFAULT NULL COMMENT '拒绝原因',
  `cancel_reason` varchar(500) DEFAULT NULL COMMENT '撤销原因',
  `status` tinyint(3) NOT NULL COMMENT '审批状态 1-待审批 2-同意  3-撤销 4-驳回 5-超时',
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  `updated_at` datetime(3) DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
  `source` tinyint(1) DEFAULT '0' COMMENT '来源=0backyard提交，1bi导入',
  PRIMARY KEY (`resign_id`) USING BTREE,
  KEY `idx_submitter_id` (`submitter_id`) USING BTREE COMMENT '申请人索引'
) ENGINE=InnoDB AUTO_INCREMENT=12556 DEFAULT CHARSET=utf8mb4 COMMENT='员工离职申请表';


--
-- Table structure for table `staff_vehicle_status_record`
--



CREATE TABLE `staff_vehicle_status_record` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `staff_info_id` int(11) DEFAULT NULL COMMENT '员工ID',
  `store_id` varchar(10) DEFAULT NULL COMMENT '网点ID',
  `sorting_no` varchar(10) DEFAULT NULL COMMENT '分拣区编号',
  `report_date` date DEFAULT NULL COMMENT '上报日期',
  `on_duty_report_time` time DEFAULT NULL,
  `on_duty_check_list` text COMMENT '上班检查情况',
  `off_duty_report_time` time DEFAULT NULL,
  `off_duty_check_list` text COMMENT '下班检查情况',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_staff_info_id` (`staff_info_id`,`store_id`,`report_date`),
  KEY `idx_date_store_id` (`report_date`,`store_id`)
) ENGINE=InnoDB AUTO_INCREMENT=52 DEFAULT CHARSET=utf8mb4 COMMENT='每天员工上下班车况检查清单提交情况';


--
-- Table structure for table `staff_work_attendance`
--



CREATE TABLE `staff_work_attendance` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `staff_info_id` int(10) unsigned DEFAULT NULL COMMENT '员工工号',
  `organization_id` varchar(10) DEFAULT NULL COMMENT '所属组织机构id',
  `organization_type` tinyint(3) unsigned DEFAULT NULL COMMENT '组织类别 1 store 2 department',
  `attendance_date` date DEFAULT NULL COMMENT '考勤日期',
  `shift_start` varchar(32) DEFAULT NULL COMMENT '班次开始时间',
  `shift_end` varchar(32) DEFAULT NULL COMMENT '班次结束时间',
  `working_day` tinyint(3) unsigned DEFAULT NULL COMMENT '是否是工作日 0:否；1:是',
  `started_at` datetime(3) DEFAULT NULL COMMENT '上班打卡时间',
  `started_state` tinyint(3) unsigned DEFAULT NULL COMMENT '上班打卡状态',
  `started_staff_lat` decimal(11,8) DEFAULT NULL COMMENT '上班打卡位置的纬度',
  `started_staff_lng` decimal(11,8) DEFAULT NULL COMMENT '上班打卡位置经度',
  `started_store_id` varchar(10) DEFAULT NULL COMMENT '上班打卡网点编号',
  `started_store_lng` decimal(11,8) DEFAULT NULL COMMENT '上班打卡网点经度',
  `started_store_lat` decimal(11,8) DEFAULT NULL COMMENT '上班打卡网点纬度',
  `started_clientid` varchar(60) DEFAULT NULL COMMENT '上班打卡客户端 ID',
  `started_clientid_num` int(10) DEFAULT NULL COMMENT '上班打卡设备打卡次数',
  `started_equipment_type` varchar(60) DEFAULT NULL COMMENT '上班打卡服务端 seed',
  `started_os` varchar(10) DEFAULT NULL COMMENT '上班打卡客户端系统',
  `started_path` varchar(100) DEFAULT NULL COMMENT '上班考勤照片',
  `started_bucket` varchar(63) DEFAULT NULL COMMENT '上班考勤照片所属bucket',
  `started_remark` varchar(500) DEFAULT NULL COMMENT '上班打卡备注信息',
  `end_at` datetime(3) DEFAULT NULL COMMENT '下班打卡时间',
  `end_state` tinyint(3) unsigned DEFAULT NULL COMMENT '下班打卡状态',
  `end_staff_lat` decimal(11,8) DEFAULT NULL COMMENT '下班打卡位置的纬度',
  `end_staff_lng` decimal(11,8) DEFAULT NULL COMMENT '下班打卡位置的经度',
  `end_store_id` varchar(10) DEFAULT NULL COMMENT '下班打卡网点编号',
  `end_store_lng` decimal(11,8) DEFAULT NULL COMMENT '下班打卡网点经度',
  `end_store_lat` decimal(11,8) DEFAULT NULL COMMENT '下班打卡网点纬度',
  `end_clientid` varchar(60) DEFAULT NULL COMMENT '下班打卡客户端 ID',
  `end_clientid_num` int(10) DEFAULT NULL COMMENT '下班打卡设备打卡次数',
  `end_equipment_type` varchar(60) DEFAULT NULL COMMENT '下班打卡服务端 seed',
  `end_os` varchar(10) DEFAULT NULL COMMENT '下班打卡客户端系统',
  `end_path` varchar(100) DEFAULT NULL COMMENT '下班考勤照片',
  `end_bucket` varchar(63) DEFAULT NULL COMMENT '下班考勤照片所属bucket',
  `end_remark` varchar(500) DEFAULT NULL COMMENT '下班打卡备注信息',
  `created_at` datetime(3) DEFAULT CURRENT_TIMESTAMP(3),
  `updated_at` datetime(3) DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_staff_date` (`staff_info_id`,`attendance_date`),
  KEY `idx_started_clientid` (`started_clientid`),
  KEY `idx_end_clientid` (`end_clientid`),
  KEY `idx_attendance_date` (`attendance_date`) USING BTREE,
  KEY `idx_store_date` (`organization_id`,`attendance_date`)
) ENGINE=InnoDB AUTO_INCREMENT=11688811 DEFAULT CHARSET=utf8mb4 COMMENT='快递员打卡信息记录表';


--
-- Table structure for table `staff_work_attendance_attachment`
--



CREATE TABLE `staff_work_attendance_attachment` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `staff_info_id` int(10) unsigned DEFAULT NULL COMMENT '操作员工工号',
  `work_attendance_path` varchar(100) DEFAULT NULL COMMENT '考勤源照片',
  `work_attendance_bucket` varchar(100) DEFAULT NULL COMMENT '考勤源照片所属bucket',
  `deleted` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否已经删除 0:否 1:是',
  `operate_id` int(10) unsigned DEFAULT NULL COMMENT '操作员工工号',
  `operate_name` varchar(50) DEFAULT NULL COMMENT '操作员工名称',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_staff_id_deleted` (`staff_info_id`,`deleted`),
  KEY `staff_id_index` (`staff_info_id`) USING BTREE COMMENT '员工id索引'
) ENGINE=InnoDB AUTO_INCREMENT=78354 DEFAULT CHARSET=utf8mb4 COMMENT='员工打卡图片信息表';


--
-- Table structure for table `staff_work_attendance_for_import`
--



CREATE TABLE `staff_work_attendance_for_import` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `staff_info_id` int(10) unsigned DEFAULT NULL COMMENT '员工工号',
  `organization_id` varchar(10) DEFAULT NULL COMMENT '所属组织机构id',
  `organization_type` tinyint(3) unsigned DEFAULT NULL COMMENT '组织类别 1 store 2 department',
  `attendance_date` date DEFAULT NULL COMMENT '考勤日期',
  `shift_start` varchar(32) DEFAULT NULL COMMENT '班次开始时间',
  `shift_end` varchar(32) DEFAULT NULL COMMENT '班次结束时间',
  `working_day` tinyint(3) unsigned DEFAULT NULL COMMENT '是否是工作日 0:否；1:是',
  `started_at` datetime(3) DEFAULT NULL COMMENT '上班打卡时间',
  `started_state` tinyint(3) unsigned DEFAULT NULL COMMENT '上班打卡状态',
  `started_staff_lat` decimal(11,8) DEFAULT NULL COMMENT '上班打卡位置的纬度',
  `started_staff_lng` decimal(11,8) DEFAULT NULL COMMENT '上班打卡位置经度',
  `started_store_id` varchar(10) DEFAULT NULL COMMENT '上班打卡网点编号',
  `started_store_lng` decimal(11,8) DEFAULT NULL COMMENT '上班打卡网点经度',
  `started_store_lat` decimal(11,8) DEFAULT NULL COMMENT '上班打卡网点纬度',
  `started_clientid` varchar(60) DEFAULT NULL COMMENT '上班打卡客户端 ID',
  `started_clientid_num` int(10) DEFAULT NULL COMMENT '上班打卡设备打卡次数',
  `started_equipment_type` varchar(60) DEFAULT NULL COMMENT '上班打卡服务端 seed',
  `started_os` varchar(10) DEFAULT NULL COMMENT '上班打卡客户端系统',
  `started_path` varchar(100) DEFAULT NULL COMMENT '上班考勤照片',
  `started_bucket` varchar(63) DEFAULT NULL COMMENT '上班考勤照片所属bucket',
  `started_remark` varchar(500) DEFAULT NULL COMMENT '上班打卡备注信息',
  `end_at` datetime(3) DEFAULT NULL COMMENT '下班打卡时间',
  `end_state` tinyint(3) unsigned DEFAULT NULL COMMENT '下班打卡状态',
  `end_staff_lat` decimal(11,8) DEFAULT NULL COMMENT '下班打卡位置的纬度',
  `end_staff_lng` decimal(11,8) DEFAULT NULL COMMENT '下班打卡位置的经度',
  `end_store_id` varchar(10) DEFAULT NULL COMMENT '下班打卡网点编号',
  `end_store_lng` decimal(11,8) DEFAULT NULL COMMENT '下班打卡网点经度',
  `end_store_lat` decimal(11,8) DEFAULT NULL COMMENT '下班打卡网点纬度',
  `end_clientid` varchar(60) DEFAULT NULL COMMENT '下班打卡客户端 ID',
  `end_clientid_num` int(10) DEFAULT NULL COMMENT '下班打卡设备打卡次数',
  `end_equipment_type` varchar(60) DEFAULT NULL COMMENT '下班打卡服务端 seed',
  `end_os` varchar(10) DEFAULT NULL COMMENT '下班打卡客户端系统',
  `end_path` varchar(100) DEFAULT NULL COMMENT '下班考勤照片',
  `end_bucket` varchar(63) DEFAULT NULL COMMENT '下班考勤照片所属bucket',
  `end_remark` varchar(500) DEFAULT NULL COMMENT '下班打卡备注信息',
  `created_at` datetime(3) DEFAULT CURRENT_TIMESTAMP(3),
  `updated_at` datetime(3) DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_staff_date` (`staff_info_id`,`attendance_date`),
  KEY `idx_attendance_date` (`attendance_date`)
) ENGINE=InnoDB AUTO_INCREMENT=11642365 DEFAULT CHARSET=utf8mb4 COMMENT='打卡记录表 针对las bkk  网点刷脸考勤备份表';


--
-- Table structure for table `staff_work_attendance_range`
--



CREATE TABLE `staff_work_attendance_range` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `organization_id` varchar(10) NOT NULL COMMENT '所属组织机构id',
  `organization_type` tinyint(3) NOT NULL COMMENT '组织类别 1 store 2 department',
  `attendance_range` int(11) NOT NULL COMMENT '打卡范围,单位:米',
  `deleted` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否已经删除 0:否 1:是',
  `created_at` datetime(3) DEFAULT CURRENT_TIMESTAMP(3),
  `updated_at` datetime(3) DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_organization_type` (`organization_id`,`organization_type`)
) ENGINE=InnoDB AUTO_INCREMENT=132 DEFAULT CHARSET=utf8mb4 COMMENT='网点打卡距离配置表';


--
-- Table structure for table `staff_work_face_verify_record`
--



CREATE TABLE `staff_work_face_verify_record` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `staff_info_id` int(10) unsigned DEFAULT NULL COMMENT '员工工号',
  `organization_id` varchar(10) DEFAULT NULL COMMENT '所属组织机构id',
  `organization_type` tinyint(3) unsigned DEFAULT NULL COMMENT '组织类别 1 store 2 department',
  `attendance_date` date DEFAULT NULL COMMENT '考勤日期',
  `verify_channel` tinyint(3) unsigned DEFAULT NULL COMMENT '验证渠道 阿里 0  腾讯 1 百度 2 ai 3 原图底片 4（验证结果存成功 1）5 静默活体',
  `success_enabled` tinyint(3) unsigned DEFAULT NULL COMMENT '是否成功 0:否；1:是',
  `image_path` varchar(100) DEFAULT NULL COMMENT '考勤照片',
  `image_bucket` varchar(63) DEFAULT NULL COMMENT '考勤照片所属bucket',
  `device_type` varchar(256) DEFAULT NULL COMMENT '设备类型',
  `created_at` datetime(3) DEFAULT CURRENT_TIMESTAMP(3),
  `updated_at` datetime(3) DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
  PRIMARY KEY (`id`),
  KEY `idx_create_time` (`created_at`)
) ENGINE=InnoDB AUTO_INCREMENT=11339240 DEFAULT CHARSET=utf8mb4 COMMENT='员工人脸识别记录表';


--
-- Table structure for table `statistic_for_overtime`
--



CREATE TABLE `statistic_for_overtime` (
  `store_id` varchar(10) NOT NULL COMMENT '网点编码',
  `date_at` date NOT NULL COMMENT '统计日期',
  `delivery_num` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '派件数量',
  `pickup_num` int(10) NOT NULL DEFAULT '0' COMMENT '揽件数量',
  `attendance_time` int(11) NOT NULL DEFAULT '0' COMMENT 'flag =1 所有人考勤时长 除了缺卡情况其他都算 单位（分钟）flag =2 记录当天网点出勤总人次',
  `attendance_time_for_37` int(11) NOT NULL DEFAULT '0' COMMENT 'DC officer 职位的考勤时长 单位（分钟）',
  `number_for_37` int(11) NOT NULL DEFAULT '0' COMMENT 'DC officer 当天网点总人数',
  `flag` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '1 一期 dc officer 相关 2 二期 shop 相关',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`store_id`,`date_at`),
  KEY `idx_date` (`date_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='加班申请 展示数据 用于审批参考';


--
-- Table structure for table `sys_attachment`
--



CREATE TABLE `sys_attachment` (
  `id` varchar(32) NOT NULL DEFAULT '' COMMENT '主键',
  `oss_bucket_type` varchar(50) DEFAULT NULL COMMENT '上传图片类型',
  `oss_bucket_key` varchar(32) DEFAULT NULL COMMENT '上传图片关联外键',
  `bucket_name` varchar(63) DEFAULT NULL COMMENT ' oss bucketName',
  `object_key` varchar(100) DEFAULT NULL COMMENT 'oss 对象 key 值',
  `original_name` varchar(200) DEFAULT NULL COMMENT '上传图片原始名称',
  `deleted` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否已经删除 0:否 1:是',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_type_key_obk` (`oss_bucket_type`,`oss_bucket_key`,`object_key`),
  KEY `index_oss_bucket_key` (`oss_bucket_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='图片上传';


--
-- Table structure for table `sys_store_goods`
--



CREATE TABLE `sys_store_goods` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `store_cate_id` varchar(150) NOT NULL COMMENT '网点id',
  `sys_store_cate` varchar(50) NOT NULL COMMENT '网点名称',
  `goods_id` varchar(100) DEFAULT NULL COMMENT '商品id',
  `create_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COMMENT='网点商品查看关联表';


--
-- Table structure for table `thailand_holiday`
--



CREATE TABLE `thailand_holiday` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `day` varchar(10) NOT NULL DEFAULT '' COMMENT '对应月份 2019-04-22',
  `type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '对应类型 0-默认 泰国法定公休 不包括周六日重叠日期 1-周六日重叠日期（原法定ph） 2- 针对泰国法律 ph当天为周六日 顺延一天休息的日期（针对总部）',
  `holiday_type` tinyint(4) DEFAULT '0' COMMENT '菲律宾假日类型分为 1 RH 2 SH',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=52 DEFAULT CHARSET=utf8mb4 COMMENT='泰国节假日表';


--
-- Table structure for table `ticket`
--



CREATE TABLE `ticket` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `item_type` tinyint(1) DEFAULT NULL COMMENT '1=手机、2=笔记本电脑、3=台式机、4其他',
  `item_code` varchar(50) DEFAULT NULL COMMENT '设备编码(1-50)',
  `line_id` varchar(20) DEFAULT NULL COMMENT 'lineId (1-20)',
  `mobile` varchar(20) DEFAULT NULL COMMENT '手机号码（默认显示员工公司手机号）(9-12)',
  `anydesk_id` varchar(20) DEFAULT NULL COMMENT 'ID Anydesk(1-20)',
  `info` varchar(500) DEFAULT NULL COMMENT '问题详情',
  `created_store_id` varchar(255) DEFAULT NULL COMMENT '申请人网点id',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间（泰国时间）',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间（泰国时间）',
  `created_id` int(11) DEFAULT NULL COMMENT '申请人工号',
  `created_name` varchar(255) DEFAULT NULL COMMENT '申请人姓名',
  `first_deal_id` int(11) DEFAULT NULL COMMENT '第一处理人工号',
  `first_deal_name` varchar(255) DEFAULT NULL COMMENT '第一处理人姓名',
  `deal_id` int(11) DEFAULT NULL COMMENT '最后处理人工号',
  `deal_name` varchar(255) DEFAULT NULL COMMENT '最后处理人姓名',
  `status` tinyint(1) DEFAULT '1' COMMENT '1待回复，2已回复，3已关闭',
  `created_department_id` int(10) DEFAULT NULL COMMENT '申请人部门id',
  `created_department_name` varchar(255) DEFAULT NULL COMMENT '申请人部门名字',
  `created_job_title_id` int(10) DEFAULT NULL COMMENT '申请人职位id',
  `created_job_title_name` varchar(255) DEFAULT NULL COMMENT '申请人职位名字',
  `created_store_name` varchar(255) DEFAULT NULL COMMENT '申请人网点名字',
  `pics` varchar(1000) DEFAULT NULL COMMENT '图片地址',
  PRIMARY KEY (`id`),
  KEY `idx_created_id` (`created_id`) USING BTREE COMMENT '创建人id'
) ENGINE=InnoDB AUTO_INCREMENT=10923 DEFAULT CHARSET=utf8mb4;


--
-- Table structure for table `ticket_item_type`
--



CREATE TABLE `ticket_item_type` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `zh-CN` varchar(255) DEFAULT NULL COMMENT '中文名',
  `th` varchar(255) DEFAULT NULL COMMENT '泰文名',
  `en` varchar(255) DEFAULT NULL COMMENT '英文名',
  `sort` tinyint(2) DEFAULT '10' COMMENT '排序=正序',
  PRIMARY KEY (`id`),
  KEY `idx_sort` (`sort`) USING BTREE COMMENT '筛选根据sort排序'
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4;


--
-- Table structure for table `ticket_log`
--



CREATE TABLE `ticket_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ticket_id` int(11) DEFAULT NULL COMMENT 'ticket表id',
  `created_id` int(11) DEFAULT NULL COMMENT '创建人id',
  `created_type` tinyint(1) DEFAULT NULL COMMENT '1员工，2审核回复',
  `mark` varchar(500) DEFAULT NULL COMMENT '内容',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间，泰国时间',
  `status` tinyint(1) DEFAULT '1' COMMENT '1提交，2回复，3关闭',
  `is_pic` tinyint(1) DEFAULT '0' COMMENT '0不是图片，1是图片（mark是个地址）',
  `avatar` varchar(255) DEFAULT '' COMMENT '创建人头像',
  PRIMARY KEY (`id`),
  KEY `idx_ticket_id` (`ticket_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=40824 DEFAULT CHARSET=utf8mb4;


--
-- Table structure for table `tr_language`
--



CREATE TABLE `tr_language` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) DEFAULT NULL COMMENT '名字',
  `value` varchar(50) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COMMENT='语言';


--
-- Table structure for table `tr_menu`
--



CREATE TABLE `tr_menu` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `parentid` int(11) DEFAULT '0' COMMENT '父级',
  `type` tinyint(4) DEFAULT '1' COMMENT '权限类型，1菜单，2操作,3按钮',
  `name` varchar(100) DEFAULT NULL COMMENT '名字',
  `project_admin` varchar(50) DEFAULT NULL COMMENT '项目管理员',
  `permission_staffs` varchar(2000) DEFAULT NULL COMMENT '权限人员',
  `stats` int(2) DEFAULT '1' COMMENT '0未启用，1启用',
  `os` varchar(100) DEFAULT NULL COMMENT '系统oa',
  `langs` varchar(255) DEFAULT NULL COMMENT '语言',
  `edit_staff` varchar(50) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=107 DEFAULT CHARSET=utf8mb4 COMMENT='翻译菜单';


--
-- Table structure for table `training_operate_log`
--



CREATE TABLE `training_operate_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `task_no` varchar(25) NOT NULL DEFAULT '' COMMENT '任务编号',
  `action_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '1 创建 2 编辑 3 审核',
  `operator_id` int(11) NOT NULL COMMENT '操作人ID',
  `training_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '培训类型',
  `training_organization` tinyint(4) NOT NULL DEFAULT '0' COMMENT '培训机构 1 EHSMAN 2 EHS部门 3 外部政府机构',
  `lecturer` varchar(100) NOT NULL DEFAULT '' COMMENT '培训讲师',
  `training_place_id` varchar(25) NOT NULL DEFAULT '' COMMENT '培训地点ID',
  `training_place` varchar(100) NOT NULL DEFAULT '' COMMENT '培训地点',
  `start_time` datetime NOT NULL DEFAULT '1970-01-01 00:00:00' COMMENT '培训开始时间',
  `end_time` datetime NOT NULL DEFAULT '1970-01-01 00:00:00' COMMENT '培训结束时间',
  `staff_ids` text NOT NULL COMMENT '员工工号',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '编辑时间',
  `version` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '消息版本',
  PRIMARY KEY (`id`),
  KEY `idx_task_no` (`task_no`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COMMENT='flash 培训任务操作记录表';


--
-- Table structure for table `training_people`
--



CREATE TABLE `training_people` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `task_no` varchar(25) NOT NULL DEFAULT '' COMMENT '任务编号',
  `operator_id` int(11) NOT NULL COMMENT '操作人ID',
  `training_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '培训类型',
  `staff_id` int(11) NOT NULL DEFAULT '0' COMMENT '员工ID',
  `status` int(11) NOT NULL DEFAULT '0' COMMENT ' 1 有效 9 删除',
  `is_join` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '是否参与：1待定，2参与，3拒绝',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_task_staff` (`task_no`,`staff_id`)
) ENGINE=InnoDB AUTO_INCREMENT=64 DEFAULT CHARSET=utf8mb4 COMMENT='flash 培训人员管理表';


--
-- Table structure for table `training_pool`
--



CREATE TABLE `training_pool` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `staff_id` int(11) NOT NULL DEFAULT '0' COMMENT '员工ID',
  `training_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '培训类型 1 - 12',
  `sys_store_id` varchar(25) NOT NULL DEFAULT '' COMMENT '网点',
  `manager_region` int(11) NOT NULL DEFAULT '0' COMMENT '管理大区',
  `manager_piece` int(11) NOT NULL DEFAULT '0' COMMENT '管理片区',
  `job_title` int(11) NOT NULL DEFAULT '0' COMMENT '职位',
  `job_title_level` tinyint(2) NOT NULL DEFAULT '0' COMMENT '职等1.Staff/2.Supervisor/3.Manager/4.Executive',
  `training_files` text COMMENT '培训文件，json格式',
  `node_department_id` int(11) NOT NULL DEFAULT '0' COMMENT '所属部门',
  `sys_department_id` int(11) NOT NULL DEFAULT '0' COMMENT '所属一级部门',
  `hire_date` date NOT NULL DEFAULT '1970-01-01' COMMENT '入职日期',
  `training_status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '1 未培训 2 等待培训 3 以培训 4 未通过',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_train_staff` (`training_type`,`staff_id`),
  KEY `idx_staff` (`staff_id`)
) ENGINE=InnoDB AUTO_INCREMENT=372444 DEFAULT CHARSET=utf8mb4 COMMENT='flash 培训添加池';


--
-- Table structure for table `training_task`
--



CREATE TABLE `training_task` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `task_no` varchar(25) NOT NULL DEFAULT '' COMMENT '任务编号',
  `training_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '培训类型',
  `training_organization` tinyint(4) NOT NULL DEFAULT '0' COMMENT '培训机构 1 EHSMAN 2 EHS部门 3 外部政府机构',
  `lecturer` varchar(100) NOT NULL DEFAULT '' COMMENT '培训讲师',
  `training_place_id` varchar(25) NOT NULL DEFAULT '' COMMENT '培训地点ID',
  `training_place` varchar(100) NOT NULL DEFAULT '' COMMENT '培训地点',
  `training_files` text COMMENT '培训文件，json格式',
  `start_time` datetime NOT NULL DEFAULT '1970-01-01 00:00:00' COMMENT '培训开始时间',
  `end_time` datetime NOT NULL DEFAULT '1970-01-01 00:00:00' COMMENT '培训结束时间',
  `time_long_hour` smallint(5) unsigned NOT NULL DEFAULT '0' COMMENT '预计时长，小时',
  `time_long_minute` tinyint(2) unsigned NOT NULL DEFAULT '0' COMMENT '预计时长，分钟',
  `version` tinyint(255) unsigned NOT NULL DEFAULT '1' COMMENT '发送消息通知版本',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '培训状态 1 待培训 2 待审核 3 已完成 4 已删除',
  `create_id` int(11) NOT NULL DEFAULT '0' COMMENT '创建人ID',
  `handler_id` int(11) NOT NULL DEFAULT '0' COMMENT '编辑人ID',
  `handler_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后编辑时间',
  `audited_id` int(11) NOT NULL DEFAULT '0' COMMENT '审核人ID',
  `audited_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '审核时间',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '编辑时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_task_no` (`task_no`),
  KEY `idx_training_type` (`training_type`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COMMENT='flash 培训任务管理表';


--
-- Table structure for table `translations`
--



CREATE TABLE `translations` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `lang` varchar(40) DEFAULT '',
  `t_key` varchar(100) DEFAULT '',
  `t_value` text,
  `type` int(2) DEFAULT '1' COMMENT '未启用',
  `os` varchar(30) DEFAULT 'oa' COMMENT '系统',
  `edit_staff` varchar(50) DEFAULT NULL COMMENT '编辑人',
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_key` (`t_key`,`os`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=179609 DEFAULT CHARSET=utf8mb4 COMMENT='语言翻译';


--
-- Table structure for table `translations_temp`
--



CREATE TABLE `translations_temp` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `lang` varchar(40) DEFAULT '',
  `t_key` varchar(100) DEFAULT '',
  `t_value` text,
  `type` int(2) DEFAULT '1' COMMENT '未启用',
  `os` varchar(30) DEFAULT 'oa' COMMENT '系统',
  `edit_staff` varchar(50) DEFAULT NULL COMMENT '编辑人',
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_key` (`t_key`,`os`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=97001 DEFAULT CHARSET=utf8mb4 COMMENT='语言翻译';


--
-- Table structure for table `vehicle_info`
--



CREATE TABLE `vehicle_info` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `vehicle_brand` varchar(20) DEFAULT '1' COMMENT '车辆品牌',
  `vehicle_model` varchar(20) DEFAULT '1' COMMENT '车辆型号',
  `vehicle_size` varchar(20) DEFAULT '1' COMMENT '车型',
  `plate_number` varchar(20) DEFAULT '' COMMENT '车牌号',
  `buy_date` date DEFAULT NULL COMMENT '购买日期',
  `oil_number` char(20) DEFAULT NULL COMMENT '油卡号',
  `oil_type` tinyint(4) DEFAULT '1' COMMENT '油类型',
  `oil_company` tinyint(4) DEFAULT '1' COMMENT '油卡企业',
  `vehicle_img` varchar(200) DEFAULT '' COMMENT '车辆图片',
  `driving_licence_img` varchar(512) DEFAULT '' COMMENT '驾驶证图片',
  `uid` int(10) unsigned DEFAULT NULL COMMENT '车辆信息所属员工id',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除(1:是, 0:否)',
  `money` int(11) DEFAULT NULL COMMENT '押金',
  `is_open` int(11) DEFAULT '0' COMMENT '0未开通，1已开通',
  `open_date` date DEFAULT NULL COMMENT '开通日期',
  `is_cut_money` tinyint(4) DEFAULT '0' COMMENT '0=未扣款，1=扣款',
  `balance` int(11) DEFAULT '0' COMMENT '剩余金额',
  `unit_price` int(11) DEFAULT '0' COMMENT '单价',
  `license_location` varchar(512) NOT NULL DEFAULT '' COMMENT '上牌地点',
  `vehicle_type` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '车辆类型: 0-bike; 1-van',
  `vehicle_source` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '车辆来源：1-个人车辆；2-租用公司车辆; 3-借用车辆；',
  `vehicle_start_date` date DEFAULT NULL COMMENT '用车开始日期',
  `engine_number` varchar(32) NOT NULL DEFAULT '' COMMENT '发动机号',
  `registration_certificate_img` varchar(255) NOT NULL DEFAULT '' COMMENT '车辆登记证书图片',
  `insurance_policy_number` varchar(64) NOT NULL DEFAULT '' COMMENT '车辆保险单号',
  `insurance_start_date` date DEFAULT NULL COMMENT '保险开始日期',
  `insurance_end_date` date DEFAULT NULL COMMENT '保险结束日期',
  `vehicle_tax_expiration_date` date DEFAULT NULL COMMENT '车辆税失效时间',
  `vehicle_tax_certificate_img` varchar(255) NOT NULL DEFAULT '' COMMENT '车辆税证明图片',
  `driver_license_type` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '驾照类型',
  `driver_license_type_other_text` varchar(255) NOT NULL DEFAULT '' COMMENT '驾照其他类型时,手动输入',
  `driver_license_number` varchar(128) NOT NULL DEFAULT '' COMMENT '驾照号码',
  `driver_license_start_date` date DEFAULT NULL COMMENT '驾照开始有效日期',
  `driver_license_end_date` date DEFAULT NULL COMMENT '驾照过期日期',
  `approval_status` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '审核状态：0-待提交；1-待审核；2-审核通过；3-审核驳回',
  `approval_staff_id` varchar(32) NOT NULL DEFAULT '' COMMENT '审核人工号',
  `approval_time` datetime DEFAULT NULL COMMENT '审核时间',
  `creator_id` varchar(32) NOT NULL DEFAULT '' COMMENT '数据创建者工号',
  `editor_id` varchar(32) NOT NULL DEFAULT '' COMMENT '数据修改者工号',
  `create_channel` varchar(32) NOT NULL DEFAULT '' COMMENT '数据添加渠道: kit;fbi;',
  `approval_remark` varchar(500) NOT NULL DEFAULT '' COMMENT '审核备注',
  `vehicle_brand_text` varchar(200) DEFAULT NULL COMMENT '车辆品牌其他',
  `vehicle_model_text` varchar(200) DEFAULT NULL COMMENT '车辆型号其他',
  `formal_data` text NOT NULL COMMENT '上次审核通过数据',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uid` (`uid`) USING BTREE,
  KEY `idx_oil_number` (`oil_number`),
  KEY `idx_engine_number` (`engine_number`),
  KEY `idx_plate_number` (`plate_number`)
) ENGINE=InnoDB AUTO_INCREMENT=33141 DEFAULT CHARSET=utf8mb4 COMMENT='车辆信息表';


--
-- Table structure for table `vehicle_info_expire_remind_log`
--



CREATE TABLE `vehicle_info_expire_remind_log` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `vehicle_info_id` int(10) unsigned NOT NULL DEFAULT '0',
  `staff_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '车辆信息所属人工号',
  `staff_name` varchar(255) NOT NULL DEFAULT '' COMMENT '车辆信息所属人姓名',
  `staff_department_name` varchar(255) NOT NULL DEFAULT '' COMMENT '车辆信息所属人部门名称',
  `staff_position_name` varchar(255) NOT NULL DEFAULT '' COMMENT '车辆信息所属人职位名称',
  `staff_manager_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '车辆信息所属人上级工号',
  `expire_period` mediumint(5) unsigned NOT NULL DEFAULT '0' COMMENT '距离业务到期天数，示例: 90,60,30',
  `remind_business_type` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '提醒业务类型: 1-保险结束；2-车辆税失效；3-驾照过期',
  `business_end_date` date NOT NULL COMMENT '业务到期日期',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '数据创建时间',
  `send_time` datetime DEFAULT NULL COMMENT '消息提醒发送时间',
  `send_status` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '消息提醒发送状态:0-待发送；1-已发送；3-无需发送，已跳过',
  PRIMARY KEY (`id`),
  KEY `idx_send_status` (`send_status`)
) ENGINE=InnoDB AUTO_INCREMENT=3128 DEFAULT CHARSET=utf8mb4 COMMENT='车辆信息 - 到期提醒日志';


--
-- Table structure for table `vehicle_info_log`
--



CREATE TABLE `vehicle_info_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `staff_id` int(11) DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `text` text,
  PRIMARY KEY (`id`),
  KEY `idx_staff_id_date` (`staff_id`,`created_at`)
) ENGINE=InnoDB AUTO_INCREMENT=25945 DEFAULT CHARSET=utf8mb4 COMMENT='车辆修改记录表';


--
-- Table structure for table `vehicle_mileage`
--



CREATE TABLE `vehicle_mileage` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `serial_no` varchar(32) DEFAULT NULL COMMENT '序列号',
  `mileage_date` date DEFAULT NULL COMMENT '里程日期',
  `start_kilometres` int(11) unsigned DEFAULT NULL COMMENT '上班公里数(m)',
  `started_img` varchar(200) DEFAULT '' COMMENT '上班里程图片',
  `started_bucket` varchar(200) DEFAULT '' COMMENT '上班里程照片所属bucket',
  `started_path` varchar(200) DEFAULT '' COMMENT '上班里程照片所属path',
  `end_kilometres` int(11) unsigned DEFAULT NULL COMMENT '下班公里数(m)',
  `end_img` varchar(200) DEFAULT '' COMMENT '上班里程照片',
  `end_bucket` varchar(200) DEFAULT '' COMMENT '上班里程照片所属bucket',
  `end_path` varchar(200) DEFAULT '' COMMENT '上班里程照片所属path',
  `status` tinyint(4) DEFAULT '0' COMMENT '状态 1 待审核 2 通过 3 驳回 4 撤销',
  `is_push` tinyint(4) DEFAULT '0' COMMENT '推送状态',
  `reject_reason` varchar(50) DEFAULT '' COMMENT '驳回原因',
  `apply_user` int(10) unsigned DEFAULT '0' COMMENT '申请人',
  `approve_user` int(11) DEFAULT '0' COMMENT '审批人',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `apply_user` (`apply_user`),
  KEY `approve_user` (`approve_user`)
) ENGINE=InnoDB AUTO_INCREMENT=30638 DEFAULT CHARSET=utf8mb4 COMMENT='车辆里程表';


--
-- Table structure for table `wms_goods`
--



CREATE TABLE `wms_goods` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '编号',
  `bar_code` char(20) DEFAULT '' COMMENT '条形码',
  `goods_name_en` varchar(100) DEFAULT '',
  `goods_name_th` varchar(100) DEFAULT '',
  `goods_name_zh` varchar(100) DEFAULT '' COMMENT '物品名称',
  `image_path` varchar(255) DEFAULT '' COMMENT '物品图片',
  `nuit_en` varchar(50) DEFAULT '',
  `nuit_th` varchar(50) DEFAULT '',
  `nuit_zh` varchar(50) DEFAULT '' COMMENT '单位',
  `nuit_detail` int(11) DEFAULT '0' COMMENT '单位明细',
  `price` decimal(8,2) DEFAULT NULL COMMENT '价格',
  `specification` char(100) DEFAULT NULL COMMENT '规格',
  `deleted` tinyint(4) DEFAULT '0',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `udpated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_barcode` (`bar_code`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=145 DEFAULT CHARSET=utf8mb4 COMMENT='物品列表';


--
-- Table structure for table `wms_inventory`
--



CREATE TABLE `wms_inventory` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `sys_store_id` varchar(30) NOT NULL DEFAULT '' COMMENT '网点ID',
  `bar_code` varchar(10) NOT NULL DEFAULT '' COMMENT '耗材 bar code',
  `store_reserve_count` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '网点剩余库存数量',
  `operator_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '编辑人id',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_store_barcode` (`sys_store_id`,`bar_code`)
) ENGINE=InnoDB AUTO_INCREMENT=11915 DEFAULT CHARSET=utf8mb4 COMMENT='物料盘库表';


--
-- Table structure for table `wms_order`
--



CREATE TABLE `wms_order` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `serial_no` varchar(32) DEFAULT NULL COMMENT '序列号',
  `order_id` char(20) DEFAULT '' COMMENT '订单编号',
  `organization_id` char(10) NOT NULL DEFAULT '' COMMENT '网点编号',
  `shipping_user` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '收货人姓名',
  `consignee_phone` char(20) DEFAULT '' COMMENT '联系方式',
  `consignee_address` varchar(255) DEFAULT '' COMMENT '详细地址',
  `province_code` varchar(50) DEFAULT '' COMMENT '省',
  `city_code` varchar(50) DEFAULT '' COMMENT '市',
  `district_code` varchar(50) DEFAULT '' COMMENT '区',
  `postal_code` char(10) DEFAULT '' COMMENT '邮编',
  `reason_application` text NOT NULL COMMENT '申请理由',
  `reason` text COMMENT '驳回原因',
  `order_status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '订单状态 1 待审核 2 审核通过 3 驳回',
  `is_push` tinyint(4) DEFAULT '0' COMMENT '推送',
  `apply_user` int(11) DEFAULT '0' COMMENT '申请人',
  `approve_user` int(11) DEFAULT '0' COMMENT '审批人',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `wf_role` varchar(50) NOT NULL DEFAULT '' COMMENT '关联流程',
  `result` varchar(200) DEFAULT '' COMMENT '返回结果',
  `succeed` tinyint(1) DEFAULT NULL COMMENT '是否成功',
  PRIMARY KEY (`id`),
  KEY `order_id` (`order_id`),
  KEY `index_store_create_time_status` (`organization_id`,`created_at`,`order_status`)
) ENGINE=InnoDB AUTO_INCREMENT=16717 DEFAULT CHARSET=utf8mb4 COMMENT='物品订单表';


--
-- Table structure for table `wms_order_detail`
--



CREATE TABLE `wms_order_detail` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `order_id` char(32) DEFAULT NULL COMMENT '订单表ID',
  `bar_code` varchar(20) DEFAULT NULL COMMENT '条形码',
  `nuit` varchar(50) DEFAULT NULL COMMENT '规格 单位',
  `goods_name` varchar(50) DEFAULT NULL COMMENT '商品名称',
  `num` int(11) DEFAULT '1' COMMENT '购买商品数量',
  `price` decimal(8,2) DEFAULT NULL COMMENT '购买商品单价',
  `sort` smallint(6) DEFAULT NULL COMMENT '序号',
  `employ_user` varchar(50) DEFAULT NULL COMMENT '使用者(多人)',
  `branch_repertory` int(11) DEFAULT '0' COMMENT '网点库存',
  `recomment_num` int(11) DEFAULT '0' COMMENT '推荐数量',
  `approval_num` int(11) DEFAULT '0' COMMENT '审批数量',
  `modified_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `order_id` (`order_id`,`bar_code`)
) ENGINE=InnoDB AUTO_INCREMENT=150014 DEFAULT CHARSET=utf8mb4 COMMENT='订单详情表';


--
-- Table structure for table `work_day_replace`
--



CREATE TABLE `work_day_replace` (
  `staff_info_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '员工工号',
  `month` varchar(10) NOT NULL DEFAULT '' COMMENT '对应月份 2019-04',
  `date_at` date NOT NULL DEFAULT '1970-01-01' COMMENT '申请日期',
  `operator` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '配置人工号',
  `remark` varchar(80) NOT NULL DEFAULT '' COMMENT '备注',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`staff_info_id`,`month`,`date_at`),
  KEY `idx_date_at` (`date_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='员工轮休 被请假顶替 备份表';


--
-- Table structure for table `workflow`
--



CREATE TABLE `workflow` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `code` varchar(45) DEFAULT NULL COMMENT '工作流代码',
  `name` varchar(45) DEFAULT NULL COMMENT '工作流名称',
  `type` tinyint(3) DEFAULT NULL COMMENT '审批流类型 1-公用 2-自定义',
  `relate_type` tinyint(3) DEFAULT NULL COMMENT '审批流关联的审批类型',
  `description` varchar(45) DEFAULT NULL COMMENT '工作流描述',
  `flow_request` text COMMENT '审批流请求数据',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_relate_type` (`relate_type`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=22 DEFAULT CHARSET=utf8mb4 COMMENT='工作流表';


--
-- Table structure for table `workflow_node`
--



CREATE TABLE `workflow_node` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `flow_id` char(19) DEFAULT NULL COMMENT '工作流ID',
  `name` varchar(45) DEFAULT NULL COMMENT '节点名称',
  `type` tinyint(4) DEFAULT NULL COMMENT '节点类型，1-开始节点 2-中间节点 3-结束节点',
  `auditor_type` tinyint(4) DEFAULT NULL COMMENT '审核人类型，1-随机人 2-指定工号 3-上级 4-部门负责人',
  `audit_level` tinyint(3) DEFAULT NULL COMMENT '3-上级 4-部门负责人对应的级别',
  `auditor_id` varchar(1000) DEFAULT NULL COMMENT '审核人类型对应的值',
  `approval_policy` tinyint(3) DEFAULT NULL COMMENT '审批人为空时审批策略 1-自动通过 2-指定人员审批',
  `specify_approver` varchar(1000) DEFAULT NULL COMMENT '指定审批人员IDs',
  `deleted` tinyint(3) DEFAULT '0' COMMENT '是否删除',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_flow_id` (`flow_id`),
  KEY `idx_auditor_type` (`auditor_type`,`approval_policy`)
) ENGINE=InnoDB AUTO_INCREMENT=13473113 DEFAULT CHARSET=utf8mb4 COMMENT='工作流节点表 ';


--
-- Table structure for table `workflow_node_base`
--



CREATE TABLE `workflow_node_base` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `flow_id` int(11) DEFAULT NULL COMMENT '工作流ID',
  `code` varchar(50) DEFAULT '' COMMENT '子审批流code',
  `name` varchar(50) DEFAULT NULL COMMENT '节点名称',
  `type` tinyint(4) DEFAULT NULL COMMENT '节点类型，0-开始节点 1-审批节点 2-抄送节点 99-结束节点',
  `auditor_type` tinyint(4) DEFAULT NULL COMMENT '审核人类型，1随机人 2指定工号 3上级 4部门负责人 5网点负责人 6DM 7RM 8AM',
  `auditor_level` tinyint(3) DEFAULT NULL COMMENT '3-上级 4-部门负责人 类型指定的级别',
  `auditor_id` varchar(1000) DEFAULT NULL COMMENT '审核人类型对应的值',
  `approval_policy` tinyint(3) DEFAULT '1' COMMENT '审批人为空，审批策略 1-自动通过 2-指定人员审批',
  `specify_approver` varchar(1000) DEFAULT NULL COMMENT '指定审批人员IDs',
  `deleted` tinyint(3) DEFAULT '0' COMMENT '是否删除 0-未删除 1-已删除',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_flow_id` (`flow_id`),
  KEY `idx_flow_code` (`code`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=236 DEFAULT CHARSET=utf8mb4 COMMENT='工作流节点表 ';


--
-- Table structure for table `workflow_node_relate`
--



CREATE TABLE `workflow_node_relate` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '节点id',
  `flow_id` char(19) NOT NULL COMMENT '审批流id',
  `from_node_id` int(11) NOT NULL COMMENT '起始节点',
  `to_node_id` int(11) DEFAULT NULL COMMENT '下个节点',
  `valuate_formula` varchar(255) DEFAULT NULL COMMENT '条件表达式',
  `valuate_code` varchar(255) DEFAULT NULL COMMENT '条件表达式中涉及方法',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `sort` tinyint(3) DEFAULT '10' COMMENT '排序',
  `deleted` tinyint(3) DEFAULT '0' COMMENT '是否删除',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_flow_id` (`flow_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=18633701 DEFAULT CHARSET=utf8mb4 COMMENT='审批流-节点关系表';


--
-- Table structure for table `workflow_node_relate_base`
--



CREATE TABLE `workflow_node_relate_base` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '节点id',
  `flow_id` int(11) NOT NULL COMMENT '审批流id',
  `code` varchar(50) DEFAULT NULL COMMENT '子审批流code',
  `from_node_id` int(11) NOT NULL COMMENT '起始节点',
  `to_node_id` int(11) DEFAULT NULL COMMENT '下个节点',
  `valuate_formula` varchar(255) DEFAULT NULL COMMENT '条件表达式',
  `valuate_code` varchar(255) DEFAULT NULL COMMENT '条件表达式中涉及方法',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `sort` tinyint(3) DEFAULT '10' COMMENT '排序',
  `deleted` tinyint(3) DEFAULT '0' COMMENT '是否删除 0-未删除 1-已删除',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_flow_id` (`flow_id`) USING BTREE,
  KEY `idx_node_from` (`from_node_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=434 DEFAULT CHARSET=utf8mb4 COMMENT='审批流-节点关系表';

/******************************************/
/*   DatabaseName = la_backyard   */
/*   TableName = sys_office_address   */
/******************************************/
CREATE TABLE `sys_office_address` (
  `id` varchar(10) NOT NULL COMMENT '网点编码',
  `name` varchar(100) DEFAULT NULL COMMENT '名字',
  `country_code` varchar(100) DEFAULT NULL COMMENT '省',
  `province_code` varchar(100) DEFAULT NULL COMMENT '国家',
  `city_code` varchar(100) DEFAULT NULL COMMENT '城市',
  `district_code` varchar(100) DEFAULT NULL COMMENT '区号',
  `postal_code` varchar(100) DEFAULT NULL COMMENT '邮编',
  `detail_address` varchar(100) DEFAULT NULL COMMENT '地址',
  `manage_region_id` varchar(100) DEFAULT NULL COMMENT '大区',
  `manage_piece_id` varchar(100) DEFAULT NULL COMMENT '片区',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='总部网点地址'
;

CREATE TABLE `hcm_permission` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '权限ID',
  `key` varchar(100) DEFAULT NULL COMMENT '权限key',
  `name` varchar(100) DEFAULT NULL COMMENT '权限名称',
  `description` varchar(100) DEFAULT NULL COMMENT '权限描述',
  `t_key` varchar(100) DEFAULT '' COMMENT '翻译key',
  `ancestry` int(11) DEFAULT NULL COMMENT '所属上级ID',
  `is_iframe` tinyint(1) unsigned DEFAULT '0',
  `url` varchar(128) NOT NULL DEFAULT '' COMMENT '路由名称 前端使用',
  `type` tinyint(4) DEFAULT NULL COMMENT '权限类型，1菜单，2操作',
  `sort` int(11) DEFAULT NULL COMMENT '排序',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=37 DEFAULT CHARSET=utf8mb4 COMMENT='系统权限';

CREATE TABLE `hcm_staff_permission` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `staff_id` int(11) DEFAULT NULL COMMENT '用户ID',
  `permission_ids` varchar(2000) DEFAULT NULL COMMENT '用户权限列表',
  `is_granted` tinyint(4) DEFAULT NULL,
  `last_updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_staff_id` (`staff_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2872 DEFAULT CHARSET=utf8mb4 COMMENT='hcm用户权限表';

ALTER TABLE hr_interview_offer
ADD `deminimis_benefits` int(11) DEFAULT '0' COMMENT '最低福利-无税金补贴',
ADD `performance_allowance` int(11) DEFAULT '0' COMMENT '绩效补贴',
ADD `other_non_taxable_allowance` int(11) DEFAULT '0' COMMENT '其他无税金补贴',
ADD `recommended` int(11) DEFAULT '0' COMMENT '推荐补贴' ;






