<?php


/**
 * Author: Bruce
 * Date  : 2024-05-27 19:25
 * Description:
 */


use FlashExpress\bi\App\Server\AsyncImportTaskServer;
use FlashExpress\bi\App\Models\backyard\WinHrAsyncImportTaskModel;
use FlashExpress\bi\App\Server\HcServer;


class HcImportTask extends BaseTask
{

    /**
     * 异步导入 hc批量申请
     * @return bool
     */
    public function addAction()
    {
        $asyncImportTaskServer = new AsyncImportTaskServer();
        $taskInfo = $asyncImportTaskServer->getTaskInfo(WinHrAsyncImportTaskModel::BATCH_ADD_HC);

        if(empty($taskInfo['data'])) {
            echo "数据为空";
            return false;
        }
        $server = new HcServer();
        $this->lang = $taskInfo['args']['lang'];
        if (in_array($this->lang, ['zh', 'zh-CN'])) {
            $this->lang = 'zh-CN';
        }

        $server->lang = $this->lang;
        $result = $server->batchInsertHc($taskInfo['data'], $taskInfo['staff_id'], $taskInfo['result_file_name']);

        $data['id']             = $taskInfo['task_id'];

        if($result['code'] != 1) {
            echo '异步导入执行失败';
            $this->logger->write_log(['HcImportTask_addAction_task_id' => $taskInfo['task_id']], 'error');
            $asyncImportTaskServer->updateTask($data);

            return false;
        }
        $data['fail_number']    = empty($result['data']['error_num']) ? 0 : $result['data']['error_num'];
        $data['result_path']    = empty($result['data']['file_url']) || $data['fail_number'] == 0 ? '' : $result['data']['file_url'];
        $data['success_number'] = empty($result['data']['success_num']) ? 0 : $result['data']['success_num'];

        $asyncImportTaskServer->updateTask($data);
    }

    /**
     * 异步导入 hc批量作废
     * @return bool
     * @throws \FlashExpress\bi\App\library\Exception\BusinessException
     */
    public function nullifyAction()
    {
        $asyncImportTaskServer = new AsyncImportTaskServer();
        $taskInfo = $asyncImportTaskServer->getTaskInfo(WinHrAsyncImportTaskModel::BATCH_VOID_HC);

        if(empty($taskInfo['data'])) {
            echo "数据为空";
            return false;
        }
        $server = new HcServer();
        $this->lang = $taskInfo['args']['lang'];
        if (in_array($this->lang, ['zh', 'zh-CN'])) {
            $this->lang = 'zh-CN';
        }

        $server->lang = $this->lang;
        $result = $server->batchNullifyHc($taskInfo['data'], $taskInfo['staff_id'], $taskInfo['result_file_name']);

        $data['id']             = $taskInfo['task_id'];

        if($result['code'] != 1) {
            echo '异步导入执行失败';
            $this->logger->write_log(['HcImportTask_nullifyAction_task_id' => $taskInfo['id']], 'error');

            $asyncImportTaskServer->updateTask($data);
            return false;
        }

        $data['fail_number']    = empty($result['data']['error_num']) ? 0 : $result['data']['error_num'];
        $data['result_path']    = empty($result['data']['file_url']) || $data['fail_number'] == 0 ? '' : $result['data']['file_url'];
        $data['success_number'] = empty($result['data']['success_num']) ? 0 : $result['data']['success_num'];

        $asyncImportTaskServer->updateTask($data);
    }


    /**
     * 异步导入 hc批量修改需求人数
     * @return bool
     */
    public function edit_demandAction()
    {
        $asyncImportTaskServer = new AsyncImportTaskServer();
        $taskInfo = $asyncImportTaskServer->getTaskInfo(WinHrAsyncImportTaskModel::BATCH_EDIT_DEMAND_NUM_HC);

        if(empty($taskInfo['data'])) {
            echo "数据为空";
            return false;
        }
        $server = new HcServer();
        $this->lang = $taskInfo['args']['lang'];
        if (in_array($this->lang, ['zh', 'zh-CN'])) {
            $this->lang = 'zh-CN';
        }

        $server->lang = $this->lang;
        $result = $server->batchUpdateHc($taskInfo['data'], $taskInfo['staff_id'], $taskInfo['result_file_name']);

        $data['id']             = $taskInfo['task_id'];

        if($result['code'] != 1) {
            echo '异步导入执行失败';
            $this->logger->write_log(['HcImportTask_nullifyAction_task_id' => $taskInfo['id']], 'error');
            $asyncImportTaskServer->updateTask($data);

            return false;
        }

        $data['fail_number']    = empty($result['data']['error_num']) ? 0 : $result['data']['error_num'];
        $data['result_path']    = empty($result['data']['file_url']) || $data['fail_number'] == 0 ? '' : $result['data']['file_url'];
        $data['success_number'] = empty($result['data']['success_num']) ? 0 : $result['data']['success_num'];

        $asyncImportTaskServer->updateTask($data);
    }

    /**
     * HC批量修改优先级
     * @return bool
     */
    public function edit_priorityAction()
    {
        $asyncImportTaskServer = new AsyncImportTaskServer();
        $taskInfo = $asyncImportTaskServer->getTaskInfo(WinHrAsyncImportTaskModel::BATCH_EDIT_PRIORITY_NUM_HC);

        if(empty($taskInfo['data'])) {
            echo "数据为空";
            return false;
        }
        $server = new HcServer();
        $this->lang = $taskInfo['args']['lang'];
        if (in_array($this->lang, ['zh', 'zh-CN'])) {
            $this->lang = 'zh-CN';
        }

        $data['id']             = $taskInfo['task_id'];

        $server->lang = $this->lang;
        $result = $server->BatchPriority($taskInfo['data'], $taskInfo['staff_id'], $taskInfo['result_file_name']);

        if(empty($result['success_num']) && empty($result['fail_num']) && empty($result['file_url'])) {
            echo '异步导入执行失败';
            $this->logger->write_log(['HcImportTask_nullifyAction_task_id' => $taskInfo['id']], 'error');
            $asyncImportTaskServer->updateTask($data);
            return false;
        }

        $data['fail_number']    = empty($result['fail_num']) ? 0 : $result['fail_num'];
        $data['result_path']    = empty($result['file_url']) || $data['fail_number'] == 0 ? '' : $result['file_url'];
        $data['success_number'] = empty($result['success_num']) ? 0 : $result['success_num'];

        $asyncImportTaskServer->updateTask($data);
    }

    /**
     * HC批量审批，将导入文件的数据 写入数据 数据库中。
     * 真正 处理审批的逻辑 在by:php app/cli.php hc dealhctask
     * @return bool
     */
    public function audit_batchAction()
    {
        $asyncImportTaskServer = new AsyncImportTaskServer();
        $taskInfo = $asyncImportTaskServer->getTaskInfo(WinHrAsyncImportTaskModel::BATCH_AUDIT_HC);

        if(empty($taskInfo['data'])) {
            echo "数据为空";
            return false;
        }
        $server = new HcServer();
        $this->lang = $taskInfo['args']['lang'];
        if (in_array($this->lang, ['zh', 'zh-CN'])) {
            $this->lang = 'zh-CN';
        }

        $data['id']             = $taskInfo['task_id'];

        $server->lang = $this->lang;
        $result = $server->dealAgentApproval($taskInfo['staff_id'], $taskInfo['data']);

        if($result['code'] != 1) {
            echo '异步导入执行失败';
            $this->logger->write_log(['HcImportTask_audit_batchAction_task_id' => $taskInfo['id']], 'error');
            $asyncImportTaskServer->updateTask($data);

            return false;
        }
        //将创建的审批任务id 回写到  导入任务中。
        $data['relate_id'] = $result['data']['task_id'];
        $asyncImportTaskServer->updateTask($data, WinHrAsyncImportTaskModel::STATE_WAIT_EXECUTE);
    }


}