<?php

namespace FlashExpress\bi\App\Repository;

use FlashExpress\bi\App\Models\StaffWorkAttendance;


class StaffWorkAttendanceRepository extends BaseRepository
{

    public function initialize()
    {

        parent::initialize();
    }

    /**
     * 查询时间内打卡记录
     * @Access  public
     * @Param   $paramIn Array
     * @Return  array 员工打卡记录
     */
    public function getPunchCardData($paramIn = [])
    {
        $reissueCardDate = $paramIn['reissue_card_date'];
        $staffId         = $paramIn['staff_id'];
        $day             = date('Y-m-d', strtotime($reissueCardDate));
        $punchCardSql    = "SELECT * FROM `staff_work_attendance` 
                           WHERE `staff_info_id` =" . $staffId . " 
                           AND `attendance_date`='" . $day . "'";
        $data            = $this->getDI()->get('db_rby')->query($punchCardSql);
        $returnData      = $data->fetch(\Phalcon\Db::FETCH_ASSOC);
        return $returnData;
    }

    /**
     * 员工补卡
     * @Access  public
     * @Param   request
     * @Return  array
     */
     public function auditInsert($paramIn = [])
     {
         
     }
}
