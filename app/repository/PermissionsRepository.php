<?php

namespace FlashExpress\bi\App\Repository;

use  FlashExpress\bi\App\Repository\SysListRepository;
use  FlashExpress\bi\App\Repository\DepartmentRepository;
use  FlashExpress\bi\App\Repository\HcRepository;
use  FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Server\SettingEnvServer;

class PermissionsRepository extends BaseRepository
{

    public function __construct()
    {
        parent::__construct();
        $this->sysList = new SysListRepository();
        $this->department = new DepartmentRepository();
        $this->hc = new HcRepository();
    }

    // Workforce Management 部门特权员工， HC全部权限
    public static function getWorkforceManagementPrivilegeStaffIds(): string
    {
        return SettingEnvServer::getSetVal('winhr_hc_all_permissions_staffs') ?: '38642,44862,48076,49000,51247,59552,23458,22574,31849,84135,86181,87082';
    }

    /**
     * 取当前登陆者hc编辑按钮对应权限
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function checkRolePermissions($userInfo = [])
    {

        $redisKey = 'checkRolePermissions:' . md5(json_encode($userInfo));
        $cache = $this->getDI()->get('redisLib');
        if ($returnData = $cache->get($redisKey)) {
            return json_decode($returnData, true);
        }


        //权限初始数组 1=有此权限，0=没有权限
        $rolePermissionsArr = [

            //是否有审批权限
            "hcApproval" => 0,

            //hc全模块基础查阅权限
            "hcBase" => 1,

            //hc添加权限
            "hcAdd" => 0,

            //hc作废权限
            "hcNullify" => 0,

            //hc修改权限
            "hcUpdate" => 0,

            //hc设置负责人工号权限
            "hcSetManager" => 0,

            //hc批量申请
            "hcAddBatch" => 0,

            //批量作废,批量修改需求数
            "hcUpdateBatch" => 0,

        ];


        //登陆者信息为空直接抛出异常
        if (empty($userInfo)) {
            return $rolePermissionsArr;
        }

        //hc超级账号
        $superAdministratorIds = env('superAdministratorIds', "");
        $superAdministratorIdsArr = explode(',', $superAdministratorIds);

        // 临时给Workforce Management 部门特权员工开通HC模块最大权限
        $wmf_staff_ids = explode(',', self::getWorkforceManagementPrivilegeStaffIds());
        $superAdministratorIdsArr = array_filter(array_unique(array_merge($superAdministratorIdsArr ?? [], $wmf_staff_ids ?? [])));
        if (in_array($userInfo['id'], $superAdministratorIdsArr)) {
            $rolePermissionsArr['hcApproval'] = 1;
            $rolePermissionsArr['hcBase'] = 1;
            $rolePermissionsArr['hcAdd'] = 1;
            $rolePermissionsArr['hcNullify'] = 1;
            $rolePermissionsArr['hcUpdate'] = 1;
            $rolePermissionsArr['hcSetManager'] = 1;
            $rolePermissionsArr['hcAddBatch'] = 1;
            $rolePermissionsArr['hcUpdateBatch'] = 1;
            $cache->set($redisKey, json_encode($rolePermissionsArr), 3600);
            return $rolePermissionsArr;
        }

        // 获取有hc批量作废,批量修改需求数权限
        $batch_hc_manager_staff_ids = SettingEnvServer::getSetVal('batch_hc_manager_staff_ids');
        $batch_hc_manager_job_title_ids = SettingEnvServer::getSetVal('batch_hc_manager_job_title_ids');
        $batch_hc_manager_departmentids = SettingEnvServer::getSetVal('batch_hc_manager_departmentids');
        if ($batch_hc_manager_staff_ids && in_array($userInfo['id'], explode(',', $batch_hc_manager_staff_ids))) {
            $rolePermissionsArr['hcUpdateBatch'] = 1;
        }
        if ($batch_hc_manager_job_title_ids && array_intersect(explode(',', $userInfo['job_title_id']), explode(',', $batch_hc_manager_job_title_ids))) {
            $rolePermissionsArr['hcUpdateBatch'] = 1;
        }
        if ($batch_hc_manager_departmentids && in_array($userInfo['node_department_id'], explode(',', $batch_hc_manager_departmentids))) {
            $rolePermissionsArr['hcUpdateBatch'] = 1;
        }
        // 获取有hc批量申请权限
        if (in_array('22',explode(',', $userInfo['position_category']))) {
            $rolePermissionsArr['hcAddBatch'] = 1;
            $rolePermissionsArr['hcUpdateBatch'] = 1;
        }

        /* 获取hc有审批权限的人 */
        $approvalFlag = $this->hc->hcApprovalStaffList($userInfo['id']);
        if ($approvalFlag) {
            $rolePermissionsArr['hcApproval'] = 1;
            $rolePermissionsArr['hcBase'] = 1;
            $rolePermissionsArr['hcAdd'] = 1;
        }
        /* 获取hc有查阅和添加权限 */

        //获取指定有查阅和新增hc权限的部门ids
        $roleDepartmentIds = env('roleDepartmentIds');

        //获取有权限的部门下角色ids
        $roleIdsData = $this->department->getRoleByDepartmentId($roleDepartmentIds);
        //获取当前登陆用户信息的角色ids
        $positionCategoryIds = $userInfo['position_category'];
        $positionCategoryIdsArr = explode(',', $positionCategoryIds);
        //比较部门下角色ids 和 用户信息下角色ids 的交集
        $position = array_intersect(array_column($roleIdsData, 'id'), $positionCategoryIdsArr);
        if ($position) {
            $rolePermissionsArr['hcBase'] = 1;
            $rolePermissionsArr['hcAdd'] = 1;
        }

        /* 获取指定有作废权限ids */
        $nullifyStaffList = $this->hc->hcNullifyStaffList($userInfo['id']);
        if ($nullifyStaffList) {
            $rolePermissionsArr['hcNullify'] = 1;
        }

        /* 获取指定有修改权限ids */
        $updateStaffList = $this->hc->hcUpdateStaffList($userInfo['id']);
        if ($updateStaffList) {
            $rolePermissionsArr['hcUpdate'] = 1;
        }
        //hc编辑按钮配置工号或角色权限
        if ($this->userInfo['hclist_edit_permission']) {
            $rolePermissionsArr['hcUpdate'] = 1;
            $rolePermissionsArr['hcNullify'] = 1;
        }

        /* 获取有设置负责人工号的权限*/
        $allow_edit_hc_manager_staffids = SettingEnvServer::getSetVal('allow_edit_hc_manager_staffids');
        $allow_edit_hc_manager_roleids = SettingEnvServer::getSetVal('allow_edit_hc_manager_roleids');
        if ($allow_edit_hc_manager_staffids && in_array($userInfo['id'], explode(',', $allow_edit_hc_manager_staffids))) {
            $rolePermissionsArr['hcSetManager'] = 1;
        }

        if ($rolePermissionsArr['hcSetManager'] === 0 && $allow_edit_hc_manager_roleids) {
            $allow_edit_hc_manager_roleids_arr = explode(',', $allow_edit_hc_manager_roleids);
            //比较部门下角色ids 和 用户信息下角色ids 的交集
            $set_manager_roleids = array_intersect($allow_edit_hc_manager_roleids_arr, $positionCategoryIdsArr);
            if ($set_manager_roleids) {
                $rolePermissionsArr['hcSetManager'] = 1;
            }
        }
        $cache->set($redisKey, json_encode($rolePermissionsArr), 3600);

        return $rolePermissionsArr;
    }

    /**
     * 判断用户"简历管理-列表"是否具有删除，修改权限
     * @param $userInfo
     * @return array
     */

    public function checkRoleResumeListPermissions($userInfo)
    {

        $staffId = $userInfo['id'];

        $rolePermissionsArr = [
            "if_update" => 0,
            "if_del" => 0,
        ];
        if (empty($userInfo)) {
            return $rolePermissionsArr;
        }

        //hc超级账号
        $superAdministratorIds = env('superAdministratorIds', "");
        $superAdministratorIdsArr = explode(',', $superAdministratorIds);
        if (in_array($staffId, $superAdministratorIdsArr)) {
            $rolePermissionsArr['if_update'] = 1;
            $rolePermissionsArr['if_del'] = 1;
            return $rolePermissionsArr;
        }

        //是否有简历管理-简历删除delCV，auth_id=12权限,  删除和更新暂时公用一个。
        $flag = (new PermissionRepository())->ifHavePermission($staffId, 12);
        //如果有=1
        if ($flag) {
            $rolePermissionsArr['if_update'] = 1;
            $rolePermissionsArr['if_del'] = 1;
        }
        return $rolePermissionsArr;
    }

    /**
     * 1. 支持配置发起薪资审批的部门和工号
     * 判断是否有发起薪资审批提交按钮权限
     *
     * @param $userinfo
     *
     */
    public static $salary_offer_departments = [];
    public static $salary_offer_staffs = [];

    public function isHasSalaryPermission($userinfo)
    {
        if (empty(self::$salary_offer_departments)) {
            $departmentIds = SettingEnvServer::getSetVal('salary_offer_departments');
            $departmentIds = explode(',', $departmentIds);
            self::$salary_offer_departments = $departmentIds;
        }
        if (empty(self::$salary_offer_staffs)) {
            $staffIds = SettingEnvServer::getSetVal('salary_offer_staffs');
            $staffIds = explode(',', $staffIds);
            self::$salary_offer_staffs = $staffIds;
        }


        return in_array($userinfo['id'], self::$salary_offer_staffs) || in_array($userinfo['node_department_id'], self::$salary_offer_departments);
    }


}