#!/bin/bash

function echo_log(){
   echo -e "\033[0;31m $1 \033[0m"
}
function echo_blue {
   echo -e "\033[47;34m $1 \033[0m"
}

function permission_add() {
    echo_blue "1 permission_add"
    if [ "$1" = "1" ];then
        echo_blue "Done，Please start `date`"
        php app/cli.php permission relationadd
        echo_blue "Done，Please end `date`"
    fi
}
echo_log "task list `date`"
permission_add


read -p "please input number: " number

case $number in
    1)
       permission_add 1
    ;;
    *)
       echo_log "input error"
    ;;
esac

