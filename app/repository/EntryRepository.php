<?php

namespace FlashExpress\bi\App\Repository;

use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\enumsMy;
use FlashExpress\bi\App\library\enumsTh;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\library\RocketMQ;
use FlashExpress\bi\App\Models\backyard\HrEntryModel;
use FlashExpress\bi\App\Models\backyard\HrhcModel;
use FlashExpress\bi\App\Models\backyard\HrInterviewOfferModel;
use FlashExpress\bi\App\Models\backyard\HrResumeModel;
use FlashExpress\bi\App\Models\backyard\HrStaffContractModel;
use FlashExpress\bi\App\Models\backyard\StaffPayrollCompanyInfoModel;
use FlashExpress\bi\App\Models\backyard\SysDepartmentModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\VehicleInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoPositionModel;
use FlashExpress\bi\App\Modules\MY\library\enumsContract;
use  FlashExpress\bi\App\Repository\HcRepository;
use FlashExpress\bi\App\Server\AddEntryServer;
use FlashExpress\bi\App\Server\ContractServer;
use FlashExpress\bi\App\Server\HcServer;
use FlashExpress\bi\App\Server\InterviewServer;
use FlashExpress\bi\App\Server\MessageQueueServer;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Server\SysServer;

class EntryRepository extends BaseRepository
{
    public function __construct()
    {
        parent::__construct();
        $this->hc = new HcRepository();

    }

    public function getStaffEntryDataList(array $staff_info_ids)
    {
        if (empty($staff_info_ids)) {
            return [];
        }
        $staffIds = getIdsStr($staff_info_ids);

        $sql        = "select 
                s.staff_info_id,
                s.name,
                s.name_en,
                s.sex,
                s.identity,
                s.mobile,
                s.email,
                s.personal_email,
                s.job_title,
                s.job_title_grade,
                s.job_title_grade_v2,
                s.job_title_level,
                s.week_working_day,
                s.sys_store_id,
                s.sys_department_id,
                s.node_department_id,
                s.state,
                s.hire_date,
                s.manger,
                s.manger as superior_staff_id,
                s.bank_no
                from 
                hr_staff_info as s where s.formal in (1,4) and s.is_sub_staff = 0 and s.staff_info_id in ($staffIds)";
        $dataObj    = $this->getDI()->get('db_rby')->query($sql);
        $returnData = $dataObj->fetchAll(\Phalcon\Db::FETCH_ASSOC);

//        $sql = "select `value`,staff_info_id from hr_staff_items where `item` = 'EQUIPMENT_COST' and staff_info_id in ($staffIds)";
//
//        $itemsData    = $this->getDI()->get('db_rby')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
//        $itemsDataArr = [];
//        foreach ($itemsData as $k => $v) {
//            if ($v["value"] > 0) {
//                $itemsDataArr[$v["staff_info_id"]]['EQUIPMENT_COST'] = bcdiv($v["value"], 100, 2);
//            }
//        }
//        foreach ($returnData as $k => $v) {
//            $returnData[$k]["equipment_cost"] = $itemsDataArr[$v["staff_info_id"]]["EQUIPMENT_COST"] ?? "";
//        }
        return $returnData;
    }



    /**
     * 入职列表
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function entryList($paramIn = [])
    {
        $searchWhere = isset($paramIn['searchWhere']) ? $paramIn['searchWhere'] : '';
        $is_sub_department = $searchWhere['is_sub_department'] ?? 2;
        unset($searchWhere['is_sub_department']);
        $pageSize = isset($paramIn['page_size']) ? $paramIn['page_size'] : 10;
        $pageNum = isset($paramIn['page_num']) ? $paramIn['page_num'] : 1;
        $no_page = isset($paramIn['no_page']) ? $paramIn['no_page'] : 0; //是否是导出操作（导出不分页）
        $staffId = isset($paramIn['staff_id']) ? $paramIn['staff_id'] : '';
        $sort = $paramIn['sort'] ?? '';
	    $authority_department_ids= $paramIn["authority_department_ids"] ?? []; //权限
	    $authority_stores_ids = $paramIn["authority_stores_ids"] ?? []; //权限
	    $is_admin = $paramIn["is_admin"] ?? enums::$is_admin['on']; //权限 1 超管
        
        
        /* 拼装sql */
        $sql = "select 
                  hr_resume.name,
                  hr_resume.nickname,
                  hr_resume.call_name,
                  hr_resume.first_name,
                  hr_resume.last_name,
                  hr_resume.first_name_en,
                  hr_resume.last_name_en,
                  hr_resume.suffix_name,
                  hr_resume.middle_name,
                  hr_resume.id as resume_id,
                  hr_resume.sex,
                  hr_resume.phone_area_code,
                  hr_resume.phone,
                  hr_resume.address_id,
                  hr_resume.nationality,
                  hr_resume.register_house_num,
                  hr_resume.register_village_num,
                  hr_resume.register_village,
                  hr_resume.register_alley,
                  hr_resume.register_street,
                  hr_resume.register_government,
                  hr_resume.register_city,
                  hr_resume.register_town,
                  hr_resume.register_country,
                  hr_resume.working_country,
                  hr_resume.recruit_channel,
                  hr_resume.resume_last_operator,
                  hr_resume.identity_validate_status as identity_status,
                  hr_entry.staff_id,
                  hr_entry.hc_id,
                  DATE_FORMAT(CONVERT_TZ(hr_entry.operate_date, '+00:00', '" . $this->timezone . "' ),'%Y-%m-%d %H:%i:%s') AS operate_date,
                  hr_resume.credentials_num,
                  hr_resume.credentials_category,
                  hr_resume.email,
                  DATE_FORMAT(CONVERT_TZ(hr_resume.created_at, '+00:00', '" . $this->timezone . "' ),'%Y-%m-%d %H:%i:%s') AS created_at,
                  hr_jd.job_name,
                  hr_jd.job_id,
                  DATE_FORMAT(CONVERT_TZ(hr_resume.date_birth, '+00:00', '" . $this->timezone . "' ),'%Y-%m-%d') AS date_birth,
                  hr_entry.status,
                  DATE_FORMAT(CONVERT_TZ(hr_entry.entry_date, '+00:00', '" . $this->timezone . "' ),'%Y-%m-%d') AS entry_date,
                  hr_entry.entry_id,
                  hr_entry.hire_type,
                  hr_entry.record_entry,
                  hc.department_id,
                  hc.worknode_id,
                  hc.hire_type as hc_hire_type,
                  hc.hire_times,
                  hr_interview_offer.position_id,
                  hr_interview_offer.equipment_cost,
                  hr_interview_offer.money,
                  hr_interview_offer.trial_salary,
                  hr_interview_offer.position,
                  hr_interview_offer.job_title_grade as job_title_grade_v2,
                  hr_interview_offer.exp,
                  hr_interview_offer.other,
                  hr_interview_offer.rental,
                  hr_interview_offer.food,
                  hr_interview_offer.dangerously,
                  hr_interview_offer.shift_id,
                  hr_interview_offer.currency,
                  hr_interview_offer.subsidy_type,
                  hr_interview_offer.project_num as offer_project_num,
                  hr_economy_ability.car_owner,
                  hr_economy_ability.car_number,
                  hr_economy_ability.place_cards,
                  hr_economy_ability.car_engine_number,
                  hr_interview.interview_id,
                  hr_interview.manager_id as higher_id,
                  hr_interview.manager_name as higher_name,
                  hr_interview.working_day_rest_type,
                  DATE_FORMAT(hr_interview_offer.work_time,'%Y-%m-%d') AS work_time
                  from  hr_entry  
                  left join hr_interview on hr_entry.interview_id=hr_interview.interview_id
                  left join hr_resume on hr_resume.id=hr_entry.resume_id
                  left join hr_jd on hr_jd.job_id=hr_interview.job_id
                  left join hr_hc as hc on hr_entry.hc_id=hc.hc_id
                  left join hr_interview_offer on hr_interview_offer.id=hr_entry.interview_offer_id
                  left join hr_economy_ability on hr_economy_ability.resume_id = hr_resume.id
                  where hr_entry.deleted=0 AND NOT EXISTS ( SELECT 1 from `hr_entry` as t_e where t_e.`interview_id` = `hr_entry`.`interview_id`  and t_e.`entry_id` > hr_entry.`entry_id` ) ";
        $sqlCount = "select 
                  count(1) 
                  from hr_entry 
                  left join hr_interview on hr_entry.interview_id=hr_interview.interview_id
                  left join hr_resume on hr_resume.id=hr_entry.resume_id
                  left join hr_jd on hr_jd.job_id=hr_interview.job_id
                  left join hr_hc as hc on hr_entry.hc_id=hc.hc_id
                  left join hr_interview_offer on hr_interview_offer.id=hr_entry.interview_offer_id
                  where hr_entry.deleted=0 AND NOT EXISTS ( SELECT 1 from `hr_entry` as t_e where t_e.`interview_id` = `hr_entry`.`interview_id`  and t_e.`entry_id` > hr_entry.`entry_id` ) ";
        if ($searchWhere) {

            foreach ($searchWhere as $k => $v) {
                //最新操作人的查询单独处理
                if (in_array($k, ['last_operator_query_type', 'resume_last_operator'])) {
                    continue;
                }

                $v = str_replace('"', '', $v);
                if ($k == 'resume_id') {
                    $sql .= ' and hr_resume.id = "' . intval($v) . '"';
                    $sqlCount .= ' and hr_resume.id = "' . intval($v) . '"';
                } elseif ($k == 'worknode_id') {
                    if($no_page && is_array($v)){
                        //导出操作追加网点过滤条件（非超管的话会有数据权限，此时网点ID是数组）
                        $ids = "'".(implode("','",$v))."'";
                        $sql .= " and hc.worknode_id in ({$ids})";
                        $sqlCount .= " and hc.worknode_id in ({$ids})";
                    }else{
                        $sql .= ' and hc.worknode_id = "' . $v . '"';
                        $sqlCount .= ' and hc.worknode_id = "' . $v . '"';
                    }

                }elseif ($no_page && $k == 'department_id' ){
                    //导出操作追加网点过滤条件
//                    $ids = implode(",",$v);
//                    $sql .= " and hc.department_id in ({$ids}) and hc.worknode_id='-1' ";
//                    $sqlCount .= " and hc.department_id in ({$ids}) and hc.worknode_id='-1' ";
                    if (!empty($v)) {
                        $deptIds = SysServer::getDepartmentConditionByParams(['department_id'=>$v,'is_sub_department'=>$is_sub_department]);
                        if (!empty($deptIds)) {
                            $ids = implode(",",$deptIds);
                            $sql .= " and hc.department_id in ({$ids}) and hc.worknode_id='-1' ";
                            $sqlCount .= " and hc.department_id in ({$ids}) and hc.worknode_id='-1' ";
                        }
                    }
                }elseif ($k == 'hc_department_id' ){
                    //所属部门
                    if (!empty($v)) {
                        $deptIds = SysServer::getDepartmentConditionByParams(['department_id'=>$v,'is_sub_department'=>$is_sub_department]);
                        if (!empty($deptIds)) {
                            $ids = implode(",",$deptIds);
                            $sql .= " and hc.department_id in ({$ids})";
                            $sqlCount .= " and hc.department_id in ({$ids})";
                        }
                    }
//                    $sql .= " and hc.department_id = " . $v;
//                    $sqlCount .= " and hc.department_id = " . $v;
                } elseif ($k == 'time_start') {
                    $createtime_s = date("Y-m-d 00:00:00", strtotime($v));
                    $sql .= ' and hr_resume.created_at>=CONVERT_TZ("' . $createtime_s . '", "' . $this->timezone . '", "+00:00")';
                    $sqlCount .= ' and hr_resume.created_at>=CONVERT_TZ("' . $createtime_s . '", "' . $this->timezone . '", "+00:00")';
                } elseif ($k == 'time_end') {
                    $createtime_e = date("Y-m-d 23:59:59", strtotime($v));
                    $sql .= ' and hr_resume.created_at<=CONVERT_TZ("' . $createtime_e . '", "' . $this->timezone . '", "+00:00")';
                    $sqlCount .= ' and hr_resume.created_at<=CONVERT_TZ("' . $createtime_e . '", "' . $this->timezone . '", "+00:00")';
                } elseif ($k == 'phone') {
                    $v = str_replace(" ", '', $v);
                    $sql .= ' and (hr_resume.phone like "%' . $v . '%" or hr_resume.name like "%' . $v . '%")';
                    $sqlCount .= ' and (hr_resume.phone like "%' . $v . '%" or hr_resume.name like "%' . $v . '%")';
                } elseif ($k == 'address_id') {
                    $sql .= ' and hr_resume.address_id="' . $v . '"';
                    $sqlCount .= ' and hr_resume.address_id="' . $v . '"';
                } elseif ($k == 'job_name') {
                    $sql .= ' and hr_jd.job_id in (select job_id from hr_jd where job_name like "%' . $v . '%")';
                    $sqlCount .= ' and hr_jd.job_id in (select job_id from hr_jd where job_name like "%' . $v . '%")';
                } elseif ($k == 'position_id') {
                    if (is_array($v)) {
                        $positionIds = getIdsStr($v);
                        $sql         .= " and hr_interview_offer.position_id IN ($positionIds) ";
                        $sqlCount    .= " and hr_interview_offer.position_id IN ($positionIds) ";
                    } else {
                        $sql      .= ' and hr_interview_offer.position_id = '.$v;
                        $sqlCount .= ' and hr_interview_offer.position_id = '.$v;
                    }
                } elseif ($k == 'entry_time_start') {
                    $entry_date_s = date("Y-m-d 00:00:00", strtotime($v));
                    $sql .= ' and hr_entry.entry_date>=\'' . $entry_date_s . '\'';
                    $sqlCount .= ' and hr_entry.entry_date>=\'' . $entry_date_s . '\'';
                } elseif ($k == 'entry_time_end') {
                    $entry_date_e = date("Y-m-d 23:59:59", strtotime($v));
                    $sql .= ' and hr_entry.entry_date<=\'' . $entry_date_e . '\'';
                    $sqlCount .= ' and hr_entry.entry_date<=\'' . $entry_date_e . '\'';
                } elseif ($k == 'work_time_start') {
                    $work_time_s = date("Y-m-d 00:00:00", strtotime($v));
                    $sql .= ' and hr_interview_offer.work_time>=\'' . $work_time_s . '\'';
                    $sqlCount .= ' and hr_interview_offer.work_time>=\'' . $work_time_s . '\'';
                } elseif ($k == 'work_time_end') {
                    $work_time_e = date("Y-m-d 23:59:59", strtotime($v));
                    $sql .= ' and hr_interview_offer.work_time<=\'' . $work_time_e . '\'';
                    $sqlCount .= ' and hr_interview_offer.work_time<=\'' . $work_time_e . '\'';
                } elseif ($k == 'record_entry') {
                    $sql .= ' and hr_entry.record_entry is not null';
                    $sqlCount .= ' and hr_entry.record_entry is not null';
                } elseif ($k == 'name') {
                    $sql .= ' and hr_resume.`name` like "%' . $v . '%"';
                    $sqlCount .= ' and hr_resume.`name` like "%' . $v . '%"';
                }elseif ($k == 'staff_id'){
                    $sql .= ' and hr_entry.staff_id = "' . intval($v) . '"';
                    $sqlCount .= ' and hr_entry.staff_id = "' . intval($v) . '"';
                } elseif ($k == 'hire_type') {
                    if (!empty($v) && is_array($v)) {
                        $ids      = "'" . (implode("','", $v)) . "'";
                        $sql      .= " and hr_entry.hire_type in ({$ids})";
                        $sqlCount .= " and hr_entry.hire_type in ({$ids})";
                    }
                //入职状态（多选）
                }elseif ($k == 'onboarding_status'){
                    if (!empty($v) && is_array($v)) {
                        $onboarding_status = "'".(implode("','",$v))."'";
                        $sql .= " and hr_entry.status in ({$onboarding_status})";
                        $sqlCount .= " and hr_entry.status in ({$onboarding_status})";
                    }
                //身份证确认状态（多选）
                }elseif ($k == 'identity_status'){
                    if (!empty($v) && is_array($v)) {
                        $onboarding_status = "'".(implode("','",$v))."'";
                        $sql .= " and hr_resume.identity_validate_status in ({$onboarding_status})";
                        $sqlCount .= " and hr_resume.identity_validate_status in ({$onboarding_status})";
                    }
                } else {
                    $sql .= ' and hr_entry.' . $k . '="' . $v . '"';
                    $sqlCount .= ' and hr_entry.' . $k . '="' . $v . '"';
                }
            }
        }

        //简历最新操作人查询
        switch ($searchWhere['last_operator_query_type']) {
            case 0://ALL
                if (!empty($searchWhere['resume_last_operator']) && $searchWhere['resume_last_operator'] > 0) {
                    $sql      .= ' AND hr_resume.resume_last_operator =  '.intval($searchWhere['resume_last_operator']);
                    $sqlCount .= ' AND hr_resume.resume_last_operator =  '.intval($searchWhere['resume_last_operator']);
                }
                break;
            case 1://为空
                $sql      .= ' AND hr_resume.resume_last_operator =  0';
                $sqlCount .= ' AND hr_resume.resume_last_operator =  0';
                break;
            case 2://不为空
                $sql      .= ' AND hr_resume.resume_last_operator >  0';
                $sqlCount .= ' AND hr_resume.resume_last_operator >  0';
                if (!empty($searchWhere['resume_last_operator']) && $searchWhere['resume_last_operator'] > 0) {
                    $sql      .= ' AND hr_resume.resume_last_operator =  '.intval($searchWhere['resume_last_operator']);
                    $sqlCount .= ' AND hr_resume.resume_last_operator =  '.intval($searchWhere['resume_last_operator']);
                }
                break;
        }

        //数据权限
        $is_menu_permission = SettingEnvServer::getMenuPermission('whr_entry_menu_list');
        $authoritySql = '';
        if($is_admin == enums::$is_admin['off'] && $is_menu_permission){
            $authoritySql = (new HcServer())->assembleAuthoritySql('',$this->userInfo);
        }
        
        if ($authoritySql) {
            $sql .= ' and ('.$authoritySql.')';
            $sqlCount .= ' and ('.$authoritySql.')';
        }

        //排序
        if (empty($sort)) {
            $sql .= ' ORDER BY hr_entry.entry_id desc';
        } else {
            $sql .= " ORDER BY {$sort}";
        }
        if ($no_page == 0) {
            $sql .= " limit " . ($pageNum - 1) * $pageSize . "," . $pageSize;
        }
        /* 获取入职列表数据 */
        $this->getDI()->get('logger')->write_log("entryList-sql:" . $sql, 'info');

        //总条数
        $pageArr['count'] = (int)$this->getDI()->get('db_rby')->fetchColumn($sqlCount);
        
        if ($pageArr['count'] > enums::EXPORT_MAX_NUM_LIMIT && $no_page) {
            throw new ValidationException($this->getTranslation()->_('export_down_num_error'));
        }
        
        $data = $this->getDI()->get('db_rby')->query($sql);
        $dataEntry = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);


        //总条数
        //$pageArr['count'] = (int)$this->getDI()->get('db_rby')->fetchColumn($sqlCount);
        //总页数
        $pageArr['pageCount'] = ceil($pageArr['count'] / $pageSize);
        //当前页 默认1
        $pageArr['pageNum'] = $pageNum;
        //每页显示多少条默认10
        $pageArr['pageSize'] = $pageSize;
        //身份证照片数量
        /* 拼装返回数组 */
        $returnData['data'] = $dataEntry ;
        $returnData['pagination'] = $pageArr ;
        
        return $returnData;
    }

    /**
     * 入职办理
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function entryAdd($paramIn = [])
    {
        $staff_info_id = $paramIn['staff_info_id'];
        $status = $paramIn['status'];
        $entryId = $paramIn['entry_id'];
        $entryDate = $paramIn['entry_date'];
        $operateDate = $paramIn['operate_date'];
        $resume_id = $paramIn['resume_id'];
        $record_entry = $paramIn['record_entry'];

        //todo 入职 到岗确认处理逻辑
        $db = $this->getDI()->get('db');

        //todo 2:更新入职表数据
        //更新入职表数据
        $update_data = [
            'staff_id'=>$staff_info_id,
            'status'=>$status,
            'entry_date'=>$entryDate,
            'operate_date'=>$operateDate,
        ];

        $db->updateAsDict('hr_entry', $update_data, 'entry_id = ' . $entryId);

        //todo 3：回写面试管理主表[START]
        //[1]查询面试ID
        $interviewData= HrEntryModel::findFirst([
            'entry_id=:hr_entry_id:',
            'bind' => ['hr_entry_id' => $entryId],
        ]);

        if (!empty($interviewData)) {
            //[2]修改面试状态为已入职
            $db->updateAsDict('hr_interview', ['state'=>40], 'interview_id = ' . $interviewData->interview_id);
        }
        //回写面试管理主表[END]

        //todo 4：生成员工电子合同数据
        //增加开关判断
        $contract_switch = SettingEnvServer::getSetVal('create_contract_switch');
        $contract_switch_data = isset($contract_switch)?json_decode($contract_switch,true):'';
        if (isset($contract_switch_data['create_contract']) && $contract_switch_data['create_contract']==1){
            $constract_staff_insert_data = $this->getStaffContractInsertData($staff_info_id,$entryDate,$resume_id,$record_entry);
            foreach ($constract_staff_insert_data as $contract_item){
                $contract_res =  $db->insertAsDict(
                    'hr_staff_contract',$contract_item
                );
                if(!$contract_res){
                    throw new \Exception('电子合同-生成电子合同失败,插入数据：'.json_encode($contract_item));
                }
            }
        }

    }


    public function fixPhContract()
    {

        $staff_info_ids = [137198,
            137199,
            137200,
            137201,
            137202,
            137203,
            137204,
            137205,
            137206,
            137207,
            137208,
            137209,
            137210,
            137211,
            137212,
            137213,
            137214,
            137215,
            137216,
            137217,
            137218,
            137219,
            137220,
            137221,
            137222,
            137223,
            137224,
            137225,
            137226,
            137227,
            137228,
            137229,
            137230,
            137231,
            137232,
            137233,
            137234,
            137235,
            137236,
            137237,
            137238,
            137239,
            137240,
            137241,
            137242,
            137243,
            137244,
            137245,
            137246,
            137247,
            137248,
            137249,
            137250,
            137251,
            137252,
            137253,
            137254,
            137255,
            137256,
            137257,
            137258,
            137260,
            137259,
            137261,
            137262,
            137263,
            137264,
            137265,
            137266,
            137267,
            137268,
            137269,
            137270,
            137271,
            137272,
            137273,
            137274,
            137275,
            137276,
            137277,
            137278,
            137279,
            137280,
            137281,
            137282,
            137283,
            137284,
            137285,
            137286,
            137287,
            137288,
            137289,
            137290,
            137291,
            137292,
            137293,
            137294,
            137295,
            137296,
            137297,
            137298,
            137299,
            137300,
            137301,
            137302,
            137303,
            137304,
            137306,
            137305,
            137307,
            137308,
            137309,
            137310,
            137311,
            137312,
            137313,
            137314,
            137315,
            137316,
            137317,
            137318,
            137319,
            137320,
            137321,
            137322,
            137323,
            137324,
            137325,
            137326,
            137327,
            137328,
            137329,
            137330,
            137331,
            137332,
            137333,
            137334,
            137335,
            137336,
            137337];

        $entry= HrEntryModel::find([
            'conditions' => 'staff_id in ({staff_info_ids:array}) ',
            'bind' => ['staff_info_ids' => $staff_info_ids]
        ])->toArray();
        //增加开关判断
        $contract_switch = SettingEnvServer::getSetVal('create_contract_switch');
        $contract_switch_data = isset($contract_switch)?json_decode($contract_switch,true):'';
        //todo 入职 到岗确认处理逻辑
        $db = $this->getDI()->get('db');
        foreach ($entry as $item) {
            $record_entry = json_decode($item['record_entry'], true);
            $entryDate = $record_entry['hire_date'];

            $constract_staff_insert_data = $this->getStaffContractInsertData($item['staff_id'],$entryDate,$item['resume_id'],$record_entry);
            var_dump($constract_staff_insert_data,$item['staff_id'],$entryDate,$item['resume_id'],$record_entry,$contract_switch_data);die;


            foreach ($constract_staff_insert_data as $contract_item){
                $contract_res =  $db->insertAsDict(
                    'hr_staff_contract',$contract_item
                );
                if(!$contract_res){
                    throw new \Exception('电子合同-生成电子合同失败,插入数据：'.json_encode($contract_item));
                }
            }

        }
    }


    /**
     * 获取入职生成的合同数据
     * @param $staff_info_id
     * @param $contract_start_date
     * @param $resume_id
     * @param $record_entry
     * @return array
     */
    public function getStaffContractInsertData($staff_info_id,$contract_start_date,$resume_id,$record_entry)
    {
        $sys_store_id       = $record_entry['sys_store_id'] ?? '';
        $job_title          = $record_entry['job_title'] ?? '';
        $nationality        = $record_entry['nationality'] ?? 0;
        $working_country    = $record_entry['working_country'] ?? 0;
        $node_department_id = $record_entry['node_department_id'] ?? 0;

        //step1: 获取员工信息
        $storeObj = SysStoreModel::findFirst([
            'conditions' => 'id = :storeId:',
            'bind'       => ['storeId' => $sys_store_id]
        ]);
        if(!empty($storeObj)){
            $store_info = $storeObj->toArray();
        }

        if(empty($nationality)){
            $staffInfo = HrStaffInfoModel::findFirst([
                'conditions' => 'staff_info_id = :staff_id:',
                'columns' => 'staff_info_id,nationality',
                'bind'       => [
                    'staff_id' => $staff_info_id,
                ],
            ]);
            $nationality = $staffInfo->nationality;
        }
        $ancestry_v3 = '';
        if (!empty($node_department_id)){
            $sysDepartment = SysDepartmentModel::findFirst([
                'conditions' => 'id = :sys_department_id:',
                'bind' => ['sys_department_id' => $node_department_id],
                'columns' => "id,name,ancestry_v3"]);
            $ancestry_v3 = $sysDepartment->ancestry_v3;
        }

        $staff_data = [
            'staff_id'            => $staff_info_id,
            'staff_name'          => $record_entry['name'] ?? '',
            'mobile'              => $record_entry['mobile'] ?? '',
            'email'               => $record_entry['email'] ?? '',
            'personal_email'      => $record_entry['personal_email'] ?? '',
            'department_id'       => $record_entry['node_department_id'] ?? 0,
            'job_title_id'        => $record_entry['job_title'] ?? 0,
            'store_id'            => $record_entry['sys_store_id'] ?? '-1',
            'manage_region'       => $store_info['manage_region'] ?? '',
            'manage_piece'        => $store_info['manage_piece'] ?? '',
            'entry_date'          => $record_entry['hire_date'],
            'resume_id'           => $resume_id,
            'nationality'         => $nationality,
        ];

        //step2:获取合同信息
        // 车辆信息表 获取车牌号、上牌地，发动机号等信息
        $vehicle_info_obj = VehicleInfoModel::findFirst([
            'columns'    => 'vehicle_source',
            'conditions' => 'uid = :uid: and deleted = 0',
            'bind'       => [
                'uid' => $staff_info_id,
            ],
        ]);
        if(!empty($vehicle_info_obj)){
            $vehicle_info = $vehicle_info_obj->toArray();
            $this->getDI()->get('logger')->write_log('getContractData vehicle_info 参数:'.json_encode($vehicle_info), 'info');
        }
        $vehicle_source = $vehicle_info["vehicle_source"] ?? 0;
        if (isCountry('TH')){
            $hireType = $record_entry['hire_type'] ?? 0;
            $hireTimes = $record_entry['hire_times'] ?? 0;
            $contract_list_data = $this->getContractDataTh($contract_start_date,$job_title,$nationality,$vehicle_source,$working_country,$ancestry_v3,[
                'hire_type' => $hireType,
                'hire_times' => $hireTimes
            ]);
        }elseif (isCountry('PH')){
            $hireType           = $record_entry['hire_type'] ?? 0;
            $hireTimes          = $record_entry['hire_times'] ?? 0;
            $conversionPermanentDate           = $record_entry['conversion_permanent_date'] ?? '';
            $contract_list_data = $this->getContractDataPH($contract_start_date, $job_title, $nationality,
                $vehicle_source, $working_country, $ancestry_v3,
                ['hire_type' => $hireType, 'hire_times' => $hireTimes,'conversion_permanent_date' => $conversionPermanentDate]);
        }elseif (isCountry('LA')){
            $contract_list_data = $this->getContractDataLA($contract_start_date,$job_title,$nationality);
        } elseif (isCountry('MY')) {
            //一线职位
            $staff_data["line_job_type"] = (new InterviewServer())->isFrontLineJob($job_title);
            $contract_list_data          = $this->getContractDataMY($staff_data, $contract_start_date, $record_entry);
        } else {
            $contract_list_data = $this->getContractData($contract_start_date, $job_title, $nationality, $vehicle_source, $working_country, $ancestry_v3);
        }

        //step3: 生成最终电子合同基本信息
        $staff_contract_insert_data = [];
        foreach ($contract_list_data as $contract_data){
            $staff_contract_insert_data[] = array_merge($staff_data,$contract_data);
        }

        //返回数据
        return $staff_contract_insert_data;

    }

    /**
     * 到岗确认-生成员工电子合同数据
     *
     * @param $staff_info_id
     * @param $entry_date
     * @param $job_title
     * @return array
     */
    public function getContractData($contract_start_date, $job_title, $nationality,$vehicle_source = 0,$working_country,$ancestry_v3){
        $ancestry_v3_arr = [];
        if ($ancestry_v3){
            $ancestry_v3_arr = explode('/', $ancestry_v3);
        }
        $contract_date_is_long = 2;
        if (isCountry("PH")){
            $contract_date_is_long = 1;
            if ($working_country == 4) {
                if (in_array(25, $ancestry_v3_arr)) {
                    // 工作所在国家=菲律宾，所在部门层级包含Flash Fulfillment id=25
                    $zcxy_contract_child_type = enums::CONTRACT_ZCXY_PH_FFM;
                } else {
                    if ($nationality == 4) {
                        // 国籍=菲律宾
                        $zcxy_contract_child_type = enums::CONTRACT_ZCXY_PH_BG;
                    } else {
                        $zcxy_contract_child_type = enums::CONTRACT_ZCXY_PH_WG;
                    }
                }
            } else {
                $zcxy_contract_child_type = enums::CONTRACT_ZCXY_PH_WG;
            }
        }else{
            $zcxy_contract_child_type = enums::CONTRACT_ZCXY_TH;
        }
        $this->getDI()->get('logger')->write_log('getContractData 参数:'.json_encode([$contract_start_date,$job_title,$nationality,$vehicle_source]), 'info');

        if (isCountry("LA")){
            $contract_start_date = "0000-00-00";
        }
        if($nationality != 1){ //非泰国国籍 合同数据
            $contract_insert_data = [

                [ //todo 劳动合同
                    'contract_start_date' => $contract_start_date, //合同起始日期（入职日期）
                    'contract_type' => enums::CONTRACT_LDHT, //合同类型：1-劳动合同
                    'contract_child_type' => enums::CONTRACT_LDHT_ENGLISH, //劳动合同子类：英文劳动合同
                    'contract_is_need' => 1, //是否需要此合同
                ],
                [ // todo 租车合同
                    'contract_start_date' => $contract_start_date, //合同起始日期（入职日期）
                    'contract_type' => enums::CONTRACT_ZCHT, //合同类型：2-租车合同
                    'contract_child_type' => 0,
                    'contract_is_need' => 2, //是否需要此合同
                ],

                [ //todo 公司损失扣款同意书
                    'contract_start_date' => $contract_start_date, //合同起始日期（入职日期）
                    'contract_type' => enums::CONTRACT_SSKK,
                    'contract_child_type' => enums::CONTRACT_SSKK_EN,
                    'contract_is_need' => 1, //是否需要此合同
                ],

                [ // todo 保密协议
                    'contract_start_date' => $contract_start_date, //合同起始日期（入职日期）
                    'contract_type' => enums::CONTRACT_BMXY, //合同类型：4-保密协议
                    'contract_child_type' => enums::CONTRACT_BMXY_EN, //保密协议子类：保密协议-英文
                    'contract_is_need' => 1, //是否需要此合同
                ],
                [ // todo 资产协议
                    'contract_start_date' => $contract_start_date, //合同起始日期（入职日期）
                    'contract_type' => enums::CONTRACT_ZCXY, //合同类型：5-资产协议
                    'contract_child_type' => $zcxy_contract_child_type, //资产协议：资产协议-泰文
                    'contract_is_need' => 1, //是否需要此合同
                    'contract_date_is_long' => $contract_date_is_long, // 是否长期
                ]
            ];

        }else{
            //皮卡车、摩托车 职位
            if(in_array($job_title,[enums::$job_title['Van Courier'],enums::$job_title['Bike Courier']])){

                $ld_contract_end_date = date('Y-m-d',strtotime('-1 day',strtotime('+1 year',strtotime($contract_start_date))));


                //租车合同子类类型
                $zcht_child_type = ($job_title == enums::$job_title['Van Courier']) ? enums::CONTRACT_ZCHT_PK : enums::CONTRACT_ZCHT_MT;

                if (isCountry("TH") and $job_title == enums::$job_title['Van Courier'] and $vehicle_source == 2){
                    $contract_child_type = enums::CONTRACT_LDHT_LONG_DATE_SIX;
                }else{
                    $contract_child_type = enums::CONTRACT_LDHT_SPECIAL;
                }

                //组装4个初始化合同类型数据
                $contract_insert_data = [

                    [ //todo 劳动合同
                        'contract_start_date' => $contract_start_date, //合同起始日期（入职日期）
                        'contract_end_date' => $ld_contract_end_date, //合同截止日期
                        'contract_ld_end_date' => $ld_contract_end_date, //劳动合同截止日期
                        'contract_type' => enums::CONTRACT_LDHT, //合同类型：1-劳动合同
                        'contract_child_type' => $contract_child_type, //劳动合同子类：特殊劳动合同（1年期）
                        'contract_is_need' => 1, //是否需要此合同
                    ],
                    [ // todo 租车合同

                        'contract_start_date' => $contract_start_date, //合同起始日期（入职日期）
                        'contract_end_date' => $ld_contract_end_date, //合同截止日期
                        'contract_ld_end_date' => $ld_contract_end_date, //劳动合同截止日期
                        'contract_type' => enums::CONTRACT_ZCHT, //合同类型：2-租车合同
                        'contract_child_type' => $zcht_child_type, //租车合同子类：Van Courier-皮卡租车合同，Bike Courier（摩托车）-摩托车租车合同
                        'contract_is_need' => 1, //是否需要此合同
                    ],

                    [ //todo 公司损失扣款同意书
                        'contract_start_date' => $contract_start_date, //合同起始日期（入职日期）
                        'contract_end_date' => $ld_contract_end_date, //合同截止日期
                        'contract_ld_end_date' => $ld_contract_end_date, //劳动合同截止日期
                        'contract_type' => enums::CONTRACT_SSKK, //合同类型：3-公司损失扣款同意书
                        'contract_child_type' => enums::CONTRACT_SSKK_TH, //合同类型：4-保密协议
                        'contract_is_need' => 1, //是否需要此合同
                    ],

                    [ // todo 保密协议
                        'contract_start_date' => $contract_start_date, //合同起始日期（入职日期）
                        'contract_end_date' => $ld_contract_end_date, //合同截止日期
                        'contract_ld_end_date' => $ld_contract_end_date, //劳动合同截止日期
                        'contract_type' => enums::CONTRACT_BMXY, //合同类型：4-保密协议
                        'contract_child_type' => enums::CONTRACT_BMXY_TH, //劳动合同子类：特殊劳动合同（1年期）
                        'contract_is_need' => 1, //是否需要此合同
                    ],
                    [ // todo 资产协议
                        'contract_start_date' => $contract_start_date, //合同起始日期（入职日期）
                        'contract_end_date' => $ld_contract_end_date, //合同截止日期
                        'contract_ld_end_date' => $ld_contract_end_date, //劳动合同截止日期
                        'contract_type' => enums::CONTRACT_ZCXY, //合同类型：5-资产协议
                        'contract_child_type' => $zcxy_contract_child_type, //劳动合同子类：特殊劳动合同（1年期）
                        'contract_is_need' => 1, //是否需要此合同
                        'contract_date_is_long' => $contract_date_is_long, // 是否长期
                    ]


                ];

            }else{
                //非 皮卡车、摩托车 职位

                $contract_insert_data = [

                    [ //todo 劳动合同
                        'contract_start_date' => $contract_start_date, //合同起始日期（入职日期）
                        'contract_type' => enums::CONTRACT_LDHT, //合同类型：1-劳动合同
                        'contract_child_type' => enums::CONTRACT_LDHT_LONG_DATE, //劳动合同子类：特殊劳动合同（1年期）
                        'contract_is_need' => 1, //是否需要此合同
                    ],
                    [ // todo 租车合同

                        'contract_start_date' => $contract_start_date, //合同起始日期（入职日期）
                        'contract_type' => enums::CONTRACT_ZCHT, //合同类型：2-租车合同
                        'contract_child_type' => 0,
                        'contract_is_need' => 2, //是否需要此合同
                    ],

                    [ //todo 公司损失扣款同意书

                        'contract_start_date' => $contract_start_date, //合同起始日期（入职日期）
                        'contract_type' => enums::CONTRACT_SSKK,
                        'contract_child_type' => enums::CONTRACT_SSKK_TH,
                        'contract_is_need' => 1, //是否需要此合同
                    ],

                    [ // todo 保密协议

                        'contract_start_date' => $contract_start_date, //合同起始日期（入职日期）
                        'contract_type' => enums::CONTRACT_BMXY, //合同类型：4-保密协议
                        'contract_child_type' => enums::CONTRACT_BMXY_TH, //劳动合同子类：特殊劳动合同（1年期）
                        'contract_is_need' => 1, //是否需要此合同
                    ],
                    [ // todo 资产协议

                        'contract_start_date' => $contract_start_date, //合同起始日期（入职日期）
                        'contract_type' => enums::CONTRACT_ZCXY, //合同类型：5-保密协议
                        'contract_child_type' => $zcxy_contract_child_type, //劳动合同子类：特殊劳动合同（1年期）
                        'contract_is_need' => 1, //是否需要此合同
                        'contract_date_is_long' => $contract_date_is_long, // 是否长期
                    ]
                ];

            }

        }

        return $contract_insert_data;
    }


    /**
     * 到岗确认-生成员工电子合同数据(泰国)
     * 该方法不能删除，有使用
     * @param $staff_info_id
     * @param $entry_date
     * @param $job_title
     * @return array
     */
    public function getContractDataTh($contract_start_date, $job_title, $nationality, $vehicle_source = 0,$working_country,$ancestry_v3,$staff_info = [])
    {
        // 获取配置的一线职位
        $setting_env = \FlashExpress\bi\App\Models\backyard\SettingEnvModel::findFirst([
                'conditions' => "code='winhr_contract_frontline_position'",
            ]
        );
        $setting_env = $setting_env ? $setting_env->toArray() : [];

        //上级部门查询
        $ancestry_v3_arr = [];
        if ($ancestry_v3) {
            $ancestry_v3_arr = explode('/', $ancestry_v3);
        }

        //合同截止日期显示规则：
        //合同开始时间默认为入职日期，合同结束时间为员工入职时间下一年（+1 year)的前一天(-1 day)
        //结束时间兼容闰年和平年：闰年+366，平年+365
        $ld_contract_end_date = date('Y-m-d',
            strtotime('-1 day', strtotime('+1 year', strtotime($contract_start_date))));

        //协议类型
        if (in_array(enums::TH_DEPARTMENT_FLASH_PAY, $ancestry_v3_arr)) {
            $ldht_child_type = enums::CONTRACT_LDHT_LONG_DATE_BILINGUAL_PAY;

            if ($nationality == enums::NATIONALITY_TH) {
                //泰国国籍 合同数据
                $zcxy_child_type = enums::CONTRACT_ZCXY_TH_FLASH_PAY;
            } else {
                $zcxy_child_type = enums::CONTRACT_ZCXY_ZH_FLASH_PAY;
            }
        } elseif (in_array(enums::TH_DEPARTMENT_FLASH_MONEY, $ancestry_v3_arr)) {
            $ldht_child_type = enums::CONTRACT_LDHT_LONG_DATE_BILINGUAL_MONEY;

            if ($nationality == enums::NATIONALITY_TH) {
                //泰国国籍 合同数据
                $zcxy_child_type = enums::CONTRACT_ZCXY_TH_FLASH_MONEY;
            } else {
                $zcxy_child_type = enums::CONTRACT_ZCXY_ZH_FLASH_MONEY;
            }
        } elseif (in_array(enums::TH_DEPARTMENT_FLASH_FULFILLMENT, $ancestry_v3_arr)) {
            $ldht_child_type = enums::CONTRACT_LDHT_LONG_DATE_BILINGUAL_FULFILLMENT;

            if ($nationality == enums::NATIONALITY_TH) {
                //泰国国籍 合同数据
                $zcxy_child_type = enums::CONTRACT_ZCXY_TH_FLASH_FULFILLMENT;
            } else {
                $zcxy_child_type = enums::CONTRACT_ZCXY_ZH_FLASH_FULFILLMENT;
            }
        } elseif (in_array(enums::TH_DEPARTMENT_F_COMMERCE, $ancestry_v3_arr)) {
            $ldht_child_type = enums::CONTRACT_LDHT_LONG_DATE_BILINGUAL_COMMERCE;

            if ($nationality == enums::NATIONALITY_TH) {
                //泰国国籍 合同数据
                $zcxy_child_type = enums::CONTRACT_ZCXY_TH_F_COMMERCE;
            } else {
                $zcxy_child_type = enums::CONTRACT_ZCXY_ZH_F_COMMERCE;
            }
        } elseif (in_array(enums::TH_DEPARTMENT_FLASH_HOME, $ancestry_v3_arr)) {
            $ldht_child_type = enums::CONTRACT_LDHT_LONG_DATE_BILINGUAL_HOME;

            if ($nationality == enums::NATIONALITY_TH) {
                //泰国国籍 合同数据
                $zcxy_child_type = enums::CONTRACT_ZCXY_TH_FLASH_HOME;
            } else {
                $zcxy_child_type = enums::CONTRACT_ZCXY_ZH_FLASH_HOME;
            }
        //Flash Incorporation长期劳动合同(英泰)
        } elseif (in_array(enums::TH_DEPARTMENT_FLASH_INCORPORATION, $ancestry_v3_arr)) {
            $ldht_child_type = enums::CONTRACT_LDHT_LONG_DATE_BILINGUAL_INCORPORATION;

            if ($nationality == enums::NATIONALITY_TH) {
                //泰国国籍 合同数据
                $zcxy_child_type = enums::CONTRACT_ZCXY_TH_FLASH_INCORPORATION;
            } else {
                $zcxy_child_type = enums::CONTRACT_ZCXY_ZH_FLASH_INCORPORATION;
            }
        } else {
            $ldht_child_type = enums::CONTRACT_LDHT_LONG_DATE_BILINGUAL;

            if ($nationality == enums::NATIONALITY_TH) {
                //泰国国籍 合同数据
                $zcxy_child_type = enums::CONTRACT_ZCXY_TH;
            } else {
                $zcxy_child_type = enums::CONTRACT_ZCXY_ZH;
            }
        }

        //个人代理合同
        $hire_type  = $staff_info['hire_type'] ?? 0;
        $hire_times = $staff_info['hire_times'] ?? 0;

        if ($hire_type == HrStaffInfoModel::HIRE_TYPE_AGENT) {
            $ld_contract_end_date = date('Y-m-d', strtotime("+ {$hire_times} month -1 day", strtotime($contract_start_date)));

            return [
                [
                    'contract_start_date'   => $contract_start_date,                //合同起始日期（入职日期）
                    'contract_end_date'     => $ld_contract_end_date,               //合同截止日期
                    'contract_ld_end_date'  => $ld_contract_end_date,               //劳动合同截止日期
                    'contract_type'         => enums::CONTRACT_LDHT,                //合同类型：1-劳动合同
                    'contract_child_type'   => enums::CONTRACT_LDHT_AGENT,          //劳动合同子类：固定期限劳动合同（泰）
                    'contract_is_need'      => HrStaffContractModel::IS_NEED_YES,   //是否需要此合同
                    'contract_date_is_long' => HrStaffContractModel::IS_LONG_NO     //是否长期合同 不是
                ],
                [
                    'contract_start_date'   => $contract_start_date,              //合同起始日期（入职日期）
                    'contract_type'         => enums::CONTRACT_ZCXY,              //合同类型：5-资产协议
                    'contract_child_type'   => enums::CONTRACT_ZCXY_AGENT,        //资产协议：资产协议-泰文
                    'contract_is_need'      => HrStaffContractModel::IS_NEED_YES, //是否需要此合同
                    'contract_date_is_long' => HrStaffContractModel::IS_LONG_YES, // 是否长期 是
                ],
                [
                    'contract_start_date'   => $contract_start_date,              //合同起始日期（入职日期）
                    'contract_type'         => enums::CONTRACT_SJCL,              //合同类型：5-资产协议
                    'contract_child_type'   => enums::CONTRACT_SJCL_AGENT,        //数据处理协议
                    'contract_is_need'      => HrStaffContractModel::IS_NEED_YES, //是否需要此合同
                    'contract_date_is_long' => HrStaffContractModel::IS_LONG_YES, // 是否长期 是
                ]
            ];
        }

        //日薪合同工
        if ($hire_type == HrStaffInfoModel::HIRE_TYPE_DAY) {
            return [
                [
                    'contract_start_date'   => $contract_start_date,              //合同起始日期（入职日期）
                    'contract_end_date'     => $ld_contract_end_date,             //合同截止日期
                    'contract_ld_end_date'  => $ld_contract_end_date,             //劳动合同截止日期
                    'contract_type'         => enums::CONTRACT_LDHT,              //合同类型：1-劳动合同
                    'contract_child_type'   => enums::CONTRACT_LDHT_SPECIAL_THAI_DAY, //劳动合同子类：固定期限劳动合同（泰）
                    'contract_is_need'      => 1,                                 //是否需要此合同
                    'contract_date_is_long' => 2,                                 //是否长期合同
                ],
                [

                    'contract_start_date' => $contract_start_date, //合同起始日期（入职日期）
                    'contract_type'       => enums::CONTRACT_ZCHT, //合同类型：2-租车合同
                    'contract_is_need'    => 2,                    //是否需要此合同
                ],
                [
                    'contract_start_date'   => $contract_start_date, //合同起始日期（入职日期）
                    'contract_type'         => enums::CONTRACT_ZCXY, //合同类型：5-资产协议
                    'contract_child_type'   => $zcxy_child_type,     //资产协议：资产协议-泰文
                    'contract_is_need'      => 1,                    //是否需要此合同
                    'contract_date_is_long' => 1,                    // 是否长期
                ],
            ];
        }

        if (
            in_array(
                $job_title,
                [
                    enums::$job_title['Bike Courier'],
                    enums::$job_title['Boat Courier'],
                    enums::$job_title['van feeder'],
                    enums::$job_title['tricycle_courier'],
                    enums::$job_title['Van Courier'],
                    enumsTh::JOB_TITLE_EV_COURIER
                ])){
            // 固定期限劳动合同（泰）
            $contract_insert_data = [

                [ //todo 劳动合同
                    'contract_start_date' => $contract_start_date, //合同起始日期（入职日期）
                    'contract_end_date' => $ld_contract_end_date, //合同截止日期
                    'contract_ld_end_date' => $ld_contract_end_date, //劳动合同截止日期
                    'contract_type' => enums::CONTRACT_LDHT, //合同类型：1-劳动合同
                    'contract_child_type' => enums::CONTRACT_LDHT_SPECIAL_THAI, //劳动合同子类：固定期限劳动合同（泰）
                    'contract_is_need' => 1, //是否需要此合同
                    'contract_date_is_long' => 2, //是否长期合同
                ],
                [ // todo 租车合同

                    'contract_start_date' => $contract_start_date, //合同起始日期（入职日期）
                    'contract_type' => enums::CONTRACT_ZCHT, //合同类型：2-租车合同
                    'contract_is_need' => 2, //是否需要此合同
                ],
                [ // todo 资产协议
                    'contract_start_date' => $contract_start_date, //合同起始日期（入职日期）
                    'contract_type' => enums::CONTRACT_ZCXY, //合同类型：5-资产协议
                    'contract_child_type' => $zcxy_child_type, //资产协议：资产协议-泰文
                    'contract_is_need' => 1, //是否需要此合同
                    'contract_date_is_long' => 1, // 是否长期
                ]
            ];
        //国籍为非泰籍员工，默认为：长期劳动合同（英泰）
        } elseif ($nationality != enums::NATIONALITY_TH) {
            //长期劳动合同（英泰）
            $contract_insert_data = [
                [
                    'contract_start_date'   => $contract_start_date,
                    'contract_type'         => enums::CONTRACT_LDHT,
                    'contract_child_type'   => enums::CONTRACT_LDHT_LONG_DATE_BILINGUAL,
                    'contract_is_need'      => HrStaffContractModel::IS_NEED_YES,
                    'contract_date_is_long' => HrStaffContractModel::IS_LONG_YES,
                ],
                [
                    'contract_start_date' => $contract_start_date,
                    'contract_type'       => enums::CONTRACT_ZCHT,
                    'contract_is_need'    => HrStaffContractModel::IS_NEED_NO,
                ],
                [
                    'contract_start_date'   => $contract_start_date,
                    'contract_type'         => enums::CONTRACT_ZCXY,
                    'contract_child_type'   => $zcxy_child_type,
                    'contract_is_need'      => HrStaffContractModel::IS_NEED_YES,
                    'contract_date_is_long' => HrStaffContractModel::IS_LONG_YES,
                ],
            ];
        }elseif (in_array($job_title,explode(',',$setting_env['set_val']))){
            if (in_array(enums::TH_DEPARTMENT_FLASH_FULFILLMENT, $ancestry_v3_arr)) {
                $ldht_child_type = enums::CONTRACT_LDHT_LONG_DATE_THAI_FULFILLMENT; //Flash Fulfillment 长期劳动合同（泰）
            } else {
                $ldht_child_type = enums::CONTRACT_LDHT_LONG_DATE_THAI; //劳动合同子类：长期劳动合同（泰）
            }

            $contract_insert_data = [
                [
                    'contract_start_date'   => $contract_start_date, //合同起始日期（入职日期）
                    'contract_type'         => enums::CONTRACT_LDHT, //合同类型：1-劳动合同
                    'contract_child_type'   => $ldht_child_type,
                    'contract_is_need'      => HrStaffContractModel::IS_NEED_YES, //是否需要此合同
                    'contract_date_is_long' => HrStaffContractModel::IS_LONG_YES, //是否长期合同
                ],
                [

                    'contract_start_date' => $contract_start_date,             //合同起始日期（入职日期）
                    'contract_type'       => enums::CONTRACT_ZCHT,             //合同类型：2-租车合同
                    'contract_is_need'    => HrStaffContractModel::IS_NEED_NO, //是否需要此合同
                ],
                [
                    'contract_start_date'   => $contract_start_date,              //合同起始日期（入职日期）
                    'contract_type'         => enums::CONTRACT_ZCXY,              //合同类型：5-资产协议
                    'contract_child_type'   => $zcxy_child_type,              //资产协议：资产协议-泰文
                    'contract_is_need'      => HrStaffContractModel::IS_NEED_YES, //是否需要此合同
                    'contract_date_is_long' => HrStaffContractModel::IS_LONG_YES, //是否长期合同
                ]
            ];
        } else {
            // 长期劳动合同（英泰）
            $contract_insert_data = [
                [
                    'contract_start_date'   => $contract_start_date,                         //合同起始日期（入职日期）
                    'contract_type'         => enums::CONTRACT_LDHT,                         //合同类型：1-劳动合同
                    'contract_child_type'   => $ldht_child_type,                             //劳动合同子类：长期劳动合同（英泰）
                    'contract_is_need'      => HrStaffContractModel::IS_NEED_YES,            //是否需要此合同
                    'contract_date_is_long' => HrStaffContractModel::IS_LONG_YES,            //是否长期合同
                ],
                [

                    'contract_start_date' => $contract_start_date,             //合同起始日期（入职日期）
                    'contract_type'       => enums::CONTRACT_ZCHT,             //合同类型：2-租车合同
                    'contract_is_need'    => HrStaffContractModel::IS_NEED_NO, //是否需要此合同
                ],
                [
                    'contract_start_date'   => $contract_start_date,              //合同起始日期（入职日期）
                    'contract_type'         => enums::CONTRACT_ZCXY,              //合同类型：5-资产协议
                    'contract_child_type'   => $zcxy_child_type,              //资产协议：资产协议-泰文
                    'contract_is_need'      => HrStaffContractModel::IS_NEED_YES, //是否需要此合同
                    'contract_date_is_long' => HrStaffContractModel::IS_LONG_YES, //是否长期合同
                ]
            ];
        }

        return $contract_insert_data;
    }

    /**
     * 到岗确认-生成员工电子合同数据(菲律宾)
     * 该方法不能删除，有使用
     * @param $staff_info_id
     * @param $entry_date
     * @param $job_title
     * @return array
     */
    public function getContractDataPH(
        $entry_date,
        $job_title,
        $nationality,
        $vehicle_source = 0,
        $working_country,
        $ancestry_v3,
        $staff_info = []
    ) {
        $ph_country = getCountryValue("PH");

        //个人代理合同
        $hire_type  = $staff_info['hire_type'] ?? 0;
        $hire_times = $staff_info['hire_times'] ?? 0;

        if ($hire_type == HrStaffInfoModel::HIRE_TYPE_AGENT) {
            $ld_contract_end_date = date('Y-m-d', strtotime("+ {$hire_times} month -1 day", strtotime($entry_date)));

            return [
                [
                    'contract_start_date'   => $entry_date,                         //合同起始日期（入职日期）
                    'contract_end_date'     => $ld_contract_end_date,               //合同截止日期
                    'contract_ld_end_date'  => $ld_contract_end_date,               //劳动合同截止日期
                    'contract_type'         => enums::CONTRACT_LDHT,                //合同类型：1-劳动合同
                    'contract_child_type'   => enums::CONTRACT_LDHT_PH_AGENT,       //劳动合同子类：固定期限劳动合同（泰）
                    'contract_is_need'      => HrStaffContractModel::IS_NEED_YES,   //是否需要此合同
                    'contract_date_is_long' => HrStaffContractModel::IS_LONG_NO     //是否长期合同 不是
                ],
            ];
        }

        //月薪制合同工
        if ($hire_type == HrStaffInfoModel::HIRE_TYPE_MONTH) {
            $conversion_permanent_date = !empty($staff_info['conversion_permanent_date']) ? $staff_info['conversion_permanent_date'] : (new AddEntryServer())->getConversionPermanentDate($entry_date);
            $ld_contract_end_date = date('Y-m-d', strtotime("-1 day", strtotime($conversion_permanent_date)));

            return [
                [
                    'contract_start_date'   => $entry_date,                         //合同起始日期（入职日期）
                    'contract_end_date'     => $ld_contract_end_date,               //合同截止日期
                    'contract_ld_end_date'  => $ld_contract_end_date,               //劳动合同截止日期
                    'contract_type'         => enums::CONTRACT_LDHT,                //合同类型：1-劳动合同
                    'contract_child_type'   => enums::CONTRACT_LDHT_PH_MONTH,       //劳动合同子类：固定期限劳动合同（泰）
                    'contract_is_need'      => HrStaffContractModel::IS_NEED_YES,   //是否需要此合同
                    'contract_date_is_long' => HrStaffContractModel::IS_LONG_NO     //是否长期合同 不是
                ],
            ];
        }

        $ancestry_v3_arr = [];
        if ($ancestry_v3) {
            $ancestry_v3_arr = explode('/', $ancestry_v3);
        }

        if ($working_country == $ph_country) {
            if (in_array(enums::PH_FLASH_FULFILLMENT_ID, $ancestry_v3_arr)) {
                $zcxy_contract_child_type = enums::CONTRACT_ZCXY_PH_FFM;
            } else {
                if ($nationality == $ph_country) {
                    $zcxy_contract_child_type = enums::CONTRACT_ZCXY_PH_BG;
                } else {
                    $zcxy_contract_child_type = enums::CONTRACT_ZCXY_PH_WG;
                }
            }
        } else {
            $zcxy_contract_child_type = enums::CONTRACT_ZCXY_PH_WG;
        }

        //FLeet driver合同数据
        if (in_array($job_title, [enums::JOB_TITLE_FLEET_DRIVER_6W, enums::JOB_TITLE_FLEET_DRIVER_10W])) {
            $ldht_contract_child_type = enums::CONTRACT_LDHT_FLEET;
            //FFM菲籍劳动合同
        } elseif (in_array(enums::PH_FLASH_FULFILLMENT_ID, $ancestry_v3_arr)) {
            $ldht_contract_child_type = enums::CONTRACT_LDHT_FFM;
        } elseif ($nationality == $ph_country && in_array(enums::PH_F_COMMERCE_ID, $ancestry_v3_arr)) {
            $ldht_contract_child_type = enums::CONTRACT_LDHT_PH_F_COMMERCE;
        } elseif ($nationality != $ph_country) {
            $ldht_contract_child_type = enums::CONTRACT_LDHT_FOREIGN;
            //菲籍员工劳动合同
        } else {
            $ldht_contract_child_type = enums::CONTRACT_LDHT_FULL_TIME;
        }

        return [
            [
                'contract_start_date'   => $entry_date,
                'contract_type'         => enums::CONTRACT_LDHT,
                'contract_child_type'   => $ldht_contract_child_type,
                'contract_is_need'      => HrStaffContractModel::IS_NEED_YES,
                'contract_date_is_long' => HrStaffContractModel::IS_LONG_YES,
            ],
            [
                'contract_start_date'   => $entry_date,
                'contract_type'         => enums::CONTRACT_ZCXY,
                'contract_child_type'   => $zcxy_contract_child_type,
                'contract_is_need'      => HrStaffContractModel::IS_NEED_NO,
                'contract_date_is_long' => HrStaffContractModel::IS_LONG_YES,
            ],
        ];
    }

    /**
     * 到岗确认-生成员工电子合同数据LA
     *
     * @param $staff_info_id
     * @param $entry_date
     * @param $job_title
     * @return array
     */
    private function getContractDataLA($entry_date, $job_title, $nationality){



            $contract_insert_data = [

                [ //todo 劳动合同

                    'contract_type' => enums::CONTRACT_LDHT, //合同类型：1-劳动合同
                ],

                [ // todo 保密协议

                    'contract_type' => enums::CONTRACT_BMXY, //合同类型：4-保密协议

                ],
                [ // todo 资产协议

                    'contract_type' => enums::CONTRACT_ZCXY, //合同类型：5-保密协议
                ]
            ];









        return $contract_insert_data;
    }


    /**
     * Offer员工列表
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function offerStaffList($paramIn = [])
    {
        $sql = "select hr_resume.name,hr_resume.id from hr_entry LEFT JOIN hr_resume on hr_resume.id=hr_entry.resume_id";
        $data = $this->getDI()->get('db_rby')->query($sql);
        $returnData = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        return $returnData;
    }

    /**
     * 根据入职id获取员工工号
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getEntryInfo($paramIn = [])
    {

        $entryId = intval($paramIn['entry_id']);
        if(empty($entryId)){
            return [];
        }
        $query_param = [
            'entry_id' => $entryId,
        ];
        $sql = "select * from hr_entry where entry_id =:entry_id" ;
        $data = $this->getDI()->get('db_rby')->query($sql,$query_param);
        $returnData = $data->fetch(\Phalcon\Db::FETCH_ASSOC);
        return $returnData;
    }

    /**
     * 根据入职id获取入职信息
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getEntryDetail($paramIn = [])
    {
        $entryId = intval($paramIn['entry_id']);
        if(empty($entryId)){
            return [];
        }

        $sql = "--
            SELECT hr_entry.status, hr_hc.hire_type, hr_hc.hire_times, hr_entry.record_entry,hr_entry.resume_id,hr_entry.interview_id,hr_interview_offer.other,hr_interview_offer.shift_id FROM hr_entry LEFT JOIN hr_interview_offer on hr_interview_offer.id = hr_entry.interview_offer_id left join hr_hc on hr_entry.hc_id=hr_hc.hc_id
                where hr_entry.entry_id=:entry_id";
        $query_param = [
            'entry_id' => $entryId,
        ];
        $data = $this->getDI()->get('db_rby')->query($sql,$query_param);
        $returnData = $data->fetch(\Phalcon\Db::FETCH_ASSOC);
        return $returnData;
    }

    /**
     * 获取指定简历中入职员工的工号列表（获取入职表信息）
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getEntryStaffInfo($paramIn = [])
    {
        $resumeIds = $paramIn['resume_ids'];
        $sql = "--
                select 
                    entry_id,
                    staff_id,
                    resume_id, 
                    hc_id, 
                    interview_offer_id, 
                    interview_id, 
                    status, 
                    deleted, 
                    DATE_FORMAT(CONVERT_TZ(operate_date, '+00:00', '" . $this->timezone . "' ),'%Y-%m-%d %H:%i:%s') AS operate_date
                from hr_entry 
                where resume_id in ({$resumeIds})";
        $data = $this->getDI()->get('db_rby')->query($sql);
        $returnData = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        return $returnData;
    }

    /*
     * 基础-修改入职表
     * @Access  public
     * @Param   array
     * @Return  array
     */
    public function changeEntry($paramIn = [])
    {
        $id = $paramIn['id'] ?? "";
        $updateData = $paramIn['updateData'] ?? [];

        if (!$updateData || !$id) return false;

        $this->getDI()->get('db')->updateAsDict(
            'hr_entry',
            $updateData,
            'entry_id = ' . $id
        );
        if ($this->getDI()->get('db')->affectedRows() < 1) {
            return false;
        }

        return true;
    }

    public function changeHrInterviewOffer($paramIn = []) {
        $id = $paramIn['interview_offer_id'] ?? "";
        $updateData = $paramIn['updateData'] ?? [];

        if (!$updateData || !$id) return false;

        $this->getDI()->get('db')->updateAsDict(
            'hr_interview_offer',
            $updateData,
            'id = ' . $id
        );
        if ($this->getDI()->get('db')->affectedRows() < 1) {
            return false;
        }

        return true;
    }

    /**
     * 根据简历ID查询个人详情
     * @param array $paramIn
     * @return mixed
     */
    public function getEntryInfoByEntryId($paramIn = [])
    {
        $resumeId = $paramIn['resume_id'] ?? '';
        $sql      = "SELECT 
            hr_resume.sex,
            hr_resume.call_name,
            hr_resume.name,
            hr_resume.phone_area_code,
            hr_resume.phone,
            hr_resume.credentials_num,
            hr_resume.id as resume_id,
            DATE_FORMAT(CONVERT_TZ(hr_resume.date_birth, '+00:00', '" . $this->timezone . "' ),'%Y-%m-%d') AS date_birth,
            hr_hc.department_id,
            hr_hc.job_title,
            hr_hc.worknode_id,
            hr_hc.job_id,
            hr_interview_offer.position_id,
            hr_interview_offer.shift_id,
            DATE_FORMAT(hr_interview_offer.work_time,'%Y-%m-%d') AS work_time,
            hr_resume.identity_validate_status
            FROM hr_resume
            left JOIN hr_interview_offer ON hr_interview_offer.resume_id = hr_resume.id  AND hr_interview_offer.hc_id=hr_resume.hc_id		
            left JOIN hr_hc ON hr_hc.hc_id=hr_interview_offer.hc_id		
            WHERE hr_resume.id = {$resumeId}";

        $data      = $this->getDI()->get('db_rby')->query($sql);
        $returnData= $data->fetch(\Phalcon\Db::FETCH_ASSOC);
        $columns = "entry_id,
                staff_id,
                status,
                interview_id,
                cancel_type,
                cancel_reason_type,
                cancel_reason_content,
                record_entry,
                DATE_FORMAT(CONVERT_TZ(entry_date, '+00:00', '" . $this->timezone . "' ),'%Y-%m-%d') AS entry_date";
        $querySql = "--
            SELECT
                {$columns}
            FROM hr_entry
            WHERE resume_id = {$resumeId}";
        $data      = $this->getDI()->get('db_rby')->query($querySql);
        $dataEntry = $data->fetch(\Phalcon\Db::FETCH_ASSOC);

        if ($dataEntry) {
            $record_entry = $dataEntry['record_entry'] ? json_decode($dataEntry['record_entry'], true) : [];
            if ((isCountry('ph') || isCountry('my')) && $record_entry) {
                $dataEntry['working_day_rest_type'] = $record_entry['working_day_rest_type'] ?? 0;
                $dataEntry['default_rest_day_date'] = $record_entry['default_rest_day_date'] ?? [];
            }
            unset($record_entry['shift_extend_id']);
            $dataEntry['entryform_submit_status'] = $record_entry ? enums::$entry_submit_form['entry_has_submit'] : enums::$entry_submit_form['entry_do_not_submit'];
            $returnData                           = array_merge($returnData, $dataEntry);
            
            $returnData['name'] = !empty($record_entry['name']) ? $record_entry['name'] : $returnData['name'];
            $returnData['phone'] = !empty($record_entry['mobile']) ? $record_entry['mobile'] : $returnData['phone'];
            $returnData['phone_area_code'] = !empty($record_entry['mobile_area_code']) ? $record_entry['mobile_area_code'] : $returnData['phone_area_code'];
            $returnData['sex'] = !empty($record_entry['sex']) ? $record_entry['sex'] : $returnData['sex'];
            $returnData['work_time'] = !empty($record_entry['hire_date']) ? $record_entry['hire_date'] : $returnData['work_time'];
            $returnData['job_title'] = !empty($record_entry['job_title']) ? $record_entry['job_title'] : $returnData['job_title'];
            $returnData['position_id'] = !empty($record_entry['job_title']) ? $record_entry['job_title'] : $returnData['position_id'];
        }
        return $returnData;
    }

    /**
     * 获取HC、入职信息
     * @Access  public
     * @Param   array
     * @Return  array
     */
    public function checkEntry($entry_id)
    {
        $data = [];
        if (empty($entry_id)) {
            return $data;
        }

        $entryInfo = HrEntryModel::findFirst([
                'conditions' => 'entry_id  = :entry_id:',
                'bind'       => [
                    'entry_id' => $entry_id,
                ],
                'columns'    => "interview_offer_id,entry_id,hc_id,hire_type",
            ]
        );

        if (empty($entryInfo)) {
            return $data;
        }

        $offerInfo = HrInterviewOfferModel::findFirst(
            [
                'conditions' => 'id  = :interview_offer_id:',
                'bind'       => [
                    'interview_offer_id' => $entryInfo->interview_offer_id,
                ],
                'columns'    => "id,position_id",
            ]
        );

        if (empty($offerInfo)) {
            return $data;
        }

        $hcInfo = HrhcModel::findFirst(
            [
                'conditions' => 'hc_id  = :hc_id:',
                'bind'       => [
                    'hc_id' => $entryInfo->hc_id,
                ],
                'columns'    => "department_id,worknode_id",
            ]
        );
        if (empty($hcInfo)) {
            return $data;
        }

        return [
            'worknode_id'   => $hcInfo->worknode_id,
            'department_id' => $hcInfo->department_id,
            'position_id'   => $offerInfo->position_id,
            'hire_type'     => $entryInfo->hire_type ?? ''
        ];
    }

    /**
     * 获取已经入职的信息
     * @param array $paramIn 传入参数
     * @return array
     */
    public function getHasEntryInfo($paramIn = [])
    {
        $offerIds = $paramIn['offer_ids'] ?? '';
        $entryStatus = $paramIn['status'] ?? '';
        if (empty($offerIds)) {
            return [];
        }

        $entrySql = "--
            SELECT 
                *
            FROM hr_entry 
            WHERE 
                interview_offer_id IN ( {$offerIds} ) 
                AND status IN ({$entryStatus}) 
                AND deleted = 0 
            ";
        $data = $this->getDI()->get('db_rby')->query($entrySql);
        $dataEntry = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        return $dataEntry ?? [];
    }



    public function delEntry($resumeIds,$path_name)
    {
        $tableName = "hr_entry";
        $db = $this->getDI()->get('db');
        try {
            $sql = "from {$tableName} where resume_id in (" . implode(",",$resumeIds).")";
            $db->begin();
            $obj = $db->query("select * ".$sql);
            $data = $obj->fetchAll(\Phalcon\Db::FETCH_ASSOC);
            if (empty($data)) {
                $db->commit();
                return true;
            }
            $this->buildBakSql($path_name,$tableName, $data);
            $db->execute("delete ".$sql);
            $this->getDI()->get('logger')->write_log('delCVReal sql: delete '.$sql,'info');
            $db->commit();
        } catch (\Exception $e) {
            $db->rollback();
            return $e->getMessage();
        }
        return true;
    }

    /**
     * 如果在职，无法删除，没问题，返回"";
     * @param $resumeIds
     * @return string
     */

    public function ifCanDelEntry($resumeIds){
        $msg = "";
        try{
            $db = $this->getDI()->get('db_rby');
            $sql = "select resume_id,staff_id from hr_entry where resume_id in (".implode(",",$resumeIds).") and staff_id is not null";
            $obj = $db->query($sql);
            $data = $obj->fetchAll(\Phalcon\Db::FETCH_ASSOC);
            //如果都是未入职的，为空，返回true
            if(empty($data)){
                return "";
            }
            $tmp = array_column($data,"resume_id","staff_id");
            $staff_id = array_column($data,"staff_id");

            $db = $this->getDI()->get('db_rby');
            $sql = "select staff_info_id from hr_staff_info where staff_info_id in (".implode(",",$staff_id).") and state = 1";
            $obj = $db->query($sql);
            $data = $obj->fetchAll(\Phalcon\Db::FETCH_ASSOC);
            if(!empty($data)){
                foreach ($data as $item){
                    $msg.=$tmp[$item['staff_info_id']].",";
                }
                $msg = trim($msg,",");
            }
        }catch (\Exception $e){
            $msg = $e->getMessage();
        }
        return $msg;
    }


    public function checkEntryData()
    {
        $entrySql = "--
            SELECT 
                interview.interview_id,
                hr_interview_offer.id as interview_offer_id,
                train.resume_id,
                train.hc_id
            FROM `hr_training` train
            LEFT JOIN `hr_hc` ON hr_hc.hc_id = train.hc_id
            LEFT JOIN `hr_resume` resume ON resume.id = train.resume_id
            LEFT JOIN `hr_interview` AS interview ON interview.resume_id = resume.id
            LEFT JOIN `hr_interview_offer` ON hr_interview_offer.resume_id = resume.id
                AND hr_interview_offer.id = (
                    SELECT max(id) AS id 
                    FROM hr_interview_offer interview_offer
                    WHERE interview_offer.resume_id = resume.id
                )
            WHERE train.`status` = 4 and resume.id not in (
                SELECT resume_id FROM hr_entry 
            )
        ";
        $data      = $this->getDI()->get('db_rby')->query($entrySql);
        $dataEntry = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        return $dataEntry;
    }

    /**
     * @param $entry_id
     */
    public function syncEntryToBuddy($entry_id,$status=0){

        //合作到期，禁用此功能
        return;


        if(env('country_code') != 'TH'){
            return;
        }

        if(empty($entry_id)){
            return;
        }

        $item = $this->getEntryInfo(['entry_id'=>$entry_id]);
        if(empty($item)){
            return;
        }

        $resume = (new ResumeRepository())->getResumeInfo(['id'=>$item['resume_id']]);
        if(empty($resume)){
            return;
        }

        if(empty($resume['buddy_id'])){
            return;
        }

        $stateArr = [1=>'Already employed',2=>'Waiting for entry',3=>'not on board'];

        $data = ['cv_id'=>$resume['id'],'state'=>$item['status'],'state_list'=>$stateArr,'buddy_id'=>$resume['buddy_id'],'hc_id'=>$item['hc_id'],'updated_at'=> $item['updated_at']];

        $message = new MessageQueueServer();
        $message->sendToMNS(MessageQueueServer::ENTRY_QUEUE,['code'=>1,'message'=>'','data'=>$data]);
    }

    /**
     *
     * 修复菲律宾历史数据脚本，仅执行一次
     */
    public function editBankType(){
        if(env('country_code') == 'PH'){
            $entrylist = HrEntryModel::find()->toArray();
            if(empty($entrylist)){
                echo 'data empty';
                return ;
            }
            foreach ($entrylist as $item){
                $record_entry = json_decode($item['record_entry'],true);
                if(isset($record_entry['bank_type']) && $record_entry['bank_type'] != 21){
                    echo "entry_id-".$item['entry_id']." 修复中".PHP_EOL;
                   $record_entry['bank_type'] = 21;
                   $this->getDI()->get('db')->updateAsDict(
                       'hr_entry',
                       ["record_entry" => json_encode($record_entry)],
                       'entry_id = ' . $item['entry_id']
                   );

                }

            }

        }
    }


    /**
     * @description:根据工号获取信息
     * @param null
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2021/7/28 17:00
     */
    public function  getStaffIdInfo($paramIn){
	    $returnData = HrEntryModel::findFirst([
		                                         'conditions' => 'staff_id = :staff_id:',
		                                         'bind'       => ['staff_id' =>  $paramIn['staff_id']]
	                                         ]);
	    if(!empty($returnData)){
		    $returnData = $returnData->toArray();
	    }
        return $returnData;
    }




	
	
	/**
	 * @description: 从 env 获取 期望薪资结构权限 拼装成员工工号数组
	 *
	 * @param null
	 *
	 * @return     :
	 * <AUTHOR> L.J
	 * @time       : 2021/8/21 10:56
	 */
	public function getAuthoritySetVal(){
		
		//获取薪资工号
		$salary_permission_staff = SettingEnvServer::getSetVal('salary_permission_staff');//薪资机构工号
		$salary_permission_staff = empty($salary_permission_staff) ? []: explode(',', $salary_permission_staff);
		//根据角色获取工号
		$salary_permission_roles = SettingEnvServer::getSetVal('salary_permission_roles');//薪资结构角色
		$salary_permission_roles = empty($salary_permission_roles) ? []:  explode(',', $salary_permission_roles);
		
		
		
		//增加超管
		$superAdministratorIds = env('superAdministratorIds',"");
		$superAdministratorIdsArr = explode(',', $superAdministratorIds);
		
		
		$staff_ids = array_merge($salary_permission_staff,$superAdministratorIdsArr);
		$staff_ids = array_values(array_unique($staff_ids));
		//获取期望薪资权限
		$this->getDI()->get("logger")->write_log("期望薪资结构权限 getAuthoritySetVal".json_encode($staff_ids)."角色:".json_encode($salary_permission_roles),"info");
		return ['staff_ids'=>$staff_ids,'roles'=>$salary_permission_roles];
	}

    /**
     * 到岗确认-生成员工电子合同数据LA
     * @param $staff_info_id
     * @param $entry_date
     * @param $job_title
     * @return array
     */
    public function getContractDataMY($staffData, $entryDate, $recordEntry)
    {
        $jobTitle = $recordEntry['job_title'] ?? 0;

        $contractData = [
            'contract_type' => enums::CONTRACT_LDHT, //合同类型：1-劳动合同
        ];

        $contractData['contract_start_date'] = formatHrDate($entryDate);

        //正式合同
        if (HrStaffInfoModel::HIRE_TYPE_OFFICIAL == $recordEntry['hire_type']) {
            $contractData['contract_date_is_long'] = HrStaffContractModel::IS_LONG_YES;
        }

        //合同结束日期
        if (HrStaffInfoModel::HIRE_TYPE_MONTH == $recordEntry['hire_type']) {
            $contractData['contract_end_date']    = $recordEntry['contract_expiry_date'] ?? '9999-12-31';
            $contractData['contract_ld_end_date'] = $recordEntry['contract_expiry_date'] ?? '9999-12-31';
        }

        //合同类型
        if ($recordEntry['sys_store_id'] != enums::HEAD_OFFICE_ID) {
            $contractData['contract_child_type'] = enums::MY_CONTRACT_LDHT_NETWORK;
        } else {
            $contractData['contract_child_type'] = enums::MY_CONTRACT_LDHT_OFFICE;
        }

        //设置合同子类
        if ($staffData['nationality'] != getCountryValue()) {
            $contractData['contract_child_type'] = enums::MY_CONTRACT_LDHT_EXPAT;
        }

        //个人代理
        if (HrStaffInfoModel::HIRE_TYPE_AGENT == $recordEntry['hire_type']) {
            $contractData['contract_end_date']    = $recordEntry['contract_expiry_date'] ?? '9999-12-31';
            $contractData['contract_ld_end_date'] = $recordEntry['contract_expiry_date'] ?? '9999-12-31';
            $contractData['contract_child_type']  = enums::MY_CONTRACT_LDHT_INDE;
        }

        //LNT合同  若员工在HCM雇佣类型=月薪制特殊合同工，且【合同公司】=LNT EXPRESS SDN BHD
        if (
            (HrStaffInfoModel::CONTRACT_COMPANY_ID_LNT == $recordEntry['contract_company_id']) &&
            (HrStaffInfoModel::HIRE_TYPE_MONTH == $recordEntry['hire_type'])
        ) {
            $contractData['contract_date_is_long'] = HrStaffContractModel::IS_LONG_NO;
            $contractData['contract_end_date']     = $recordEntry['contract_expiry_date'] ?? '9999-12-31';
            $contractData['contract_ld_end_date']  = $recordEntry['contract_expiry_date'] ?? '9999-12-31';
            $contractData['contract_child_type']   = enums::MY_CONTRACT_LDHT_LNT;
        }

        //兼职个人代理
        if (HrStaffInfoModel::HIRE_TYPE_PART_TIME_AGENT == $recordEntry['hire_type']) {
            $contractData['contract_end_date']    = $recordEntry['contract_expiry_date'] ?? '9999-12-31';
            $contractData['contract_ld_end_date'] = $recordEntry['contract_expiry_date'] ?? '9999-12-31';
            $contractData['contract_child_type']  = enums::MY_CONTRACT_LDHT_PART_TIME;
        }

        //BDC个人代理合同
        if (
            HrStaffInfoModel::HIRE_TYPE_AGENT == $recordEntry['hire_type']
            &&  ($jobTitle == enumsMy::JOB_TITLE_BDC_DRIVER)
        ) {
            $contractData['contract_end_date']    = $recordEntry['contract_expiry_date'] ?? '9999-12-31';
            $contractData['contract_ld_end_date'] = $recordEntry['contract_expiry_date'] ?? '9999-12-31';
            $contractData['contract_child_type']  = enums::MY_CONTRACT_LDHT_BDC_DRIVER;
        }

        return [$contractData];
    }

    /**
     * 获取需要确认身份证 数量
     * @param $paramIn
     * @return int
     */
    public function getIdentityConfirmed($paramIn)
    {
        //身份证确认
        $is_admin = $paramIn["is_admin"] ?? enums::$is_admin['on']; //权限 1 超管

        $builder = $this->modelsManager->createBuilder();
        $builder->columns('count(1) as total')->from(['hr_entry' => HrEntryModel::class]);
        $builder->leftjoin(HrResumeModel::class,"hr_entry.resume_id = hr_resume.id","hr_resume");
        $builder->leftjoin(HrhcModel::class,"hr_entry.hc_id = hc.hc_id","hc");

        $builder->where("hr_entry.deleted = :hr_entry_deleted: AND NOT EXISTS ( SELECT 1 from " . HrEntryModel::class . " as t_e where t_e.interview_id = hr_entry.interview_id  and t_e.entry_id > hr_entry.entry_id )", ['hr_entry_deleted' => HrEntryModel::DELETED_0]);

        $builder->andWhere('hr_entry.status in ({entry_status:array}) and hr_resume.identity_validate_status in ({identity_validate_status:array})', ['entry_status' => [HrEntryModel::STATUS_EMPLOYED, HrEntryModel::STATUS_TO_BE_EMPLOYED], 'identity_validate_status' => [HrResumeModel::IDENTITY_VALIDATE_STATUS_FAIL, HrResumeModel::IDENTITY_VALIDATE_STATUS_UNTREATED]]);

        //数据权限
        $is_menu_permission = SettingEnvServer::getMenuPermission('whr_entry_menu_list');
        $authoritySql = '';
        if($is_admin == enums::$is_admin['off'] && $is_menu_permission){
            $authoritySql = (new HcServer())->assembleAuthoritySql('',$this->userInfo);
        }

        if (!empty($authoritySql)) {
            $builder->andWhere($authoritySql);
        }

        $totalInfo  = $builder->getQuery()->getSingleResult();
        return !empty($totalInfo) ? intval($totalInfo->total) : 0;
    }

    /**
     * 获取 单条入职信息
     * @param $params
     * @param array $columns
     * @return array
     */
    public static function getOne($params, $columns = ['*'])
    {
        if(empty($params)) {
            return [];
        }

        $conditions = '1 = 1';
        $bind = [];

        if(!empty($params['resume_id'])) {
            $conditions .= ' and resume_id = :resume_id:';
            $bind['resume_id'] = $params['resume_id'];
        }

        $data = HrEntryModel::findFirst([
            'columns'    => $columns,
            'conditions' => $conditions,
            'bind'       => $bind,
        ]);

        return empty($data) ? [] : $data->toArray();
    }
}
