<?php

namespace FlashExpress\bi\App\Repository;

use FlashExpress\bi\App\Helper\PermissionHelper;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\CommunicationResumeLogModel;
use FlashExpress\bi\App\Models\backyard\HrEntryModel;
use FlashExpress\bi\App\Models\backyard\HrhcModel;
use FlashExpress\bi\App\Models\backyard\HrInterviewerOperationModel;
use FlashExpress\bi\App\Models\backyard\HrInterviewInfoModel;
use FlashExpress\bi\App\Models\backyard\HrInterviewModel;
use FlashExpress\bi\App\Models\backyard\HrInterviewOfferModel;
use FlashExpress\bi\App\Models\backyard\HrInterviewOfferSignApproveModel;
use FlashExpress\bi\App\Models\backyard\HrInterviewSubscribeModel;
use FlashExpress\bi\App\Models\backyard\HrJdModel;
use FlashExpress\bi\App\Models\backyard\HrJobTitleModel;
use FlashExpress\bi\App\Models\backyard\HrResumeAiScoreModel;
use FlashExpress\bi\App\Models\backyard\HrResumeExtendModel;
use FlashExpress\bi\App\Models\backyard\HrResumeFilterModel;
use FlashExpress\bi\App\Models\backyard\HrResumeModel;
use FlashExpress\bi\App\Models\backyard\ResumeOutLogModel;
use FlashExpress\bi\App\Models\backyard\SalaryApproveModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\SysDepartmentModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Server\HcServer;
use FlashExpress\bi\App\Server\ResumeServer;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Models\fle\StaffInfoModel;
use  FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Server\InterviewServer;
use FlashExpress\bi\App\Server\MessageQueueServer;
use FlashExpress\bi\App\Server\StaffServer;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoPositionModel;
use FlashExpress\bi\App\Server\SysListServer;
use FlashExpress\bi\App\Server\SysServer;

class InterviewRepository extends BaseRepository
{

    protected $db;
    protected $db_rby;
    protected $staff;
    public $staffInfoId;
    public $staffName;
    //hr interview status
    const NO_COMMUNICATION            = 1;//未沟通
    const WAITING_SELECT              = 2;//待筛选
    const REHC_NO_COMMUNICATION       = 3;//重新关联hc待反馈（属于已沟通）
    const WAITING_INTERVIEW           = 5;//待面试

    const INTERVIEW_IN_THE            = 10;//面试中

    const INTERVIEW_ON_BEHALF_OFFER   = 20;//代发offer

    const INTERVIEW_LSSUED_OFFER      = 25;//已发offer

    const INTERVIEW_REJECTED          = 30;//已拒绝

    const INTERVIEW_CANCELLED         = 31;//已取消

    const INTERVIEW_DELETED           = 32;//已删除

    const INTERVIEW_EMPLOYEED         = 40;//已入职

    //其他
    const INTERVIEW_TWENTY_SEVEN      = 27;//面试中：面试管理-hc列表页签中面试中人数筛选条件
    const INTERVIEW_TWENTY_SIX      = 26;//已招人数：面试管理-hc列表页签中已招人数筛选条件

    //面试中是否有反馈 is_feedback
    const INTERVIEW_IS_FEEDBACK_YES   = 1;//有反馈
    const INTERVIEW_IS_FEEDBACK_NO    = 2;//无反馈


    public function __construct($userInfo = [])
    {
        parent::__construct();
        $this->staffName = isset($this->userInfo['name']) ? $this->userInfo['name'] : '';
        $this->staffInfoId = isset($this->userInfo['staff_id']) ? $this->userInfo['staff_id'] : '';
        $this->staff = new StaffRepository();
        $this->db = $this->getDI()->get('db');//主库（写）
        $this->db_rby = $this->getDI()->get('db_rby');//从库（读）
        $this->userInfo = $userInfo ? $userInfo : $this->userInfo;
        $this->lang = !empty($userInfo['lang'] ) ? $userInfo['lang'] : $this->lang;
    }

    public function getInterviewResume($paramIn = [])
    {
        //参数
        $phone                             = $paramIn["phone"] ?? "";
        $job_name                          = $paramIn["job_name"] ?? "";
        $hc_job_name                       = $paramIn["hc_job_name"] ?? "";
        $province_name                     = $paramIn["province_name"] ?? "";
        $work_city_id                      = $paramIn["work_city_id"] ?? "";
        $work_district_id                  = $paramIn["work_district_id"] ?? "";
        $state                             = $paramIn["state"] ?? "";
        $updated_start                     = $paramIn["updated_start"] ?? "";
        $updated_end                       = $paramIn["updated_end"] ?? "";
        $delivery_time_start               = $paramIn["delivery_time_start"] ?? "";
        $delivery_time_end                 = $paramIn["delivery_time_end"] ?? "";
        $ai_score_type                     = $paramIn["ai_score_type"] ?? [];
        $created_start                     = $paramIn["created_start"] ?? "";
        $created_end                       = $paramIn["created_end"] ?? "";
        $department_id                     = $paramIn["department_id"] ?? "";
        $hc_department_id                  = $paramIn["hc_department_id"] ?? "";
        $interview_id                      = $paramIn["interview_id"] ?? "";
        $store_id                          = $paramIn["store_id"] ?? "";
        $hc_id                             = $paramIn["hc_id"] ?? "";
        $cvId                              = $paramIn["cv_id"] ?? "";
        $approval_status                   = $paramIn["approval_status"] ?? "";
        $send_time_start                   = $paramIn["send_time_start"] ?? "";
        $send_time_end                     = $paramIn["send_time_end"] ?? "";
        $resume_store_id                   = $paramIn["resume_store_id"] ?? "";
        $noPage                            = $paramIn["noPage"] ?? 0;
        $pageNum                           = $paramIn["page_num"] ?? 1;
        $pageSize                          = $paramIn["page_size"] ?? 10;
        $pageSize                          = !$noPage ? $pageSize : 50000;
        $resumeIds                         = $paramIn["resumeIds"] ?? [];
        $alternativeJobId                  = $paramIn["alternative_job_id"] ?? 0;
        $isWeedOut                         = $paramIn["is_weed_out"] ?? 0;
        $expected_arrivaltime_left         = $paramIn['expected_arrivaltime_left'] ?? '';
        $expected_arrivaltime_right        = $paramIn['expected_arrivaltime_right'] ?? '';
        $job_title                         = $paramIn["job_title"] ?? "";
        $authority_department_ids          = $paramIn["authority_department_ids"] ?? [];     //权限
        $authority_stores_ids              = $paramIn["authority_stores_ids"] ?? [];         //权限
        $is_admin                          = $paramIn["is_admin"] ?? enums::$is_admin['on']; //权限 1 超管
        $out_type                          = $paramIn["out_type"] ?? "";
        $is_feedback                       = $paramIn["is_feedback"] ?? "";          //面试中，是否有反馈
        $resume_source                     = $paramIn["resume_source"] ?? "";        //简历来源
        $resume_approve_state              = $paramIn["resume_approve_state"] ?? ""; //by内推简历审核状态
        $recommender_staff_id              = $paramIn["recommender_staff_id"] ?? ""; //by内推简历推荐人工号
        $process_status                    = $paramIn["process_status"] ?? 0;        //客服审核状态
        $reason                            = $paramIn["reason"] ?? 0;                //终止原因
        $filter_state                      = $paramIn["filter_state"] ?? 0;          //简历筛选状态：1-筛选待反馈，2-筛选通过，3-筛选不通过，4-筛选撤回（取消）
        $entry_status                      = $paramIn['entry_status'] ?? 0;          //入职状态
        $entry_staff_id                    = $paramIn['entry_staff_id'] ?? 0;
        $is_menu_permission                = $paramIn['is_menu_permission'] ?? 0;
        $interview_communication_status    = $paramIn['interview_communication_status'] ?? 0;   //面试管理-沟通状态下拉code
        $communication_failure_reason_type = $paramIn['communication_failure_reason_type'] ?? 0;//面试管理-沟通失败原因下拉code,1=联系不上、2=要求岗位或网点没有hc、3=不符合岗位要求、4=候选人已拒绝、5=其他
        $recruit_channel                   = $paramIn['recruit_channel'] ?? 0;//招聘渠道
        $recruit_type                      = $paramIn['recruit_type'] ?? 0;
        $last_operator_query_type          = intval($paramIn['last_operator_query_type']);           // 简历最新操作人筛选类型
        $resume_last_operator              = $paramIn['resume_last_operator'];               // 简历最新操作人
        $is_relation_hc                    = $paramIn['is_relation_hc'];                     // 是否关联hc 1-是 2-否 0表示所有
        $priority_ids                      = $paramIn['priority_ids'] ?? [];                 // hc优先级
        $credentials_num                   = $paramIn['credentials_num'] ?? '';
        $manage_region                     = $paramIn['manage_region'] ?? [];
        $manage_piece                      = $paramIn['manage_piece'] ?? [];
        $hire_type                         = $paramIn['hire_type'] ?? []; // 雇佣类型
        $reserve_type                      = $paramIn['reserve_type'] ?? ''; // 简历类型

        $deleted = 0;
        $ifHrisSales = $paramIn['ifHrisSales']??false;
        $join_str ="LEFT JOIN";
        if($ifHrisSales){
            $join_str="JOIN";
        }

        // 初始化返回
        $returnData = [
            'dataList' => [],
            'pagination' => [
                'count' => 0,
                'pageCount' => 0,
                'pageNum' => $pageNum,
                'pageSize' => $pageSize
            ]
        ];
        $builder = $this->modelsManager->createBuilder();
        $findBuilder = clone $builder;
        $builder->columns('count(resume.id) as total')->from(['resume' => HrResumeModel::class]);
        $builder->leftjoin(HrInterviewModel::class,"interview.resume_id = resume.id and interview.hc_id = resume.hc_id","interview");
        $builder->leftjoin(HrInterviewSubscribeModel::class,"interview_subscribe.interview_id = interview.interview_id","interview_subscribe");
        $builder->leftjoin(HrEntryModel::class,"entry.interview_id = interview.interview_id","entry");
        $builder->where('resume.deleted = '.$deleted);
        $hrhcFlag = $hrJdFlag = $interviewSubFlag = $salaryApproveFlag = false;

        //简历最新操作人查询
        switch ($last_operator_query_type) {
            case 0://ALL
                if (!empty($resume_last_operator) && $resume_last_operator > 0) {
                    $builder->andWhere("resume.resume_last_operator = :resume_last_operator_staff_id:",
                        ["resume_last_operator_staff_id" => intval($resume_last_operator)]);
                }
                break;
            case 1://为空
                $builder->andWhere("resume.resume_last_operator = :resume_last_operator:",
                    ["resume_last_operator" => 0]);
                break;
            case 2://不为空
                $builder->andWhere("resume.resume_last_operator > :resume_last_operator:",
                    ["resume_last_operator" => 0]);

                if (!empty($resume_last_operator) && $resume_last_operator > 0) {
                    $builder->andWhere("resume.resume_last_operator = :resume_last_operator_staff_id:",
                        ["resume_last_operator_staff_id" => intval($resume_last_operator)]);
                }
                break;
        }

        if (!empty($is_relation_hc)) {
            $hrhcFlag = true;
            if (1 == $is_relation_hc) {
                $builder->andWhere("resume.hc_id > :hc_id:", ["hc_id" => 0]);
            }
            if (2 == $is_relation_hc) {
                $builder->andWhere("resume.hc_id = :hc_id:", ["hc_id" => 0]);
            }
        }

        //检索条件
        $listSqlMain = '';
        //resume_id
        if($expected_arrivaltime_left || $expected_arrivaltime_right){
            $builder->leftjoin(HrInterviewOfferModel::class,"interview_offer.interview_id = interview.interview_id","interview_offer");
            if($expected_arrivaltime_left){
                $expected_arrivaltime_left = date('Y-m-d 00:00:00',strtotime($expected_arrivaltime_left));
                $builder->andWhere("interview_offer.work_time >= :work_time_start:", [ "work_time_start" => $expected_arrivaltime_left ] );
            }
            if($expected_arrivaltime_right){
                $expected_arrivaltime_right = date('Y-m-d 23:59:59',strtotime($expected_arrivaltime_right));
                $builder->andWhere("interview_offer.work_time <= :work_time_end:", [ "work_time_end" => $expected_arrivaltime_right ] );
            }
        }
        //手机号
        if (!empty($phone)) {
            $phone = str_replace(" ", '', $phone);
            $phone = str_replace("'", '', $phone);
            $builder->andWhere(
       'resume.phone like :phone: or resume.name like :name: or resume.first_name_en like :first_name_en: or resume.last_name_en like :last_name_en:',
                ['phone' => "%{$phone}%", 'name' => "%{$phone}%", 'first_name_en' => "%{$phone}%", 'last_name_en' => "%{$phone}%"]);
        }
        if (!empty($isWeedOut) && !empty($hc_id)) {
            // 淘汰人数
            $interviews = ResumeOutLogModel::find([
                'conditions' => ' hc_id = :hc_id:  ',
                'bind' => [
                    'hc_id' => $hc_id
                ]
            ])->toArray();
            $resumeIds = array_column($interviews, 'resume_id');
//            $resumeIdsStr = implode(',',$resumeIds);
//            $resumeIdsStr = empty($resumeIdsStr) ? "''" : $resumeIdsStr;
//            $listSqlMain .= " And resume.id in ({$resumeIdsStr}) ";
            $builder->inWhere('resume.id',$resumeIds);

        }
        if (!empty($reserve_type)) {
            $builder->andWhere("resume.reserve_type = :reserve_type:", [ "reserve_type" => $reserve_type ] );

        }

        if (!empty($job_name)) {
            $builder->andWhere("resume.job_id = :job_id:", [ "job_id" => $job_name ] );

        }

        if ($process_status) {
            $builder->andWhere("resume.process_status = :process_status:", [ "process_status" => $process_status ] );
        }

        if ($reason) {
            $builder->andWhere("resume.reason = :reason:", [ "reason" => $reason ] );
        }

        if (!empty($hc_job_name)) {

            $hrhcFlag = true;
            $builder->andWhere("hc.job_id = :hc_job_id:", [ "hc_job_id" => $hc_job_name ] );

        }
        if (!empty($paramIn['hc_state_code']) && is_array($paramIn['hc_state_code'])){
            $hrhcFlag = true;
            $builder->andWhere("hc.state_code IN ({hc_state_code:array})", ["hc_state_code" => $paramIn['hc_state_code']]);
        }
        if (!empty($province_name)) {
            $builder->andWhere("resume.address_id = :address_id:", [ "address_id" => $province_name ] );
        }

        //期望城市
        if (!empty($work_city_id)) {
            $work_city_id = array_values(array_unique($work_city_id));
            $builder->andWhere("resume.work_city_id IN ({work_city_id:array})", ["work_city_id" => $work_city_id]);
        }
        //期望城市-区
        if (!empty($work_district_id)) {
            $work_district_id = array_values(array_unique($work_district_id));
            $builder->andWhere("resume.work_district_id IN ({work_district_id:array})", ["work_district_id" => $work_district_id]);
        }

        //调整日期
        if (!empty($updated_start)) {
            $updated_start = zero_time_zone(date("Y-m-d 00:00:00", strtotime($updated_start)));
            $builder->andWhere("resume.updated_at >=  :resume_updated_at_start:",['resume_updated_at_start' => $updated_start]);
        }

        if (!empty($updated_end)) {
            $updated_end = zero_time_zone(date("Y-m-d 23:59:59", strtotime($updated_end)));
            $builder->andWhere("resume.updated_at <=  :resume_updated_at_end:",['resume_updated_at_end' => $updated_end]);
        }

        if (!empty($created_start)) {
            $created_start = zero_time_zone(date("Y-m-d 00:00:00", strtotime($created_start)));
            $builder->andWhere("resume.created_at >=  :resume_created_at_start:",['resume_created_at_start' => $created_start]);
        }

        if (!empty($created_end)) {
            $created_end = zero_time_zone(date("Y-m-d 23:59:59", strtotime($created_end)));
            $builder->andWhere("resume.created_at <=  :resume_created_at_end:",['resume_created_at_end' => $created_end]);
        }

        if (!empty($send_time_start) && !empty($send_time_end)) {
            $send_time_start = date("Y-m-d 00:00:00", strtotime($send_time_start));
            $send_time_end = date("Y-m-d 23:59:59", strtotime($send_time_end));
            $builder->andWhere(
                "interview.send_time >= CONVERT_TZ('".$send_time_start."', '" . $this->timezone . "', '+00:00' )
                 and interview.send_time <= CONVERT_TZ('". $send_time_end . "', '" . $this->timezone . "', '+00:00' )"
            );

        }

        $is_join_c_r_log = false;
        // 沟通状态
        if (!empty($interview_communication_status)){
            $builder->leftjoin(CommunicationResumeLogModel::class,"c_r_log.resume_id = resume.id","c_r_log");
            $is_join_c_r_log = true;
            if ($interview_communication_status == 1){
                // 沟通状态=沟通成功
                $builder->andWhere("c_r_log.newest = 1 and c_r_log.status = :interview_communication_status:", [ "interview_communication_status" => $interview_communication_status ] );
            }elseif ($interview_communication_status == 2){
                // 沟通状态=沟通失败
                $builder->andWhere("c_r_log.newest = 1 and c_r_log.status = :interview_communication_status:", [ "interview_communication_status" => $interview_communication_status ] );
            }elseif ($interview_communication_status == 3){
                // 沟通状态=无沟通记录
                $builder->andWhere("c_r_log.id is null" );
            } elseif ($interview_communication_status == CommunicationResumeLogModel::STATUS_AFRESH) {
                // 沟通状态=重新沟通
                $builder->andWhere("c_r_log.newest = :newest: and c_r_log.status = :interview_communication_status:", [
                    'newest'                         => CommunicationResumeLogModel::NEWST_YES,
                    'interview_communication_status' => $interview_communication_status
                ]);
            }
        }
        // 沟通失败原因
        if (!empty($communication_failure_reason_type)){
            if(!$is_join_c_r_log){
                $builder->leftjoin(CommunicationResumeLogModel::class,"c_r_log.resume_id = resume.id","c_r_log");
            }
            $builder->andWhere("c_r_log.newest = 1 and c_r_log.failure_reason_type = :failure_reason_type:", [ "failure_reason_type" => $communication_failure_reason_type ] );
        }
        if( !empty( $state ) ){

            if ( $state == self::NO_COMMUNICATION || $state == self::REHC_NO_COMMUNICATION ) {
                //状态为未沟通
                $builder->andWhere("resume.state_code = :state_code:", [ "state_code" => $state ] );

            }elseif( $state == self::INTERVIEW_IN_THE ){
                //面试中
                $interviewSubFlag = true;

                $builder->andWhere("interview.state = :state:", [ "state" => $state ] );

                //面试中，是否有反馈
                if( $is_feedback == self::INTERVIEW_IS_FEEDBACK_YES ){//有反馈
                    $builder->andWhere(
                        " EXISTS (select 1 from ".HrInterviewInfoModel::class." interview_info where interview_info.interview_id = interview_subscribe.interview_id ) 
                        or NOT EXISTS (select 1 from ".HrInterviewerOperationModel::class." interview_operation where interview_operation.state = ".HrInterviewerOperationModel::STATE_NOT_OPERATION." and interview_operation.interview_sub_id = interview_subscribe.interview_id)
                        "
                    );
                }
                //未反馈
                if( $is_feedback == self::INTERVIEW_IS_FEEDBACK_NO ){
                    $builder->andWhere(
                        " ( NOT EXISTS (select 1 from ".HrInterviewInfoModel::class." interview_info where interview_info.interview_id = interview_subscribe.interview_id) 
                        OR EXISTS (select 1 from ".HrInterviewerOperationModel::class." interview_operation where interview_operation.state = ".HrInterviewerOperationModel::STATE_NOT_OPERATION." and interview_operation.interview_sub_id = interview_subscribe.interview_id) )
                        "
                    );
                }

            }elseif( $state == self::INTERVIEW_REJECTED ){

                //已拒绝
                $builder->andWhere("resume.is_out = :is_out: or interview.state = :state:", [ "is_out" => HrResumeModel::IS_OUT_YEW, "state" => self::INTERVIEW_REJECTED ] );

            }elseif( $state == self::INTERVIEW_TWENTY_SEVEN ){ //面试管理-hc列表-面试中人数

                //待面试  面试中 代发offer
                $builder->inWhere('interview.state', [ self::WAITING_INTERVIEW,self::INTERVIEW_IN_THE,self::INTERVIEW_ON_BEHALF_OFFER ] );

            }elseif( $state == self::INTERVIEW_TWENTY_SIX ){//面试管理-hc列表-已招人数，由于非一线职位已发offer时，人数并没有变更，导致已招人数和列表数据对不上，所以改为读取入职表

                //25 已发offer 40 已入职
                $builder->inWhere('entry.status', [ 1,2] );

            }elseif(in_array($state,array_values(enums::$resume_filter_state))){

                $builder->andWhere("resume.filter_state = :filter_state:", [ "filter_state" => $state ] );

            }else{

                $builder->andWhere("interview.state = :state:", [ "state" => $state ] );
            }
        }

        //入职状态筛选
        if(!empty($entry_status)){
            $builder->andWhere("entry.status = :entry_status:", [ "entry_status" => $entry_status ] );
        }

        //入职工号筛选
        if(!empty($entry_staff_id)){
            $builder->andWhere("entry.staff_id = :staff_id:", [ "staff_id" => $entry_staff_id ] );
        }

        if (!empty($interview_id)) {
            $builder->andWhere("interview.interview_id = :interview_id:", [ "interview_id" => $interview_id ] );
        }

        if (!empty($approval_status)) {
            $salaryApproveFlag = true;
            $builder->andWhere("sa.status = :sa_status:", [ "sa_status" => $approval_status ] );
        }

//        if (!empty($department_id)) {
//            $hrJdFlag = true;
//            if (is_array($department_id)) {
//                $department_id = implode(",", $department_id);
//                $builder->inWhere("jd.department_id", $department_id );
//            } else {
//                $builder->andWhere("jd.department_id = :jd_department_id:", [ "jd_department_id" => $department_id ] );
//            }
//        }
//        if (!empty($hc_department_id)) {
//            $hrhcFlag = true;
//            $builder->andWhere("hc.department_id = :hc_department_id:", [ "hc_department_id" => $hc_department_id ] );
//        }
        if (!empty($paramIn["department_id"])) {
            $deptIds = SysServer::getDepartmentConditionByParams($paramIn);
            if (!empty($deptIds)) {
                $hrJdFlag = true;
                $builder->inWhere('jd.department_id', $deptIds);
            }
        }
        if (!empty($paramIn["hc_department_id"])) {
            $deptIds = SysServer::getDepartmentConditionByParams(['department_id'=>$paramIn["hc_department_id"],'is_sub_department'=>$paramIn['is_sub_department'] ?? 2]);
            if (!empty($deptIds)) {
                $hrhcFlag = true;
                $builder->inWhere('hc.department_id', $deptIds);
            }
        }
        
        if (!empty($store_id)) {
            $hrhcFlag = true;
            if(is_array($store_id)){
                $store_id = explode(",", getIdsStr($store_id));
                $builder->inWhere("hc.worknode_id", $store_id );
            }else{
                $builder->andWhere("hc.worknode_id = :worknode_id:", [ "worknode_id" => $store_id ] );
            }
        }

        if ( !empty( $cvId ) ) {
            $builder->andWhere("resume.id = :resumeid:", [ "resumeid" => $cvId ] );
        }

        if ( empty( $isWeedOut ) && !empty( $hc_id ) ) {
            $hrhcFlag = true;
            $builder->andWhere("( hc.hc_id = :hc_hc_id: or resume.hc_id = :hc_hc_id: ) ", [ "hc_hc_id" => $hc_id ] );
        }

        if ( !empty( $resume_store_id ) ) {

            if( is_array( $resume_store_id ) ){
                $resumeStoreIdArr = explode(",", getIdsStr($resume_store_id) );
                $builder->inWhere("resume.store_id", $resumeStoreIdArr );
            }else{
                $builder->andWhere("resume.store_id = :resume_store_id:", [ "resume_store_id" => $resume_store_id ] );
            }

        }

        if ( !empty($job_title) ) {
            $hrhcFlag = true;
            $builder->andWhere("hc.job_title = :job_title:", [ "job_title" => $job_title ] );
        }

        if ( !empty($alternativeJobId) ) {
            $builder->andWhere('find_in_set(' . $alternativeJobId . ', resume.alternative_job_ids)' );
        }

        if(!empty($out_type)){
            $builder->andWhere("resume.out_type = :out_type:", [ "out_type" => $out_type ] );
        }

        if(!empty($resume_source)){
            $builder->andWhere("resume.source = :source:", [ "source" => $resume_source ] );
        }

        if(!empty($resume_approve_state)){
            $builder->andWhere("resume.approve_state = :resume_approve_state:", [ "resume_approve_state" => $resume_approve_state ] );
        }

        if(!empty($recommender_staff_id)){
            $builder->andWhere("resume.recommender_staff_id = :recommender_staff_id:", [ "recommender_staff_id" => $recommender_staff_id ] );
        }

        //招聘渠道
        if (!empty($recruit_channel)) {
            $builder->andWhere("resume.recruit_channel = :recruit_channel:", [ "recruit_channel" => $recruit_channel ] );
        }

        //招聘类型
        if (!empty($recruit_type)) {
            $builder->andWhere("resume.recruit_type = :recruit_type:", [ "recruit_type" => $recruit_type ] );
        }

        //不是管理员 并且开启了菜单权限
        $is_role_permission = 0;
        $winhr_staff_role_ids = SettingEnvServer::getSetVal('winhr_staff_role_ids');
        if ($winhr_staff_role_ids && array_intersect(explode(',', $this->userInfo['position_category']), explode(',', $winhr_staff_role_ids))) {
            $is_role_permission = 1;
        }
        if( $is_admin == enums::$is_admin['off'] && $is_menu_permission && empty($is_role_permission)){
            $hrhcFlag = true;
           // $this->getDi()->get('logger')->write_log(['assembleAuthoritySql'=>$this->userInfo], 'notice');
            $authoritySql = (new HcServer())->assembleAuthoritySql('resume',$this->userInfo);
            if ($authoritySql) {
                $builder->andWhere($authoritySql);
            }
        }

        //简历优先级
        if (!empty($priority_ids)) {
            $hrhcFlag = true;
            $builder->inWhere('hc.priority_id', $priority_ids);
        }

        //身份证号
        if (!empty($credentials_num)) {
            $builder->andWhere('resume.credentials_num = :credentials_num:', ['credentials_num' => $credentials_num]);
        }

        //投递日期
        if (!empty($delivery_time_start)) {
            $extendFlag = true;
            $delivery_time_start = zero_time_zone(date("Y-m-d 00:00:00", strtotime($delivery_time_start)));
            $builder->andWhere("resume_extend.delivery_time >=  :delivery_time_start:",['delivery_time_start' => $delivery_time_start]);
        }

        if (!empty($delivery_time_end)) {
            $extendFlag = true;
            $delivery_time_end = zero_time_zone(date("Y-m-d 23:59:59", strtotime($delivery_time_end)));
            $builder->andWhere("resume_extend.delivery_time <=  :delivery_time_end:",['delivery_time_end' => $delivery_time_end]);
        }

        //AI评分枚举
        if (!empty($ai_score_type)) {
            //插入分数
            $scoreTypesList = array_map(function ($item) {
                return HrResumeAiScoreModel::$scoreInterval[$item] ?? [];
            }, $ai_score_type);

            if (!empty($scoreTypesList)) {
                // 找到最小的 start 和最大的 end
                $minStart = min(array_column($scoreTypesList, 'start'));
                $maxEnd   = max(array_column($scoreTypesList, 'end'));


                $extendFlag = true;
                $builder->andWhere("resume_extend.ai_score >=  :ai_score_start:",
                    ['ai_score_start' => $minStart]);
                $builder->andWhere("resume_extend.ai_score <  :ai_score_end:", ['ai_score_end' => $maxEnd]);
            }
        }

        //大区
        if (!empty($manage_region)) {
            $sysStoreFlag = $hrhcFlag = true;
            $builder->inWhere('store.manage_region', $manage_region);
        }

        //片区
        if (!empty($manage_piece)) {
            $sysStoreFlag = $hrhcFlag = true;
            $builder->inWhere('store.manage_piece', $manage_piece);
        }

        //雇佣类型
        if (!empty($hire_type)) {
            $hrhcFlag = true;
            $builder->inWhere('hc.hire_type', $hire_type);
        }

        if (isCountry('TH') && !empty($paramIn['is_task_new_hc_id'])) {
            $builder->andWhere("resume.hc_id !=0 and resume.task_old_hc_id !=0 and resume.hc_id = resume.task_old_hc_id");
            $builder->andWhere("(entry.status != " . HrEntryModel::STATUS_EMPLOYED . " or entry.status is null)");
        }

        //hc链接
        if ($hrhcFlag) {
            //面试表是最新的
            $builder->leftjoin(HrhcModel::class, "resume.hc_id = hc.hc_id", "hc");
        }

        //网点链接
        if ($sysStoreFlag) {
            $builder->leftjoin(SysStoreModel::class, "hc.worknode_id = store.id", "store");
        }

        if( $hrJdFlag ){
            if($ifHrisSales){
                $builder->join(HrJdModel::class," jd.job_id = resume.job_id ","jd");
            }else{
                $builder->leftjoin(HrJdModel::class," jd.job_id = resume.job_id ","jd");
            }
        }

        if (!empty($extendFlag)) {
            $builder->leftjoin(HrResumeExtendModel::class," resume.id = resume_extend.resume_id ","resume_extend");
        }

//        $builder->leftjoin(SalaryApproveModel::class," sa.resume_id = resume.id and sa.id = (SELECT max( salaryApprove.id ) as sa_id FROM ".SalaryApproveModel::class." salaryApprove  WHERE salaryApprove.resume_id = resume.id GROUP BY salaryApprove.resume_id) ","sa");

        if( $salaryApproveFlag ){
            $builder->leftjoin(SalaryApproveModel::class," sa.resume_id = resume.id and sa.id = (SELECT max( salaryApprove.id ) as sa_id FROM ".SalaryApproveModel::class." salaryApprove  WHERE salaryApprove.resume_id = resume.id GROUP BY salaryApprove.resume_id) ","sa");
        }
        $builder->orderBy('resume.updated_at desc');
        $totalInfo  = $builder->getQuery()->getSingleResult();
        $count = intval($totalInfo->total);
        $this->getDI()->get('logger')->write_log("interviewResumeListAction resumeTotal:" . $count, 'info');

        $builder->columns([
            'resume.id as resume_id',
            'resume.hc_id as hc_id',
            'resume.job_id as expect_job_id'
        ]);
        $builder->limit($pageSize,($pageNum - 1) * $pageSize);
        $data = $builder->getQuery()->execute()->toArray();

//        print_r($builder->getQuery()->getSql());exit;
        //记录执行sql
        $this->getDI()->get('logger')->write_log("interviewResumeListAction resumeSql:" . var_export($builder->getQuery()->getSql(),true), 'info');

        $resume_ids = array_column($data,'resume_id');
        $resume_filed = [
            'resume.id as resume_id',
            'resume.credentials_num',
            'resume.recruit_channel',
            'resume.source',
            'resume.submitter_id',
            'resume.is_perfect',
            'resume.is_out',
            'resume.name',
            'resume.first_name',
            'resume.last_name',
            'resume.middle_name',
            'resume.suffix_name',
            'resume.first_name_en',
            'resume.last_name_en',
            'resume.sex',
            'resume.call_name',
            'resume.nationality',
            'resume.phone_area_code',
            'resume.phone',
            'resume.email',
            'resume.currency',
            'resume.out_type',
            'resume.date_birth',
            'resume.address_id AS work_code',
            'resume.work_city_id',
            'resume.work_district_id',
            'resume.entry_salary',
            'resume.state_code',
            'resume.store_id',
            'resume.hc_id',
            'resume.is_can_change_filter_result',
            'resume.job_id as expect_job_id',
            'resume.alternative_job_ids as alternative_job_ids',
            'resume.recommender_staff_id',
            'resume.recommend_store_id',
            'resume.approve_state',
            'resume.current_salary',
            'resume.exclusive_customer',
            'resume.process_status',
            'resume.reason',
            'resume.filter_state',
            'resume.recruiter_id',
            'resume.recruit_type',
            'resume.reserve_type',
            'resume.resume_last_operator',
            'resume.expected_arrivaltime',
            'hc.priority_id',
            'hc.hire_type',
            'hc.state_code as hc_state_code',
            "DATE_FORMAT( CONVERT_TZ( resume.created_at, '+00:00', '" . $this->timezone . "' ), '%Y/%m/%d %H:%i:%s' ) AS resume_created_at",
            "CONVERT_TZ(resume.created_at, '+00:00', '" . $this->timezone . "' ) AS created_at",
            'resume.line_id',
            "resume_extend.current_school_id",
            "resume_extend.current_college",
            "resume_extend.current_major",
            "resume_extend.current_grade_id",
            "resume_extend.work_start_date",
            "resume_extend.work_end_date",
            "resume_extend.tutor_email",
            "resume_extend.tutor_phone_area_code",
            "resume_extend.tutor_phone",
            "resume_extend.delivery_time",
            "resume_extend.ai_score",
            "resume_extend.interested_department_id",
            "resume_extend.interested_department_other",
            "resume_extend.old_staff_id",
            "resume.is_head_office_recruit",
        ];
        //获取resume简历表展示字段
        $findBuilder->columns($resume_filed)->from(['resume' => HrResumeModel::class]);
        $findBuilder->inWhere('resume.id',$resume_ids);
        $findBuilder->leftJoin(HrResumeExtendModel::class," resume_extend.resume_id = resume.id","resume_extend");
        $findBuilder->leftjoin(HrhcModel::class,"hc.hc_id = resume.hc_id","hc");
        $findBuilder->orderBy('resume.updated_at desc');
        $resumeRst = $findBuilder->getQuery()->execute()->toArray();

        $expect_job_ids = array_column($resumeRst,'expect_job_id');
        if( !empty($expect_job_ids) ){
            $expect_job_ids = array_unique($expect_job_ids);
        }

        $resume_hc_ids = array_column($resumeRst,'hc_id');

        //查询面试表
        $builderInterview = $this->modelsManager->createBuilder();
        $builderInterview->columns([
            'interview.resume_id',
            'interview.state',
            'interview.hc_id',
            "DATE_FORMAT( CONVERT_TZ( interview.send_time, '+00:00', '" . $this->timezone . "' ), '%Y/%m/%d %H:%i:%s' ) AS send_time",
            'interview.interview_id',
            'manager_id',
            'manager_name',
            'working_day_rest_type'
        ])->from(['interview' => HrInterviewModel::class]);
        $builderInterview->rightjoin(HrResumeModel::class," interview.hc_id = resume.hc_id and interview.resume_id = resume.id","resume");
        $builderInterview->inWhere('interview.resume_id',$resume_ids);
        $interviewRst = $builderInterview->getQuery()->execute()->toArray();
        $interviewRst = array_column($interviewRst,null,'resume_id');
        $interview_ids = array_column($interviewRst,'interview_id');
        $interview_hc_ids = array_column($interviewRst,'hc_id');

        $all_hc_ids = [];
        if($resume_hc_ids){
            $all_hc_ids = array_merge($all_hc_ids,array_filter($resume_hc_ids));
        }
        if($interview_hc_ids){
            $all_hc_ids = array_merge($all_hc_ids,array_filter($interview_hc_ids));
        }
        $all_hc_ids = array_unique($all_hc_ids);
        //查询hr_hc表
        $builderHrHc = $this->modelsManager->createBuilder();
        $builderHrHc->columns([
            'hc.worknode_id',
            'hc.department_id AS hc_department_id',
            'hc.job_id AS hc_job_id',
            'hc.hire_type',
            'hc.province_code AS province_code',
            'hc.job_title as job_title_id',
            'hc.worknode_id as hc_worknode_id',
            'hc.hc_id'
        ])->from(['hc' => HrhcModel::class]);
        $builderHrHc->inWhere('hc.hc_id',$all_hc_ids);
        $hrHcRst = $builderHrHc->getQuery()->execute()->toArray();

        $hrHcRst = array_column($hrHcRst,null,'hc_id');
        //查询hr_jd
        $builderHrJd = $this->modelsManager->createBuilder();
        $builderHrJd->columns([
            'jd.job_name',
            'jd.job_id',
            'jd.department_id',
        ])->from(['jd' => HrJdModel::class]);
        $builderHrJd->inWhere('jd.job_id',$expect_job_ids);
        $hrJdRst = $builderHrJd->getQuery()->execute()->toArray();
        $hrJdRst = array_column($hrJdRst,null,'job_id');

        //查询预约面试表
        $builderInterviewSub = $this->modelsManager->createBuilder();
        $builderInterviewSub->columns(['interview_subscribe.id as sub_id,interview_subscribe.interview_id,interview_subscribe.interview_back'])->from(['interview_subscribe' => HrInterviewSubscribeModel::class]);
        $builderInterviewSub->inWhere('interview_subscribe.interview_id',$interview_ids);
        $interviewSubRst = $builderInterviewSub->getQuery()->execute()->toArray();
        $interviewSubRst = array_column($interviewSubRst,null,'interview_id');

        //查询入职表表
        $entryRst = [];
        if (!empty($interview_ids)) {
            $builderEntry = $this->modelsManager->createBuilder();
            $builderEntry->columns(['entry.interview_id,entry.status,entry.staff_id'])->from(['entry' => HrEntryModel::class]);
            $builderEntry->inWhere('entry.interview_id',$interview_ids);
            $entryRst = $builderEntry->getQuery()->execute()->toArray();
            $entryRst = array_column($entryRst,null,'interview_id');
        }
        
        //获取简历筛选最新一条数据中的筛选信息
        $resumeFilterInfo = $this->getBatchResumeFilterLastedData($resume_ids);
        $t =  $this->getTranslation();
        $entry_status_text = [
            1 => $t->_('4903'),
            2 => $t->_('4904'),
            3 => $t->_('4905'),
        ];

        //查询沟通记录表
        $builder_c_r_log = $this->modelsManager->createBuilder();
        $builder_c_r_log->columns([
            'c_r_log.resume_id',
            'c_r_log.status',
            'c_r_log.failure_reason_type',
            'c_r_log.success_reason_type',
            'c_r_log.content'
        ])->from(['c_r_log' => CommunicationResumeLogModel::class]);
        $builder_c_r_log->inWhere('c_r_log.resume_id',$resume_ids);
        $builder_c_r_log->andWhere("c_r_log.newest = 1");
        $c_r_log_Rst = $builder_c_r_log->getQuery()->execute()->toArray();
        $c_r_log_status_Rst = array_column($c_r_log_Rst,'status','resume_id');
        $reason_type_rst = array_column($c_r_log_Rst,null,'resume_id');
        $c_r_log_status_text = [
            CommunicationResumeLogModel::STATUS_SUCCESS => $t->_('communicate_state_1'),//沟通成功
            CommunicationResumeLogModel::STATUS_FAIL    => $t->_('communicate_state_2'),//沟通失败
            CommunicationResumeLogModel::STATUS_EMPTY   => $t->_('communicate_state_3'),//无沟通记录
            CommunicationResumeLogModel::STATUS_AFRESH  => $t->_('communicate_state_4'),//重新沟通
        ];

        // 简历类型
        $reserve_type_list = (new SysListServer())->reserveTypeList($this->lang);
        $reserve_type_arr = array_column($reserve_type_list,"name","key");
        unset($reserve_type_arr[0]);

        $syateArr = (new HcRepository())->getStateArr();
        $dataList = [];
        foreach ( $resumeRst as $key=>$val ){
            $dataList[$key] = $val;
            // 沟通状态
            if (!empty($c_r_log_status_Rst) && !empty($c_r_log_status_Rst[$val['resume_id']]) && in_array($c_r_log_status_Rst[$val['resume_id']],[
                    CommunicationResumeLogModel::STATUS_SUCCESS,
                    CommunicationResumeLogModel::STATUS_FAIL,
                    CommunicationResumeLogModel::STATUS_AFRESH
                ])){
                $communicate_state = $c_r_log_status_text[$c_r_log_status_Rst[$val['resume_id']]];
            }else{
                $communicate_state = $t->_('communicate_state_3');
            }


            $dataList[$key]['communicate_state_name'] = $communicate_state ?  : '';
            $dataList[$key]['communicate_state'] = $c_r_log_status_Rst[$val['resume_id']] ?? 0;

            // 沟通原因
            if (!empty($reason_type_rst) && !empty($reason_type_rst[$val['resume_id']]['failure_reason_type'])){
                $communicate_state_name = $this->getTranslation()->_('communication_failure_reason_'.$reason_type_rst[$val['resume_id']]['failure_reason_type']);
            }else{
                $communicate_state_name = "";
            }
            $dataList[$key]['communicate_failure_reason_type_name'] = $communicate_state_name ?? "";
            $dataList[$key]['communicate_failure_reason_type']      = $reason_type_rst[$val['resume_id']]['failure_reason_type'] ?? 0;

            if (isCountry('PH')) {
                $dataList[$key]['communicate_success_reason_type_name'] = '';
                $dataList[$key]['communicate_successs_reason_type']     = 0;

                if (
                    !empty($reason_type_rst[$val['resume_id']]['status'])
                    && $reason_type_rst[$val['resume_id']]['status'] == CommunicationResumeLogModel::STATUS_SUCCESS
                ) {
                    $success_reason_type = !empty($reason_type_rst[$val['resume_id']]['success_reason_type']) ? json_decode($reason_type_rst[$val['resume_id']]['success_reason_type'],
                        true) : [];

                    $success_reason_type_data = [];
                    foreach ($success_reason_type as $ve) {
                        $success_reason_type_data[] = $this->getTranslation()->_('communication_success_reason_' . $ve);
                    }
                    $dataList[$key]['communicate_success_reason_type_name'] = implode('; ', $success_reason_type_data);
                    $dataList[$key]['communicate_successs_reason_type']     = $success_reason_type;
                }

                $dataList[$key]['communicate_content'] = $reason_type_rst[$val['resume_id']]['content'] ?? '';
            }

            //面试列表 状态字段获取
            $interview_state = $interviewRst[$val['resume_id']]['state'] ?? null;
            $dataList[$key]['state'] = $this->getInterviewState($val['filter_state'],$interview_state,$val['is_out'],$val['state_code']);
            $hc_id = '';
            if ((!empty($interviewRst)&&!empty($interviewRst[$val['resume_id']]))) {

                $hc_id = $interviewRst[$val['resume_id']]['hc_id'];
            }
            if ($val['hc_id'] && $val['hc_id'] != 0) {

                $hc_id = $val['hc_id'];
            }
            if(isset($hrHcRst[$hc_id]) && empty($dataList[$key]['store_id'])){
                $dataList[$key]['store_id'] = $hrHcRst[$hc_id]['hc_worknode_id'];
            }

            $dataList[$key]['hc_id'] = $hc_id ?  : '';
            $dataList[$key]['interview_id'] = (!empty($interviewRst)&&!empty($interviewRst[$val['resume_id']])) ? $interviewRst[$val['resume_id']]['interview_id'] : '';
            $dataList[$key]['send_time'] = (!empty($interviewRst)&&!empty($interviewRst[$val['resume_id']])) ? $interviewRst[$val['resume_id']]['send_time'] : '';
            $dataList[$key]['worknode_id'] = (!empty($hrHcRst[$hc_id])) ? $hrHcRst[$hc_id]['worknode_id'] : '';
            $dataList[$key]['hc_department_id'] = (!empty($hrHcRst[$hc_id])) ? $hrHcRst[$hc_id]['hc_department_id'] : '';
            $dataList[$key]['hc_job_id'] = (!empty($hrHcRst[$hc_id])) ? $hrHcRst[$hc_id]['hc_job_id'] : '';
            $dataList[$key]['hire_type'] = (!empty($hrHcRst[$hc_id])) ? $hrHcRst[$hc_id]['hire_type'] : '';
            $dataList[$key]['hire_type_text'] = $dataList[$key]['hire_type'] ? $this->getTranslation()->_('hire_type_'.$dataList[$key]['hire_type']) : '';
            $dataList[$key]['reserve_type_text'] = $reserve_type_arr[$dataList[$key]['reserve_type']] ?? "";
            $dataList[$key]['province_code'] = (!empty($hrHcRst[$hc_id])) ? $hrHcRst[$hc_id]['province_code'] : '';
            $dataList[$key]['job_title_id'] = (!empty($hrHcRst[$hc_id])) ? $hrHcRst[$hc_id]['job_title_id'] : '';
            $expect_job_id = $val['expect_job_id'];
            $dataList[$key]['job_name'] = (!empty($hrJdRst[$expect_job_id])) ? $hrJdRst[$expect_job_id]['job_name'] : '';
            $dataList[$key]['job_id'] = (!empty($hrJdRst[$expect_job_id])) ? $hrJdRst[$expect_job_id]['job_id'] : '';
            $dataList[$key]['department_id'] = (!empty($hrJdRst[$expect_job_id])) ? $hrJdRst[$expect_job_id]['department_id'] : '';
            $interview_id = $dataList[$key]['interview_id'];
            $dataList[$key]['sub_id'] = (!empty($interviewSubRst[$interview_id])) ? $interviewSubRst[$interview_id]['sub_id'] : '';
            $dataList[$key]['interview_back'] = (!empty($interviewSubRst[$interview_id])) ? $interviewSubRst[$interview_id]['interview_back'] : 0;
            //入职表字段信息
            $dataList[$key]['entry_staff_id'] = $entryRst[$interview_id]['staff_id'] ?? '';
            $item_entry_status = $entryRst[$interview_id]['status'] ?? null;
            $dataList[$key]['entry_status'] = $item_entry_status;
            $dataList[$key]['entry_status_text'] = $entry_status_text[$item_entry_status]?? '';

            $dataList[$key]['nationality'] = $val['nationality'];
            $dataList[$key]['nationality_name'] =  $this->getTranslation()->_('nationality_'.$val['nationality']);

            $dataList[$key]["recommend_time"] = null;//简历筛选-推荐时间
            $dataList[$key]["recommend_reason"] = null;//简历筛选-推荐理由
            $dataList[$key]["feedback_time"] = null;//简历筛选-反馈时间
            $dataList[$key]["filter_staff_id"] = null;//简历筛选人工号（by上筛选简历是否通过的人）
            $dataList[$key]["filter_staff_name"] = null;//简历筛选人姓名（by上筛选简历是否通过的人）

            if($resume_filter = $resumeFilterInfo[$val['resume_id']] ?? []){
                $dataList[$key]["filter_staff_id"] = $resume_filter['filter_staff_id'];//简历筛选人工号（by上筛选简历是否通过的人）
                $dataList[$key]["feedback_reason"] = $resume_filter['feedback_reason'] ?? '';//筛选评价（通过/不通过原因）
                $dataList[$key]["recommend_time"]  = $resume_filter['recommend_time'];//简历筛选-推荐时间
                $dataList[$key]["recommend_reason"] = $resume_filter['recommend_reason'];//推荐原因

                //如果是取消推荐状态，先判断是否有过反馈时间，有的话计算反馈等待时长，没有则标识没有反馈直接撤回推荐了，就不计算反馈等待时长了
                if($resume_filter['filter_state']!= enums::$resume_filter_state['cancel']){
                    $dataList[$key]["feedback_time"] = $resume_filter['feedback_time'] ? $resume_filter['feedback_time'] : gmdate('Y-m-d H:i:s', time());//简历筛选-反馈时间
                }

            }

            $dataList[$key]['manager_id'] = '';
            $dataList[$key]['manager_name'] = '';
            $dataList[$key]['working_day_rest_type'] = '';
            if(!empty($interviewRst)) {
                $manager_id = !empty($interviewRst[$val['resume_id']]) ? $interviewRst[$val['resume_id']]['manager_id'] : '';
                $manager_name = !empty($interviewRst[$val['resume_id']]) ? $interviewRst[$val['resume_id']]['manager_name'] : '';
                $working_day_rest_type = !empty($interviewRst[$val['resume_id']]) ? $interviewRst[$val['resume_id']]['working_day_rest_type'] : '';
                $dataList[$key]['manager_id'] = !empty($manager_id) ? $manager_id : '';
                $dataList[$key]['manager_name'] = $manager_name;
                $dataList[$key]['working_day_rest_type'] = !empty($working_day_rest_type) ? $working_day_rest_type : '';
            }
            $dataList[$key]['old_staff_id'] = empty($val['old_staff_id']) ? '' : $val['old_staff_id'];//旧工号
            $dataList[$key]['hc_state_code_text'] = !empty($syateArr[$val['hc_state_code']]) ? $this->getTranslation()->_($syateArr[$val['hc_state_code']]) : '';

        }
        if( !empty( $dataList ) ){
            $returnData['dataList'] = $dataList;
            $returnData['pagination']['count'] = $count;
            $returnData['pagination']['pageCount'] = ceil($count / $pageSize);
            $returnData['pagination']['pageNum'] = $pageNum;
            $returnData['pagination']['pageSize'] = $pageSize;
        }

        return $returnData;
    }

    public function getInterviewState($resume_filter_state,$interview_state ,$resume_is_out,$resume_state_code){

        if(!empty($interview_state)){
            //已经进入面试环节后，则用面试表中状态
            $state =  $interview_state;

        }elseif (!empty($resume_filter_state)){
            //未进入面试前判断是否有筛选状态，如果有返回筛选状态，
            $state = $resume_filter_state;

        }elseif ($resume_state_code == enums::$resume_state_code['re_hc_wait_feedback'] ){
            //重新关联hc未沟通
            $state = $resume_state_code;
        }else{
            //默认显示简历初始状态（未沟通）
            $state = self::NO_COMMUNICATION;
        }

        if ($resume_is_out == 1) {
            $state = enums::$interview_state['rejected'];//已拒绝
        }

        return $state;


    }

    /**
     * 获取简历筛选最新的一条数据
     * @param $resume_id
     * @return array
     */
    public function getResumeFilterLastedData($resume_id)
    {
        $resumeObj = HrResumeFilterModel::findFirst([
            'conditions' => "resume_id = :resume_id: and is_deleted = 0",
            'bind' => ['resume_id'=>$resume_id],
            'order' => 'id desc'
        ]);
        if ($resumeObj) {
            return $resumeObj->toArray();
        } else {
            return [];
        }
    }

    /**
     * 获取简历筛选最新的一条数据
     * @param $resume_id
     * @return array
     */
    public function getResumeFilterLastedDataByHc($resume_id,$hc_id){

        $resumeObj = HrResumeFilterModel::findFirst([
            'conditions' => "resume_id = :resume_id: and hc_id = :hc_id: and is_deleted = 0",
            'bind' => ['resume_id'=>$resume_id,'hc_id'=>$hc_id],
            'order' => 'id desc'
        ]);
        if($resumeObj){
            return $resumeObj->toArray();
        }else{
            return [];
        }
    }

    //批量获取简历筛选最新的一条数据
    public function getBatchResumeFilterLastedData($resume_ids)
    {
        if(empty($resume_ids)){
            return [];
        }
        $resumeFilter = HrResumeFilterModel::find([
                                                        'conditions' => "resume_id in  ({resume_id:array}) and is_deleted = 0",
                                                        'columns'=>'max(id) resume_filter_id',
                                                        'bind' => ['resume_id'=>$resume_ids],
                                                        'group' => 'resume_id',
                                                    ])->toArray();
        if($ids = array_column($resumeFilter,'resume_filter_id')){
            $resumeFilterObj = HrResumeFilterModel::find([
                                                         'conditions' => "id in  ({ids:array}) and is_deleted = 0",
                                                         'bind' => ['ids'=>$ids],
                                                     ])->toArray();
            return array_column($resumeFilterObj,null,'resume_id');
        }
        return  [];
    }



    public function getInterviewSubscribe( $paramIn= [])
    {
        $returnData = [];
        $interviewIds = $paramIn["interview_ids"] ?? "";
        if ($interviewIds) {
            $sql = "--
                    SELECT
                        DATE_FORMAT(CONVERT_TZ(subscribe.interview_time, '+00:00', '" . $this->timezone . "' ),'%Y/%m/%d %H:%i:%s') as interview_time,
                        subscribe.detail_address,
                        subscribe.resume_provider_id,
                        subscribe.id,
                        subscribe.created_at,
                        subscribe.interviewer_id,
                        subscribe.interview_id,
                        subscribe.status 
                    FROM
                        hr_interview_subscribe AS subscribe
                        JOIN ( SELECT max( id ) AS id FROM hr_interview_subscribe WHERE interview_id IN ( {$interviewIds} ) GROUP BY interview_id ) AS interview_subscribe ON interview_subscribe.id = subscribe.id 
                    WHERE
                        subscribe.interview_id IN ( {$interviewIds} )";
            $obj = $this->db_rby->query($sql);
            $data = $obj->fetchAll(\Phalcon\Db::FETCH_ASSOC);
            $returnData = $data;
        }
        return $returnData;
    }

    /**
     * 获取最新offer数据
     * @param array $paramIn
     * @return array
     */
    public function getInterviewOffer(array $interview_id_arr)
    {

        if(empty($interview_id_arr)) return [];

        $interview_id_arr = array_values($interview_id_arr);
        $lasted_offer_data = HrInterviewOfferModel::find([
            'conditions' => "interview_id in ({interview_id:array}) ",
            'columns'=>'max(id) id',
            'bind' => ['interview_id'=>$interview_id_arr],
            'group' => 'interview_id',
        ])->toArray();
        $lasted_offer_id_arr = array_column($lasted_offer_data,'id');

        if(empty($lasted_offer_id_arr)) {
            return [];
        }
        //获取offer数据
        $offer_data = HrInterviewOfferModel::find([
            'conditions' => "id in ({offer_id:array}) ",
            'bind' => ['offer_id'=>$lasted_offer_id_arr],
            'columns'=>[
                'resume_id',
                'id as offer_id',
                'interview_id',
                "DATE_FORMAT(work_time,'%Y/%m/%d') AS offer_work_time",
                "DATE_FORMAT( CONVERT_TZ(updated_at, '+00:00', '" . $this->timezone . "' ), '%Y/%m/%d %H:%i:%s' ) AS offer_updated_at",
                'dangerously',
                'renting',
                'computer',
                'equipment_cost',
                'staff_job_type',
                'fuel',
                'rental',
                'position',
                'food',
                'other',
                'money',
                'exp',
                'shift_id',
                'position',
                'xinghuo_allowance',
                'submitter_id',
                'offer_annex_url',
                'bg_check_annex_url',
                'offer_annex_upload_time',
                'currency',
                'subsidy_type'
            ]
        ])->toArray();


        return $offer_data;
    }


    /**
     * 面试管理HC列表
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getInterviewHcList($paramIn = [])
    {
        //[1]参数定义

        $staffId          = $paramIn['staff_id'];
        $noPage           = $paramIn['no_page'];
        $pageSize         = !$noPage ? $paramIn['page_size'] : 50000;
        $pageNum          = $paramIn['page_num'];
        $jobId            = $paramIn['job_id'];
        $departmentId     = $paramIn['department_id'];
        $priorityId       = $paramIn['priority_id'];
        $searchPriorityId = $paramIn['search_priority_id'];
        $cityCode         = $paramIn['city_code'];
        $stateCode        = $paramIn['state_code'];
        $orderField       = $paramIn['order_field'];
        $orderSort        = $paramIn['sort'];
        $store_id         = $paramIn['store_id'];
        $hcId             = $paramIn['hc_id'];
        $export           = $paramIn['export'];
        $reasonType       = $paramIn['reason_type'] ?? '';
        $deleted          = $paramIn['deleted'] ?? '';
        $job_title        = $paramIn['job_title'] ?? '';
        $filter_job_title = $paramIn['filter_job_title'] ?? 0;
        $hire_type        = $paramIn['hire_type'] ?? [];

        $day = date('Y-m-d', time());
        $statusArr = [
            1 => $this->getTranslation()->_('4201'),
            2 => $this->getTranslation()->_('4202'),
            3 => $this->getTranslation()->_('4004'),
            4 => $this->getTranslation()->_('4204')
        ];

        $returnData = [
            'dataList' => [],
            'pagination' => [
                'count' => 0,
                'pageCount' => 0,
                'pageNum' => $pageNum,
                'pageSize' => $pageSize
            ]
        ];

        //[2]数据查询
        $stateStr = '2,3,4,9';
        $listSql = "
                       SELECT 
                            hr_hc.hc_id,
                            hr_hc.priority_id, 
                            hr_hc.job_id, 
                            hr_hc.hire_type, 
                            hr_hc.surplusnumber,
                            hr_hc.department_id,
                            hr_hc.worknode_id,
                            date_format(hr_hc.expirationdate, '%Y-%m-%d') as expirationdate, 
                            hr_hc.demandnumber, 
                            hr_hc.province_code, 
                            (hr_hc.demandnumber-hr_hc.surplusnumber) as success_cou,
                            subscribe.subscribe_cou, 
                            hr_jd.job_name,
                            hr_hc.job_title as job_title_id,
                            CONVERT_TZ(hr_hc.updated_at, '+00:00', '" . $this->timezone . "' ) AS updated_at,
                            CONVERT_TZ(hr_hc.createtime, '+00:00', '" . $this->timezone . "' ) AS createtime,
                            CONVERT_TZ(hr_hc.approval_completion_time, '+00:00', '" . $this->timezone . "' ) AS approval_completion_time,
                            (CASE
                               WHEN hr_hc.state_code = 4 THEN 3
                               WHEN offer.success_cou = hr_hc.demandnumber and hr_hc.state_code=3 THEN 2
                               WHEN offer.success_cou is NULL
                                    And  hr_hc.state_code != 9 and hr_hc.state_code!=3 and hr_hc.state_code = 2 THEN 1
                               WHEN offer.success_cou < hr_hc.demandnumber 
                                    And  hr_hc.state_code != 9 and hr_hc.state_code!=3 and hr_hc.state_code = 2 THEN 1
                               WHEN hr_hc.state_code = 3 THEN 2
                               WHEN hr_hc.state_code = 9 THEN 4
                               WHEN hr_hc.state_code = 2 THEN 1
                            END) as state_code,
                            hr_hc.working_day_rest_type
                        ";
        $listSqlMain = "
                        FROM `hr_hc`
                            LEFT JOIN (
                                SELECT hr_interview.hc_id, COUNT(interview_id) AS success_cou
                                FROM hr_interview
                                WHERE hr_interview.hc_id = hc_id AND (state = 25 or (state = 40 and cancel_type is null))
                                GROUP BY hr_interview.hc_id
                            ) offer
                            ON hr_hc.hc_id = offer.hc_id
                            LEFT JOIN (
                                SELECT hr_interview.hc_id, COUNT(interview_id) AS subscribe_cou
                                FROM hr_interview
                                WHERE hr_interview.hc_id = hc_id
                                    AND (state = 5 Or state = 10 OR state = 20)
                                GROUP BY hr_interview.hc_id
                            ) subscribe
                            ON hr_hc.hc_id = subscribe.hc_id
                            LEFT JOIN (
                                SELECT hr_jd.job_id, hr_jd.job_name
                                FROM hr_jd
                                WHERE hr_jd.job_id = job_id
                            ) hr_jd
                            ON hr_hc.job_id = hr_jd.job_id
                        WHERE hr_hc.state_code IN(" . $stateStr . ")";
        $listWhereSqlMain = ' ';
        if (!empty($jobId)) {
            $listWhereSqlMain .= "And hr_hc.job_id = '" . $jobId . "' ";
        }

//        if ($departmentId) {
//            if (is_array($departmentId)) {
//                $listWhereSqlMain .= "And hr_hc.department_id in (" . implode(",",$departmentId) . ") ";
//            } else {
//                $listWhereSqlMain .= "And hr_hc.department_id =" . $departmentId . " ";
//            }
//        }
        if (!empty($paramIn['department_id'])) {
            $deptIds = SysServer::getDepartmentConditionByParams($paramIn);
            if (!empty($deptIds)) {
                $listWhereSqlMain .= "And hr_hc.department_id in (" . implode(",",$deptIds) . ") ";
            }
        }

        if ($priorityId) {
            $listWhereSqlMain .= "And hr_hc.priority_id =" . $priorityId . " ";
        }

        if (isset($searchPriorityId) && ($searchPriorityId !== '')) {
            $listWhereSqlMain .= "And hr_hc.priority_id =".$searchPriorityId." ";
        }

        if ($cityCode) {
            $listWhereSqlMain .= "And hr_hc.province_code = '" . $cityCode . "' ";
        }

        if ($store_id) {
            $listWhereSqlMain .= "And hr_hc.worknode_id = '" . $store_id . "' ";
        }
        if ($reasonType) {
            $listWhereSqlMain .= " And hr_hc.reason_type IN({$reasonType}) ";
        }
        if ($deleted) {
            $listWhereSqlMain .= " And hr_hc.deleted =" . $deleted . " ";
        }
        if ($stateCode) {
            $listWhereSqlMain .= " HAVING state_code in (" . $stateCode . ") ";
        }
        if ($hcId) {
            $listWhereSqlMain .= " And hr_hc.hc_id =" . $hcId . " ";
        }
        if ($job_title) {
            $listWhereSqlMain .= " And hr_hc.job_title = {$job_title} ";
        }

        if ($filter_job_title) {
            $listWhereSqlMain .= " And hr_hc.job_title IS NOT NULL ";
        }
        if ($hire_type){
            $hire_type_id = implode(',', $hire_type);
            $listWhereSqlMain  .= " AND hr_hc.hire_type IN (".$hire_type_id.") ";
        }

        $listCountSql = "select count(hc_id) as cou FROM (" . $listSql . $listSqlMain . $listWhereSqlMain . ") cou";
        $this->getDI()->get("logger")->write_log("interview : ". $listCountSql, "info");
        
        $dataObj = $this->getDI()->get('db_rby')->query($listCountSql);
        $data = $dataObj->fetch(\Phalcon\Db::FETCH_ASSOC);
        
        
        if (!empty($data)) {
            $returnData['pagination']['count'] = intval($data['cou']);
            $returnData['pagination']['pageCount'] = ceil($data['cou'] / $pageSize);
        }

        if ($returnData['pagination']['count'] > enums::EXPORT_MAX_NUM_LIMIT && $export) {
            throw new ValidationException($this->getTranslation()->_('export_down_num_error'));
        }
        
        $orderField = 'hr_hc.hc_id';
        if ($orderSort == 1) {
            $orderSql = $orderField . ' asc';
        } elseif ($orderSort == 2) {
            $orderSql = $orderField . ' desc';
        } else {
            $orderSql = "hr_hc.hc_id desc";
        }
        if ($export == 1) {
            $listSql .= $listSqlMain . $listWhereSqlMain . "  order by " . $orderSql;
        } else {
            $listSql .= $listSqlMain . $listWhereSqlMain . "  order by " . $orderSql . " limit " . ($pageNum - 1) * $pageSize . "," . $pageSize;
        }
        $dataObj = $this->getDI()->get('db_rby')->query($listSql);
        $data = $dataObj->fetchAll(\Phalcon\Db::FETCH_ASSOC);

        //查询部门 城市
        if (!empty($data)) {

            $hcIds = array_column($data, 'hc_id');
            $hrInterviews = ResumeOutLogModel::find([
                'columns' => ' count(*) as count, hc_id',
                'conditions' => ' hc_id in ({hc_ids:array})',
                'bind' => [
                    'hc_ids' => $hcIds
                ],
                'group' => 'hc_id'
            ])->toArray();
            $hrInterviews = array_column($hrInterviews, 'count', 'hc_id');

            $provinceCodesArr = array_unique(array_column($data, "province_code"));
            $provinceCodes = getIdsStr($provinceCodesArr);
            $citySql = "select code,name from sys_province where deleted =0 and code in ({$provinceCodes})";
            $dataObj = $this->getDI()->get('db_fle')->query($citySql);
            $cityData = $dataObj->fetchAll(\Phalcon\Db::FETCH_ASSOC);
            $cityData = array_column($cityData, NULL, 'code');

            $departmentIdsArr = array_unique(array_column($data, "department_id"));
            $departmentIds = getIdsStr($departmentIdsArr);
            $citySql = "select id,name from sys_department where id in ({$departmentIds})";
            $dataObj = $this->getDI()->get('db_fle')->query($citySql);
            $departmentData = $dataObj->fetchAll(\Phalcon\Db::FETCH_ASSOC);
            $departmentData = array_column($departmentData, NULL, 'id');

            /* hc优先级*/
            $priorityArr = PermissionHelper::getUcPriorityArr();

            foreach ($data as $k => $v) {
                if ($v['worknode_id'] == '-1') {
                    $city_name = $this->getTranslation()->_(strtolower(env('country_code')).'_hc_head_office_city_name');
                } else {
                    $city_name = isset($cityData[$v['province_code']]['name']) ? $cityData[$v['province_code']]['name'] : '';
                }
                $data[$k]['department_name'] = isset($departmentData[$v['department_id']]['name']) ? $departmentData[$v['department_id']]['name'] : '';
                $data[$k]['priority_name'] = isset($priorityArr[$v['priority_id']]) ? $this->getTranslation()->_($priorityArr[$v['priority_id']]) : '';
                $data[$k]['priority_id'] = $v['priority_id'] ?? 1;
                $data[$k]['city_name'] = $city_name;
                $data[$k]['surplusnumber'] = !empty($v['surplusnumber']) ? $v['surplusnumber'] : 0;
                $data[$k]['success_cou'] = !empty($v['success_cou']) ? $v['success_cou'] : 0;
                $data[$k]['subscribe_cou'] = !empty($v['subscribe_cou']) ? $v['subscribe_cou'] : 0;
                $data[$k]['weed_out'] = isset($hrInterviews[$v['hc_id']]) ? $hrInterviews[$v['hc_id']] : 0;
                $data[$k]['state_name'] = isset($statusArr[$v['state_code']]) ? $statusArr[$v['state_code']] : '';
                $data[$k]['working_day_rest_type'] = !empty($v['working_day_rest_type']) ? $v['working_day_rest_type'] : '';
                $data[$k]['hire_type_text'] = $v['hire_type'] ? $this->getTranslation()->_('hire_type_'.$v['hire_type']) : '';
            }
        }
        $returnData['dataList'] = $data;
        return $returnData;
    }


    /**
     * 面试主表添加
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function interviewAddOrUpdate($paramIn = [])
    {
        $hcId = $paramIn['hc_id'];
        $resumeId = $paramIn['resume_id'];
        $interviewId = 0;

        //查询HC是否存在
        $hcSql = "select job_id from hr_hc where hc_id = " . $hcId;
        $dataObj = $this->getDI()->get('db_rby')->query($hcSql);
        $hcData = $dataObj->fetch(\Phalcon\Db::FETCH_ASSOC);
        if (empty($hcData)) {
            return $interviewId;
        }

        //查询此HC下简历是否存在
        $interviewSql = "select interview_id from hr_interview where resume_id=" . $resumeId ." and hc_id = ".$hcId;
        $dataObj = $this->getDI()->get('db_rby')->query($interviewSql);
        $interviewData = $dataObj->fetch(\Phalcon\Db::FETCH_ASSOC);
        if (!empty($interviewData)) {
            $result = $this->db->updateAsDict(
                'hr_interview',
                [
                    'hc_id' => $hcId,
                    'job_id' => $hcData['job_id'],
                ],
                'interview_id = ' . $interviewData['interview_id']
            );
            $interviewId = $interviewData['interview_id'];
        } else {
            $insertData = [
                'resume_id' => $resumeId,
                'hc_id' => $hcId,
                'job_id' => $hcData['job_id'],
                'state' => 5
            ];
            $this->db->begin();
            $data = $this->db->insertAsDict(
                'hr_interview', $insertData
            );
            if (!$data) {
                $this->db->rollback();
                return false;
            }
            $id = $this->db->lastInsertId();
            $this->db->commit();
            $interviewId = $id;
            //发送待面试
            $this->sendMsgToBuddy($interviewId,5,1);
        }
        return $interviewId;
    }

    /**
     * 预约添加
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function interviewAdd($insertData = [], $resumeId)
    {
        $this->db->begin();
        $data = $this->db->insertAsDict(
            'hr_interview_subscribe', $insertData
        );
        if (!$data) {
            $this->db->rollback();
            return false;
        }
        $id = $this->db->lastInsertId();
        $this->db->commit();

        //简历修改状态
        $result = $this->db->updateAsDict(
            'hr_resume',
            [
                'state_code' => 2
            ],
            'id = ' . $resumeId
        );
        return $id;
    }

    /**
     * 预约修改
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function interviewUpdate($paramIn = [])
    {
        //[1]参数定义
        $interviewerId = $paramIn['interviewer_id'] ?? "";
        $interviewTime = $paramIn['interview_time'] ?? "";
        $resume_provider_id = $paramIn['resume_provider_id'] ?? "";
        $countryCode = $paramIn['country_code'] ?? "";
        $countryName = $paramIn['country_name'] ?? "";
        $provinceCode = $paramIn['province_code'] ?? "";
        $provinceName = $paramIn['province_name'] ?? "";
        $cityCode = $paramIn['city_code'] ?? "";
        $cityName = $paramIn['city_name'] ?? "";
        $districtCode = $paramIn['district_code'] ?? "";
        $districtName = $paramIn['district_name'] ?? "";
        $detailAddress = $paramIn['detail_address'] ?? "";
        $interviewId = $paramIn['interview_id'] ?? "";
        $subscribeId = $paramIn['subscribe_id'] ?? "";
        $shopId = $paramIn['shop_id'] ?? "";
        $postalCode = $paramIn['postal_code'] ?? "";
        $hc_id = $paramIn['hc_id'] ?? "";
        
        $insertOrUpdateParam = [
            'hc_id' => $hc_id,
            'interviewer_id' => $interviewerId,
            'interview_time' => $interviewTime,
            'resume_provider_id' => $resume_provider_id,
            'country_code' => $countryCode,
            'country_name' => $countryName,
            'province_code' => $provinceCode,
            'province_name' => $provinceName,
            'city_code' => $cityCode,
            'city_name' => $cityName,
            'district_code' => $districtCode,
            'district_name' => $districtName,
            'detail_address' => $detailAddress,
            'postal_code' => $postalCode,
            'shop_id' => $shopId,
            'interview_id' => $interviewId,
            'interview_back' => HrInterviewSubscribeModel::INTERVIEW_BACK_NO,
        ];
        if (!isset($paramIn["resume_provider_id"])) {
            unset($insertOrUpdateParam["resume_provider_id"]);
        }
        //[2]修改语句拼装
        $result = $this->db->updateAsDict(
            'hr_interview_subscribe',
            $insertOrUpdateParam,
            'id = ' . $subscribeId
        );
        return $subscribeId;
    }

    /**
     * 预约详情
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function subscribeDetail($paramIn, $type = 2)
    {
        //[1]参数定义
        $subscribeId = $paramIn['subscribe_id'];

        //[2]语句拼装
        $detailSql = "
                         --
                         select
                         id,
                         hs.interview_id,
                         hs.interviewer_id,
                         hs.hc_id,
                         hs.level,
                         DATE_FORMAT(CONVERT_TZ(hs.interview_time, '+00:00', '" . $this->timezone . "' ),'%Y-%m-%d %H:%i:%s') as interview_time,
                         hs.shop_id,
                         hs.country_code,
                         hs.country_name,
                         hs.province_code,
                         hs.province_name,
                         hs.city_code,
                         hs.city_name,
                         hs.district_code,
                         hs.district_name,
                         hs.resume_provider_id,
                         hs.postal_code,
                         hs.detail_address,
                         status,
                         staff_id,
                         CONVERT_TZ(hs.created_at, '+00:00', '" . $this->timezone . "' ) AS created_at,
                         CONVERT_TZ(hs.updated_at, '+00:00','" . $this->timezone . "') AS updated_at,
                         hr_hc.state_code as hc_state_code, 
                         hr_hc.worknode_id 
                         from hr_interview_subscribe as hs
                         left join hr_hc 
                         ON hs.hc_id = hr_hc.hc_id
                         WHERE id=" . $subscribeId;
        $data = $this->getDI()->get('db_rby')->query($detailSql);
        $detailData = $data->fetch(\Phalcon\Db::FETCH_ASSOC);

        $this->getDI()->get('logger')->write_log("interviewserver subscribeDetail sql:".$detailSql, 'info');

        if (!empty($detailData) && $type == 1 && !empty($detailData['hc_id'])) {
            //查询 岗位 部门 城市
            $detailData['department_name'] = '';
            $detailData['job_name'] = '';
            $detailData['city_name'] = '';
            $listSql = "
                        --
                        SELECT hr_hc.hc_id,hr_hc.department_id
                            , hr_hc.province_code, hr_jd.job_name 
                        FROM `hr_hc`
                            LEFT JOIN (
                                SELECT hr_jd.job_id, hr_jd.job_name
                                FROM hr_jd
                                WHERE hr_jd.job_id = job_id
                            ) hr_jd
                            ON hr_hc.job_id = hr_jd.job_id
                        WHERE hr_hc.hc_id =" . $detailData['hc_id'];
            $dataObj = $this->getDI()->get('db_rby')->query($listSql);
            $data = $dataObj->fetch(\Phalcon\Db::FETCH_ASSOC);

            $this->getDI()->get('logger')->write_log("interviewserver subscribeDetail sql:".$listSql, 'info');
            if (!empty($data)) {
                $detailData['city_name'] = '';
                if ($data['province_code']) {
                    $citySql = "select code,`name` from sys_province where deleted =0 and code = '{$data['province_code']}'";
                    $dataObj = $this->getDI()->get('db_fle')->query($citySql);
                    $cityData = $dataObj->fetch(\Phalcon\Db::FETCH_ASSOC);
                    $detailData['city_name'] = isset($cityData['name']) ? $cityData['name'] : '';
                }

                $detailData['department_name'] = '';
                if ($data['department_id']) {
                    $departmentSql = "select id,name from sys_department where id=" . $data['department_id'];
                    $dataObj = $this->getDI()->get('db_fle')->query($departmentSql);
                    $departmentData = $dataObj->fetch(\Phalcon\Db::FETCH_ASSOC);
                    $detailData['department_name'] = isset($departmentData['name']) ? $departmentData['name'] : '';
                }
                $detailData['job_name'] = $data['job_name'];
            }
        }
        $returnData = $detailData;
        return $returnData;
    }

    /**
     * 检查是否有预约没有完成的面试
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function interviewSubscribeCheck($paramIn, $subscribeId)
    {
        $interviewerId = $paramIn['interview_id'];
        $detailData = [];
        if (!$subscribeId) {
            $detailSql = "
                         --
                         select hr_interview.* from hr_interview left JOIN  hr_interview_subscribe on hr_interview.interview_id=hr_interview_subscribe.interview_id
                         WHERE hr_interview_subscribe.status = 1 And hr_interview.interview_id=" . $interviewerId;
            $data = $this->getDI()->get('db_rby')->query($detailSql);
            $detailData = $data->fetch(\Phalcon\Db::FETCH_ASSOC);
        }
        return $detailData;
    }

    /**
     * 根据面试id取面试信息
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getInterview($paramIn)
    {
        $interview_id = isset($paramIn['interview_id']) ? $paramIn['interview_id'] : '';
        $detailData = [];
        if ($interview_id) {
            $detailSql = "
                         --
                         select hr_interview.* from hr_interview 
                         WHERE hr_interview.interview_id=" . $interview_id;
            $data = $this->getDI()->get('db_rby')->query($detailSql);
            $detailData = $data->fetch(\Phalcon\Db::FETCH_ASSOC);
        }
        return $detailData;
    }

    /**
     * 取消预约
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function interviewAppointmentStatus($paramIn = [])
    {
        //[1]参数定义
        $subscribeId = $paramIn['subscribe_id'];
        $status = $paramIn['status'];

        //[2]修改语句拼装
        $result = $this->db->updateAsDict(
            'hr_interview_subscribe',
            [
                'status' => $status
            ],
            'id = ' . $subscribeId
        );
        return $subscribeId;
    }

    /**
     * 取消面试回调
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function interviewCancel($paramIn = [])
    {
        //[1]参数定义
        $interviewId = $paramIn['interview_id'];
        $cancelType = $paramIn['cancel_type'];
        $cancelReason = $paramIn['cancel_reason'];

        //[2]修改语句拼装
        $result = $this->db->updateAsDict(
            'hr_interview',
            [
                'state' => 31,
                'cancel_type' => $cancelType,
                'cancel_reason' => $cancelReason
            ],
            'interview_id = ' . $interviewId
        );

        $this->sendMsgToBuddy($interviewId,31);

        return $interviewId;
    }


    /**
     * 查询面试轮次
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function interviewSelectLevel($interviewId)
    {
        $level = 1;
        $detailSql = "
                         --
                         select count(id) as cou from hr_interview_subscribe 
                         WHERE interview_id=" . $interviewId . " And status = 3";
        $data = $this->getDI()->get('db_rby')->query($detailSql);
        $detailData = $data->fetch(\Phalcon\Db::FETCH_ASSOC);
        if (!empty($detailData)) {
            $level = $detailData['cou'] + 1;
        }
        return $level;

    }

    /**
     * [recordFeedback 面试反馈]
     * @return [type] [array]
     * <AUTHOR> <[email address]>
     */
    public function recordFeedback($paramIn = [])
    {
        // 处理数据
        $person_accessory = isset($paramIn['person_accessory']) ? $paramIn['person_accessory'] : '';
        $feedback_accessory = isset($paramIn['feedback_accessory']) ? $paramIn['feedback_accessory'] : '';
        $evaluate_accessory = isset($paramIn['evaluate_accessory']) ? $paramIn['evaluate_accessory'] : '';
        unset($paramIn['person_accessory']);
        unset($paramIn['feedback_accessory']);
        unset($paramIn['evaluate_accessory']);

        // 插入
        $this->db->begin();

        //查询预约面试信息
        $reserve_info = (new HrInterviewerOperationModel())->findFirst([
            'conditions' => 'id = :id:',
            'bind'       => [
                'id' => $paramIn['ope_id'],
            ],
        ]);
        if(!$reserve_info){
            $this->getDI()->get('logger')->write_log("recordFeedback: hr_interview_operation 记录不存在 id：".$paramIn['ope_id'] , 'notice');
            return false;
        }
        //面试官ID
        $paramIn['interviewer_id'] = $reserve_info->interviewer_id;

        //查询面试官反馈信息
        $paramIn['opt_id'] = $paramIn['ope_id'];
        //记录反馈信息
        $interview_state = $paramIn['state'];
        if($paramIn['is_last'] ==1){
            $paramIn['state'] = 0;  //直接这条数据不在前端列表展示
        }
        unset($paramIn['is_last']);
        unset($paramIn['ope_id']);

        if($paramIn['state'] == 3) {
            unset($paramIn['manager_id']);
            unset($paramIn['manager_name']);
            unset($paramIn['working_day_rest_type']);
        }

        $insert = $this->db->insertAsDict(
            'hr_interview_info', $paramIn
        );
        if (!$insert) {
            $this->getDI()->get('logger')->write_log("recordFeedback: hr_interview_info写入失败  id：".json_encode($paramIn,JSON_UNESCAPED_UNICODE) , 'notice');
            $this->db->rollback();
            return false;
        }
        $interview_info_id = $this->db->lastInsertId();

        // 面试信息附件
        if ($person_accessory) {
            if (!$this->createfile($interview_info_id, $person_accessory, 1)) {
                $this->db->rollback();
                return false;
            };
        }
        // 反馈附件
        if ($feedback_accessory) {
            if (!$this->createfile($interview_info_id, $feedback_accessory, 2)) {
                $this->db->rollback();
                return false;
            };
        }
        // 简历附件
        if ($evaluate_accessory) {
            if (!$this->createfile($interview_info_id, $evaluate_accessory, 3)) {
                $this->db->rollback();
                return false;
            };
        }
        // 修改主表状态
        $update_params =[
            'interview_id' => $paramIn['interview_id'],
            'state' => $interview_state,
            'manager_id' => $paramIn['manager_id'],
            'manager_name' => $paramIn['manager_name'],
            'working_day_rest_type' => $paramIn['working_day_rest_type']
        ];
        if (!$this->updateInterview($update_params)) {
            return false;
        };
        // 修改表状态
        if (!$this->updateSubscribe($paramIn['interview_id'])) {
            return false;
        };

        $this->db->commit();
        return $interview_info_id;
    }


    /**
     * [updateFeedback 修改面试反馈]
     * @return [type] [array]
     * <AUTHOR> <[email address]>
     */
    public function updateFeedback($paramIn = [])
    {

        // 处理数据
        $interview_info_id = $paramIn['interview_info_id'];

        $interview_id = $paramIn['interview_id']; //面试ID
        $person_accessory = isset($paramIn['person_accessory']) ? $paramIn['person_accessory'] : '';
        $feedback_accessory = isset($paramIn['feedback_accessory']) ? $paramIn['feedback_accessory'] : '';
        $evaluate_accessory = isset($paramIn['evaluate_accessory']) ? $paramIn['evaluate_accessory'] : '';
        unset($paramIn['id']);
        unset($paramIn['person_accessory']);
        unset($paramIn['feedback_accessory']);
        unset($paramIn['evaluate_accessory']);
        //获取面试对应的简历ID
        $interview_info = HrInterviewModel::findFirst([
            'conditions'=>'interview_id=:interview_id:',
            'bind'=>['interview_id'=>$interview_id]
        ]);

        if(empty($interview_info)) return false;
        $resume_id = $interview_info->resume_id;


        $this->db->begin();
        // 修改简历
        unset($paramIn['is_last']);
        unset($paramIn['ope_id']);
        if($paramIn['state'] == 3) {
            unset($paramIn['manager_id']);
            unset($paramIn['manager_name']);
            unset($paramIn['working_day_rest_type']);
        }

        $updateFeedback = $this->db->updateAsDict('hr_interview_info', $paramIn, [
            'conditions' => 'interview_info_id = ?',
            'bind' => [$interview_info_id],
        ]);
        // 修改失败
        if ($updateFeedback === false) {
            $this->db->rollback();
            return false;
        }
        // 逻辑删除文件
        $delFile = $this->delFile($interview_info_id);
        if ($delFile === false) {
            $this->db->rollback();
            return false;
        }
        // 有面试信息附件就修改
        if ($person_accessory) {
            $updateFile = $this->createfile($interview_info_id, $person_accessory, 1);
            // 修改失败
            if ($updateFile === false) {
                $this->db->rollback();
                return false;
            }
        }
        // 有反馈附件就修改
        if ($feedback_accessory) {
            $updateFile = $this->createfile($interview_info_id, $feedback_accessory, 2);
            // 修改失败
            if ($updateFile === false) {
                $this->db->rollback();
                return false;
            }
        }
        // 有简历附件就修改
        if ($evaluate_accessory) {
            $updateFile = $this->createfile($interview_info_id, $evaluate_accessory, 3);
            // 修改失败
            if ($updateFile === false) {
                $this->db->rollback();
                return false;
            }
        }
        //终试通过的面试反馈操作 修改简历状态为正常
        if($paramIn['state'] == 1){
            //修改简历状态
            $up_resume_res = $this->db->updateAsDict('hr_resume', ['is_out' => 2], [
                'conditions' => 'id = ?',
                'bind' => [$resume_id],
            ]);
            if ($up_resume_res === false) {
                $this->db->rollback();
                return false;
            }
        }


        // 修改主表状态
        $update_params =[
            'interview_id' => $paramIn['interview_id'],
            'state' => $paramIn['state'],
            'manager_id' => $paramIn['manager_id'],
            'manager_name' => $paramIn['manager_name'],
            'working_day_rest_type' => $paramIn['working_day_rest_type']
        ];
        if (!$this->updateInterview($update_params)) {
            return false;
        };
        // 修改表状态
        if (!$this->updateSubscribe($paramIn['interview_id'])) {
            return false;
        };


        $this->db->commit();
        return true;
    }


    /**
     * [delFile 逻辑删除文件]
     * @return [type] [array]
     * <AUTHOR> <[email address]>
     */
    public function delFile($id = '')
    {
        // 旧文件做逻辑删除
        $data = $this->db->updateAsDict(
            'hr_interview_file',
            [
                'is_del' => 1
            ],
            'interview_info_id = ' . $id
        );
        if ($data) return true;
        return false;
    }


    /**
     * [createfile 存入文件]
     * @return [type] [array]
     * <AUTHOR> <[email address]>
     */
    public function createfile($interview_info_id = '', $file_url = [], $type = '')
    {
        // 非空验证
        if (empty($interview_info_id) || empty($file_url) || empty($type)) return false;
        // 开始处理文件
        $imgInsetData = [];
        foreach ($file_url as $v) {
            $imgInsetData[] = [
                'interview_info_id' => $interview_info_id,
                'type' => $type,
                'file_path' => isset($v['file_path']) ? $v['file_path'] : '',
                'file_name' => isset($v['file_name']) ? $v['file_name'] : '',
                'file_size' => isset($v['file_size']) ? $v['file_size'] : '',
            ];
        }
        // 批量插入
        foreach ($imgInsetData as $k => $v) {
            $imgData = $this->db->insertAsDict(
                'hr_interview_file', $v
            );
        }
        if (!$imgData) return false;
        return true;
    }


    /**
     * [selectLevel 查询轮次]
     * @return [type] [array]
     * <AUTHOR> <[email address]>
     */
    public function selectLevel($interview_id = '')
    {
        // 非空验证
        if (empty($interview_id)) return false;

        // sql
        $sql = "
            SELECT 
                *
            FROM
                `hr_interview_info`
            WHERE
                `hr_interview_info`.`interview_id` = :interview_id
        ";
        $sql_param = [
            'interview_id' => $interview_id,
        ];
        // 执行sql
        $this->getDI()->get('db_rby')->execute($sql, $sql_param);
        return $this->getDI()->get('db_rby')->affectedRows();
    }


    /**
     * [updateInterview 修改状态]
     * @return [type] [array]
     * <AUTHOR> <[email address]>
     */
    //public function updateInterview($interview_id = '', $state = '')
    public function updateInterview($params)
    {
        $interview_id = $params['interview_id'] ?? '';
        $state = $params['state'] ?? '';

        $manager_id = $params['manager_id'] ?? 0;
        $manager_name = $params['manager_name'] ?? '';
        $working_day_rest_type = $params['working_day_rest_type'] ?? 0;

        // 非空验证
        if (empty($interview_id) || !in_array($state, ['1', '2', '3'])) {
            return false;
        }

        // 1、终试通过 - 20、代发offre
        $state == '1' && $state = '20';

        // 2、进入下一轮 - 10、面试中
        $state == '2' && $state = '10';

        // 3、不通过 - 30、已拒绝
        $state == '3' && $state = '30';

        $update_params['state'] = $state;
        if($state != 3) {
            $update_params['manager_id'] = $manager_id;
            $update_params['manager_name'] = $manager_name;
            $update_params['working_day_rest_type'] = $working_day_rest_type;
        }

        // 拼接sql
        $data = $this->db->updateAsDict('hr_interview', $update_params, 'interview_id = ' . $interview_id);
        if ($data === false) return false;

        $this->sendMsgToBuddy($interview_id,$state);

        return true;
    }


    /**
     * [updateSubscribe 修改状态]
     * @return [type] [array]
     * <AUTHOR> <[email address]>
     */
    public function updateSubscribe($interview_id = '')
    {
        // 非空验证
        if (empty($interview_id)) return false;

        // 拼接sql
        $data = $this->db->updateAsDict(
            'hr_interview_subscribe',
            [
                'status' => 3
            ],
            'interview_id = 5 ORDER BY level DESC LIMIT 1'
        );
        if ($data === false) return false;
        return true;
    }
    /**
     * 修改操作
     * @param type $id
     * @param type $params
     * @return boolean
     */
    public function updateSubscribeById($id = '',$params = [])
    {
        // 非空验证
        if (empty($id)) return false;

        // 拼接sql
        $data = $this->db->updateAsDict(
            'hr_interview_subscribe',
            $params,
            "id = {$id}"
        );
        if ($data === false) return false;
        return true;
    }

    /**
     * hr_interviewer_operation 修改操作
     * @param type $params
     * @return boolean
     */
    public function updateInterviewerInfo($params = [])
    {

        $info = HrInterviewerOperationModel::findFirst([
            'conditions' => 'interviewer_id = :interviewer_id: and interview_sub_id = :interview_sub_id:',
            'bind'       => [
                'interviewer_id' => $params['interviewer_id'],
                'interview_sub_id' => $params['interview_sub_id'],
            ]
        ]);

        if($info){
            $info->interview_url = $params['interview_url'];
            $info->hr_remark = $params['hr_remark'];
            return $info->save();
        }
        return false;
    }


    /**
     * [feedbackList 面试反馈查看]
     * @return [type] [array]
     * <AUTHOR> <[email address]>
     */
    public function feedbackList($paramIn = [])
    {
        $config = $this->getDI()->getConfig();
        // 非空验证
        if (empty($paramIn)) return false;

        // sql
        $sql = "
            SELECT
                `hr_interview_info`.`interview_info_id`
                ,`hr_interview_info`.`interview_id`
                ,`hr_interview_info`.`state`
                ,`hr_interview_info`.`evaluate`
                ,`hr_interview_info`.`currency`
                ,`hr_interview_info`.`money`
                ,`hr_interview_file`.`file_path`
                ,`hr_interview_file`.`file_name`
                ,`hr_interview_file`.`file_size`
                ,`hr_interview_file`.`type`
                ,`hr_interviewer_operation`.`interviewer_id` as opt_interviewer_id
                ,`hr_interviewer_operation`.`create_id`
                ,`hr_interview_info`.`manager_id`
                ,`hr_interview_info`.`manager_name`
                ,`hr_interview_info`.`working_day_rest_type`
            FROM
                `hr_interview_info`
            LEFT JOIN 
                `hr_interview_file` ON `hr_interview_info`.`interview_info_id` = `hr_interview_file`.`interview_info_id` and hr_interview_file.is_del = 2
           LEFT JOIN
                `hr_interviewer_operation` ON `hr_interview_info`.`opt_id` = `hr_interviewer_operation`.`id`
            WHERE
                `hr_interview_info`.`interview_info_id` = '{$paramIn['interview_info_id']}'
        ";
        $data = $this->getDI()->get('db_rby')->query($sql);

        $returnData = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);

        $pno_list = [];
        if ($returnData) {

            // 处理数组
            foreach ($returnData as $row) {

                // 过滤重复
                if (!isset($pno_list[$row['interview_info_id']])) {

                    // 组建数据
                    $pno_list[$row['interview_info_id']] = [
                        'interview_info_id' => $row['interview_info_id'],
                        'interview_id' => $row['interview_id'],
                        'state' => $row['state'],
                        'evaluate' => $row['evaluate'],
                        'interviewer_id' => $row['opt_interviewer_id'] ?? 0,
                        'create_id' => $row['create_id'] ?? 0,
                        'currency' => $row['currency'],
                        'money' => $row['money'],
                        'person_accessory' => [],
                        'feedback_accessory' => [],
                        'evaluate_accessory' => [],
                        'manager_id' => !empty($row['manager_id']) ? $row['manager_id'] : '',
                        'manager_name' => !empty($row['manager_name']) ? $row['manager_name'] : '',
                        'working_day_rest_type' => !empty($row['working_day_rest_type']) ? $row['working_day_rest_type'] : '',
                    ];
                }
                // 面试信息附件
                if ($row['type'] == '1') {
                    $pno_list[$row['interview_info_id']]['person_accessory'][] = [
                        'file_path' => $row['file_path'],
                        'file_name' => $row['file_name'],
                        'file_size' => $row['file_size'],
                        'file_url' => $config->application['img_prefix'] . $row['file_path'],
                        'bucket_name' => $config->bucket_name,
                    ];
                }
                // 反馈附件
                if ($row['type'] == '2') {
                    $pno_list[$row['interview_info_id']]['feedback_accessory'][] = [
                        'file_path' => $row['file_path'],
                        'file_name' => $row['file_name'],
                        'file_size' => $row['file_size'],
                        'file_url' => $config->application['img_prefix'] . $row['file_path'],
                        'bucket_name' => $config->bucket_name,
                    ];
                }
                // 简历评价附件
                if ($row['type'] == '3') {
                    $pno_list[$row['interview_info_id']]['evaluate_accessory'][] = [
                        'file_path' => $row['file_path'],
                        'file_name' => $row['file_name'],
                        'file_size' => $row['file_size'],
                        'file_url' => $config->application['img_prefix'] . $row['file_path'],
                        'bucket_name' => $config->bucket_name,
                    ];
                }
            }
            foreach ($pno_list as $v) {
                $pno_list = $v;
            };
        }

        $staffIds = array_values(array_unique(array_filter([
            $pno_list['create_id'],
            $pno_list['manager_id'],
            $pno_list['interviewer_id']
        ])));

        $staffList = (new StaffServer())->getStaffListByIds($staffIds);
        $staffList = array_column($staffList,null,'staff_info_id');

        //查询面试官
        $pno_list['interviewer_name'] = '';
        if ($pno_list['interviewer_id']) {
            $interviewerStaffinfo = $staffList[$pno_list['interviewer_id']] ?? [];
            $pno_list['interviewer_name'] = StaffServer::getStaffNameView($interviewerStaffinfo, 2,$pno_list['interviewer_id']);
        }

        //查询创建人
        $pno_list['create_name'] = '';
        if ($pno_list['create_id']) {
            $createInfo = $staffList[$pno_list['create_id']] ?? [];
            $pno_list['create_name'] = StaffServer::getStaffNameView($createInfo, 2,$pno_list['create_id']);
        }

        //查询HR
        if (!empty($pno_list['manager_id'])) {
            $managerInfo = $staffList[$pno_list['manager_id']] ?? [];
            $pno_list['manager_name'] = StaffServer::getStaffNameView($managerInfo, 2,$pno_list['manager_id']);
        }

        return $pno_list ?: false;
    }


    /**
     * [getFeedbackLog 面试反馈log]
     * @return [type] [array]
     * <AUTHOR> <[email address]>
     */
    public function getFeedbackLog($interview_id = '')
    {
        // sql
        $sql = "
            SELECT
              `interview_log`
            FROM
              `hr_interview_log`
            WHERE
              `interview_id` = '{$interview_id}'
        ";
        $data = $this->getDI()->get('db_rby')->query($sql);
        return $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);
    }


    /**
     * 面试官
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getInterviewerList()
    {
        //面试官黑名单
        $interviewerBlackList = (new SettingEnvServer())->getSetVal('interviewer_black_list');
        //初始化定义
        $staffInfoData = [];

        //排除特定岗位
        $sql = 'SELECT id FROM hr_job_title where id not in (' . $interviewerBlackList . ') and status = 1';
        $data = $this->getDI()->get('db_rby')->query($sql);
        $jobData = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        //查询面试官 hr_job_title
        if (!empty($jobData)) {
            $jobIds = array_unique(array_column($jobData, 'id'));
//            $staffInfoSql = "SELECT a.id,a.name,a.mobile, a.mobile_company, b.name department_name,c.name as job_name FROM `staff_info` a left join sys_department b  on a.department_id = b.id "
//                    . "left join staff_info_job_title c on a.job_title=c.id where a.job_title in (" . implode(',', $jobIds) . ") and a.state=1 and c.state = 1 and c.deleted = 0 ";
//
//            $staffInfoData = $this->getDI()->get("db_fle")->query($staffInfoSql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);


            //获取列表
            $builder = $this->modelsManager->createBuilder();
            $builder->columns("
                a.staff_info_id as id,
                a.name,
                IFNULL(a.nick_name, '') as nick_name,
                a.mobile, 
                a.mobile_company,
                b.name department_name,
                c.job_name as job_name
            ");

            $builder->from(['a' => HrStaffInfoModel::class]);
            $builder->leftJoin(SysDepartmentModel::class, 'a.node_department_id = b.id', 'b');
            $builder->leftJoin(HrJobTitleModel::class, 'a.job_title = c.id', 'c');

            $builder->andWhere('a.job_title IN ({job_title:array})', ['job_title' => array_values($jobIds)]);
            $builder->andWhere('a.state = :state: and a.is_sub_staff = 0', ['state' => HrStaffInfoModel::STAFF_STATE_IN]);
            $builder->andWhere('c.status = :c_status:', ['c_status' => HrJobTitleModel::STATUS_NORMAL]);
            $builder->inWhere('a.formal',[HrStaffInfoModel::FORMAL_1,HrStaffInfoModel::FORMAL_INTERN]);
//            $builder->groupBy('a.staff_info_id');
            $staffInfoData = $builder->getQuery()->execute()->toArray();
        }


        //数据返回
        return $staffInfoData;
    }
    
    /**
     * 简历提供人
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getResumeSupplyList()
    {
        $interviewerJobIds = SettingEnvServer::getSetVal('interviewerJobIds') ? :  env("interviewerJobIds");
        //初始化定义
        $staffInfoData = [];

        //查询员工职位
        $sql = '
                          --
                          SELECT id FROM hr_job_title where id in (' . $interviewerJobIds . ') and status = 1';
        $data = $this->getDI()->get('db_rby')->query($sql);
        $jobData = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        //查询面试官 hr_job_title
        if (!empty($jobData)) {
            $jobIds = array_unique(array_column($jobData, 'id'));

            //查询符合条件的员工
            $staffInfoData = HrStaffInfoModel::find([
                'columns'    => "staff_info_id as id, name, mobile, mobile_company,IFNULL(nick_name, '') as nick_name",
                'conditions' => "job_title IN  ({job_title:array}) and state=:state:",
                'bind'       => [
                    'job_title' => array_values($jobIds),
                    'state'     => HrStaffInfoModel::STAFF_STATE_IN,
                ],
            ])->toArray();
        }

        //数据返回
        return $staffInfoData;
    }
    /**
     * 面试官
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getInterviewerListMY()
    {

        $staffInfoData = StaffInfoModel::find([
                'conditions' => 'state = 1 ',
                'columns' => 'id, name, mobile, mobile_company',
            ])->toArray();


        //数据返回
        return $staffInfoData;
    }

    public function screenedList($paramIn = [])
    {
        $name = $paramIn['name'];
        $positionSql = ' group by hr_job_title.id';
        $data = $this->getDI()->get('db_rby')->query($positionSql);
        $positionData = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        return $positionData;
    }


    public function delInterview($resumeIds,$path_name)
    {
        $tableName = "hr_interview";
        $db = $this->db;
        try {
            $sql = "from {$tableName} where resume_id in (" . implode(",",$resumeIds).")";
            $db->begin();
            $obj = $db->query("select * ".$sql);
            $data = $obj->fetchAll(\Phalcon\Db::FETCH_ASSOC);
            if (empty($data)) {
                $db->commit();
                return true;
            }
            $this->buildBakSql($path_name,$tableName, $data);
            $db->execute("delete ".$sql);
            $this->getDI()->get('logger')->write_log('delCVReal sql: delete '.$sql,'info');
            $db->commit();
        } catch (\Exception $e) {
            $db->rollback();
            return $e->getMessage();
        }
        return true;
    }



    /**
     * 获取面试offer列表
     */
    public function gertInterviewOfferList($paramIn = [])
    {
        $pageSize    = $paramIn['page_size'] ?? 10;
        $pageNum     = $paramIn['page_num'] ?? 1;
        $no_page     = $paramIn['no_page'] ?? 0;
        $sort        = $paramIn['sort'] ?? '';

        $builder = $this->modelsManager->createBuilder();
        $builder->columns('count(1) as total');
        $builder->from(["hr_interview_offer"=>HrInterviewOfferModel::class]);
        $builder->join(HrhcModel::class,'hr_hc.hc_id = hr_interview_offer.hc_id','hr_hc');
        $builder->join(HrResumeModel::class,'hr_resume.id = hr_interview_offer.resume_id','hr_resume');
        $builder->leftjoin(HrEntryModel::class,'hr_entry.resume_id = hr_resume.id','hr_entry');
        $builder->where('hr_interview_offer.status = :status:', ['status' => $paramIn['status']]);

        $builder->andWhere('hr_entry.status = '.HrEntryModel::STATUS_TO_BE_EMPLOYED.' or hr_entry.status IS NULL');
        $builder->andWhere('hr_interview_offer.work_time >= :work_time:', ['work_time' => date("Y-m-d 00:00:00",strtotime("-7 day"))]);

        $stringWhere = '';
        $where['manger'] = $paramIn['staff_id'];//上级
        if(!empty($paramIn['worknode_ids'])) {
            $stringWhere .= ' or hr_hc.worknode_id in ({worknode_ids:array})';
            $where['worknode_ids'] = $paramIn['worknode_ids'];
        }
        if(!empty($paramIn['dept_ids'])) {
            $stringWhere .= ' or hr_hc.department_id in ({dept_ids:array})';
            $where['dept_ids'] = $paramIn['dept_ids'];
        }
        $builder->andWhere('hr_entry.manager = :manger: ' . $stringWhere, $where);
        if (empty($sort)) {
            $builder->orderby('hr_resume.created_at desc');
        } else {
            $builder->orderby($sort);
        }
        $totalInfo  = $builder->getQuery()->getSingleResult();
        $cnt = intval($totalInfo->total);
        $builder->columns([
            'hr_interview_offer.id as offer_id',//offer id
            'hr_interview_offer.resume_id',//简历id
            'hr_interview_offer.position_id',//职位id
            "DATE_FORMAT(hr_interview_offer.work_time,'%Y-%m-%d') AS work_time",//到岗时间
            'hr_resume.first_name',//名
            'hr_resume.last_name',//姓
            'hr_resume.name',//姓
            'hr_hc.worknode_id',//工作网点id
            "hr_entry.record_entry",
            "DATE_FORMAT(CONVERT_TZ(hr_entry.entry_date, '+00:00', '" . $this->timezone . "' ),'%Y-%m-%d') AS entry_date",
            'hr_resume.identity_validate_status',//身份证确认状态

        ]);
        if ($no_page == 0) {
            $builder->limit($pageSize,($pageNum - 1) * $pageSize);
        }
        $dataList = $builder->getQuery()->execute()->toArray();
        $this->getDI()->get('logger')->write_log('get_offer_list_sql: ' . json_encode($builder->getPhql()), 'info');
        $pageData = [
            "count"     => $cnt,
            "pageCount" => ceil($cnt/$pageSize),
            "pageNum"   => intval($pageNum),
            "pageSize"  => $pageSize,
        ];

        $returnData = [
            'data'          => $dataList ?? [],
            'pagination'    => $pageData,
        ];
        $this->getDI()->get('logger')->write_log('get_offer_list_returnData: ' . json_encode($returnData), 'info');
        return $returnData;
    }

    /**
     * 修改简历
     * @param integer $interviewId 简历信息
     * @param integer $state       状态
     * @param integer $level       当前轮次
     */
    public function sendMsgToBuddy($interviewId,$state=0,$level = 0){
        //合作到期停用此功能
        return;

        if(env('country_code') != 'TH'){
            return;
        }
        $sql ="select * from hr_interview where interview_id=:id";
        $item = $this->db_rby->fetchOne($sql,\Phalcon\Db::FETCH_ASSOC,['id'=>$interviewId]);

        if(empty($item)){
            $this->getDI()->get("logger")->write_log("not found interview","info");
            return;
        }

        if(empty($state)){
            $state = $item['state'];
        }

        $resume = (new ResumeRepository())->getResumeInfo(['id'=>$item['resume_id']]);
        if(empty($resume)){
            $this->getDI()->get("logger")->write_log("not found resume","info");
            return;
        }
        //如果不是buddy的简历，不用管
        if($resume['source'] != enums::$resume_source['buddy']){
            $this->getDI()->get("logger")->write_log("not buddy resume","info");
            return;
        }

        if(empty($level)){
            $level = $this->selectLevel($interviewId);
        }
        if(empty($level)){
            $level = 1;
        }

        $sql = "select * from hr_interview_info where interview_id = :id and `level`=:level";
        $info = $this->db_rby->fetchOne($sql,\Phalcon\Db::FETCH_ASSOC,['id'=>$interviewId,'level'=>$level]);
        $mark = '';
        $attachment = [];
        if(!empty($info)){
            $mark = $info['evaluate'];
            $prefix = $this->getDI()->getConfig()->application['img_prefix'];
            $sql = "select * from hr_interview_file where interview_info_id = :id and `type`=3 and `is_del`=2 ";
            $files = $this->db_rby->fetchAll($sql,\Phalcon\Db::FETCH_ASSOC,['id'=>$info['interview_info_id']]);
            if(!empty($files)){
                foreach ($files as $file){
                    $attachment[] = ['url'=>$prefix.$file['file_path'],'name'=>$file['file_name']];
                }
            }
        }

        $this->state_array     = [
            '1'  => $this->getTranslation()->_('4801'),
            '5'  => $this->getTranslation()->_('4802'),
            '10' => $this->getTranslation()->_('4803'),
            '20' => $this->getTranslation()->_('4804'),
            '25' => $this->getTranslation()->_('4805'),
            '30' => $this->getTranslation()->_('4806'),
            '31' => $this->getTranslation()->_('4807'),
            '32' => $this->getTranslation()->_('4808'),
            '40' => $this->getTranslation()->_('4809'),
            '2'  => $this->getTranslation()->_('4810'),
            '3'  => $this->getTranslation()->_('4811'),
        ];

        $state_arr = [1=>4801,5=>4802,10=>4803,20=>4804,25=>4805,30=>4806,31=>4807,32=>4808,40=>4809,2=>4810,3=>4811];
        $this->lang = 'en';
        foreach ($state_arr as $k=>$v){
            $state_arr[$k] = $this->getTranslation()->_(''.$v);
        }

        $hc_id = $item['hc_id'];

        $sql = "select * from hr_interview_offer where interview_id = :id order by id desc";
        $offer = $this->db_rby->fetchOne($sql,\Phalcon\Db::FETCH_ASSOC,['id'=>$interviewId]);
        if(!empty($offer)){
            $hc_id = $offer['hc_id'];
        }

        //简历id,轮次，状态，轮次评价内容，时间是英国时间，buddy会加7
        $data = ['cv_id'=>$resume['id'],'hc_id'=>$hc_id,'level'=>$level,'state'=>$state,'mark'=>$mark,'attachment'=>$attachment,'state_list'=>$state_arr,'buddy_id'=>$resume['buddy_id'],'updated_at'=> $item['updated_at']];

        $message = new MessageQueueServer();
        $message->sendToMNS(MessageQueueServer::INTERVIEW_QUEUE,$data);
    }
	
	/**
	 * @description: 从 env 获取 期望薪资权限 拼装成员工工号数组
	 *
	 * @param null
	 *
	 * @return     :
	 * <AUTHOR> L.J
	 * @time       : 2021/8/21 10:561
	 */

	public function getAuthoritySetVal()
    {

        //获取薪资工号
        $salary_permission_staff = SettingEnvServer::getSetVal('expected_salary_staff');//期望薪资工号
        $salary_permission_staff = empty($salary_permission_staff) ? [] : explode(',', $salary_permission_staff);
        //根据角色获取工号
        $salary_permission_roles = SettingEnvServer::getSetVal('expected_salary_roles');//期望薪资角色
        $salary_permission_roles = empty($salary_permission_roles) ? [] : explode(',', $salary_permission_roles);


        //增加超管
        $superAdministratorIds = env('superAdministratorIds', "");
        $superAdministratorIdsArr = explode(',', $superAdministratorIds);


        $staff_ids = array_merge($salary_permission_staff, $superAdministratorIdsArr);
        $staff_ids = array_unique($staff_ids);

        //获取期望薪资权限
        $this->getDI()->get("logger")->write_log("获取期望薪资权限 getAuthoritySetVal" . json_encode($staff_ids) . "角色:" . json_encode($salary_permission_roles), "info");
        return ['staff_ids' => $staff_ids, 'roles' => $salary_permission_roles];
    }

    
    /**
     * 通过面试ID查找面试者
     * @param type $interview_id
     * @return array
     */
    public function getResumeByInterviewId($interview_id){
        
        if(!$interview_id){
            return false;
        }
        
        $builder = $this->modelsManager->createBuilder();

        $builder->columns('resume.name, resume.id as cvid, resume.first_name_en, resume.last_name_en');
        $builder->from(['main'=> HrInterviewModel::class]);
        $builder->leftjoin(\FlashExpress\bi\App\Models\backyard\HrResumeModel::class, 'main.resume_id=resume.id', 'resume');
        $builder->andWhere('main.interview_id = :interview_id:', ['interview_id' => $interview_id]);

        $departmentData = $builder->getQuery()->execute()->toArray();
        return $departmentData ?? [];
    }

    /**
     * 根据面试ID获取每个offer最新的offer签字状态数据
     * @param array $interviewIds
     */
    public function getOfferSignApproveData(array $interviewIds){

        $this->getDI()->get("logger")->write_log("getOfferSignApproveData:".json_encode($interviewIds,JSON_UNESCAPED_UNICODE),"info");

        $interviewIds = array_values($interviewIds);
        if(empty($interviewIds)){
            return  [];
        }
        // 获取每个面试ID对应的最新的一条offer签字 数据
        $interview_offer_sign_lasted_data = HrInterviewOfferSignApproveModel::find([
            'columns'=>'max(id) max_offer_sign_id',
            'conditions' => 'interview_id in ({interview_ids:array}) group by interview_id ',
            'bind' => [
                'interview_ids' => $interviewIds
            ]
        ]);

        if($interview_offer_sign_lasted_data){
            $interview_offer_sign_lasted_data = $interview_offer_sign_lasted_data->toArray();
            //根据最新offer签字ID互殴对应的offer签字状态等信息
            $lasted_offer_sign_id = array_column($interview_offer_sign_lasted_data,'max_offer_sign_id');
            if(!$lasted_offer_sign_id){
                return  [];
            }
            $interview_offer_sign_lasted_data = HrInterviewOfferSignApproveModel::find([
                'conditions' => 'id in ({lasted_offer_sign_id:array}) ',
                'bind' => [
                    'lasted_offer_sign_id' => array_values($lasted_offer_sign_id)
                ]
            ]);
            //每个offer最新的一条offer签字状态 数据（关系数组结构：面试ID=>offer签字状态）

            return $interview_offer_sign_lasted_data ? array_column($interview_offer_sign_lasted_data->toArray(),null,'interview_id'):[];
        }
        return [];
    }

    /**
     * 获取最新的一条offer签字数据
     * @param $resume_id
     * @return array
     */
    public function getLastOfferSignData($resume_id){
        $offer_sign_data =  HrInterviewOfferSignApproveModel::findFirst([
            'conditions'=>'resume_id=:resume_id:',
            'bind'=>['resume_id'=>$resume_id],
            'order'=>'id desc'
        ]);
        if($offer_sign_data){
            $offer_sign_data = $offer_sign_data->toArray();
            return $offer_sign_data;
        }

        return [];
    }

    /**
     * 获取面试信息
     * @param $interview_id
     * @return array
     */
    public function getInfoByInterviewId($interview_id)
    {
        $info = HrInterviewModel::findFirst([
            'conditions' => "interview_id = :interview_id:",
            'bind'       => ['interview_id' => $interview_id],
        ]);

        return $info ? $info->toArray() : [];
    }
}
