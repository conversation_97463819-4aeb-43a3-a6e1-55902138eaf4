package log

import (
	"os"

	logging "github.com/op/go-logging"
)

var (
	Logger *logging.Logger
	format logging.Formatter
)

func InitLogger(mod string) {
	Logger = logging.MustGetLogger(mod)
	format = logging.MustStringFormatter(`{"id":"%{id:03x}","function":"%{shortfile}","class":"%{shortfunc}","type":"%{level:.4s}","timestamp":"%{time:2006/01/02 15:04:05.000}","data":"%{message}"}`)
	backend1 := logging.NewLogBackend(os.Stdout, "", 0)
	backend1Formatter := logging.NewBackendFormatter(backend1, format)
	logging.SetBackend(backend1Formatter)
}
