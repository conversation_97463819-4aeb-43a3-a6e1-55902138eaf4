<?php

namespace FlashExpress\bi\App\Models\backyard;


class HrResumeCustomerAuditModel  extends  BackyardBaseModel
{

    //审核状态:1待审核2审核通过3审核驳回
    const AUDIT_STATUS_NO_SUBMIT = 0;//未提交
    const AUDIT_STATUS_PENDING = 1;//待审核
    const AUDIT_STATUS_PASS = 2;//审核通过
    const AUDIT_STATUS_REJECT = 3;//审核驳回

    ////列表搜索映射关系 1 今日待处理 2延期处理 3待审核 4 审核通过 5 终止处理
    public static $search_process_mapping = [
        3 => self::AUDIT_STATUS_PENDING,
        4 => self::AUDIT_STATUS_PASS,
    ];

    public function initialize()
    {
        $this->useDynamicUpdate(true);
        $this->setSource('hr_resume_customer_audit');
    }
}