<?php
use Phalcon\Di\FactoryDefault;

#部署
define('BASE_PATH', dirname(__DIR__));
define('APP_PATH', BASE_PATH . '/app');

try {
    require BASE_PATH.'/vendor/autoload.php';
    require BASE_PATH.'/app/common/functions.php';
    require BASE_PATH.'/app/common/ErrorHandler.php';
    //加载环境变量
    $dotenv = new Dotenv\Dotenv(BASE_PATH);
    $dotenv->load();

    ini_set('memory_limit', '-1');
    ini_set('max_execution_time', 0);
    
    //设定运行环境变量
    $runtime = env('runtime','dev');
    //设置报错级别
    $level = $runtime == 'pro' ? 0 : E_ALL;
    error_reporting($level);

    define('RUNTIME', $runtime);
    date_default_timezone_set(env('timeZoneGMT'));

    defined('TIMEZONE') or define('TIMEZONE', env('timeZone'));
    defined('TIMEZONE_GMT') or define('TIMEZONE_GMT', env('timeZoneGMT'));
    defined('TIME_ADD_HOUR') or define('TIME_ADD_HOUR', env('add_hour'));
    defined('DEFAULT_TIMEZONE') or define('DEFAULT_TIMEZONE',env('default_timezone'));
    defined('COUNTRY_CODE') or define('COUNTRY_CODE', env('country_code'));

    //注册服务
    include APP_PATH . '/config/services.php';

    /**
     * Handle the request
     * 开始响应请求
     */
    $application = new \Phalcon\Mvc\Application($di);

    $modules = require APP_PATH . '/config/modules.php';
    /**
     * 国家模块
     */
    $application->registerModules($modules);
     //注册自动加载服务
    include APP_PATH . '/config/loader.php';
    /**
     *
     */
    $application->handle()->send();

} catch (\Throwable $e) {
    if (in_array(RUNTIME, ['dev','test','training'])) {
        $msg =  $e->getMessage() . '<br>';
        $msg .= '<pre>' . $e->getTraceAsString() . '</pre>';
    }else {
        $msg = "<pre> Something went wrong, if the error continue please contact us </pre>";
    }
    //region 记录系统异常日志
    $log = array(
        'file' => $e->getFile(),
        'line' => $e->getLine(),
        'code' => $e->getCode(),
        'msg' => $e->getMessage(),
        'trace' => $e->getTraceAsString(),
    );
    $di = $application->getDI();
    $exception_logger = $di->get('logger');
    $exception_logger->write_log(json_encode($log,JSON_UNESCAPED_UNICODE));

    //endregion
    $result = [
        'code' => 0,
        'msg' => $msg,
        'data' => null
    ];
    $response = $application->response;
    $response->setStatusCode(500);
    $response->setJsonContent($result);
    $response->send();
}