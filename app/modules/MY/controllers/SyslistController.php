<?php
namespace FlashExpress\bi\App\Modules\MY\Controllers;
use FlashExpress\bi\App\Server\SysListServer;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use Exception;
class SyslistController extends ControllerBase
{
    protected $paramIn;

    public function initialize()
    {
        parent::initialize();

        $this->department_id = 7;
        $this->paramIn = $this->request->getPost();
        if(empty($this->paramIn))
        {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }

    }

    public function onConstruct()
    {
        //echo 'IndexController onConstruct<br>';
        parent::onConstruct();
    }


    public function getStaticSalaryApproveAction()
    {
        $paramIn = $this->paramIn;
        $sysListServer = new SysListServer();
        $company = $sysListServer->getStaticSalaryApprove($paramIn);

        $this->jsonReturn($this->checkReturn(['data' => ['companies' => $company]]));

    }
}