<?php
/**
 * Created by PhpStor<PERSON>.
 * User: zhaowei
 * Date: 2021/3/9
 * Time: 14:30
 */

namespace FlashExpress\bi\App\Repository;

use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\enumsContract;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\HrResumeModel;
use FlashExpress\bi\App\Models\backyard\HrStaffContractLogModel;
use FlashExpress\bi\App\Models\backyard\HrStaffContractModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffItemsModel;
use FlashExpress\bi\App\Models\backyard\HrStaffRenewContractApplyModel;
use FlashExpress\bi\App\Models\backyard\JobTransferModel;
use FlashExpress\bi\App\Models\backyard\SalaryApproveModel;
use FlashExpress\bi\App\Models\fle\StaffAccountModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Server\ContractServer;
use FlashExpress\bi\App\Server\EntryServer;
use FlashExpress\bi\App\Server\InterviewServer;
use FlashExpress\bi\App\Server\SysServer;

class HrStaffContractRepository extends BaseRepository
{
    public function __construct()
    {
        parent::__construct();
    }

    private static $instance;

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 电子合同 员工列表
     * @param $paramIn
     * @param int $total
     * @param bool $export
     * @return array
     */
    public function getStaffList($paramIn, &$total = 0)
    {
        //获取数据权限
        $is_admin                 = $this->userInfo['is_admin'];                    //1是超管
        $authority_stores_ids     = $this->userInfo['permission_stores_ids'];       //网点权限
        $authority_department_ids = $this->userInfo['permission_department_ids'];   //部门权限

        //获取数据
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('count(distinct contract.staff_id) as total');
        $builder->from(['contract' => HrStaffContractModel::class]);
        $builder->leftJoin(HrStaffInfoModel::class, 'contract.staff_id = staff.staff_info_id', 'staff');
        $builder->leftJoin(SysStoreModel::class, 'staff.sys_store_id = store.id', 'store');

        //添加合同审批
        $builder->leftjoin(HrStaffRenewContractApplyModel::class,
            " contract_apply.renew_staff_info_id = contract.staff_id and contract_apply.id = (SELECT max( contract_apply.id ) as sa_id FROM " . HrStaffRenewContractApplyModel::class . " contract_apply  WHERE contract_apply.renew_staff_info_id = contract.staff_id GROUP BY contract_apply.renew_staff_info_id) ",
            "contract_apply");

        //组合搜索条件
        $builder = $this->getCondition($builder, $paramIn);

        //数据权限
        if ($is_admin == enums::$is_admin['off']) {
            $staffPermissionSql = $this->getStaffPermission($authority_department_ids,$authority_stores_ids);
            $builder->andWhere($staffPermissionSql);
        }

        $totalInfo = $builder->getQuery()->getSingleResult();
        $total = intval($totalInfo->total);

        if (!empty($paramIn['is_total'])) {
            return [];
        }

        $builder->columns([
            'group_concat(distinct contract.staff_id) as group_concat',
            'staff.name as staff_name',
            'staff.hire_type as hire_type',
            'store.name as store_name',
            'contract.staff_id as staff_id',
            'staff.mobile as mobile',
            'staff.node_department_id as department_id',
            'staff.job_title as job_title_id',
            'staff.sys_store_id as store_id',
            'store.manage_region as manage_region',
            'store.manage_piece as manage_piece',
            'staff.nationality as nationality',
            'staff.state as state',
            'staff.wait_leave_state as wait_leave_state',
            'contract.entry_date as entry_date',
            'contract.resume_id as resume_id',
            'contract_apply.status as apply_status',
            'contract_apply.id as apply_id',
            "MIN(contract.contract_ld_end_date) as contract_ld_end_date",
        ]);
        if ($total == 0) {
            return [];
        }

        $page_size = $paramIn['page_size'];
        $offset    = $page_size * ($paramIn['page_num'] - 1);
        $builder->groupby('contract.staff_id');
        $builder->orderBy('contract_ld_end_date asc, entry_date asc , staff_id asc');
        $builder->limit($page_size, $offset);

        $data_list = $builder->getQuery()->execute()->toArray();

        return $data_list;
    }

    /**
     * 获取多个员工对应下所有合同
     * @param $staff_id_list
     */
    public function getStaffContractList($staff_id_list)
    {

        $builder = $this->modelsManager->createBuilder();
        $builder->from(HrStaffContractModel::class);
        $builder->andWhere('contract_is_deleted = 0');
        $builder->inWhere('staff_id', $staff_id_list);
        $contract_obj = $builder->getQuery()->execute();
        if (empty($contract_obj)) return [];
        $contract_list      = $contract_obj->toArray();
        $staff_contract_map = [];
        foreach ($contract_list as $item) {
            $staff_contract_map[$item['staff_id']][] = $item;
        }

        return $staff_contract_map;


    }


    /**
     * 获取winhr端的员工合同
     *
     * @param $staff_id 工号
     * @param $only_get_is_need 是否只展示"是否需要"值为需要 的合同（过滤不需要的合同），默认：是
     */
    public function getStaffContract($staff_id, $only_get_is_need = true)
    {

        $builder = $this->modelsManager->createBuilder();
        $builder->from(HrStaffContractModel::class);
        $builder->andWhere('contract_is_deleted = 0 and staff_id=:staff_id:', ['staff_id' => $staff_id]);
        if ($only_get_is_need) {
            $builder->andWhere('contract_is_need = 1');
        }
        $builder->inWhere('contract_status', [
            enums::CONTRACT_STATUS_ADD,
            enums::CONTRACT_STATUS_SEND,
            enums::CONTRACT_STATUS_SIGNATURE,
            enums::CONTRACT_STATUS_SIGN_PROCESSING,
            enums::CONTRACT_STATUS_AUDIT,
            enums::CONTRACT_STATUS_ARCHIVE,
            enums::CONTRACT_STATUS_ARCHIVED,
            enums::CONTRACT_STATUS_RDNEWAL,
            enums::CONTRACT_STATUS_EXPIRES,
            enums::CONTRACT_STATUS_UNSIGNED,
            enums::CONTRACT_STATUS_FEEDBACK,
        ]);
        $builder->orderBy('contract_type asc');

        $contract_obj = $builder->getQuery()->execute();
        if (empty($contract_obj)) return [];
        $contract_list = $contract_obj->toArray();
        return $contract_list;
    }

    /**
     * 获取员工最近的合同
     * @param $staffInfoId
     */
    public function getStaffContractOneInfo($staffInfoId,$contractType = enums::CONTRACT_LDHT)
    {
        //获取员工最近一份历史劳动合同
        $contractInfo = HrStaffContractModel::findFirst([
            'conditions' => "
                    staff_id = :staff_id: 
                    and contract_type = :contract_type:
                    and contract_status = :contract_status: 
                    and contract_is_need = :contract_is_need: 
                    and contract_is_deleted = :contract_is_deleted:",
            'bind'       => [
                'staff_id'            => $staffInfoId,
                'contract_type'       => $contractType,
                'contract_status'     => enums::CONTRACT_STATUS_ADD,
                'contract_is_need'    => HrStaffContractModel::IS_NEED_YES,
                'contract_is_deleted' => enums::IS_DELETED_NO,
            ],
            'order'      => 'id desc',
        ]);

        return $contractInfo ? $contractInfo->toArray() :[];
    }

    /**
     * 获取员工合同
     * @param $staffInfoId
     * @param int $contractStatus
     * @return mixed
     */
    public function getContractListByStaffId($staffInfoId,$contractStatus = enums::CONTRACT_STATUS_AUDIT)
    {
        //获取员工最近一份历史劳动合同
        return HrStaffContractModel::find([
            'conditions' => "
                    staff_id = :staff_id: 
                    and contract_status = :contract_status: 
                    and contract_is_need = :contract_is_need: 
                    and contract_is_deleted = :contract_is_deleted:",
            'bind'       => [
                'staff_id'            => $staffInfoId,
                'contract_status'     => $contractStatus,
                'contract_is_need'    => HrStaffContractModel::IS_NEED_YES,
                'contract_is_deleted' => enums::IS_DELETED_NO,
            ],
            'order'      => 'contract_type asc',
        ])->toArray();
    }

    /**
     * 获取资产审核电子合同
     * @param $staff_id 工号
     * @param $only_get_is_need 是否只展示"是否需要"值为需要 的合同（过滤不需要的合同），默认：是
     * @return  array
     **/
    public function getAsstContract($staff_id, $only_get_is_need = true)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(HrStaffContractModel::class);
        $builder->andWhere('contract_is_deleted = 0 and staff_id=:staff_id:', ['staff_id' => $staff_id]);
        if ($only_get_is_need) {
            $builder->andWhere('contract_is_need = 1');
        }
        $builder->andWhere('contract_type = :contract_type:', ['contract_type' => enums::CONTRACT_ZCXY]);
        $builder->inWhere('contract_status', [enums::CONTRACT_STATUS_ADD, enums::CONTRACT_STATUS_SEND, enums::CONTRACT_STATUS_SIGNATURE, enums::CONTRACT_STATUS_SIGN_PROCESSING,enums::CONTRACT_STATUS_AUDIT, enums::CONTRACT_STATUS_ARCHIVE, enums::CONTRACT_STATUS_ARCHIVED, enums::CONTRACT_STATUS_RDNEWAL, enums::CONTRACT_STATUS_EXPIRES, enums::CONTRACT_STATUS_UNSIGNED, enums::CONTRACT_STATUS_FEEDBACK]);
        $builder->orderBy('contract_type asc');

        $contract_obj = $builder->getQuery()->execute();
        if (empty($contract_obj)) return [];
        $contract_list = $contract_obj->toArray();

        return $contract_list[0] ?? [];
    }



    /**
     * 获取By端的员工合同
     * 状态条件：待签字之后节点的合同
     * @param $staff_id 工号
     */
    public function getByStaffContract($staff_id, $ct_id = 0)
    {
        $builder = $this->modelsManager->createBuilder();

        $builder->from(HrStaffContractModel::class);
        $builder->andWhere('contract_is_deleted = 0 and contract_is_need=1');
        $builder->andWhere('staff_id=:staff_id:', ['staff_id' => $staff_id]);

        // 马来虚拟合同
        if (isCountry('MY')) {
            $builder->andWhere('is_system_generate=:is_system_generate:', ['is_system_generate' => HrStaffContractModel::IS_SYSTEM_GENERATE_NO]);
        }

        $builder->inWhere('contract_status', [
            enums::CONTRACT_STATUS_SIGNATURE,
            enums::CONTRACT_STATUS_SIGN_PROCESSING,
            enums::CONTRACT_STATUS_AUDIT,
            enums::CONTRACT_STATUS_ARCHIVE,
            enums::CONTRACT_STATUS_ARCHIVED,
            enums::CONTRACT_STATUS_UNSIGNED,
            enums::CONTRACT_STATUS_FEEDBACK,
            enums::CONTRACT_STATUS_RDNEWAL,
            enums::CONTRACT_STATUS_TAKE,
            enums::CONTRACT_STATUS_REFUSE
        ]);

        if ($ct_id) {
            $builder->andWhere('id = :id:', ['id' => $ct_id]);
        }

        $builder->orderBy('contract_type asc');
        $contract_obj = $builder->getQuery()->execute();
        if (empty($contract_obj)) {
            return [];
        }
        $contract_list = $contract_obj->toArray();
        return $contract_list;
    }


    /**
     * 获取查询条件
     * @param $builder
     * @param $paramIn
     * @return mixed
     */
    private function getCondition($builder, $paramIn)
    {
        $builder->andWhere('contract.contract_is_deleted = 0 and contract.contract_is_need = 1');
        $manage_where = $paramIn['manage_where'] ?? []; //负责人管辖 部门、大区、片区、网点等信息

        switch ($paramIn['tag_options']) { //标签标识
            case 1: // 全部
                $builder->notInWhere('contract.contract_status',
                    [enums::CONTRACT_STATUS_RESCIND, enums::CONTRACT_STATUS_RDNEWED]);
                break;
            case 2: // 反馈处理
                $builder->andWhere('contract.contract_status = '.enums::CONTRACT_STATUS_FEEDBACK);
                break;
            case 3: // 未签字
                $builder->andWhere('contract.contract_status = '.enums::CONTRACT_STATUS_UNSIGNED);
                break;
            case 4: // 待发送
                $builder->inWhere('contract.contract_status', [
                    enums::CONTRACT_STATUS_ADD,
                    enums::CONTRACT_STATUS_SEND,
                    enums::CONTRACT_STATUS_RDNEWAL,
                    enums::CONTRACT_STATUS_EXPIRES,
                ]);

                break;
            case 5:// 待员工签署
                $builder->andWhere('contract.contract_status = '.enums::CONTRACT_STATUS_SIGNATURE);
                break;
            case 6:// 待归档
                $builder->inWhere('contract.contract_status',
                    [enums::CONTRACT_STATUS_AUDIT, enums::CONTRACT_STATUS_ARCHIVE]);
                break;
            case 7:// 已归档
                $builder->notInWhere('contract.contract_status',
                    [enums::CONTRACT_STATUS_RESCIND, enums::CONTRACT_STATUS_RDNEWED]);
                $builder->andWhere('contract.contract_is_all_archived = 1');
                break;
        }

        //劳动合同截止时间筛选
        if ($paramIn['ldht_end_date_start']) {
            $builder->andWhere('contract.contract_type= 1 and contract.contract_end_date >= :ldht_end_date_start:',
                ['ldht_end_date_start' => $paramIn['ldht_end_date_start']]);
        }
        if ($paramIn['ldht_end_date_end']) {
            $builder->andWhere('contract.contract_type= 1 and contract.contract_end_date <= :ldht_end_date_end:',
                ['ldht_end_date_end' => $paramIn['ldht_end_date_end']]);
        }

        //劳动合同类型筛选
        if ($paramIn['labor_contract_type']) {
            $builder->andWhere('contract.contract_type= 1 and contract.contract_child_type = :labor_contract_type:',
                ['labor_contract_type' => $paramIn['labor_contract_type']]);
        }

        //入职时间时间筛选
        if ($paramIn['entry_date_start']) {
            $builder->andWhere('contract.entry_date >= :entry_date_start:',
                ['entry_date_start' => $paramIn['entry_date_start']]);
        }
        if ($paramIn['entry_date_end']) {
            $builder->andWhere('contract.entry_date <= :entry_date_end:',
                ['entry_date_end' => $paramIn['entry_date_end']]);
        }
        // 简历ID
        if ($paramIn['resume_id']) {
            $builder->andWhere('contract.resume_id = :resume_id:', ['resume_id' => $paramIn['resume_id']]);
        }
        //工号
        if ($paramIn['staff_id']) {
            $builder->andWhere('contract.staff_id = :staff_id:', ['staff_id' => $paramIn['staff_id']]);
        }
        //员工姓名
        if ($paramIn['staff_name']) {
            $builder->andWhere("staff.name like'%{$paramIn['staff_name']}%' ");
        }
        //手机号
        if ($paramIn['mobile']) {
            $builder->andWhere('staff.mobile = :mobile:', ['mobile' => $paramIn['mobile']]);
        }

        //职位
        if ($paramIn['job_title_id']) {
            $builder->andWhere('staff.job_title = :job_title_id:', ['job_title_id' => $paramIn['job_title_id']]);
        }

        if (!empty($paramIn['hire_type'])) {
            $builder->andWhere('staff.hire_type IN ({hire_type:array})', ['hire_type' => array_values($paramIn['hire_type'])]);
        }

        //网点
        if ($manage_where && $manage_where['type'] == '1') { // 如果有管辖大区数据权限，则将管辖部门和筛选部门 取交集
            if ($paramIn['store_id']) {
                $builder->inWhere('staff.sys_store_id', array_intersect($manage_where['ids'], [$paramIn['store_id']]));
            } else {
                $builder->inWhere('staff.sys_store_id', $manage_where['ids']);
            }
        } elseif ($paramIn['store_id']) {
            $builder->andWhere('staff.sys_store_id = :store_id:', ['store_id' => $paramIn['store_id']]);
        }

        //片区
        if ($manage_where && $manage_where['type'] == '2') { // 如果有管辖大区数据权限，则将管辖部门和筛选部门 取交集
            if ($paramIn['manage_piece']) {
                $builder->inWhere('store.manage_piece',
                    array_intersect($manage_where['ids'], [$paramIn['manage_piece']]));
            } else {
                $builder->inWhere('store.manage_piece', $manage_where['ids']);
            }
        } elseif ($paramIn['manage_piece']) {
            $builder->andWhere('store.manage_piece = :manage_piece:', ['manage_piece' => $paramIn['manage_piece']]);
        }

        //大区
        if ($manage_where && $manage_where['type'] == '3') { // 如果有管辖大区数据权限，则将管辖部门和筛选部门 取交集
            if ($paramIn['manage_region']) {
                $builder->inWhere('store.manage_region',
                    array_intersect($manage_where['ids'], [$paramIn['manage_region']]));
            } else {
                $builder->inWhere('store.manage_region', $manage_where['ids']);
            }
        } elseif ($paramIn['manage_region']) {
            $builder->andWhere('store.manage_region = :manage_region:', ['manage_region' => $paramIn['manage_region']]);
        }

        //部门
        if ($manage_where && $manage_where['type'] == '4') { // 如果有管辖部门数据权限，则将管辖部门和筛选部门 取交集
//            if ($paramIn['department_id']) {
//                $builder->inWhere('staff.node_department_id',
//                    array_intersect($manage_where['ids'], [$paramIn['department_id']]));
//            } else {
//                $builder->inWhere('staff.node_department_id', $manage_where['ids']);
//            }
            if (!empty($paramIn['department_id'])) {
                $deptIds = SysServer::getDepartmentConditionByParams($paramIn);
                if (!empty($deptIds)) {
                    $builder->inWhere('staff.node_department_id',array_intersect($manage_where['ids'], $deptIds));
                }else{
                    $builder->inWhere('staff.node_department_id', $manage_where['ids']);
                }
            }else{
                $builder->inWhere('staff.node_department_id', $manage_where['ids']);
            }
            $builder->andWhere("staff.sys_store_id = '-1'");
        } else {
//            if ($paramIn['department_id']) {
//                $builder->andWhere('staff.node_department_id = :department_id:',
//                    ['department_id' => $paramIn['department_id']]);
//            }
            if (!empty($paramIn['department_id'])) {
                $deptIds = SysServer::getDepartmentConditionByParams($paramIn);
                if (!empty($deptIds)) {
                    $builder->inWhere('staff.node_department_id',$deptIds);
                }
            }
        }


        //在职状态(当未筛选工号也未筛选手机号的条件下使用在职状态筛选条件）
        if (!empty($paramIn['on_job_status']) && empty($paramIn['staff_id']) && empty($paramIn['mobile'])) {
            //获取对应在职状态下的员工工号列表
            if ($paramIn['on_job_status'] == 1) {
                //在职
                $builder->andWhere('staff.state = :staff_state: and wait_leave_state=:wait_leave_state:',
                    ['staff_state' => 1, 'wait_leave_state' => 0]);
            } elseif ($paramIn['on_job_status'] == 2) {
                //离职
                $builder->andWhere('staff.state = :staff_state:', ['staff_state' => 2]);
            } elseif ($paramIn['on_job_status'] == 3) {
                //停职
                $builder->andWhere('staff.state = :staff_state:', ['staff_state' => 3]);
            } else {
                //待离职
                $builder->andWhere('staff.state = :staff_state: and wait_leave_state=:wait_leave_state:',
                    ['staff_state' => 1, 'wait_leave_state' => 1]);
            }
        }

        //审核状态
        if (!empty($paramIn['apply_status'])) {
            $builder->andWhere('contract_apply.status = :status:', ['status' => $paramIn['apply_status']]);
        }

        return $builder;
    }

    /**
     * 获取指定工号下 历史合同
     *
     * 取值条件：
     * 工号：指定员工工号
     * 状态为：已到期、已解除、已续约
     * @param $staff_id
     * @return array
     */
    public function getStaffHistoryContractList($staff_id)
    {
        //TODO 获取历史合同
        $builder = $this->modelsManager->createBuilder();
        $builder->columns([
                'id',
                'staff_id',
                'staff_name',
                'contract_path',
                'contract_path_v2',
                'contract_start_date',
                'contract_end_date',
                'contract_date_is_long',
                'contract_status',
                'contract_type',
                'contract_child_type',
            ]

        );
        $builder->from(HrStaffContractModel::class);

        //组合搜索条件
        $builder->andWhere('contract_is_need = 1 and staff_id = :staff_id:', ['staff_id' => $staff_id]);
        //解除、已续约、已到期的合同显示在历史合同
        $builder->inWhere('contract_status', [
            enums::CONTRACT_STATUS_RESCIND,
            enums::CONTRACT_STATUS_RDNEWED,
            enums::CONTRACT_STATUS_EXPIRES,
            enums::CONTRACT_STATUS_TRANSFER,
        ]);

        //获取员工总条数
        $total = $builder->getQuery()->execute()->count();
        if ($total == 0) return [];
        $data_list = $builder->getQuery()->execute()->toArray();

        return $data_list;
    }


    /**
     * 获取员工合同的操作日志
     * @param $staff_id
     * @return array
     */
    public function getStaffLogList($staff_id, $action = [])
    {
        //TODO 获取历史合同
        $builder = $this->modelsManager->createBuilder();
        $builder->from(HrStaffContractLogModel::class);
        $builder->andWhere('staff_id = :staff_id: AND is_deleted = 0', ['staff_id' => $staff_id]);
        if(!empty($action)){
            $builder->inWhere('action', $action);
        }

        $builder->orderBy('created_at desc,id DESC');
        $log_obj = $builder->getQuery()->execute();
        if (empty($log_obj)) return [];
        $log_list = $log_obj->toArray();
        return $log_list;
    }


    /**
     * 保存并添加合同处理
     */
    public function saveContract($paramIn, $staff_id,$db)
    {
        $ct_id = $paramIn['ct_id'];

        //todo step1: 读取bi员工信息 同步更新到电子合同表

        //bi员工表数据
        $bi_staff_info_obj = HrStaffInfoModel::FindFirst([
            'conditions' => 'staff_info_id = :staff_info_id:',
            'bind'       => [
                'staff_info_id' => $staff_id,
            ],
        ]);
        if (!$bi_staff_info_obj) {
            //失败返回
            throw new \Exception("电子合同-saveContract 获取用户信息失败，staff_info_id:" . $staff_id);
        }
        $bi_staff_info = $bi_staff_info_obj->toArray();

        $nationality    = $bi_staff_info['nationality'] ?? '0';

        //网点信息
        if ($bi_staff_info['sys_store_id'] == -1) {
            $manage_region = '';
            $manage_piece  = '';

        } else {
            $store_obj  = SysStoreModel::findFirst([
                'conditions' => 'id = :storeId:',
                'bind'       => ['storeId' => $bi_staff_info['sys_store_id']],
            ]);
            $store_info = $store_obj ? $store_obj->toArray() : [];

            $manage_region = $store_info['manage_region'] ?? '';
            $manage_piece  = $store_info['manage_piece'] ?? '';
        }

        // 整理要更新的电子合同表用户字段信息
        $staff_update_data = [
            'staff_name'     => $bi_staff_info['name'] ?? '',
            'mobile'         => $bi_staff_info['mobile'] ?? '',
            'email'          => $bi_staff_info['email'] ?? '',
            'personal_email' => $bi_staff_info['personal_email'] ?? '',
            'department_id'  => $bi_staff_info['node_department_id'] ?? 0,
            'job_title_id'   => $bi_staff_info['job_title'] ?? 0,
            'store_id'       => $bi_staff_info['sys_store_id'] ?? '',
            'manage_region'  => $manage_region,
            'manage_piece'   => $manage_piece,
            'nationality'    => $nationality,
        ];


        $contract_staff_res = $db->updateAsDict('hr_staff_contract', $staff_update_data, 'staff_id = ' . $staff_id);
        if (!$contract_staff_res) {
            //失败返回
            throw new \Exception("电子合同-saveContract 更新用户信息失败:staff_id:{$staff_id},修改数据：" . json_encode($staff_update_data));
        }


        //todo step2: 构造合同信息保存字段
        $save_data = [
            'contract_child_type'   => $paramIn['contract_child_type'],//合同子类型
            'contract_start_date'   => $paramIn['contract_start_date'],//合同开始日期
            'contract_date_is_long' => $paramIn['contract_date_is_long'],//是否长久合同
            'contract_ld_no'        => ContractServer::getInstance()->genLdContractNo($paramIn['contract_child_type']),//是否长久合同

        ];

        //非永久合同，记录合同截止日期（永久合同不需要截止日期
        if ($paramIn['contract_date_is_long'] != 1) {
            $save_data['contract_end_date'] = $paramIn['contract_end_date'];
        }else{
            $save_data['contract_end_date'] = '9999-12-31';
        }

        //格式化职位类型
        if (isCountry('MY')) {
            $save_data['is_renewal']           = $paramIn['is_renewal'];
            $save_data['contract_ld_end_date'] = $save_data['contract_end_date'];
            $save_data["line_job_type"]        = (new InterviewServer())->isFrontLineJob($bi_staff_info['job_title'] ?? 0);

            if (isset($paramIn['is_transfer'])) {
                $save_data['is_transfer']  = $paramIn['is_transfer'];
            }
        }

        if (isCountry() && isset($paramIn['is_renewal'])) {
            $save_data['is_renewal'] = $paramIn['is_renewal'] ?? HrStaffContractModel::IS_RENEWAL_NO;
        }

        //菲律宾续签必传，和泰国不一样，泰国没有续签
        if (isCountry('PH')) {
            $save_data['is_renewal'] = $paramIn['is_renewal'] ?? HrStaffContractModel::IS_RENEWAL_NO;
        }

        //非永久的劳动合同
        if ($paramIn['contract_date_is_long'] != 1 && $paramIn['contract_type'] == enums::CONTRACT_LDHT) {
            //如果修改的是劳动合同，则同步更改表中劳动合同截止时间字段为劳动合同截止日期
            $save_data['contract_ld_end_date'] = $paramIn['contract_end_date'];
            $ldht_end_date_save_data           = ['contract_ld_end_date' => $paramIn['contract_end_date']];
        }

        $contract_res = $db->updateAsDict('hr_staff_contract', $save_data, 'id = ' . $ct_id);
        if (!$contract_res) {
            //失败返回
            throw new \Exception("电子合同-saveContract 更新合同信息失败:合同ID-{$ct_id},修改数据：" . json_encode($save_data));
        }

        if (isset($ldht_end_date_save_data)) {
            //同步更新其他合同数据中 劳动合同截止日期的字段内容
            $status_not_in = implode(',', [enums::CONTRACT_STATUS_RESCIND, enums::CONTRACT_STATUS_RDNEWED]);
            $this->getDI()->get('db')->updateAsDict('hr_staff_contract', $ldht_end_date_save_data, "staff_id ={$staff_id} and contract_is_need = 1 and  contract_is_deleted = 0 and contract_status not in({$status_not_in})");
        }
    }

    /**
     * 生成合同表数据
     * @param $insert_data
     * @return bool
     */
    public function insertContract($insert_data)
    {

        $insert_log_res = $this->getDI()->get('db')->insertAsDict('hr_staff_contract', $insert_data);
        if ($insert_log_res) {
            $id = $this->db->lastInsertId();
            return $id;
        } else {
            throw new \Exception("插入合同信息失败，合同字段：" . json_encode($insert_data));
        }


    }

    /**
     * 根据合同ID更新合同表
     * @param int $ct_id 合同ID
     * @param $updated_data
     * @return bool|string
     */
    public function updateContractById($ct_id, $updated_data)
    {
        $contract_res = $this->getDI()->get('db')->updateAsDict('hr_staff_contract', $updated_data, [
            'conditions' => 'id = ?',
            'bind'       => [$ct_id],
        ]);
        if (!$contract_res) {
            throw new \Exception("更新合同信息失败，合同ID：{$ct_id},合同字段：" . json_encode($updated_data));
        }
        return $this->getDI()->get('db')->affectedRows();

    }

    /**
     * 根据指定员工和员工合同状态更新合同表
     * @param $ct_cond 合同更新条件
     * @param $updated_data
     * @return bool|string
     */
    public function updateContractByStaffStatus($staff_id, $status_cond, $updated_data)
    {
        $updated_cond = "contract_is_deleted=0 and contract_is_need = 1 and staff_id = ? and contract_status = {$status_cond}";
        $contract_res = $this->getDI()->get('db')->updateAsDict('hr_staff_contract', $updated_data, [
            'conditions' => $updated_cond,
            'bind'       => [$staff_id],
        ]);
        if (!$contract_res) {
            throw new \Exception("更新合同信息失败，更新条件：{$updated_cond},合同字段：" . json_encode($updated_data));
        }
        return $this->getDI()->get('db')->affectedRows();

    }

    /**
     * 根据指定员工更新合同表
     * @param $ct_cond 合同更新条件
     * @param $updated_data
     * @return bool|string
     */
    public function updateContractByStaff($staff_id, $updated_data)
    {
        $updated_cond = "contract_is_deleted=0 and contract_is_need = 1 and staff_id = ?";
        $contract_res = $this->getDI()->get('db')->updateAsDict('hr_staff_contract', $updated_data, [
            'conditions' => $updated_cond,
            'bind'       => [$staff_id],
        ]);
        if (!$contract_res) {
            throw new \Exception("更新合同信息失败，更新条件：{$updated_cond},合同字段：" . json_encode($updated_data));
        }
        return $this->getDI()->get('db')->affectedRows();

    }


    /**
     * 记录日志
     * @param $staff_id 员工ID
     * @param $ct_id 合同ID
     * @param $action_type 操作类型
     * @param $user_info 管理员用户信息
     * @param int $ct_type 合同类型
     * @return bool
     * @throws \Exception
     */
    public function insertLog($staff_id, $ct_id, $action_type, $user_info, $ct_type = 0,$reject_content = "")
    {
        $insert_log_data = [
            'staff_id'           => $staff_id,
            'operate_staff_id'   => $user_info['id'] ?? 0,
            'operate_staff_name' => $user_info['name'] ?? 0,
            'contract_id'        => $ct_id,
            'contract_type'      => $ct_type,
            'action'             => $action_type,
            'reject_content'             => $reject_content,
        ];
        $insert_log_res  = $this->getDI()->get('db')->insertAsDict(
            'hr_staff_contract_log', $insert_log_data
        );

        if (!$insert_log_res) {
            throw new \Exception("电子合同-changeContractStatus 插入日志失败:日志数据：" . json_encode($insert_log_data));
        }
        return true;

    }

    /**
     * 获取合同详情
     * @param $ct_id
     * @return array
     */
    public function getContractDetail($ct_id)
    {
        $sql  = "--
                select 
                    *,
                    CONVERT_TZ(updated_at, '+00:00', '".$this->timezone."' ) AS updated_date 
                from hr_staff_contract 
                where id=" . $ct_id ." and contract_is_deleted = 0";
        $model      = $this->getDI()->get('db')->query($sql);
        $data = $model->fetch(\Phalcon\Db::FETCH_ASSOC);
        //查询用户信息
        $staffInfo         = HrStaffInfoModel::FindFirst([
            'columns'    => 'hire_type',
            'conditions' => 'staff_info_id = :staff_info_id:',
            'bind'       => [
                'staff_info_id' => $data['staff_id']??0,
            ],
        ]);
        $data['hire_type'] = $staffInfo->hire_type ?? 0;
        return $data;
    }


    /**
     * 处理超时未签字的合同数据
     * 更新逻辑：
     * 1、合同发送时间 < (当前时间-1天）
     * 2、不需要的合同、已删除的合同无需处理
     */
    public function doUnSignedContract()
    {

        $expired_time = gmdate('Y-m-d H:i:s', strtotime('-1 days'));
        $status       = enums::CONTRACT_STATUS_SIGNATURE;

        //过几天删除  零时支撑
        if (isCountry('TH')) {
            $expired_three_time = gmdate('Y-m-d H:i:s', strtotime('-2 days'));
            $staffIdsStr = "66937,66938,66939,66940,66941,66943,66957,67524,67534,67539,67546,67553,67559,67605,67615,67660,67671,67975,67989,68162,68502,68579,69001,69532,69705,69706,69725,69823,69930,70529,70532,70534,70832,71147,71462,71713,71830,71946,71956,71963,72139,72253,72276,72279,72284,72460,72647,72649,72651,72653,72659,72673,72812,72815,72816,72817,72853,73062,73304,73315,73320,73337,73466,73600,73626,73794,73806,73808,73810,73827,73828,73840,73852,73895,73896,73897,73959,74100,74157,74191,74411,74428,74431,74434,74475,74537,74619,74627,74693,74757,74909,74911,74924,74990,75014,75138,75204,75205,75207,75340,75351,75377,75444,75482,75499,75515,75596,75719,75726,75727,75733,75740,75744,75746,75748,75749,75755,75756,75760,75769,75861,75863,75866,75961,76066,76072,76088,76162,76203,76205,76244,76296,76322,76396,76397,76553,76563,76611,76620,76700,76705,76876,76954,76955,76971,76978,76980,76984,76998,77018,77049,77057,77060,77108,77115,77482,77511,77641,77701,77748,77749,77894,77917,77957,77966,77967,77974,77980,77981,77990,78017,78044,78059,78062,78127,78146,78174,78178,78200,78230,78309,78312,78313,78315,78316,78317,78319,78321,78323,78325,78334,78339,78342,78344,78345,78352,78368,78370,78486,78487,78488,78490,78491,78494,78495,78525,78666,78668,78669,78671,78674,78677,78678,78679,78683,78686,78688,78692,78695,78705,78729,78790,78862,78865,78891,78924,78977,79008,79134,79138,79139,79140,79141,79142,79143,79146,79147,79155,79165,79172,79199,79256,79258,79290,79292,79411,79413,79551,79656,79667,79866,79871,79876,79895,79928,79976,80006,80041,80044,80045,80280,80460,80726,85684,85689,85705,85724,85740,85749,85779,85790,85959,86001,86115,86117,86125,86136,86137,86145,86225,86241,86347,86389,86390,86392,86399,86409,86411,86412,86438,86486,86487,86603,86605,86610,86611,86637,86641,86655,86657,86667,86678,86743,86797,86799,86803,86814,86901,86911,87241,600043,600243,600245,600376,600494,600495,600497,600500,600507,600536,600620,600621,600712,600715,600886,600893,601129,601141,601211,601349,601437,601447,601455,601468,601513,601521,601522,601524,601528,601593,601909,602059,602189,602446,602456,602462,602464,602466,602474,602490,602603,602845,602859,602882,602959,603605,603609,603610,603613,603622,603640,603670,603693,603724,603760,603985,603992,603996,604001,604172,604173,604178,604399,604402,604406,604490,604505,604538,604595,604599,604607,604631,604632,604633,604635,604638,604640,604731,604750,604800,604986,605007,605014,605018,605019,605020,605021,605022,605026,605052,605061,605083,605084,605087,605090,605191,605246,605287,605300,605302,605432,605525,605526,605528,605530,605532,605535,605538,605546,605548,605555,605610,605638,605639,605644,605651,605653,605654,605906,605993,605994,606046,606111,606150,606155,606157,606170,606173,606296,606310,606319,606321,606442,606633,606826,606829,606832,606834,606841,606852,606856,607077,607084,607086,607092,607095,607097,607099,607116,607120,607135,607142,607213,607255,607405,607523,607558,607564,607657,607663,607806,607812,607813,607815,608120,608121,608180,608183,608186,608205,608333,608334,608336,608340,608402,608404,608405,608416,608417,608418,608589,608607,608611,608632,608635,608805,608834,608893,609221,609261,609267,609269,609601,609603,609607,609671,609702,609703,609704,609708,609715,609726,609800,609824,609921,609922,609923,609933,610031,610037,610041,610046,611981,611986,611987,612131,612142,612366,612374,612379,612380,612407,612647,612648,612652,612664,612691,612915,612916,612931,613114,613470,613473,613475,613729,613777,614130,614132,614135,614137,614145,614146,614153,614154,614155,614320,614688,614689,614691,614692,614693,614695,614696,614702,614703,614708,614712,614714,614890,614990,615095,615163,615167,615168,615169,615175,615176,615181,615189,615429,615431,615578,615908,615915,615927,616389,616509,616512,616513,616518,616520,616533,616680,617260,617263,617265,617449,617567,617723,617782,617783,617989,618033,618192,618193,618530,618997,619149,619161,619162,619165,619632,619633,619648,619656,619657,619665,620223,620392,620411,620757,620759,620760,620766,620767,620770,620826,620871,620932,620944,621288,621298,621302,621307,621317,621405,621497,621561,621562,621884,621885,621886,621887,621889,622050,622282,622283,622300,622301,622307,622308,622309,622312,622357,622388,622450,622478,622485,622951,622952,622954,623228,623232,623235,623236,623237,623256,623258,623259,623271,623498,623609,623611,623617,623620,623622,623648,623890,623892,623893,624189,624190,624193,624222,624360,624429,624430,624431,624432,624433,624442,624582,624603,624950,624951,624954,624957,624958,625180,625181,625186,625507,625630,625715,625716,625726,625741,625745,625834,625980,626037,626040,626043,626071,626076,626078,626085,626099,626120,626251,626252,626253,626428,626433,626446,626453,626493,626958,626959,626971,627047,627051,627053,627057,627059,627062,627063,627065,627068,627069,627070,627093,627236,627744,627745,627752,627753,627754,627771,627784,627785,627979,627993,627999,628420,628424,628425,628426,628427,628429,628432,628434,628435,628438,628450,628463,628470,628541,628579,628580,628581,628584,628585,628589,628780,628947,628948,628949,628953,628977,629078,629582,629675,629678,629679,629680,629681,629682,633404,633406,633408,633410,633411,633412,633414,633416,633440,633448,633458,633682,633869,633871,634198,634199,634201,634203,634204,634207,634475,634476,634477,634480,634623,634918,634920,634921,634945,634955,634956,634958,634961,634965,634969,634971,634973,635028,635186,635192,635194,635203,635204,635213,635217,635225,635251,635319,635331,635620,635636,635637,635638,635639,635645,635648,635655,635659,635734,635869,635870,635871,635874,635875,635883,16889,16890,16898,16944,16947,16952,16985,16987,17000,17026,17029,17044,17062,17063,17074,17097,17100,17117,17133,17160,17176,17190,17200,17212,17266,17273,17347,17348,17389,17465,17495,17497,17550,17586,17603,17608,17628,17641,17656,17663,17672,17677,17742,17772,17787,19015,19060,19199,19303,19349,19372,19402,19515,19577,19607,19616,19681,19689,19701,19793,19796,19799,19804,19856,19882,19900,19915,19919,19937,19948,19950,19953,19957,19979,19982,19992,20142,20166,20243,20279,20291,20383,20384,20469,20824,21314,21358,21365,21426,21448,21615,21621,21666,21667,21706,21715,21719,21866,21908,22096,22237,22266,22473,22477,22614,22615,22653,22750,23080,23083,23116,23117,23126,23156,23161,23188,23429,23430,23431,23464,23471,23476,23584,23680,23708,23735,23746,23776,23785,23799,23858,23917,23993,24022,24023,24214,24237,24403,24415,24612,24626,24671,24673,24701,24718,24902,24905,24906,24908,24917,24994,25101,25156,25488,25491,25493,25507,25508,25509,25515,25535,25621,25730,25865,25921,26335,26394,26474,26582,26808,26974,27009,27190,27313,27359,27468,27490,27596,27597,27680,27691,27695,27736,27762,27918,28121,28123,28140,28228,28540,28545,28555,28558,28562,28569,28573,28574,28586,28588,28595,28728,28765,28810,28916,28952,28962,28987,28991,29191,29194,29217,29290,29324,29362,29456,29480,29595,29601,29687,29746,29798,29825,29846,29984,30003,30113,30120,30157,30185,30201,30233,30344,30383,30477,30488,30516,30555,30649,30654,30874,30899,30915,31047,31121,31123,31163,31253,31287,31336,31342,31348,31351,39462,39465,39486,39703,39803,39814,40014,40441,40450,40472,40550,40625,41075,41236,41329,41590,41782,41922,41974,42296,42613,42884,43072,43435,43577,43809,43848,43949,44048,44656,44832,44933,45147,45606,45608,45612,46017,46150,46701,46943,47031,47394,48330,48430,48618,48622,48684,49581,49584,49606,49949,50749,51285,51288,51290,51293,51294,51295,51621,51676,51677,51725,51821,52100,52264,52446,52450,52461,52546,52713,52865,53011,53223,53635,53637,53640,53643,53651,53698,53741,53795,53845,54249,54252,54253,54254,54326,54376,54436,54760,54782,54788,54791,54858,54907,54913,54934,54944,55216,55217,55223,55247,55393,55531,55578,55620,55678,55748,55749,55804,55827,55879,55883,55927,56003,56006,56007,56009,56011,56012,56028,56033,56035,56118,56239,56240,56244,56266,56346,56530,56578,56663,56962,56963,56964,56981,57000,57151,57266,57859,57928,58005,58068,58144,58152,58256,58289,58291,58373,58466,58467,58475,58557,58755,58760,58773,58803,58862,58980,58981,58983,58984,59046,59080,59081,59082,59204,59212,59297,59362,59598,59615,59755,59759,59760,59793,59898,59928,60088,60176,60178,60203,60276,60384,60431,60502,60510,60516,60628,60645,60684,60721,60740,60826,60912,61104,61197,61255,61298,61374,61376,61381,61617,61618,61735,61950,62060,62160,62231,62242,62243,62265,62266,62301,62323,62400,62417,62572,62670,62750,62751,62947,62948,62965,62966,62969,63208,63446,63447,63473,64101,64103,64159,64859,64862,65150,65253,65656,65862,65866,65867,65881,66013,66045,66281,66322,66323,66473,66589,66668,68155,71801,80825,81149,81259,81269,81534,81660,81698,81699,81700,81712,82049,82254,82529,82816,82980,83089,83091,83213,83292,83306,83312,83359,83362,83373,83374,83401,83404,83480,83557,83636,83637,83639,83641,83642,83643,83644,83655,83675,83677,83680,83682,83684,83695,83714,83719,83928,84274,84275,84277,84278,84279,84280,84282,84284,84286,84287,84289,84299,84308,84312,84331,84333,84551,84556,84610,84635,84702,84714,84823,84824,84826,84828,84829,84830,84832,84840,84851,84861,84888,84894,84949,85069,85109,85187,85341,85349,85350,85399,85436,85463,85502,85512,85557,85645,85647,85649,85666,85669,85670,85671,85678,86122,86564,609678,609975,610048,610049,610051,610052,610054,610062,610067,610071,610080,610336,610337,610339,610344,610345,610358,610359,610360,610387,610398,610407,610533,610534,610558,610659,610671,610848,610870,610968,610991,611007,611021,611126,611127,611354,611355,611356,611357,611358,611374,611379,611573,629683,629684,629685,629695,629706,629813,629986,630329,630330,630333,630334,630349,630357,630640,630642,630643,630646,630647,630662,630700,630722,631392,631393,631396,631400,631403,631420,631423,631424,631434,631435,631449,631450,631465,631477,631506,631744,632321,632322,632657,632662,632663,632664,632683,632895,633093,633097,633104,633107,633108,633394,633398,633400,633402,634241";
            $staffIds = explode(',' , $staffIdsStr);

            $hrStaffContract = HrStaffContractModel::find([
                'conditions' => 'staff_id in ({staff_ids:array}) and contract_child_type = :contract_child_type: and contract_is_deleted != 1',
                'bind'       => [
                    'staff_ids'           => $staffIds,
                    'contract_child_type' => 103
                ],
                'columns'    => "id,staff_id,contract_child_type",
            ])->toArray();

            $hrStaffContractIds = getIdsStr(array_column($hrStaffContract,'id'));

            $condition    = "id IN (".$hrStaffContractIds.") AND contract_is_deleted=0 and contract_is_need=1 and contract_status={$status} and contract_send_time < '{$expired_three_time}'";

            $this->getDI()->get('db')->updateAsDict('hr_staff_contract', ['contract_status' => enums::CONTRACT_STATUS_UNSIGNED], $condition);

            $condition    =  "id NOT IN (".$hrStaffContractIds.")  AND contract_is_deleted=0 and contract_is_need=1 and contract_status={$status} and contract_send_time < '{$expired_time}'";

        } else {
            $condition    = "contract_is_deleted=0 and contract_is_need=1 and contract_status={$status} and contract_send_time < '{$expired_time}'";
        }

        $this->getDI()->get('db')->updateAsDict('hr_staff_contract', ['contract_status' => enums::CONTRACT_STATUS_UNSIGNED], $condition);

        return $this->getDI()->get('db')->affectedRows();

    }

    /**
     * 处理待续约的合同数据
     * 更新逻辑：
     * 1、截止日期 < （当前日期+60天）
     * 2、永久合同 、不需要的合同、已删除的合同无需处理
     * 3、合同状态为：已归档 ：60
     *
     */
    public function doRdnewalContract()
    {
        $add_hour = $this->getDI()['config']['application']['add_hour'];
        $rd_newal_date_l = gmdate('Y-m-d', time() + $add_hour * 3600); //待续约日期节点
        $rd_newal_date_r = gmdate('Y-m-d', strtotime('+60 days') + $add_hour * 3600); //待续约日期节点

        $status        = enums::CONTRACT_STATUS_ARCHIVED;

        if (isCountry('MY')) {
            $rd_newal_date_l = date('Y-m-d');                //待续约日期节点
            $rd_newal_date_r = date('Y-m-d', strtotime('+30 days')); //待续约日期节点
            $status          = enums::CONTRACT_STATUS_TAKE;
        }

        $condition     = "contract_is_deleted=0 and contract_is_need=1 and contract_date_is_long=2 and contract_status={$status} and contract_end_date <= '{$rd_newal_date_r}' and contract_end_date >='{$rd_newal_date_l}' ";

        if (isCountry('PH')) {
            $condition .= ' and contract_child_type NOT IN ('.enums::CONTRACT_LDHT_PH_AGENT.','.enums::CONTRACT_LDHT_PH_MONTH.')';
        }

        //泰国个人代理续约单独走逻辑
        if (isCountry()) {
            $condition .= ' and contract_child_type NOT IN (' . enums::CONTRACT_LDHT_AGENT . ')';
        }

        $this->getDI()->get('db')->updateAsDict('hr_staff_contract', ['contract_status' => enums::CONTRACT_STATUS_RDNEWAL], $condition);


        return $this->getDI()->get('db')->affectedRows();
    }

    /**
     * 待续约的合同数据-IC类型
     */
    public function doIcRdnewalContract()
    {
        $status      = enums::CONTRACT_STATUS_ARCHIVED;
        $rdDateLeft  = date('Y-m-d');                        //待续约日期节点
        $rdDateRight = date('Y-m-d', strtotime('+30 days')); //待续约日期节点

        $condition = "contract_is_deleted = " . enums::IS_DELETED_NO . " and contract_is_need = " . HrStaffContractModel::IS_NEED_YES . " and contract_date_is_long = " . HrStaffContractModel::IS_LONG_NO . " and contract_status={$status} and contract_end_date <= '{$rdDateRight}' and contract_end_date >='{$rdDateLeft}' and contract_child_type = " . enums::CONTRACT_LDHT_AGENT;

        $this->getDI()->get('db')->updateAsDict('hr_staff_contract',
            [
                'contract_status' => enums::CONTRACT_STATUS_RDNEWAL,
            ]
            , $condition
        );

        return $this->getDI()->get('db')->affectedRows();
    }

    /**
     * 处理已过期的合同数据
     * 更新逻辑：
     * 1、截止日期 < 当前日期
     * 2、永久合同 、不需要的合同、已删除的合同无需处理
     * 3、合同状态为：已归档（60）、待续约（80）
     *
     */
    public function doExpiresContract()
    {
        $add_hour = $this->getDI()['config']['application']['add_hour'];
        $expires_date = gmdate('Y-m-d', time() + $add_hour * 3600); //合同过期时间节点

        $status       = implode(',', [enums::CONTRACT_STATUS_ARCHIVED, enums::CONTRACT_STATUS_RDNEWAL]);

        //马来独有逻辑
        if (isCountry('MY')) {
            $expires_date = date('Y-m-d'); //合同过期时间节点
            $status       = implode(',', [enums::CONTRACT_STATUS_TAKE, enums::CONTRACT_STATUS_RDNEWAL]);
        }

        $condition    = "contract_is_deleted=0 and contract_is_need=1 and contract_date_is_long=2 and contract_status in({$status}) and contract_end_date < '{$expires_date}'";
        $this->getDI()->get('db')->updateAsDict('hr_staff_contract', ['contract_status' => enums::CONTRACT_STATUS_EXPIRES], $condition);

        return $this->getDI()->get('db')->affectedRows();
    }

    /**
     * 获取员工邮箱
     * @param $staff_id
     * @return array
     */
    public function getStaffEmail($staff_id)
    {
        $staff_email = [];
        //bi库员工表信息
        $hr_staff_obj = HrStaffInfoModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id:',
            'bind'       => [
                'staff_id' => $staff_id,
            ],
        ]);
        if (empty($hr_staff_obj)) {
            return $staff_email;
        }
        $hr_staff_info = $hr_staff_obj->toArray();

        if (isCountry('MY')) {
            if (!empty($hr_staff_info['personal_email'])) {
                $staff_email[] = $hr_staff_info['personal_email'];
            }

            if (!empty($hr_staff_info['email'])) {
                $staff_email[] = $hr_staff_info['email'];
            }
        } else {
            if (!empty($hr_staff_info['email'])) {
                $staff_email[] = $hr_staff_info['email'];
            }
            if (!empty($hr_staff_info['personal_email'])) {
                $staff_email[] = $hr_staff_info['personal_email'];
            }
        }

        return $staff_email;
    }

    /**
     * 获取员工在by上的系统语言
     * @param $staff_id
     * @return mixed|string
     */
    public function getByStaffLang($staff_id)
    {
        $cond  = 'staff_info_id = :staff_info_id: and equipment_type in (1,3)';
        $model = StaffAccountModel::findFirst([
            'conditions' => $cond,
            'bind'       => ['staff_info_id' => $staff_id],
            'order'      => 'updated_at desc',
        ]);
        if (empty($model)) {
            return getCountryDefaultLang();
        }
        $staff_account = $model->toArray();
        return empty($staff_account['accept_language']) ? getCountryDefaultLang() : $staff_account['accept_language'];
    }

    /**
     * 获取待合成签字的合同数据
     */
    public function getStaffSignProcessingData($limit = 5){
        $timeZone = env('timeZone');
        $data = HrStaffContractModel::find([
            'columns' => 'id,contract_signature_img,CONVERT_TZ(updated_at, \'+00:00\', \'' . $timeZone . '\' ) AS updated_at',
            'conditions' => 'contract_is_deleted = 0 and contract_status = '.enums::CONTRACT_STATUS_SIGN_PROCESSING,
            'order'=>'updated_at asc',
            'limit'=>$limit,
        ])->toArray();

        return $data;
    }

    /**
     * 获取待合成签字的合同数据=指定合同id
     */
    public function getStaffSignProcessingDataById($contractIds){
        if(empty($contractIds)) {
            return [];
        }
        $timeZone = env('timeZone');
        $data = HrStaffContractModel::find([
            'columns' => 'id,contract_signature_img,CONVERT_TZ(updated_at, \'+00:00\', \'' . $timeZone . '\' ) AS updated_at',
            'conditions' => 'id in ({ids:array})',
            'bind'       => ['ids' => $contractIds],
        ])->toArray();

        return $data;
    }

    /**
     * 获取员工待签字的合同
     * @param $staff_id
     * @param $msg_id
     * @return array
     */
    public function getWaitSignContractIdsByMsgId($staff_id, $msg_id): array
    {
        $data = HrStaffContractModel::find([
            'columns'    => 'id',
            'conditions' => 'staff_id = :staff_id: and contract_msg_id = :contract_msg_id: and contract_is_deleted = 0 and contract_status = :contract_status:',
            'bind'       => [
                'staff_id'        => $staff_id,
                'contract_msg_id' => $msg_id,
                'contract_status' => enums::CONTRACT_STATUS_SIGNATURE
                ,
            ],
        ])->toArray();
        if (empty($data)) {
            return [];
        }
        return array_column($data, 'id');
    }


    /**
     * 获取合同详情
     * 该方法返回最新的用户信息
     * @param $ct_id
     * @return array
     */
    public function getContractNewDetail($ctId)
    {
        //获取数据 ,要全部数据，会生成新数据
        $ctInfo = HrStaffContractModel::findFirst([
            'conditions' => ' id = :id: AND contract_is_deleted=:contract_is_deleted:',
            'bind' => [
                'id' => $ctId,
                'contract_is_deleted' => 0
            ]
        ]);

        if (empty($ctInfo)) {
            $this->logger->write_log("合同信息不存在 ".$ctId, 'info');
            return [];
        }

        $ctInfo = $ctInfo->toArray();

        //查询用户信息
        $staffInfo = HrStaffInfoModel::FindFirst([
            'columns'    => 'id,name,mobile,email,personal_email,node_department_id,job_title,sys_store_id,nationality,hire_type,hire_times',
            'conditions' => 'staff_info_id = :staff_info_id:',
            'bind'       => [
                'staff_info_id' => $ctInfo['staff_id'],
            ],
        ]);

        if (!$staffInfo) {
            $this->logger->write_log("用户信息不存在 ".$ctId, 'info');
            return [];
        }

        $staffInfo = $staffInfo->toArray();

        //网点信息
        $manageRegion = '';
        $managePiece  = '';

        if ($staffInfo['sys_store_id'] != -1) {
            $storeInfo = SysStoreModel::findFirst([
                'columns'    => 'manage_region,manage_piece',
                'conditions' => 'id = :storeId:',
                'bind'       => ['storeId' => $staffInfo['sys_store_id']],
            ]);

            $storeInfo = $storeInfo ? $storeInfo->toArray() : [];

            $manageRegion = $storeInfo['manage_region'] ?? '';
            $managePiece  = $storeInfo['manage_piece'] ?? '';
        }

        // 整理要更新的电子合同表用户字段信息
        $ctInfo['hire_type']      = $staffInfo['hire_type'];
        $ctInfo['hire_times']     = $staffInfo['hire_times'] ?? 0;
        $ctInfo['staff_name']     = $staffInfo['name'] ?? '';
        $ctInfo['mobile']         = $staffInfo['mobile'] ?? '';
        $ctInfo['email']          = $staffInfo['email'] ?? '';
        $ctInfo['personal_email'] = $staffInfo['personal_email'] ?? '';
        $ctInfo['department_id']  = $staffInfo['node_department_id'] ?? 0;
        $ctInfo['job_title_id']   = $staffInfo['job_title'] ?? 0;
        $ctInfo['store_id']       = $staffInfo['sys_store_id'] ?? '';
        $ctInfo['manage_region']  = $manageRegion;
        $ctInfo['manage_piece']   = $managePiece;
        $ctInfo['nationality']    = $staffInfo['nationality'] ?? '0';
        $ctInfo['updated_date']   = show_time_zone($ctInfo['updated_at']);


        return $ctInfo;
    }

    /**
     * 电子合同合同列表
     * @param $paramIn
     * @param int $total
     * @return array
     */
    public function getContractMyList($paramIn, &$total = 0)
    {
        $returnData             = [];
        $auth                   = $paramIn['auth'] ?? [];
        $authorityStoresIds     = $paramIn['userInfo']['permission_stores_ids'] ?? [];
        $authorityDepartmentIds = $paramIn['userInfo']['permission_department_ids'] ?? [];
        $staffInfoId            = $paramIn['userInfo']['id'] ?? 0;
        $isAdmin                = $paramIn['userInfo']['is_admin'] ?? 0;

        //获取数据
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('count(1) as total');
        $builder->from(['contract' => HrStaffContractModel::class]);
        $builder->leftJoin(HrStaffInfoModel::class, 'contract.staff_id = staff.staff_info_id', 'staff');
        $builder->leftJoin(HrResumeModel::class, 'contract.resume_id = resume.id', 'resume');
        $builder->leftJoin(SysStoreModel::class, 'staff.sys_store_id = store.id', 'store');
        $builder->leftjoin(HrStaffRenewContractApplyModel::class,
            " contract_apply.renew_staff_info_id = contract.staff_id and contract_apply.id = (SELECT max( contract_apply.id ) as sa_id FROM " . HrStaffRenewContractApplyModel::class . " contract_apply  WHERE contract_apply.renew_staff_info_id = contract.staff_id GROUP BY contract_apply.renew_staff_info_id) ",
            "contract_apply");
        $builder->andWhere('contract.contract_is_deleted = :is_deleted:', ['is_deleted' => enums::IS_DELETED_NO]);

        if ($isAdmin == enums::$is_admin['off']) {
            //无权限
            if ($auth['head_office_contract'] == enumsContract::DATA_AUTH_HEAD_NO && $auth['first_line_contract'] == enumsContract::DATA_AUTH_FIRST_NO) {
                return [];
            }
//print_r($auth);
            //组装SQL
            $listSqlMain = '';
            if ($auth['head_office_contract'] != enumsContract::DATA_AUTH_HEAD_ALL || $auth['first_line_contract'] != enumsContract::DATA_AUTH_FIRST_ALL) {
                $listSqlMain = $this->getAuthSql($staffInfoId, $auth, $authorityStoresIds, $authorityDepartmentIds);
            }
//print_r($listSqlMain);
//exit();
            if ($listSqlMain) {
                $builder->andWhere($listSqlMain);
            }
        }

        //组合搜索条件
        $builder = $this->getListMyCondition($builder, $paramIn);

        $totalInfo = $builder->getQuery()->getSingleResult();

        $total = intval($totalInfo->total);

        if (!empty($paramIn['is_total'])) {
            return $returnData;
        }

        $builder->columns([
            'staff.name as staff_name',
            'staff.hire_type as hire_type',
            'store.name as store_name',
            'staff.mobile as mobile',
            'staff.node_department_id as department_id',
            'staff.job_title as job_title_id',
            'staff.sys_store_id as store_id',
            'store.manage_region as manage_region',
            'store.manage_piece as manage_piece',
            'staff.nationality as nationality',
            'staff.state as state',
            'staff.wait_leave_state as wait_leave_state',
            'staff.hire_date as entry_date',            //本次需求客户端不调整，后续需求entry_date改为hire_date
            'contract.id as id',
            'contract.staff_id as staff_id',
            'contract.contract_child_type as contract_child_type',
            'contract.resume_id as resume_id',
            'contract.line_job_type as line_job_type',
            'contract.contract_is_need as contract_is_need',
            'contract.contract_status as contract_status',
            'contract.is_renewal as is_renewal',
            'contract.contract_end_date as contract_end_date',
            'contract.contract_ld_end_date as contract_ld_end_date',
            'contract.contract_start_date as contract_start_date',

            'contract_apply.status as apply_status',
            'contract_apply.id as apply_id',
        ]);

        if (!$total) {
            return $returnData;
        }

        $page_size = $paramIn['page_size'];
        $offset    = $page_size * ($paramIn['page_num'] - 1);
        $builder->orderBy('contract_end_date ASC, entry_date ASC, staff_id DESC');

        $builder->limit($page_size, $offset);

        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 获取查询条件
     * @param $builder
     * @param $paramIn
     * @return mixed
     */
    public function getListMyCondition($builder, $paramIn)
    {
        if (empty($paramIn['tag_options'])) {
            $builder->notInWhere('contract.contract_status', [enums::CONTRACT_STATUS_RESCIND, enums::CONTRACT_STATUS_RDNEWED]);
        } else {
            $builder->andWhere('contract.contract_status = '.$paramIn['tag_options']);
        }

        //劳动合同截止时间筛选
        if ($paramIn['ldht_end_date_start']) {
            $builder->andWhere('contract.contract_end_date >= :ldht_end_date_start:', ['ldht_end_date_start' => $paramIn['ldht_end_date_start']]);
        }

        if ($paramIn['ldht_end_date_end']) {
            $builder->andWhere('contract.contract_end_date <= :ldht_end_date_end:', ['ldht_end_date_end' => $paramIn['ldht_end_date_end']]);
        }

        //劳动合同类型筛选
        if ($paramIn['labor_contract_type']) {
            $builder->andWhere('contract.contract_child_type = :labor_contract_type:', ['labor_contract_type' => $paramIn['labor_contract_type']]);
        }

        //入职时间时间筛选
        if ($paramIn['entry_date_start']) {
            $builder->andWhere('staff.hire_date >= :entry_date_start:', ['entry_date_start' => $paramIn['entry_date_start'].' 00:00:00']);
        }

        if ($paramIn['entry_date_end']) {
            $builder->andWhere('staff.hire_date <= :entry_date_end:', ['entry_date_end' => $paramIn['entry_date_end'].' 23:59:59']);
        }

        // 简历ID
        if ($paramIn['resume_id']) {
            $builder->andWhere('contract.resume_id = :resume_id:', ['resume_id' => $paramIn['resume_id']]);
        }

        //工号
        if ($paramIn['staff_id']) {
            $builder->andWhere('contract.staff_id = :staff_id:', ['staff_id' => $paramIn['staff_id']]);
        }

        //员工姓名
        if ($paramIn['staff_name']) {
            $builder->andWhere("staff.name like :staff_name:",['staff_name' => '%'.$paramIn['staff_name'].'%']);
        }

        //手机号
        if ($paramIn['mobile']) {
            $builder->andWhere('staff.mobile = :mobile:', ['mobile' => $paramIn['mobile']]);
        }

        //职位
        if ($paramIn['job_title_id']) {
            $builder->andWhere('staff.job_title = :job_title_id:', ['job_title_id' => $paramIn['job_title_id']]);
        }

        //网点搜索
        if ($paramIn['store_id']) {
            $builder->andWhere('staff.sys_store_id = :store_id:', ['store_id' => $paramIn['store_id']]);
        }

        //片区
        if ($paramIn['manage_piece']) {
            $builder->andWhere('store.manage_piece = :manage_piece:', ['manage_piece' => $paramIn['manage_piece']]);
        }

        //大区
        if ($paramIn['manage_region']) {
            $builder->andWhere('store.manage_region = :manage_region:', ['manage_region' => $paramIn['manage_region']]);
        }

        //部门
//        if ($paramIn['department_id']) {
//            $builder->andWhere('staff.node_department_id = :department_id:', ['department_id' => $paramIn['department_id']]);
//        }
        if (!empty($paramIn['department_id'])) {
            $deptIds = SysServer::getDepartmentConditionByParams($paramIn);
            if (!empty($deptIds)) {
                $builder->inWhere('staff.node_department_id', $deptIds);
            }
        }

        //雇佣类型
        if (!empty($paramIn['hire_type'])) {
            $builder->andWhere('staff.hire_type IN ({hire_type:array})', ['hire_type' => array_values($paramIn['hire_type'])]);
        }

        //在职状态(当未筛选工号也未筛选手机号的条件下使用在职状态筛选条件）
        if (!empty($paramIn['on_job_status'])) {
            //在职
            if ($paramIn['on_job_status'] == HrStaffInfoModel::STAFF_STATE_IN) {
                $builder->andWhere('staff.state = :staff_state: and wait_leave_state=:wait_leave_state:', [
                    'staff_state'      => HrStaffInfoModel::STAFF_STATE_IN,
                    'wait_leave_state' => HrStaffInfoModel::WAIT_LEAVE_STATE_NO,
                ]);
                //离职
            } elseif ($paramIn['on_job_status'] == HrStaffInfoModel::STAFF_STATE_LEAVE) {
                $builder->andWhere('staff.state = :staff_state:', ['staff_state' => HrStaffInfoModel::STAFF_STATE_LEAVE]);
                //停职
            } elseif ($paramIn['on_job_status'] == HrStaffInfoModel::STAFF_STATE_STOP) {
                $builder->andWhere('staff.state = :staff_state:', ['staff_state' => HrStaffInfoModel::STAFF_STATE_STOP]);
                //待离职
            } elseif ($paramIn['on_job_status'] == HrStaffInfoModel::STAFF_STATUS_WAIT) {
                $builder->andWhere('staff.state = :staff_state: and wait_leave_state=:wait_leave_state:', [
                    'staff_state'      => HrStaffInfoModel::STAFF_STATE_IN,
                    'wait_leave_state' => HrStaffInfoModel::WAIT_LEAVE_STATE_YES,
                ]);
            }
        }

        //审核状态
        if (!empty($paramIn['apply_status'])) {
            $builder->andWhere('contract_apply.status = :status:', ['status' => $paramIn['apply_status']]);
        }

        return $builder;
    }


    /**
     * 组装SQL
     * @param $staffInfoId
     * @param $auth
     * @param $authorityStoresIds
     * @param $authorityDepartmentIds
     * @return string
     */
    public function getAuthSql($staffInfoId, $auth, $authorityStoresIds, $authorityDepartmentIds): string
    {
        $head = $first = '';

        //非一线权限
        if ($auth['head_office_contract'] == enumsContract::DATA_AUTH_HEAD_ALL) {
            $head = 'ALL';
        } elseif ($auth['head_office_contract'] == enumsContract::DATA_AUTH_HEAD_EMP) {
            $head = 'HCM';
        } elseif ($auth['head_office_contract'] == enumsContract::DATA_AUTH_HEAD_TA) {
            $head = 'RECRUITER';
        } elseif ($auth['head_office_contract'] == enumsContract::DATA_AUTH_HEAD_PPS) {
            $head = 'NOT';
        }

        //一线权限
        if ($auth['first_line_contract'] == enumsContract::DATA_AUTH_FIRST_ALL) {
            $first = 'ALL';
        } elseif ($auth['first_line_contract'] == enumsContract::DATA_AUTH_FIRST_EMP) {
            $first = 'HCM';
        } elseif ($auth['first_line_contract'] == enumsContract::DATA_AUTH_FIRST_TA) {
            $first = 'RECRUITER';
        } elseif ($auth['first_line_contract'] == enumsContract::DATA_AUTH_FIRST_PPS) {
            $first = 'HCM';
        }

        //hcm数据权限组装SQL
        $hcmAuthSql = ' (( ';

        //部门权限
        if (empty($authorityDepartmentIds)) {
            $hcmAuthSql .= "  staff.node_department_id = 0 ";
        } elseif (is_array($authorityDepartmentIds)) {
            $hcmAuthSql .= "  staff.node_department_id in (".getIdsStr($authorityDepartmentIds).")";
        } else {
            $hcmAuthSql .= "  staff.node_department_id = '".$authorityDepartmentIds."'";
        }

        $hcmAuthSql .= " AND staff.sys_store_id = '-1' ) OR ( ";

        //网点
        if (empty($authorityStoresIds)) {
            $hcmAuthSql .= "  staff.sys_store_id = '' ";
        } else if ($authorityStoresIds == '-2' || in_array('-2', $authorityStoresIds)) {
            $hcmAuthSql .= "  staff.sys_store_id != '-1' ";
        } else if (is_array($authorityStoresIds)) {
            $hcmAuthSql .= "  staff.sys_store_id in (".getIdsStr($authorityStoresIds).")";
        } else {
            $hcmAuthSql .= "  staff.sys_store_id = '".$authorityStoresIds."'";
        }

        $hcmAuthSql .= " )) ";


        //-------------------------配置相同-----------------------------------

        //全部权限
        if (($first == $head) && ($head == 'ALL')) {
            return '';
        }

        //招聘负责人
        if (($first == $head) && ($head == 'RECRUITER')) {
            return " ( resume.recruiter_id =  {$staffInfoId} ) ";
        }

        //hcm数据权限
        if (($first == $head) && ($head == 'HCM')) {
            return $hcmAuthSql;
        }
        //-------------------------配置相同-----------------------------------


        //-------------------------配置不相同-----------------------------------
        $headSql = '';
        $firstSql = '';

        //非一线全部权限
        if ($head == 'ALL') {
            $headSql = '  contract.line_job_type = 0 ';
        }

        //非一线HCM权限
        if ($head == 'HCM') {
            $headSql = '  contract.line_job_type = 0 AND ' . $hcmAuthSql;
        }

        //非一线招聘负责人权限
        if ($head == 'RECRUITER') {
            $headSql = " contract.line_job_type = 0 AND resume.recruiter_id = {$staffInfoId} ";
        }

        //非一线无权查看
        if ($head == 'NOT') {
            $headSql = " contract.line_job_type = 0 AND  contract.id = 0 ";
        }

        //一线全部权限
        if ($first == 'ALL') {
            $firstSql = '  contract.line_job_type = 1 ';
        }

        //一线HCM权限
        if ($first == 'HCM') {
            $firstSql = '  contract.line_job_type = 1 AND ' . $hcmAuthSql;
        }

        //一线招聘负责人权限
        if ($first == 'RECRUITER') {
            $firstSql = " contract.line_job_type = 1 AND resume.recruiter_id =  {$staffInfoId} ";
        }

        //组装SQL
        if ($headSql && $firstSql) {
            return '('.$firstSql.') OR (' . $headSql . ')';
        }

        if ($headSql) {
            return '('.$headSql.')';
        }

        if ($firstSql) {
            return '('.$firstSql.')';
        }

        //-------------------------配置不相同-----------------------------------

        return '';
    }

    /**
     * 电子合同快速复核的员工列表
     * @param $paramIn
     * @param int $total
     * @param bool $export
     * @return array
     */
    public function getCheckStaffList($paramIn, &$total = 0)
    {
        //获取数据权限
        $is_admin                 = $this->userInfo['is_admin'];                    //1是超管
        $authority_stores_ids     = $this->userInfo['permission_stores_ids'];       //网点权限
        $authority_department_ids = $this->userInfo['permission_department_ids'];   //部门权限

        //获取数据
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('count(distinct staff_id) as total');
        $builder->from(['contract' => HrStaffContractModel::class]);
        $builder->leftJoin(HrStaffInfoModel::class, 'contract.staff_id = staff.staff_info_id', 'staff');
        $builder->leftJoin(SysStoreModel::class, 'staff.sys_store_id = store.id', 'store');

        $builder->andWhere('contract.contract_is_deleted = :contract_is_deleted:',
            ['contract_is_deleted' => enums::IS_DELETED_NO]);
        $builder->andWhere('contract.contract_is_need = :contract_is_need:',
            ['contract_is_need' => HrStaffContractModel::IS_NEED_YES]);
        $builder->andWhere('contract.contract_status = :contract_status:',
            ['contract_status' => enums::CONTRACT_STATUS_AUDIT]);

        if (!empty($paramIn['next_staff_id'])) {
            $builder->andWhere('contract.staff_id > :next_staff_id:', ['next_staff_id' => $paramIn['next_staff_id']]);
        }

        //数据权限
        if ($is_admin == enums::$is_admin['off']) {
            $staffPermissionSql = $this->getStaffPermission($authority_department_ids,$authority_stores_ids);
            $builder->andWhere($staffPermissionSql);
        }

        $totalInfo = $builder->getQuery()->getSingleResult();

        $total = intval($totalInfo->total);
        $builder->columns([
            'group_concat(distinct staff_id) as group_concat',
            'staff.name as staff_name',
            'staff.staff_id as staff_id',
            'staff.hire_type as hire_type',
            'store.name as store_name',
            'contract.staff_id as staff_id',
            'staff.mobile as mobile',
            'staff.node_department_id as department_id',
            'staff.job_title as job_title_id',
            'staff.sys_store_id as store_id',
            'store.manage_region as manage_region',
            'store.manage_piece as manage_piece',
            'staff.nationality as nationality',
            'staff.state as state',
            'staff.wait_leave_state as wait_leave_state',
            'contract.entry_date as entry_date',
            'contract.resume_id as resume_id',
        ]);

        $builder->groupby('contract.staff_id');
        $builder->orderBy('staff_id asc');
        $builder->limit(1);

        $data_list = $builder->getQuery()->execute()->toArray();

        return $data_list;
    }

    /**
     * 获取权限配置sql
     * @param $authorityDepartmentIds
     * @param $authorityStoresIds
     * @return string
     */
    private function getStaffPermission($authorityDepartmentIds,$authorityStoresIds): string
    {
        $headOfficeSql = $networkSql = '';

        $contract = (new ContractServer());
        $contract->userInfo = $this->userInfo;

        if (isCountry()) {
            $res = $contract->getStaffPermissionSetting();

            //未开启总部权限
            if (!$res['head_office_contract']) {
                $authorityDepartmentIds = [];
            }

            //未开启网点权限
            if (!$res['first_line_contract']) {
                $authorityStoresIds = [];
            }
        }

        //总部判定
        if (empty($authorityDepartmentIds)) {
            $headOfficeSql .= "  staff.node_department_id = 0 ";
        } elseif (is_array($authorityDepartmentIds)) {
            $headOfficeSql .= "  staff.node_department_id in (".getIdsStr($authorityDepartmentIds).")";
        } else {
            $headOfficeSql .= "  staff.node_department_id = '".$authorityDepartmentIds."'";
        }

        $headOfficeSql .= " AND staff.sys_store_id = '-1'";

        //增加权限数据排重
        if (!empty($authorityStoresIds) && is_array($authorityStoresIds)) {
            if (!in_array('-2', $authorityStoresIds)) {
                $authorityStoresIds = array_intersect($authorityStoresIds, $this->getStaffStoreIds());
            }
        }

        //网点判定
        if (empty($authorityStoresIds)) {
            $networkSql .= " staff.sys_store_id = '' ";
        } elseif ($authorityStoresIds == '-2' || in_array('-2', $authorityStoresIds)) {
            $networkSql .= "  staff.sys_store_id != '-1' ";
        } elseif (is_array($authorityStoresIds)) {
            //获取员工表网点列表
            $networkSql .= "  staff.sys_store_id in (".getIdsStr($authorityStoresIds).")";
        } else {
            $networkSql .= "  staff.sys_store_id = '".$authorityStoresIds."'";
        }

        $returnSql = '(' . $headOfficeSql .') OR (' . $networkSql . ')';

        $this->logger->write_log("getStaffPermission sql ".$returnSql, 'info');

        return $returnSql;
    }

    /**
     * 获取员工所有的网点
     * @return array
     */
    public function getStaffStoreIds(): array
    {
        $staffInfoList = HrStaffInfoModel::find([
            'columns'    => 'DISTINCT(sys_store_id) as sys_store_id',
            'conditions' => 'formal in ({formals:array}) and is_sub_staff = :is_sub_staff:',
            'bind'       => [
                'formals' => [HrStaffInfoModel::FORMAL_1,HrStaffInfoModel::FORMAL_INTERN],
                'is_sub_staff' => HrStaffInfoModel::IS_SUB_STAFF_NO,
            ]
        ])->toArray();

        return array_column($staffInfoList, 'sys_store_id');
    }

    /**
     * 获取转岗信息
     * @param $staff_id
     * @param $contract_transfer_date
     * @return mixed
     */
    public function getJobTransferInfo($staff_id, $contract_transfer_date)
    {
        $jobTransferInfo = JobTransferModel::findFirst([
            'conditions' => 'staff_id = :staff_id: and actual_after_date = :after_date: and state = :state:',
            'bind' => [
                'staff_id'   => $staff_id,
                'after_date' => $contract_transfer_date,
                'state'      => JobTransferModel::JOBTRANSFER_STATE_TRANSFERED,
            ]
        ]);
        return !empty($jobTransferInfo) ? $jobTransferInfo->toArray() : [];
    }

    /**
     * 是否存在转岗
     * @param $staff_id
     * @param $contract_transfer_date
     * @return bool
     */
    public function isExistJobTransfer($staff_id, $contract_transfer_date): bool
    {
        return !empty($this->getJobTransferInfo($staff_id, $contract_transfer_date));
    }
}