<?php
namespace FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\Server\RolesService;

class RolesController extends Controllers\ControllerBase
{
    
    /**
     * 获取角色列表
     */
    public function listAction()
    {
        $roleName = $this->request->get('name', 'trim');
        $returnArr = (new RolesService())->getList($roleName);
        return $this->jsonReturn($returnArr);
    }
}
