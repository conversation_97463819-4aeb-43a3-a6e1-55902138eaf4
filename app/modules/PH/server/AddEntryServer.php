<?php
/**
 *AddEntryServer.php
 * Created by: Lqz.
 * Description: 这个代码是在以前的代码里粘贴过来了，只增加了教育，简历信息向fbi同步
 * User: Administrator
 * CreateTime: 2020/11/6 0006 14:03
 */

namespace FlashExpress\bi\App\Modules\PH\Server;

use Exception;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\DateTime;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\HrAnnexModel;
use FlashExpress\bi\App\Models\backyard\HrEconomyAbilityModel;
use FlashExpress\bi\App\Models\backyard\HrEntryModel;
use FlashExpress\bi\App\Models\backyard\HrFamilyModel;
use FlashExpress\bi\App\Models\backyard\HrhcModel;
use FlashExpress\bi\App\Models\backyard\HrResumeEducationModel;
use FlashExpress\bi\App\Models\backyard\HrResumeExtendModel;
use FlashExpress\bi\App\Models\backyard\HrResumeModel;
use FlashExpress\bi\App\Models\backyard\HrStaffAnnexInfoModel;
use FlashExpress\bi\App\Models\backyard\StaffInfoAuditCheckModel;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Server\OutsourcingBlackListServer;
use FlashExpress\bi\App\Server\ResumeServer;
use FlashExpress\bi\App\Repository\BlacklistRepository;
use FlashExpress\bi\App\Repository\EntryRepository;
use FlashExpress\bi\App\Repository\LogRepository;
use FlashExpress\bi\App\Server\AddEntryServer as GlobalBaseServer;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Server\AIServer;
use FlashExpress\bi\App\Models\backyard\HrStaffItemsModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;

class AddEntryServer extends GlobalBaseServer
{

    /**
     * 待发送消息的id
     * 不同国家不同业务在这里加就好
     * @param $staff_info_id
     * @return array
     */

    public function getMessageId($staff_info_id): array
    {
        return [
            'full_resume_msg_id'  => time() . $staff_info_id . rand(1000000, 9999999),
            'full_vehicle_msg_id' => time() . $staff_info_id . rand(2000000, 9999999),
            'commitment_msg_id'   => time() . $staff_info_id . rand(3000000, 9999999),
        ];
    }

    /**
     * 入职
     * @param $paramIn
     * @return array
     * @throws BusinessException
     * @throws ValidationException
     * @throws Exception
     */
    public function addEntry($paramIn)
    {
        $staffId  = $paramIn['staff_id'];
        $entry_id = $paramIn['entry_id'];

        // 辅导员ID
        $instructor_id = $paramIn['instructor_id'] ?? '';
        $shift_id      = $paramIn['shift_id'] ?? '';
        $entrySql      = "--
                select 
                    hr_entry.*,
                    hr_interview_offer.staff_job_type
                from hr_entry 
                LEFT JOIN hr_interview_offer on hr_interview_offer.id = hr_entry.interview_offer_id  
                where hr_entry.entry_id=" . $entry_id;
        $data          = $this->getDI()->get('db')->query($entrySql);
        $entryData     = $data->fetch(\Phalcon\Db::FETCH_ASSOC);
        if (empty($entryData)) {
            throw new BusinessException($this->getTranslation($this->lang)->_('4012'), 4012);
        }

        if ($entryData['status'] != enums::$entry_status['entry_pending']) { //验证已入职
            throw new BusinessException($this->getTranslation($this->lang)->_('4012'), 4012);
        }

        //获取用户暂存信息
        $info = (new EntryRepository())->getEntryDetail(['entry_id' => $entry_id]);
        if (empty($info)) {
            throw new BusinessException($this->getTranslation($this->lang)->_('err_msg_3'));
        }
        $recordEntry = json_decode($info['record_entry'], true);
        if (empty($recordEntry['hire_date']) || date('Ymd',strtotime($recordEntry['hire_date'])) != date('Ymd')){
            throw new BusinessException($this->getTranslation($this->lang)->_('entry_hire_date_error'));
        }
        $hcInfo                            = HrhcModel::FindFirst([
            'conditions' => 'hc_id=:hc_id:',
            'bind'       => ['hc_id' => $entryData['hc_id']],
        ]);
        //验证 自由轮休天数
        if(!empty($paramIn['working_day_rest_type']) && $paramIn['working_day_rest_type'] == HrStaffInfoModel::WEEK_WORKING_DAY_FREE . HrStaffInfoModel::REST_TYPE_1){
            //获取自由轮休 配置天数 getFreeLeaveConfig
            // worknode_id 网点 position_id
            $server = new EntryServer();
            $setting = $server->getFreeLeaveConfig(date('Y-m-d',strtotime($recordEntry['hire_date'])),$hcInfo->worknode_id, $hcInfo->job_title);
            if(!empty($setting)){
                if ($setting->days_num && $setting->days_num > count($paramIn['default_rest_day_date'])) {
                    throw new BusinessException($this->getTranslation($this->lang)->_('default_less_setting_days',
                        ['days_num' => $setting->days_num]));
                }
                if ($setting->max_days_num && $setting->max_days_num < count($paramIn['default_rest_day_date'])) {
                    throw new BusinessException($this->getTranslation($this->lang)->_('default_more_then_setting_days',
                        ['days_num' => $setting->max_days_num]));
                }
            }
        }
        // 验证 证件号和手机号是否重复
        $resumeServerObj = new ResumeServer();
        $resumeServerObj->checkCredentialsNumANDPhoneNum($recordEntry['identity'], $recordEntry['mobile'], 'whr',
            $entryData['resume_id'], false);
        (new OutsourcingBlackListServer())->check($recordEntry['identity'],'winhr',true,$this->lang);
        //获取新的职级同步到hris
        $job_title_grade_v2 = $recordEntry['job_title_grade_v2'] ?? '';
        if (empty($job_title_grade_v2)) {
            $job_title_grade_v2 = self::newLevelMap($recordEntry['job_title_grade']);
        }
        $recordEntry['job_title_grade_v2'] = $job_title_grade_v2;

        //CONVERSION_PERMANENT_DATE
        $recordEntry['conversion_permanent_date'] = '';

        if ($recordEntry['hire_type'] == HrStaffInfoModel::HIRE_TYPE_MONTH) {
            $recordEntry['conversion_permanent_date'] = $this->getConversionPermanentDate($recordEntry['hire_date']);
        }

        if (isset($recordEntry['hire_type']) && $recordEntry['hire_type']) {
            if (in_array($recordEntry['hire_type'], [1, 2, 3, 4, 13])) {
                $recordEntry['formal'] = 1;
            } elseif (in_array($recordEntry['hire_type'], [5])) {
                $recordEntry['formal'] = 4;
            }

            $recordEntry['hire_times'] = (int)$recordEntry['hire_times'];
        } elseif (isset($entryData["staff_job_type"]) && ($entryData["staff_job_type"] == 1 || $entryData["staff_job_type"] == 2 || $entryData["staff_job_type"] == 4)) {
            //正式
            $recordEntry['formal'] = 1;
        } elseif (isset($entryData["staff_job_type"]) && $entryData["staff_job_type"] == 3) {
            //实现
            $recordEntry['formal'] = 4;
        }
        // 添加辅导员
        if (!empty($instructor_id)) {
            $recordEntry['instructor_id'] = $instructor_id;
        }

        if (!empty($shift_id)) {
            $recordEntry['shift_id'] = $shift_id;
        }
        $recordEntry['nick_name'] = $recordEntry['nickname'] ?? '';
        $recordEntry['remarks']   = $recordEntry['other'] ?? '';

        // 查询简历数据
        $resumeId      = $entryData['resume_id'];
        $resume        = HrResumeModel::findFirst([
            'conditions' => 'id = :id:',
            'bind'       => [
                'id' => $resumeId,
            ],
        ]);
        $resume_extend = HrResumeExtendModel::findFirst(
            [
                'conditions' => 'resume_id = :resume_id:',
                'bind'       => [
                    'resume_id' => $resumeId,
                ],
            ]
        );
        if ($resume_extend) {
            $resume_extend = $resume_extend->toArray();
            if (isset($resume_extend['is_have_fund']) && $resume_extend['is_have_fund'] == 1) {
                if ($resume) {
                    $fund_num = $resume->fund_num;
                }
            }

            if (isset($resume_extend['is_have_medical_insurance']) && $resume_extend['is_have_medical_insurance'] == 1) {
                $medical_insurance_num = $resume_extend['medical_insurance_num'];
            }
            if (isset($resume_extend['is_have_social_security']) && $resume_extend['is_have_social_security'] == 1) {
                $social_security_num = $resume_extend['social_security_num'];
            }
        }

        if (empty($resume)) {
            throw new BusinessException("The staff 【resume_id:{$resumeId}】 in table HrResume not found!");
        }
        $this->getDI()->get('logger')->write_log('entry-add resume info :' . json_encode($resume->toArray()), 'info');

        //身份证确认状态是:不同，则by 到岗确认 无法到岗确认。
        if (!empty($paramIn['source']) && $paramIn['source'] == 'by' && $resume->identity_validate_status == HrResumeModel::IDENTITY_VALIDATE_STATUS_FAIL) {
            throw new ValidationException($this->getTranslation()->_('identity_confirm_status_no_allow_confirm'));
        }
        //身份证确认状态是:待确认，则by 到岗确认要检验是否，确认。
        if (!empty($paramIn['source']) && $paramIn['source'] == 'by' && $resume->identity_validate_status == HrResumeModel::IDENTITY_VALIDATE_STATUS_PENDING) {
            //传了 必定是 '相同' 通过
            if (empty($paramIn['identity_status']) || (!empty($paramIn['identity_status']) && $paramIn['identity_status'] != HrResumeModel::IDENTITY_VALIDATE_STATUS_PASS)) {
                throw new ValidationException($this->getTranslation()->_('identity_confirm_status_empty'));
            }
            //必传
            if (empty($paramIn['hand_identity_url'])) {
                throw new ValidationException('Handheld ID card photo cannot be empty');
            }

            $identityParams['status']            = $paramIn['identity_status'];
            $identityParams['resume_id']         = $resumeId;
            $identityParams['staff_id']          = $staffId;
            $identityParams['source']            = $paramIn['source'];
            $identityParams['hand_identity_url'] = $paramIn['hand_identity_url'];
            $res = (new ResumeServer())->identityStatusUpdate($identityParams);
            $this->getDI()->get('logger')->write_log(['add_entry_identity_confirm' => ['param' => $identityParams, 'res' => $res]], 'info');
        }

        // 查找简历信息
        $edu = HrResumeEducationModel::findFirst([
            'conditions' => 'resume_id = :resumeId: and is_delete = 0',
            'bind'       => [
                'resumeId' => $resumeId,
            ],
            'order'      => 'education_level DESC',
        ]);

        $hr_ecomomy_ability = HrEconomyAbilityModel::findFirst([
            'conditions' => 'resume_id = :resume_id:',
            'bind'       => [
                'resume_id' => $resumeId,
            ],
        ]);
        if (empty($edu)) {
            $edu = $hr_ecomomy_ability;
        }

//        if (empty($edu)) {
//            throw new \Exception("The staff 【resume_id:{$resumeId}】 education in table HrResumeEducation and HrEconomyAbility  not found!");
//        }

        //教育
        $graduate_school = $edu->graduate_school ?? '';
        $major           = $edu->major ?? '';
        $graduate_time   = $edu->graduate_time ?? '';
        $education       = $edu->education_level ?? '';
        //获取发送机号和上牌地点
        $place_cards       = $hr_ecomomy_ability->place_cards ?? '';
        $car_engine_number = $hr_ecomomy_ability->car_engine_number ?? '';
        $driver_number     = $hr_ecomomy_ability->driver_number ?? '';

        //简历信息
        $nationality = $resume->nationality ?? '';
        //江奇需求 https://l8bx01gcjr.feishu.cn/docs/doccnIu2w1SoHhlPGBHwE7HC9ke
        //菲律宾系统新增"中国台湾"国籍，并将国籍选项 "中国台湾" 同步fbi时转为"中国"
        //国家、国籍调整为字典配置，不需要再转为"中国"
        //if ($nationality == 13) {
        //    $nationality = 2;
        //}
        $birthday                = $resume->date_birth ?? '';
        $register_country        = $resume->register_country ?? '';
        $register_province       = $resume->register_government ?? '';
        $register_city           = $resume->register_city ?? '';
        $register_district       = $resume->register_town ?? '';
        $register_postcodes      = $resume->register_postcodes ?? '';
        $register_house_num      = $resume->register_house_num ?? '';
        $register_village_num    = $resume->register_village_num ?? '';
        $register_village        = $resume->register_village ?? '';
        $register_alley          = $resume->register_alley ?? '';
        $register_street         = $resume->register_street ?? '';
        $register_detail_address = $resume->register_detail_address ?? '';

        $middle_name = $resume->middle_name ?? '';
        $first_name  = $resume->first_name ?? '';
        $last_name   = $resume->last_name ?? '';
        $suffix_name = $resume->suffix_name ?? '';
        $name        = $resume->name ?? '';

        $residence_country        = $resume->residence_country ?? '';
        $residence_province       = $resume->residence_government ?? '';
        $residence_city           = $resume->residence_city ?? '';
        $residence_district       = $resume->residence_town ?? '';
        $residence_postcodes      = $resume->residence_postcodes ?? '';
        $residence_house_num      = $resume->residence_house_num ?? '';
        $residence_village_num    = $resume->residence_village_num ?? '';
        $residence_village        = $resume->residence_village ?? '';
        $residence_alley          = $resume->residence_alley ?? '';
        $residence_street         = $resume->residence_street ?? '';
        $residence_detail_address = $resume->residence_detail_address ?? '';

        $family = HrFamilyModel::findFirst([
            'conditions' => 'resume_id = :resume_id:',
            'bind'       => [
                'resume_id' => $resumeId,
            ],
        ]);
//        if (empty($family)) {
//            throw new \Exception("The staff 【resume_id:{$resumeId}】 education in table HrResumeEducation and HrEconomyAbility  not found!");
//        }
        //10=驾驶证正面，11=驾驶证反面，12=车辆登记薄（CR)，13=车辆照片，14=车辆使用授权
        $annex               = HrAnnexModel::find([
            'conditions' => 'type = 1 and oss_bucket_key = :resume_id: and deleted = 0 and file_type in (10,11,12,13,14)',
            'bind'       => [
                'resume_id' => $resumeId,
            ],
        ])->toArray();
        $driving_licence_img = [];
        if ($annex) {
            $config = $this->getDI()->getConfig();
            foreach ($annex as $item) {
                if (in_array($item['file_type'], [10, 11])) {
                    $driving_licence_img[] = $config->application['img_prefix'] . $item['object_key'];
                } elseif ($item['file_type'] = 12) {
                    //车辆登记薄
                    $registration_certificate_img = $config->application['img_prefix'] . $item['object_key'];
                } elseif ($item['file_type'] = 13) {
                    //13=车辆照片
                    $vehicle_img = $config->application['img_prefix'] . $item['object_key'];
                } elseif ($item['file_type'] = 14) {
                    //14=车辆使用授权
                }
            }
        }

        //处理部门异常
        $recordEntry['node_department_id'] = $hcInfo->department_id;

        //获取发动机号和上牌地点

        $dad_first_name         = $family->dad_first_name ?? '';
        $dad_last_name          = $family->dad_last_name ?? '';
        $mum_first_name         = $family->mum_first_name ?? '';
        $mum_last_name          = $family->mum_last_name ?? '';
        $relatives_relationship = $family->relationship ?? '';
        $relatives_first_name   = $family->relationship_first_name ?? '';
        $relatives_last_name    = $family->relationship_last_name ?? '';
        $relatives_call_name    = $family->relationship_call_name ?? '';
        $relatives_mobile       = $family->relationship_mobile ?? '';

        $recordEntry['graduate_school']         = $graduate_school;
        $recordEntry['major']                   = $major;
        $recordEntry['graduate_time']           = $graduate_time;
        $recordEntry['education']               = $education;
        $recordEntry['nationality']             = $nationality;
        $recordEntry['birthday']                = $birthday;
        $recordEntry['register_country']        = $register_country;
        $recordEntry['register_province']       = $register_province;
        $recordEntry['register_city']           = $register_city;
        $recordEntry['register_district']       = $register_district;
        $recordEntry['register_postcodes']      = $register_postcodes;
        $recordEntry['register_house_num']      = $register_house_num;
        $recordEntry['register_village_num']    = $register_village_num;
        $recordEntry['register_village']        = $register_village;
        $recordEntry['register_alley']          = $register_alley;
        $recordEntry['register_street']         = $register_street;
        $recordEntry['register_detail_address'] = $register_detail_address;

        $recordEntry['first_name']  = $first_name;
        $recordEntry['last_name']   = $last_name;
        $recordEntry['middle_name'] = $middle_name;
        $recordEntry['suffix_name'] = $suffix_name;
        $recordEntry['name']        = trim($recordEntry['name']);

        $recordEntry['residence_country']            = $residence_country;
        $recordEntry['residence_province']           = $residence_province;
        $recordEntry['residence_city']               = $residence_city;
        $recordEntry['residence_district']           = $residence_district;
        $recordEntry['residence_postcodes']          = $residence_postcodes;
        $recordEntry['residence_house_num']          = $residence_house_num;
        $recordEntry['residence_village_num']        = $residence_village_num;
        $recordEntry['residence_village']            = $residence_village;
        $recordEntry['residence_alley']              = $residence_alley;
        $recordEntry['residence_street']             = $residence_street;
        $recordEntry['residence_detail_address']     = $residence_detail_address;
        $recordEntry['dad_first_name']               = $dad_first_name;
        $recordEntry['dad_last_name']                = $dad_last_name;
        $recordEntry['mum_first_name']               = $mum_first_name;
        $recordEntry['mum_last_name']                = $mum_last_name;
        $recordEntry['relatives_relationship']       = $relatives_relationship;
        $recordEntry['relatives_first_name']         = $relatives_first_name;
        $recordEntry['relatives_last_name']          = $relatives_last_name;
        $recordEntry['relatives_call_name']          = $relatives_call_name;
        $recordEntry['relatives_mobile']             = $relatives_mobile;
        $recordEntry['place_cards']                  = $place_cards;
        $recordEntry['car_engine_number']            = $car_engine_number;
        $recordEntry['data_from']                    = 'winhr';
        $recordEntry['fund_num']                     = $fund_num ?? '';
        $recordEntry['social_security_num']          = $social_security_num ?? '';
        $recordEntry['medical_insurance_num']        = $medical_insurance_num ?? '';
        $recordEntry['driver_number']                = $driver_number;    //驾照号码
        $recordEntry['vehicle_img']                  = $vehicle_img ?? '';//车辆图片
        $recordEntry['driving_licence_img']          = empty($driving_licence_img) ? '' : implode("\n",
            $driving_licence_img);
        $recordEntry['registration_certificate_img'] = $registration_certificate_img ?? '';//车辆登记簿
        // by 操作 已到岗会传递此参数
        if (isset($paramIn['default_rest_day_date'])) {
            $recordEntry['default_rest_day_date'] = $paramIn['default_rest_day_date'];
        }
        if (isset($recordEntry['manager'])){
            $recordEntry['manager'] = $this->autoUpdateManager($recordEntry['sys_store_id'],$recordEntry['department_id'],$recordEntry['manager']);
        }
        //更新入职表数据
        $update_data = [
            'record_entry' => json_encode($recordEntry),
        ];
        $this->getDI()->get('db')->updateAsDict('hr_entry', $update_data, 'entry_id = ' . $entry_id);
        $ac = new ApiClient('hr_rpc', '', 'staffs-create', $this->lang);
        // 菲律宾国家需要同步税号
        $recordEntry['tax_card'] = !empty($resume->tax_no) ? trim($resume->tax_no) : "";
        $recordEntry['fbid']     = $staffId;
        $ac->setParams($recordEntry);
        $data = $ac->execute();
        if ($data && $data['result']) {
            $data = $data['result'];
        }
        $this->getDI()->get('logger')->write_log('entry-add,request:' . json_encode($recordEntry,
                JSON_UNESCAPED_UNICODE) . ',response:' . json_encode($data, JSON_UNESCAPED_UNICODE), 'info');

        if (isset($data['code']) && $data['code'] == 0) {
            $staff_info_id = $data['body']['staff_info_id'] ?? 0;
            if (empty($staff_info_id)) {
                throw new Exception("entry-add,resume_id-{$resumeId} 入职同步返回工号失败");
            }
            $afterParams['new_staff_info_id'] = $staff_info_id;
            $afterParams['admin_id']          = $staffId;
            $afterParams['entry_id']          = $entry_id;
            return $this->afterCreateStaff($afterParams);
        }
        throw new BusinessException(is_array($data['msg']) ? implode('', $data['msg']) : $data['msg']);
    }

    /**
     * 放在事物里差异的逻辑
     */
    protected function afterEntryOtherLogic()
    {
        $update_data = [
            'commitment_sign_state' => enums::$commitment_sign_state['un_signed'],
            'commitment_msg_id'     => $this->entryParams['commitment_msg_id'],
        ];
        $this->getDI()->get('db')->updateAsDict('hr_resume_extend', $update_data,
            'resume_id = ' . $this->entryParams['resume_id']);

        $this->aiIdCardAudit(
            [
                'staff_info_id' => $this->entryParams['new_staff_info_id'],
                'resume_id'     => $this->entryParams['resume_id'],
                'identity'      => $this->entryParams['identity'],
                'nationality'   => $this->entryParams['nationality'],
            ]
        );
    }


    /**
     * 统一的发送消息的方法
     * @throws ValidationException
     */
    public function sendNotice()
    {
        $noticeServer = new NoticeServer();
        $noticeServer->setParams($this->popNoticeDataFromRedisList());
        //完善简历
        $noticeServer->sendResumeMessage();
        //入职消息提醒
        $noticeServer->onBoardingMessageReminder();
        //车辆信息
        $noticeServer->sendVehicleMessage();
        //承诺书
        $noticeServer->sendCommitmentSignMessage();
        // 辅导员
        $noticeServer->instructorMsg();
    }

    /**
     * 菲律宾 ai 识别身份证照片 到岗确认
     * @param $param
     */
    public function aiIdCardAudit($param): bool
    {
        $this->getDI()->get('logger')->write_log('entry PH aiIdCardAudit param:' . json_encode($param), 'info');
        // 查询简历附件表信息
        $annex = HrAnnexModel::findFirst([
            'conditions' => 'type = 1 and oss_bucket_key = :resume_id: and deleted = 0 and file_type = 2',
            'bind'       => [
                'resume_id' => $param['resume_id'],
            ],
        ]);
        if (empty($annex)) {
            $this->getDI()->get('logger')->write_log('PH-未检测到身份证附件，参数：' . json_encode($param), 'info');
            return false;
        }

        //AI 识别，限制校验文件类型为：jpg,jpeg,png,bmp。
        $fileInfo = explode('.', $annex->object_key);
        $type     = strtolower($fileInfo[1]);
        if (!in_array($type, ['jpg', 'jpeg', 'png', 'bmp'])) {
            $this->getDI()->get('logger')->write_log('PH-检测到身份证附件，非图片类型-jpg,jpeg,png,bmp，参数：' . $annex->object_key,
                'info');
            return false;
        }

        // 查询附件表信息
        $staff_info_id       = $param['staff_info_id'];
        $identity_annex_info = HrStaffAnnexInfoModel::findFirst([
            'conditions' => 'staff_info_id = :staff_info_id: AND type = :type:',
            'bind'       => [
                'staff_info_id' => $staff_info_id,
                'type'          => HrStaffAnnexInfoModel::TYPE_ID_CARD,   // 到岗确认时只是验证身份证

            ],
        ]);

        if (empty($identity_annex_info)) {
            $identity_annex_info                = new HrStaffAnnexInfoModel();
            $identity_annex_info->staff_info_id = $staff_info_id;
            $identity_annex_info->type          = HrStaffAnnexInfoModel::TYPE_ID_CARD;
        }

        $config     = $this->getDI()->getConfig();
        $id_car_url = $config->application['img_prefix'] . $annex->object_key;

        // 查询国籍是否为 菲律宾国家 是走ai 否则 走人工审核
        $hr_staff_item = HrStaffItemsModel::findFirst([
            'conditions' => "staff_info_id = :staff_info_id: and item = 'NATIONALITY'",
            'bind'       => [
                'staff_info_id' => $staff_info_id,
            ],
        ]);
        $hr_staff_item = empty($hr_staff_item) ? [] : $hr_staff_item->toArray();
        $nationality   = $hr_staff_item['value'] ?? 0;

        // 默认
        $identity_annex_info->audit_state         = HrStaffAnnexInfoModel::AUDIT_STATE_WAIT;
        $identity_annex_info->ai_audit_state      = HrStaffAnnexInfoModel::AI_AUDIT_STATE_REJECT;
        $identity_annex_info->identity_file_a     = $id_car_url;
        $identity_annex_info->ai_audit_state_date = date('Y-m-d H:i:s');
        $identity_annex_info->annex_path_front    = trim($id_car_url);

        // 非 菲律宾国家 不走ai 直接进入人工审核
        if ($nationality != enums::IS_PH_NATIONALITY) {
            return $identity_annex_info->save();
        }

        $this->getDI()->get('logger')->write_log('PH 进入AI审核 itemsRet:' . json_encode($hr_staff_item), 'info');

        // 菲律宾国家员工需要走ai
        $ai_result = (new AIServer())->ai_id_card_ocr_post($id_car_url);
        // 如果返回空 则直接返回
        if (empty($ai_result)) {
            $this->getDI()->get('logger')->write_log('AiIdCardAuditPost PH rpc empty id_card_url: ' . $id_car_url,
                'info');
            return $identity_annex_info->save();
        }

        // 如果识别失败，也直接返回
        if (strtoupper($ai_result['status']) != 'OK') {
            $this->getDI()->get('logger')->write_log('AiIdCardAuditPost PH rpc OK id_card_url: ' . $id_car_url . ' result:' . json_encode(['ai_result' => $ai_result]),
                'info');
            return $identity_annex_info->save();
        }

        $this->getDI()->get('logger')->write_log('ph-ai审核审核结果 result：' . json_encode($ai_result) . ' 参数: ' . json_encode([
                'staff_info_id' => $staff_info_id,
                'id_card_url'   => $id_car_url,
            ]), 'info');
        $result         = $ai_result['result'];
        $ai_result_data = $result;
        if (in_array($result['document_type'], ['pag-ibig-card', 'social-security-system-card', 'pro-id-card'])) {
            // document_type = "pag-ibig-card"时 返回的证件类型为公积金卡
            // document_type = "social-security-system-card"返回的证件类型为社保卡
            // document_type = "pro-id-card" 返回的证件类型为职业卡
            // 提示 请上传要求的证件类型
            $this->getDI()->get('logger')->write_log('AiIdCardAuditPost PH rpc type error id_card_url: ' . $id_car_url . ' result:' . json_encode(['ai_result' => $ai_result]),
                'info');
            return $identity_annex_info->save();
        }

        // ai识别成功 则判断返回的数据是否合法。
        //查询符合条件的员工
        $info          = [];
        $staffInfoData = HrStaffInfoModel::findFirst([
            'conditions' => "staff_info_id = :staff_info_id:",
            'bind'       => [
                'staff_info_id' => $staff_info_id,
            ],
        ]);
        if (is_object($staffInfoData) && !empty($staffInfoData->toArray())) {
            $info         = $staffInfoData->toArray();
            $info['name'] = trim(strtoupper($info['name']));
            // “姓”+“名”+“中间名”
            $info['splice_name'] = strtoupper(trim($info['last_name']) . ' ' . trim($info['first_name']) . ' ' . trim($info['middle_name']));
        }

        // 如果为空 则返回
        if (empty($info)) {
            $this->getDI()->get('logger')->write_log('AiIdCardAuditPost PH hr_staff_info empty staff_info_id: ' . $staff_info_id,
                'info');
            return $identity_annex_info->save();
        }
        //获取生日
        $staffItems       = (new StaffRepository())->getSpecStaffItemsInfo($staff_info_id, ['BIRTHDAY']);
        $info['birthday'] = !empty($staffItems['BIRTHDAY']) ? $staffItems['BIRTHDAY'] : '';

        $compare_id_card = (new AiCompareServer())->idCard($result, $info);
        $this->getDI()->get('logger')->write_log('ai_id_card_audit_card_type_info:' . json_encode([
                'ai_result' => $ai_result,
                'params'    => $param,
            ]),
            'info');
        // 审核通过则进行存储
        // 发现之前人 没保存card_number 修正一下这里
        $identity_annex_info->card_number    = $info['identity'] ?? '';
        if (!empty($compare_id_card['is_pass'])) {
            $identity_annex_info->audit_state    = HrStaffAnnexInfoModel::AUDIT_STATE_PASS;
            $identity_annex_info->ai_audit_state = HrStaffAnnexInfoModel::AI_AUDIT_STATE_PASS;
        } else {
            // 19847 AI审核不通过，BY端是审核中状态，HCM显示待审核，照片是已上传
            $identity_annex_info->audit_state    = HrStaffAnnexInfoModel::AUDIT_STATE_WAIT;
            $identity_annex_info->ai_audit_state = HrStaffAnnexInfoModel::AI_AUDIT_STATE_REJECT;
        }
        // 添加ai识别记录结果
        $auditCheckInfo = StaffInfoAuditCheckModel::findFirst([
            'conditions' => 'staff_info_id = :staff_info_id: and type = :type: ',
            'bind'       => [
                'staff_info_id' => $staff_info_id,
                'type'          => StaffInfoAuditCheckModel::TYPE_IDENTITY,
            ],
        ]);
        if (empty($auditCheckInfo)) {
            $auditCheckInfo = new StaffInfoAuditCheckModel();
        }
        $auditCheckInfo->staff_info_id           = $staff_info_id;
        $auditCheckInfo->type                    = StaffInfoAuditCheckModel::TYPE_IDENTITY;
        $auditCheckInfo->ai_recognition_data     = $compare_id_card ? json_encode($compare_id_card, JSON_UNESCAPED_UNICODE) : '';
        $auditCheckInfo->ai_recognition_end_data = $compare_id_card ? json_encode($compare_id_card, JSON_UNESCAPED_UNICODE) : '';
        if ($auditCheckInfo->save() !== true) {
            $this->getDI()->get('logger')->write_log('staff_info_audit_check 保存失败 staff_info_id : ' . $staff_info_id,
                'error');
        }

        return $identity_annex_info->save();
    }

}
