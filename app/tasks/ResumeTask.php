<?php

use FlashExpress\bi\App\library\Tools;
use FlashExpress\bi\App\Modules\PH\Server\OfferSignServer;
use FlashExpress\bi\App\library\Excel;
use FlashExpress\bi\App\Server\DownLoadTaskService;
use FlashExpress\bi\App\Models\backyard\WinhrExcelTaskModel;
use FlashExpress\bi\App\library\RedisListEnums;

/**
 * Created by PhpStorm.
 * User: zhaowei
 * Date: 2022/3/3
 * Time: 15:25
 */
class ResumeTask extends BaseTask
{

    /**
     * 清洗简历性别字段根据称呼
     * 执行一次即可，泰国、菲律宾、老挝、印尼 国家执行
     * 马来、越南 本身就有性别字段
     */
    public function reflashSexAction()
    {
        $resume_list = \FlashExpress\bi\App\Models\backyard\HrResumeModel::find([
            'conditions' => 'sex is null or sex =0',
            'columns'    => 'id,sex,call_name',
        ])->toArray();

        if (empty($resume_list)) {
            echo '没有需要处理的数据' . PHP_EOL;
            die;
        } else {
            echo '脚本启动中，共' . count($resume_list) . '条简历数据待处理.' . date("Y-m-d H:i:s") . PHP_EOL;
        }

        foreach ($resume_list as $item) {
            try {
                $this->getDI()->get('db')->updateAsDict('hr_resume',
                    ['sex' => getSex($item['call_name'], $item['sex'])], 'id = ' . $item['id']);
            } catch (Exception $e) {
                echo '简历' . $item['id'] . "处理失败，失败原因：" . $e->getMessage() . PHP_EOL;
            }
        }

        echo '脚本结束.' . date("Y-m-d H:i:s") . PHP_EOL;
    }

    /**
     * 根据面试表中hc_id刷新简历表hc_id字段
     * 执行一次即可，全国家执行
     */
    public function resumeHcIdAction()
    {
        //$sql = 'SELECT resume_id,i.hc_id FROM hr_interview as i LEFT JOIN `hr_resume` as r on i.`resume_id` =r.`id` where  i.`hc_id` != r.`hc_id` and i.hc_id > 0';
        $sql  = 'select max(`interview_id`) as interview_id  from `hr_interview` WHERE resume_id in( SELECT id from `hr_resume` WHERE `hc_id` =0) GROUP BY `resume_id`';
        $data = $this->getDI()->get('db')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        if (empty($data)) {
            echo '没有需要处理的数据' . PHP_EOL;
            die;
        } else {
            echo '脚本启动中，共' . count($data) . '条简历数据待处理.' . date("Y-m-d H:i:s") . PHP_EOL;
        }
        $interview_id_arr = array_column($data, 'interview_id');
        $interview_ids    = implode(',', $interview_id_arr);
        $sql2             = "select resume_id,hc_id from `hr_interview` WHERE interview_id in({$interview_ids})";
        $fix_data         = $this->getDI()->get('db')->query($sql2)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        if ($fix_data) {
            foreach ($fix_data as $item) {
                try {
                    $sql3 = "update hr_resume set hc_id = {$item['hc_id']},updated_at = updated_at where id = {$item['resume_id']}";
                    $this->getDI()->get('db')->execute($sql3);
                } catch (Exception $e) {
                    echo '简历' . $item['resume_id'] . "处理失败，失败原因：" . $e->getMessage() . PHP_EOL;
                }
            }
        }


        echo '脚本结束.' . date("Y-m-d H:i:s") . PHP_EOL;
    }


    /**
     * 批量导出附件v2
     *
     * todo 问题：会超时报错，需要解决后使用，目前没找到解决方案，default_socket_timeout;方式尝试了不行；
     *
     * 通过redis阻塞队列(list） 获取简历附件并发送至用户邮箱
     * 阻塞时间内，如果没数据redis会一直阻塞不执行下面代码，超时后还没有数据的话会返回空数组
     * 1小时启动一次
     */
    public function batchExportAnnexV2Action()
    {
        set_time_limit(0);

        try {
            $redis          = $this->getDI()->get('redisLib');
            $cache_list_key = "resume_annex_export_list";
            echo "任务启动，监听处理中..." . PHP_EOL;
            while (true) {
                //设置阻塞等待时间为1小时，0的话是一直阻塞，
                $result = $redis->brpop($cache_list_key, 3600);

                //返回值是一个数组,0: 队列key值,1:队列中当前弹出的数据data
                if ($result) {
                    echo "有待处理数据，开始处理" . PHP_EOL;

                    $cache_data  = json_decode($result[1], true);
                    $resume_ids  = $cache_data['resume_ids'];//要导出的简历ID串
                    $staff_id    = $cache_data['staff_id'];//要导出的简历ID串
                    $staff_email = $cache_data['staff_email'];//用户邮箱


                    $resumeServerObj = new \FlashExpress\bi\App\Server\ResumeServer();
                    //todo 获取简历相关附件数据
                    $annex_data = $resumeServerObj->getAnnexList($resume_ids);
                    if (!$annex_data) {
                        $this->getDI()->get('logger')->write_log("batchExportAnnexTask-简历附件数据为空，停止发送附件,导出人工号:{$staff_id}，导出简历id:{$resume_ids}",
                            'info');
                        echo "简历附件数据为空，停止发送附件邮件，导出人工号:{$staff_id},cvid:{$resume_ids}" . PHP_EOL;
                    }
                    //todo 打包附件并发送邮件至用户企业邮箱
                    $resumeServerObj->zipResumeAnnex2Email($annex_data, $staff_email);
                }
            }
        } catch (Exception $e) {
            $this->getDI()->get('logger')->write_log("batchExportAnnexTask导出任务异常：" . $e->getMessage() . $e->getTraceAsString());
            echo "导出任务异常：" . $e->getMessage() . $e->getTraceAsString();
        }
    }


    /**
     * 批量导出附件V1每分钟一次，非阻塞消费，每次消费一条
     *
     * 获取简历附件并发送至用户邮箱
     * 一分钟一次
     */
    public function batchExportAnnexAction()
    {
        set_time_limit(0);

        $redis          = $this->getDI()->get('redisLib');
        $cache_list_key = "resume_annex_export_list";
        $list_len       = $redis->llen($cache_list_key);
        if (empty($list_len)) {
            echo '当前无待处理数据，队列返回：' . $list_len;
            die;
        }


        while ($result = $redis->rpop($cache_list_key)) {
            //todo 导出附件并发送邮箱处理开始
            try {
                echo "开始处理，当前处理数据内容为：" . $result . PHP_EOL;

                $cache_data = json_decode($result, true);
                $resume_ids = $cache_data['resume_ids'];//要导出的简历ID串
                $staff_email = $cache_data['staff_email'];//用户邮箱
                $staff_id = $cache_data['staff_id'];//用户邮箱
                $log_perfix = "batchExportAnnexTask-当前用户($staff_id)";
                $resumeServerObj = new \FlashExpress\bi\App\Server\ResumeServer();
                $resumeServerObj->lang = $cache_data['lang']??getCountryDefaultLang();
                //todo 获取简历相关附件数据
                $annex_data = $resumeServerObj->getAnnexList($resume_ids);
                if ($annex_data) {
                    //todo 打包附件并发送邮件至用户企业邮箱
                    $resumeServerObj->zipResumeAnnex2Email($annex_data, $staff_email);
                    echo "{$log_perfix}导出数据处理成功并发送至邮箱{$staff_email},数据内容:{$result}" . PHP_EOL;
                    $this->getDI()->get('logger')->write_log("该用户{$staff_id}导出数据处理成功并发送至邮箱{$staff_email},导出参数内容:{$result}",
                        'info');
                }
                $redis->del('resume_annex_export_' . $cache_data['staff_id']);
            } catch (Exception $e) {
                $this->getDI()->get('logger')->write_log("{$log_perfix}导出数据处理失败，失败原因：" . $e->getMessage() . $e->getTraceAsString(),
                    "error");
                echo "该用户{$staff_id}导出数据处理失败，，失败原因：" . $e->getMessage() . $e->getTraceAsString();
            }
        }
    }


    // 修复菲律宾数据 只跑一次
    public function offerSignApproveAction($params)
    {
        if (isset($params[0]) && $params[0]) {
            $id = $params[0];


            $offer_data = (new \FlashExpress\bi\App\Modules\PH\Server\InterviewServer())->getOfferInfo($id);

            $pdf_type     = OfferSignServer::PDF_TYPE_1;
            $new_pdf_path = (new OfferSignServer())->fillPdfForm($pdf_type, $offer_data);
            var_dump($new_pdf_path);
        }
    }

    public function hr_interview_pass_type_recordAction()
    {
        $sql = "update hr_interview_pass_type_record SET resume_id = business_id,updated_at=updated_at WHERE business_type= 1 and deleted=0";
        $this->getDI()->get('db')->query($sql);

        $list = \FlashExpress\bi\App\Models\backyard\HrInterviewPassTypeRecordModel::find([
            'conditions' => 'business_type in (2,3) and deleted =0',
            'columns'    => 'id,business_type,business_id',
        ])->toArray();
        $i    = 0;
        foreach ($list as $v) {
            $resume_id = 0;
            // 1:resume表；2:interview_info表；3:resume_out_log表
            if ($v["business_type"] == 2) {
                $sql       = "select hr_interview.resume_id from hr_interview_info LEFT JOIN hr_interview on hr_interview.interview_id = hr_interview_info.interview_id where hr_interview_info.interview_info_id = " . $v["business_id"];
                $dataObj   = $this->getDI()->get('db_rby')->query($sql);
                $data      = $dataObj->fetch(\Phalcon\Db::FETCH_ASSOC);
                $resume_id = intval($data["resume_id"]);
                $i++;
                $sql = "update hr_interview_pass_type_record SET resume_id = " . $resume_id . ",updated_at=updated_at WHERE id = " . $v["id"];
                $this->getDI()->get('db')->execute($sql);
            } elseif ($v["business_type"] == 3) {
                $sql       = "select resume_id from resume_out_log where id = " . $v["business_id"];
                $dataObj   = $this->getDI()->get('db_rby')->query($sql);
                $data      = $dataObj->fetch(\Phalcon\Db::FETCH_ASSOC);
                $resume_id = intval($data["resume_id"]);
                $i++;
                $sql = "update hr_interview_pass_type_record SET resume_id = " . $resume_id . ",updated_at=updated_at WHERE id = " . $v["id"];
                $this->getDI()->get('db')->execute($sql);
            }

            $sql     = "select state from hr_interview where state = 30 and resume_id = " . $resume_id;
            $dataObj = $this->getDI()->get('db_rby')->query($sql);
            $data    = $dataObj->fetch(\Phalcon\Db::FETCH_ASSOC);
            if (!$data) {
                $sql = "update hr_interview_pass_type_record SET deleted = 1,updated_at=updated_at WHERE resume_id = " . $resume_id;
                $this->getDI()->get('db')->execute($sql);
            }
        }
        print_r("共操作:" . $i . "条数据");
        die;
    }

    /**
     * 调整问卷数据
     * 一次性跑500条
     * 拉翻译
     */
    public function updateQuestionAction()
    {
        set_time_limit(0);

        $resumeTask = new \FlashExpress\bi\App\Server\ResumeTaskServer();

        $page = 1;

        try {
            while (true) {
                $info = $resumeTask->getSurveyQuestionList($page);


                if (empty($info)) {
                    echo 'success';
                    exit;
                }

                foreach ($info as $v) {
                    if (empty($v['question_text']) || strpos($v['question_text'], "k2") !== false) {
                        continue;
                    }

                    $questionTextArr = explode('#', $v['question_text']);

                    $k1 = $questionTextArr[0] ?? '';
                    $k2 = $questionTextArr[1] ?? '';

                    $questionArr = [];
                    if ($k1) {
                        $questionArr['k1'] = $k1;
                    }

                    if ($k2) {
                        $questionArr['k2'] = $k2;
                    }

                    $questionText = !empty($questionArr) ? json_encode($questionArr, JSON_UNESCAPED_UNICODE) : '';

                    $this->db->updateAsDict(
                        'hr_survey_question',
                        [
                            'question_text' => $questionText,
                        ],
                        'id = ' . $v['id']
                    );
                }

                echo 'sueess-all';
                exit;
                ++$page;
                $this->logger->write_log('updateQuestion  数据处理中' . '---- ' . json_encode($info), 'info');
            }
        } catch (Exception $exception) {
            $this->logger->write_log('updateQuestion' . '---- ' . json_encode($exception->getMessage()), 'error');
        }

        echo 'ALL ---- SUCCESS';
    }

    public function fixComAction()
    {
        $create_time = date('Y-m-d H:i:s',strtotime('-30 days'));
        $sql = "SELECT resume_id FROM communication_resume_log where create_time >= '{$create_time}'  group by resume_id";
        $fix_data = $this->getDI()->get('db')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        if ($fix_data) {
            foreach ($fix_data as $item) {
                $sql = "update communication_resume_log SET newest = 0 WHERE resume_id = " . $item['resume_id'] ;
                $res = $this->getDI()->get('db')->execute($sql);

                $sql = "update communication_resume_log SET newest = 1 WHERE resume_id = " . $item['resume_id'] . ' order by  id desc limit 1';
                $res = $this->getDI()->get('db')->execute($sql);
            }
        }
    }


    /**
     * 简历列表导出 公共
     * @Access  public
     * @Param   request
     * @return void
     * @throws ReflectionException
     * @throws \FlashExpress\bi\App\library\Exception\BusinessException
     */
    public function exportResumeListAction($input)
    {
        //获取任务参数
        $input  = !isset($input[0]) ? (new DownLoadTaskService())->getExcelTaskArgv() : $input;
        $params = isset($input[0]) ? json_decode(base64_decode($input[0]), true) : [];
        $taskId = $input[1] ?? '';
        $this->info('ExportResumeListAction start ' . ' params :' . json_encode($params,
                JSON_UNESCAPED_UNICODE) . ' taskId=>' . $taskId);
        $this->lang = $params['lang'];
        $excelTask  = WinhrExcelTaskModel::findFirst($taskId);
        if (!$excelTask) {
            throw new \Exception('没有找到相关记录!');
        }
        $excelTask              = $excelTask->toArray();
        $file_name              = $excelTask['file_name'];
        $ResumeServer = Tools::reBuildCountryInstance(new \FlashExpress\bi\App\Server\ResumeServer($this->lang));
        $ResumeServer->lang     = $this->lang;
        $ResumeServer->userInfo = $params['userinfo'];

        //[2]业务处理
        $params['staff_id'] = $params['userinfo']['id'];
        $params['page_num'] = 1;
        $params['no_page'] = false;
        $params['page_size'] = 3000;
        $_data = [];
        $i = 0;
        while (true) {
            try {
                $returnArr           = $ResumeServer->resumeList($params);
                $data                = $returnArr['data']['dataList'] ?? [];
                if (empty($data)) {
                    break;
                }
                foreach ($data as $k => $v) {
                    $id                        = $v['id'] ?? '';
                    $name                      = $v['name'] ?? '';
                    $nameEn                    = $v['name_en'] ?? '';
                    $sex                       = $v['sex_title'] ?? '';
                    $phone                     = $v['phone'] ?? '';
                    $email                     = $v['email'] ?? '';
                    $jobName                   = $v['job_name'] ?? '';
                    $alternativeJobNames       = $v['alternative_job_names'] ?? '';
                    $residence_province_city   = $v['residence_province_city'] ?? '';     //居住地城市
                    $work_province_city        = $v['work_province_city'] ?? '';          //$addressName    = $v['address_name'] ?? '';   //期望城市
                    $entrySalary               = $v['entry_salary'] > 0 ? bcdiv($v['entry_salary'], 100, 0) : '';
                    $dateBirth                 = $v['date_birth'] ?? '';
                    $credentialsNum            = $v['credentials_num'] ?? '';
                    $education                 = $v['education_name'] ?? '';
                    $updateTime                = $v['updated_at'] ?? '';
                    $status                    = $v['state_name'] ?? '';
                    $submitterId               = $v['submitter_id'] && in_array($v['source'], [2, 3]) ? $v['submitter_id'] : '';
                    $hcId                      = $v['hc_id'] ?? '';
                    $recruit_channel_name      = $v['recruit_channel_name'] ?? '';
                    $source_name               = $v['source_name'] ?? '';
                    $resume_last_operator_name = $v['resume_last_operator_name'] ?? '';
                    $hire_type_text            = $v['hire_type_text'] ?? '';
                    $reserve_type_text         = $v['reserve_type_text'] ?? '';
                    $delivery_time             = $v['delivery_time'] ?? '';
                    $ai_score                  = $v['ai_score'] ?? '';
                    $job_title_name            = $v['job_title_name'] ?? '';

                    $_data[$i]               = [
                        $id,
                        $hcId,
                        $job_title_name,
                        $jobName,
                        $ai_score,
                        $hire_type_text,
                        $name,
                        $nameEn,
                        $sex,
                        $phone,
                        $email,
                        $alternativeJobNames,
                        $residence_province_city,   //居住所在地
                        $work_province_city,    //$addressName, //期望城市
                        $entrySalary,
                        $dateBirth,
                        $credentialsNum,
                        $education,
                        $updateTime,
                        $status,
                        $submitterId,
                        $recruit_channel_name,
                        $source_name,
                    ];
                    if (isCountry('MY')) {
                        $_data[$i][] = $reserve_type_text;
                    }

                    if (isCountry('PH') || isCountry('MY')) {
                        $_data[$i][] = $resume_last_operator_name;
                    }

                    //投递时间
                    $_data[$i][] = $delivery_time;

                    $i++;
                }
            } catch (Exception $e) {
                break;
            }
            $params['page_num']++;
        }

        $field = [
            'CV ID',
            'HC ID',
            $this->getTranslation()->_('7044'),                       //职位
            $this->getTranslation()->_('8051'),                       //期望岗位
            $this->getTranslation()->_('resume_ai_score_type'),       //简历评估分数
            $this->getTranslation()->_('hire_type'),                  //雇佣类型
            $this->getTranslation()->_('7022'),                       //姓名
            $this->getTranslation()->_('7024'),                       //姓名
            $this->getTranslation()->_('7023'),                       //性别
            $this->getTranslation()->_('7505'),                       //手机号
            $this->getTranslation()->_('8050'),                       //电子邮箱
            $this->getTranslation()->_('8049'),                       //备选岗位
            $this->getTranslation()->_('8068'),                       //居住所在地
            $this->getTranslation()->_('8052'),                       //期望城市
            $this->getTranslation()->_('8053'),                       //期望薪资
            $this->getTranslation()->_('8054'),                       //出生日期
            $this->getTranslation()->_('7503'),                       //证件号
            $this->getTranslation()->_('8056'),                       //最高学历
            $this->getTranslation()->_('8057'),                       //最后更新时间
            $this->getTranslation()->_('7008'),                       //状态
            $this->getTranslation()->_('8116'),                       //简历创建人
            $this->getTranslation()->_('42003'),                      //招聘渠道
            $this->getTranslation()->_('resume_source_text'),         //简历来源
        ];

        if (isCountry('MY')) {
            $field[] = $this->getTranslation()->_('reserve_type');//简历类型
        }

        if (isCountry('PH') || isCountry('MY')) {
            $field[] = $this->getTranslation()->_('principal_staff_name');  //最新操作人
        }

        $field[] = $this->getTranslation()->_('delivery_time_text');

        $file_path = Excel::exportExcel($field, $_data, $file_name);
        $fileUrl   = $file_path ? $file_path['object_url'] : '';
        $res       = (new DownLoadTaskService($this->lang))->updateTask($taskId, $fileUrl);
        $this->info('ExportResumeListAction end ' . $res, true);
    }

    /**
     * 复制简历
     */
    public function copyAction()
    {
        $redis  = $this->getDI()->get('redisLib');
        $resumeBatchImportServer = new \FlashExpress\bi\App\Server\ResumeBatchImportServer();

        while ($params = $redis->rpop(RedisListEnums::AFTER_BATCH_IMPORT_COPY_FLAG)) {
            try {
                $params = json_decode($params,true);
                $this->getDI()->get('logger')->write_log(['popResumeCopyRedisList' => $params], 'info');

                if (!$resumeBatchImportServer->copyResumeInfo($params)) {
                    throw new Exception('copy resume Error');
                }
            } catch (Exception $exception) {
                $this->error('popResumeCopyRedisList end ' . $exception->getMessage(), true);
            }
        }

        echo 'SUCCESS';
    }

    /**
     * 一次性 刷入职表 雇佣类型数据
     */
    public function entry_hire_typeAction()
    {
        echo 'start' . PHP_EOL;

        try {
            $num = ((new \FlashExpress\bi\App\Server\ResumeTaskServer())->entry_hire_type());
            echo $num ."\n";
        } catch (\FlashExpress\bi\App\library\Exception\ValidationException $e) {
            echo $e->getMessage();
        }

        echo 'end';
    }
}