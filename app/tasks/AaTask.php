<?php


use FlashExpress\bi\App\library\RocketMQ;
use FlashExpress\bi\App\Models\backyard\HrInterviewerOperationModel;
use FlashExpress\bi\App\Repository\SysListRepository;
use FlashExpress\bi\App\Repository\SysStoreRepository;
use FlashExpress\bi\App\Server\ResumeBatchImportServer;

class AaTask extends BaseTask
{

    public function ccAction($params)
    {
        $ope_id = 2;
        // 3 修改面试官记录表
        $interviewer_operation = HrInterviewerOperationModel::findFirst([
            'conditions' => 'id = :id:',
            'bind'       => ['id' => $ope_id],
        ]);
        if ($interviewer_operation) {
            $interviewer_operation->status = HrInterviewerOperationModel::STATE_CANCEL_OPERATION;
            dd($interviewer_operation->save());
        }

        $s  = new \FlashExpress\bi\App\Server\HcHireTypeConfigServer($this->lang);
        $a = $s->getConfigHireTypesIds(110,'TH01010101');
        dd($a);
    }
    public function t1Action()
    {
        $store_list = (new SysStoreRepository())->getStoreList([
            'id',
            'name',
            'district_code',
            'city_code',
            'province_code',
        ], ['state = :state:'], ['state' => 1]);

        $server = new ResumeBatchImportServer('zh');
        $re = $server->validateStore(['name'=>'test新建网点11'],$store_list,'MY01','MY0101','MY010101');
        dd($re);
    }

    public function t2Action(){
        //加入人脸黑名单
        $identity = '111ss1';
        $mobile = 'dddddd';
        $submitter_staff_id = '1111';
        $data = [
            'business_type'      => 'add_face_blacklist',
            'identity'           => $identity,
            'mobile'             => $mobile,
            'submitter_staff_id' => $submitter_staff_id,
        ];
        $rmq  = new RocketMQ('face-blacklist');
        $rmq->setShardingKey($identity);
        $rid  = $rmq->sendOrderlyMsg($data);

        dd($rid);
    }

}

