<?php

namespace FlashExpress\bi\App\Modules\PH\Server;

use FlashExpress\bi\App\Helper\InterviewHelper;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\HrhcModel;
use FlashExpress\bi\App\Models\backyard\HrResumeExtendModel;
use FlashExpress\bi\App\Models\backyard\OfferSendSalaryModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\fle\StaffInfoModel;
use FlashExpress\bi\App\Repository\CustomerresumeRepository;
use FlashExpress\bi\App\Repository\H5appointRepository;
use FlashExpress\bi\App\Repository\SysManagePieceRepository;
use FlashExpress\bi\App\Repository\SysManageRegionRepository;
use FlashExpress\bi\App\Server\HcServer;
use FlashExpress\bi\App\Server\OfferSignServer;
use FlashExpress\bi\App\Server\OutsourcingBlackListServer;
use FlashExpress\bi\App\Server\ResumeServer;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\Models\backyard\HrInterviewerOperationModel;
use FlashExpress\bi\App\Models\backyard\HrResumeModel;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\Repository\SysListRepository;
use FlashExpress\bi\App\Server\SettingEnvServer;
use FlashExpress\bi\App\Server\StaffServer;
use FlashExpress\bi\App\Server\SysListServer;
use FlashExpress\bi\App\Server\InterviewServer as BaseServer;
use FlashExpress\bi\App\Repository\BlacklistRepository;
use FlashExpress\bi\App\Repository\TrainRepository;
use FlashExpress\bi\App\Repository\PermissionsRepository;
use FlashExpress\bi\App\Repository\ResumeRepository;
use FlashExpress\bi\App\Repository\InterviewRepository;
use FlashExpress\bi\App\Repository\OfferRepository;
use FlashExpress\bi\App\Repository\EntryRepository;
use  FlashExpress\bi\App\Repository\StaffRepository;
use  FlashExpress\bi\App\Repository\HcRepository;


class InterviewServer extends BaseServer
{
    public function __construct($lang = 'en')
    {
        parent::__construct($lang);

    }

    public function sendOffer($paramIn = [])
    {
        $this->sysList      = new SysListRepository();
        $this->resume       = new ResumeRepository();
        $this->offer        = new OfferRepository();
        $this->hc           = new HcRepository();
        $this->log          = new LogServer();
        $this->train   = new TrainRepository();

        //[1]参数定义
        $currencyId = $this->processingDefault($paramIn, 'currency', 2, 1);
        $other      = $this->processingDefault($paramIn, 'other', 1); // 备注

        $staff_job_type = $this->processingDefault($paramIn, 'staff_job_type', 2);
        $hire_type = $this->processingDefault($paramIn, 'hire_type', 2);
        $exp            = $this->processingDefault($paramIn, 'exp', 2);
        $food           = $this->processingDefault($paramIn, 'food', 2);
        $position       = $this->processingDefault($paramIn, 'position', 2);
        $rental         = $this->processingDefault($paramIn, 'rental', 2);
        $fuel           = $this->processingDefault($paramIn, 'fuel', 2);
        $computer       = $this->processingDefault($paramIn, 'computer', 2);
        $renting        = $this->processingDefault($paramIn, 'renting', 2);
        $submitterId = $this->processingDefault($paramIn, 'staff_id', 2);
        $dangerously    = $this->processingDefault($paramIn, 'dangerously', 2);
        $is_blacklist   = $this->processingDefault($paramIn, 'is_balcklist', 2);
        $position_id    = $this->processingDefault($paramIn, 'position_id', 2);

        $in_salary_range = $this->processingDefault($paramIn, 'in_salary_range', 2);

        $work_time      = $paramIn['work_time'];
        $train_time     = $paramIn['train_time']??'';
        $train_place    = $paramIn['train_place']??'';
        if (isset($paramIn['job_title_grade']) && $paramIn['job_title_grade'] != null) {
            $job_title_grade = $paramIn['job_title_grade'];//职级字段
        }
        $gasoline_allowance = $this->processingDefault($paramIn, 'gasoline_allowance', 2);
        $island_allowance = $this->processingDefault($paramIn, 'island_allowance', 2);
        $performance_allowance = $this->processingDefault($paramIn, 'performance_allowance', 2);
        $deminimis_benefits = $this->processingDefault($paramIn, 'deminimis_benefits', 2);
        $other_non_taxable_allowance = $this->processingDefault($paramIn, 'other_non_taxable_allowance', 2);
        $recommended = $this->processingDefault($paramIn, 'recommended', 2);
        $shift_id       = $this->processingDefault($paramIn, 'shift_id', 2);
        $train_switch = SettingEnvServer::getSetVal('offer_train_switch');
        $is_train = true;
        if (1 == $train_switch) {
            $is_train = false;
        }
        if(!$is_train){//非培训下校验到岗时间和当前时间对比
            if (date("Y-m-d", strtotime($paramIn['work_time'])) < date("Y-m-d", time()) || empty($paramIn['work_time'])) {
                return $this->checkReturn(-3, $this->getTranslation()->_('4428'));
            }
        }

        if($is_train){
            //判断培训时间是否大于到岗时间
            if (strtotime(date("Y-m-d", strtotime($paramIn['train_time']))) > strtotime(date("Y-m-d", strtotime($paramIn['work_time']))) || empty($paramIn['work_time'])) {
                return $this->checkReturn(-3, $this->getTranslation()->_('err_msg_1'));
            }

            //判断培训时间是否早于当前时间
            if (date("Y-m-d", strtotime($paramIn['train_time'])) < date("Y-m-d", time())) {
                return $this->checkReturn(-3, $this->getTranslation()->_('err_msg_2'));
            }

            //校验培训
            $trainData = (new TrainRepository())->checkTrain([
                "resume_ids" => $paramIn['resume_id'],
            ]);

            //如果已经存在培训了，未完成培训的，不可以重新发送offer
            if ($trainData && !in_array($trainData[0]["status"], [
                    enums::$train_status['train_training_passed'],
                    enums::$train_status['train_do_not_pass']
                ])) {
                return $this->checkReturn(-3, $this->getTranslation()->_('err_msg_training_status_error'));
            }
        }

        //校验简历是否完善
        $interviewSql  = "select hr_resume.is_perfect,hr_resume.hc_id,hr_resume.credentials_num,hr_resume.date_birth from hr_interview LEFT JOIN hr_resume on hr_interview.resume_id = hr_resume.id where hr_interview.interview_id=" . $paramIn['interview_id'];
        $data          = $this->getDI()->get('db_rby')->query($interviewSql);
        $interviewData = $data->fetch(\Phalcon\Db::FETCH_ASSOC);
        if (!$interviewData || $interviewData['is_perfect'] == 2) {
            return $this->checkReturn(-3, $this->getTranslation()->_('6119'));
        }

        //增加校验
        if (
            !empty($interviewData['hc_id'])
            &&
            !empty($paramIn['hc_id'])
            &&
            $interviewData['hc_id'] != $paramIn['hc_id'])
        {
            throw new ValidationException($this->getTranslation()->_('no_server'));
        }

        //校验生日
        if ((strtotime("-18 years",time())<strtotime($interviewData['date_birth']))){
            return $this->checkReturn(-3, $this->getTranslation()->_('8810'));
        }

        //校验黑名单
        $isBalcklist = (new BlacklistRepository())->blacklistCheck([
            "identity" => $interviewData['credentials_num'],
        ]);
        if ($isBalcklist > 0) {
            return $this->checkReturn(-3, $this->getTranslation()->_('7501'));
        }
        (new OutsourcingBlackListServer())->check($interviewData['credentials_num'] ?? '','winhr',true,$this->lang);

        //获取offer指定职位ids有设备费 需求：当入职职位为“Bike Courier”或“Van Courier”时，才展示需扣除设备费字段，其余职位不体现
//        $equipmentCostPositionIds    = env("equipmentCostPositionIds", "{}");
//        $equipmentCostPositionIdsArr = json_decode($equipmentCostPositionIds, true);
//        if (!$position_id || !in_array($position_id, array_keys($equipmentCostPositionIdsArr))) {
//            $equipment_cost = 0;
//        } else {
//            $equipment_cost = $equipmentCostPositionIdsArr[$position_id];
//        }
//
//        if ($hire_type == HrStaffInfoModel::HIRE_TYPE_AGENT){
//            // 个人代理强转0
//            $equipment_cost = 0;
//        }

        //校验班次id是否正确
        /*
        $shift_list = (new SysListRepository())->getAllShiftList();
        $shift_list = array_column($shift_list, null, 'id');
        if(!isset($shift_list[$shift_id])) {
            return $this->checkReturn(-3, 'shift ID error');
        }
        */

        //[2]校验是否存在
        $param = [
            'interview_id'   => $paramIn['interview_id'],
            'resume_id'      => $paramIn['resume_id'],
            'hc_id'          => $paramIn['hc_id'],
            'currency'       => $currencyId,
            'money'          => $paramIn['money'],
            'trial_salary'          => isset($paramIn['trial_salary']) ? $paramIn['trial_salary'] : 0,
            'in_salary_range'=> $in_salary_range,
            'position_id'    => $position_id,
            'job_title_grade' => $job_title_grade, //新增职级字段
            'other'          => $other,
            'work_time'      => $work_time,
            // 'train_time'     => $train_time,
            'staff_job_type' => $staff_job_type,
            'hire_type'      => $hire_type,
            'exp'            => $exp,
            'food'           => $food,
            'position'       => $position,
            'rental'         => $rental,
            'fuel'           => $fuel,
            'computer'       => $computer,
            'renting'        => $renting,
            'dangerously'    => $dangerously,
//            'equipment_cost' => $equipment_cost,
            'submitter_id' => $submitterId,
            'status'         => 1,
            'shift_id'       => $shift_id,
            'gasoline_allowance' => $gasoline_allowance,
            'island_allowance' => $island_allowance,
            'performance_allowance' => $performance_allowance,
            'deminimis_benefits' => $deminimis_benefits,
            'other_non_taxable_allowance' => $other_non_taxable_allowance,
            'recommended' => $recommended,
        ];
        if($is_train){
            $param['train_time'] = $train_time;
        }
        $dbcon = $this->getDI()->get('db');
        $dbcon->begin();
        try {
            //TH需要薪资审批
            //PH暂时不需要
            $resume   = HrResumeModel::findFirst([
                'conditions' => 'id = :id:',
                'bind'       => [
                    'id' => $paramIn['resume_id'],
                ]
            ]);
            $resume = $resume ? $resume->toArray() : [];
            /* 校验hc */
            $hcData = $this->hc->checkHc(['hc_id' => $paramIn['hc_id'],'for_update'=>true]);
            if ($hcData['state_code'] == 3 || $hcData['surplusnumber'] == 0) {
                return $this->checkReturn(-3, $this->getTranslation()->_('11027'));
            }
            if ($hcData['state_code'] == 4) {
                return $this->checkReturn(-3, $this->getTranslation()->_('11028'));
            }
            if ($hcData['state_code'] == 9) {
                return $this->checkReturn(-3, $this->getTranslation()->_('11031'));
            }
            if ($resume && !in_array($hcData["hire_type"],[HrStaffInfoModel::HIRE_TYPE_AGENT])) {
                $params = [
                    'userinfo' => $this->userInfo,
                    'resume_id' => $paramIn['resume_id'],
                    'job_id' => $position_id,
                    'money' => $paramIn['money'] / 100,
                    'trial_salary' => isset($paramIn['trial_salary']) ? $paramIn['trial_salary'] / 100 : 0,
                    'renting' => isset($paramIn['renting']) ? $paramIn['renting'] / 100 : 0,
                    'in_salary_range' => $paramIn['in_salary_range'] ??  0,
                ];
                $this->getDI()->get("logger")->write_log("sendOffer check " . json_encode($params, JSON_UNESCAPED_UNICODE), "info");
                $apiClient = (new ApiClient('by_rpc', '', 'salary_can_approve', $this->lang));
                $apiClient->setParams($params);
                $result = $apiClient->execute();
                $this->getDI()->get("logger")->write_log("sendOffer check " . json_encode($result, JSON_UNESCAPED_UNICODE), "info");
                if ($result['result'] && $result['result']['code'] != 1) {
                    return $this->checkReturn(-3, $result['result']['msg']);
                }
            }

            /* 校验此offer是否存在 */
            $interviewOfferData = $this->offer->checkOffer([
                'interview_id' => $paramIn['interview_id'],
                'status' => 1,
            ]);
            if ($interviewOfferData) {
                return $this->checkReturn(-3, $this->getTranslation()->_('4424'));
            }

            if ($hcData['state_code'] == 4) {
                return $this->checkReturn(-3, $this->getTranslation()->_('4021'));
            }
            /* 执行插入操作 */
            $success = $dbcon->insertAsDict('hr_interview_offer', $param);
            if (!$success) {
                return $this->checkReturn(-3, $this->getTranslation()->_('4701'));
            }
            $interview_offer_id = $dbcon->lastInsertId();

            /* 修改面试主表状态 */
            $add_hour = $this->getDI()['config']['application']['add_hour'];
            $send_time    = gmdate('Y-m-d H:i:s', time());
            $flag = self::updateInterviewStatus([
                'id'    => $paramIn['interview_id'],
                'state' => 25,
                'send_time' => $send_time,
            ]);
            if (!$flag) {
                $dbcon->rollback();
                return $this->checkReturn(-3, $this->getTranslation()->_('4701'));
            }
            if(!$is_train){
                $entrySql  = "--
                        select * from hr_entry 
                        where deleted = 0 and resume_id = " . $paramIn['resume_id'];
                $data      = $this->getDI()->get('db_rby')->query($entrySql);
                $entryData = $data->fetch(\Phalcon\Db::FETCH_ASSOC);
                $this->getDI()->get('logger')->write_log("access training resumeID:" . $paramIn['resume_id'], 'info');
                if (empty($entryData)) {
                    /* 添加入职信息 */
                    $paramEntry = [
                        'interview_id'       => $paramIn['interview_id'],
                        'interview_offer_id' =>  $interview_offer_id,
                        'resume_id'          => $paramIn['resume_id'],
                        'hc_id'              => $paramIn['hc_id'],
                        'hire_type'          => $hcData['hire_type'],
                    ];
                    $this->getDI()->get('logger')->write_log("access training resumeID:" . $trainData['resume_id'] . "add entry data:" . json_encode($paramEntry), 'info');
                    $this->getDI()->get('db')->insertAsDict(
                        'hr_entry', $paramEntry
                    );
                    if ($this->getDI()->get('db')->affectedRows() < 1) {
                        throw new BusinessException($this->getTranslation()->_('4008'), enums::$ERROR_CODE['1000']);
                    }
                    $entryData['entry_id'] = $this->getDI()->get('db')->lastInsertId();
                } else {
                    $this->getDI()->get('logger')->write_log("access training resumeID and update:" . $trainData['resume_id'], 'info');

                    //更新入职信息
                    //将已经存在的入职日期、入职记录字段清空
                    $this->getDI()->get('db')->updateAsDict(
                        'hr_entry',
                        [
                            'status'       => 2,
                            'record_entry' => null,
                            'entry_date'   => '0000-00-00 00:00:00',
                            'interview_id'       => $paramIn['interview_id'],
                            'interview_offer_id' => $interview_offer_id,
                            'hc_id'              => $paramIn['hc_id'],
                            'hire_type' => $hcData['hire_type'],
                        ],
                        "entry_id = " . $entryData['entry_id']
                    );

                    if ($this->getDI()->get('db')->affectedRows() < 1) {
                        throw new BusinessException($this->getTranslation()->_('4008'), enums::$ERROR_CODE['1000']);
                    }
                }

                //如果是buddy的简历同步给buddy
//                if(env('country_code','TH')=='TH'){
//                    (new EntryRepository())->syncEntryToBuddy($entryData['entry_id'],2);
//                }
            }


            /* 修改hc剩余数-1 */
            if ($interview_offer_id) {
                if ($hcData['surplusnumber'] == 1) {
                    /* 修改hc表状态 改为已完成 */
                    $hcSql = "update hr_hc set surplusnumber=surplusnumber-1,state_code=3 where hc_id = " . $paramIn['hc_id'];
                    $dbcon->query($hcSql);
                } elseif ($hcData['surplusnumber'] > $hcData['demandnumber']) {
                    $hcSql = "update hr_hc set surplusnumber=demandnumber-1 where hc_id = " . $paramIn['hc_id'];
                    $dbcon->query($hcSql);
                } else {
                    $hcSql = "update hr_hc set surplusnumber=surplusnumber-1 where hc_id = " . $paramIn['hc_id'];
                    $dbcon->query($hcSql);
                }
                /* 改简历状态 25=已发offer */
                $send_time    = gmdate('Y-m-d H:i:s', time());
                $interviewSql = "update hr_interview set state=25,send_time = '{$send_time}'  where interview_id = " . $paramIn['interview_id'];
                $dbcon->query($interviewSql);
            }
            if($is_train){
                if ($trainData) {
                    $this->getDI()->get('db')->updateAsDict(
                        'hr_training',
                        [
                            'status'      => 1,
                            'hc_id'       => $paramIn['hc_id'],
                            'train_date'  => $train_time,
                            'train_place' => $train_place,
                        ],
                        "train_id = " . $trainData[0]["train_id"]
                    );
                } else {
                    //添加培训
                    $addTrainData = [
                        'resume_id'   => $paramIn['resume_id'],
                        'hc_id'       => $paramIn['hc_id'],
                        'train_date'  => $train_time,
                        'train_place' => $train_place,
                        'status'      => 1,
                    ];
                    $trainId      = $this->train->addTrain($addTrainData);
                    if ($trainId === false) {
                        $dbcon->rollback();
                        $this->getDI()->get('logger')->write_log('添加培训失败:' . json_encode($paramIn), 'notice');
                        return $this->checkReturn(-3, $this->getTranslation()->_('4008'));
                    }
                }
            }

            //记录薪资日志
            (new OfferServer())->addOfferSalaryOperatorLog([
                'interview_offer_id' => $interview_offer_id,
                'money'=> $param
            ]);

            //添加日志
            $this->log->addLog([
                'module_id'   => $paramIn['interview_id'],
                'module_type' => enums::$log_module_type['offer'],
                'action'      => enums::$log_option['send'],
                'status'      => enums::$log_status['to_be_confirmed'],
                'data_after'  => $param,
            ]);
            (new ResumeServer())->setLastOperator($paramIn['resume_id']);
            // 创建保存点
            $dbcon->commit();

            //添加hc记录
            (new HcServer())->sync_hc($paramIn['hc_id']);
        } catch (\Exception $e) {
            // 发生错误，回滚操作
            $dbcon->rollback();
            $this->getDI()->get('logger')->write_log('发送offer失败:' . $e->getMessage() . $e->getTraceAsString(), 'notice');
            return $this->checkReturn(-3, $this->getTranslation()->_('4008'));
        }

        return $this->checkReturn(['data'=>['offer_id'=>$interview_offer_id]]);
    }



    /**
     * hr_interview 状态更新
     * @param array $paramIn ['id'=>4,'state'=>25]
     * @return array
     */
    public function updateInterviewStatus($paramIn = [])
    {
        $this->interview    = new InterviewRepository();

        //[1]参数定义
        $Id = $this->processingDefault($paramIn, 'id', 2);

        //[2]校验是否存在
        $update_param       = [
            'state'      => $paramIn['state'],
            'updated_at' => gmdate('Y-m-d H:i:s', time())
        ];

        if(isset($paramIn['send_time']) && $paramIn['state']==25) {
            $update_param['send_time'] = $paramIn['send_time'];
        }

        $validations = [
            'state' => "Required|IntIn:1,5,10,20,25,30,31,32",
        ];
        $this->validateCheck($update_param, $validations);

        $success = $this->getDI()->get('db')->updateAsDict('hr_interview', $update_param, [
            'conditions' => " interview_id = $Id ",
        ]);

        $this->interview->sendMsgToBuddy($Id,$paramIn['state']);

        if (!$success) {
            return $this->checkReturn(-3, $this->getTranslation()->_('4701'));
        }
        return $this->checkReturn(1);
    }



    /**
     * 获取简历列表
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function interviewList($paramIn = [], $userinfo)
    {
        $this->sysList      = new SysListRepository();
        $this->entry        = new EntryRepository();
        $this->interview    = new InterviewRepository($userinfo);
        $this->staff           = new StaffRepository();

        //参数
        $phone                             = $this->processingDefault($paramIn, 'phone', 1);
        $job_name                          = $this->processingDefault($paramIn, 'job_name', 1);
        $hc_job_name                       = $this->processingDefault($paramIn, 'hc_job_name', 1);
        $department_id                     = $this->processingDefault($paramIn, 'department_id', 2);
        $hc_department_id                  = $this->processingDefault($paramIn, 'hc_department_id', 2);
        $province_name                     = $this->processingDefault($paramIn, 'province_id', 1);
        $work_city_id                      = $this->processingDefault($paramIn, 'work_city_id', 1);
        $state                             = $this->processingDefault($paramIn, 'state', 2);
        $updated_start                     = $this->processingDefault($paramIn, 'updated_start', 2);
        $updated_end                       = $this->processingDefault($paramIn, 'updated_end', 2);
        $delivery_time_start               = $this->processingDefault($paramIn, 'delivery_time_start', 2);
        $delivery_time_end                 = $this->processingDefault($paramIn, 'delivery_time_end', 2);
        $ai_score_type                     = $this->processingDefault($paramIn, 'ai_score_type', 3);
        $created_start                     = $this->processingDefault($paramIn, 'created_start', 2);
        $created_end                       = $this->processingDefault($paramIn, 'created_end', 2);
        $interview_id                      = $this->processingDefault($paramIn, 'interview_id', '');
        $store_id                          = $this->processingDefault($paramIn, 'store_id');
        $resume_store_id                   = $this->processingDefault($paramIn, 'resume_store_id');
        $hc_id                             = $this->processingDefault($paramIn, 'hc_id', 2);
        $cvId                              = $this->processingDefault($paramIn, 'cv_id', 2);
        $is_weed_out                       = $this->processingDefault($paramIn, 'is_weed_out', 2);
        $alternative_job_id                = $this->processingDefault($paramIn, 'alternative_job_id', 2);
        $approvalStatus                    = $this->processingDefault($paramIn, 'approval_status',
            2); // 审批状态 1 待审批 2 已同意 3 已驳回 4 已撤销
        $send_time_start                   = $this->processingDefault($paramIn, 'send_time_start');
        $send_time_end                     = $this->processingDefault($paramIn, 'send_time_end');
        $noPage                            = $this->processingDefault($paramIn, 'no_page', 4, false);
        $pageNum                           = $this->processingDefault($paramIn, 'page_num', 2, 1);
        $pageSize                          = $this->processingDefault($paramIn, 'page_size', 2, 10);
        $expected_arrivaltime_left         = $this->processingDefault($paramIn, 'expected_arrivaltime_left');
        $expected_arrivaltime_right        = $this->processingDefault($paramIn, 'expected_arrivaltime_right');
        $jobTitle                          = $this->processingDefault($paramIn, 'job_title_id', 2);
        $is_feedback                       = $this->processingDefault($paramIn, 'is_feedback',
            2);                                                                                             //面试结果 1:未取消 1：已取消 3：面试完成
        $resume_source                     = $this->processingDefault($paramIn, 'source',
            2);                                                                                             //简历来源 1=h5，2=whr，4-by推荐
        $resume_approve_state              = $this->processingDefault($paramIn, 'resume_approve_state',
            2);                                                                                             //by简历审核状态 1-待审核，2-审核通过，3-审核驳回
        $recommender_staff_id              = $this->processingDefault($paramIn, 'recommender_staff_id', 2); //by简历推荐人工号
        $entry_status                      = $this->processingDefault($paramIn, 'entry_status', 2);         //入职状态筛选
        $entry_staff_id                    = $this->processingDefault($paramIn, 'entry_staff_id', 2);       //入职工号筛选
        $process_status                    = $this->processingDefault($paramIn, 'process_status', 2, 0);
        $reason                            = $this->processingDefault($paramIn, 'reason', 2, 0);
        $interview_communication_status    = $this->processingDefault($paramIn, 'interview_communication_status',
            2);                                                                                             //面试管理-沟通状态下拉code
        $communication_failure_reason_type = $this->processingDefault($paramIn, 'communication_failure_reason_type',
            2);                                                                                             //面试管理-沟通失败原因下拉code,1=联系不上、2=要求岗位或网点没有hc、3=不符合岗位要求、4=候选人已拒绝、5=其他
        $last_operator_query_type          = $this->processingDefault($paramIn, 'last_operator_query_type');  // 简历最新操作人筛选类型
        $resume_last_operator              = $this->processingDefault($paramIn, 'resume_last_operator');    // 简历最新操作人
        $is_relation_hc                    = $this->processingDefault($paramIn, 'is_relation_hc');          // 是否关联hc
        $priority_ids                      = $this->processingDefault($paramIn, 'priority_ids', 3);         // hc优先级
        $credentials_num                   = $this->processingDefault($paramIn, 'credentials_num');         // 身份证号
        $manage_region                     = $this->processingDefault($paramIn, 'manage_region', 3);        //大区
        $manage_piece                      = $this->processingDefault($paramIn, 'manage_piece', 3);         //片区
        $hire_type                         = $this->processingDefault($paramIn, 'hire_type',3);    //雇佣类型
        $recruit_channel                   = $this->processingDefault($paramIn, 'recruit_channel',2);    //招聘渠道
        $pageSize                          = !$noPage ? $pageSize : 50000;
        $pageNum                           = !$noPage ? $pageNum : 1;

        //判断是否为超管  不是超管使用权限  是超管无限制
        $is_admin = $userinfo['is_admin'];   //1是超管
        $authority_stores_ids=$userinfo['permission_stores_ids'];   //网点权限
        $authority_department_ids = $userinfo['permission_department_ids'];   //部门权限
        $is_menu_permission = SettingEnvServer::getMenuPermission('whr_interview_menu_list');

        $resumeIds = [];

        //获取业务数据
        $resumeData = $this->interview->getInterviewResume([
            "phone"                             => $phone,
            "job_name"                          => $job_name,
            "hc_job_name"                       => $hc_job_name,
            "province_name"                     => $province_name,
            "work_city_id"                      => $work_city_id,
            "state"                             => $state,
            "updated_start"                     => $updated_start,
            "updated_end"                       => $updated_end,
            "delivery_time_start"               => $delivery_time_start,
            "delivery_time_end"                 => $delivery_time_end,
            "ai_score_type"                     => $ai_score_type,
            "created_start"                     => $created_start,
            "created_end"                       => $created_end,
            "department_id"                     => $department_id,
            "hc_department_id"                  => $hc_department_id,
            "interview_id"                      => $interview_id,
            "store_id"                          => $store_id,
            "resume_store_id"                   => $resume_store_id,
            "hc_id"                             => intval($hc_id),
            "cv_id"                             => intval($cvId),
            "approval_status"                   => $approvalStatus,
            "send_time_start"                   => $send_time_start,
            "send_time_end"                     => $send_time_end,
            "noPage"                            => $noPage,
            "page_num"                          => $pageNum,
            "page_size"                         => $pageSize,
            'ifHrisSales'                       => $this->ifHrisSales,
            'resumeIds'                         => $resumeIds,
            'alternative_job_id'                => $alternative_job_id,
            'is_weed_out'                       => $is_weed_out,
            'expected_arrivaltime_left'         => $expected_arrivaltime_left,
            'expected_arrivaltime_right'        => $expected_arrivaltime_right,
            'job_title'                         => $jobTitle,
            'authority_department_ids'          => $authority_department_ids,
            'authority_stores_ids'              => $authority_stores_ids,
            'is_admin'                          => $is_admin,
            'resume_source'                     => $resume_source,
            'resume_approve_state'              => $resume_approve_state,
            'recommender_staff_id'              => $recommender_staff_id,
            "process_status"                    => $process_status,
            'reason'                            => $reason,
            'entry_status'                      => $entry_status,                      //入职状态筛选
            'entry_staff_id'                    => $entry_staff_id,                    //入职工号筛选
            'interview_communication_status'    => $interview_communication_status,    //沟通状态
            'communication_failure_reason_type' => $communication_failure_reason_type, //沟通失败原因
            'is_menu_permission'                => $is_menu_permission,                //is_menu_permission权限范围开关
            'last_operator_query_type'          => $last_operator_query_type,
            'resume_last_operator'              => $resume_last_operator,
            'is_relation_hc'                    => $is_relation_hc,
            'priority_ids'                      => $priority_ids,
            'credentials_num'                   => $credentials_num,
            'manage_region'                     => $manage_region,
            'manage_piece'                      => $manage_piece,
            'hire_type'                         => $hire_type,
            'recruit_channel'                   => $recruit_channel,
            'is_sub_department'                 => $paramIn['is_sub_department'] ?? 2,
            'hc_state_code'                     => $paramIn['hc_state_code'] ?? [],
        ]);
        if (isset($paramIn['is_count']) && $paramIn['is_count'] == 1) {
            return $resumeData['pagination']['count'] ?? 0;
        }

        $resumeData['permissions'] = (new PermissionsRepository())->checkRoleResumeListPermissions($this->userInfo);

        if (!$resumeData["dataList"]) {
            $returnData['data'] = $resumeData;
            return $this->checkReturn($returnData);
        }
        //状态列表
        $state_array = $this->getStateArray();

        //关联查询 hr_interview_subscribe 表 条件 interview_id
        $interviewIdsArr = array_filter(array_unique(array_column($resumeData["dataList"], "interview_id")));
        $submitterIds =  array_filter(array_unique(array_column($resumeData['dataList'], 'submitter_id')));
        $submitterInfos = $this->sysList->getStaffListByIds($submitterIds);
        $interviewIds           = getIdsStr($interviewIdsArr);
        $interviewSubscribeData = $this->interview->getInterviewSubscribe([
            "interview_ids" => $interviewIds,
        ]);
        $interviewSubscribeData = array_column($interviewSubscribeData, null, 'interview_id');
        //关联查询 面试状态和操作相关联
        $result = [];
        if($interviewIds){
            $interview_sql = "--
                        SELECT
                            hi.interview_id,
                            ifnull( hif.interview_info_id, '' ) AS interview_info_id,
                            hif.hc_id
                        FROM
                            hr_interview hi
                            LEFT JOIN hr_interview_info hif ON hi.interview_id = hif.interview_id 
                        WHERE
                            hi.interview_id IN ( $interviewIds ) and hif.state != 0";
            $dataObj       = $this->getDI()->get('db_rby')->query($interview_sql);
            $data          = $dataObj->fetchAll(\Phalcon\Db::FETCH_ASSOC);

            foreach ($data as $k => $v) {
                $result[$v['interview_id']][$v['hc_id']][] = $v['interview_info_id'];
            }
        }


        //取最新的hc
        if($result){
            foreach($result as $k=>$v){
                $current = '';
                $hc_id = '';
                foreach($v as $k_2=>$v2){
                    $max = max($v2);
                    if($max > $current){
                        unset($result[$k][$hc_id]);
                        $current = $max;
                        $hc_id = $k_2;
                    }else{
                        unset($result[$k][$k_2]);
                    }
                }
                unset($current);
                unset($hc_id);
                $result[$k] = (current($result[$k]));
            }
        }

        //关联查询 hr_interview_offer 表 条件 interview_id
        $interviewOfferData = $this->interview->getInterviewOffer($interviewIdsArr);
        $interviewOfferData = array_column($interviewOfferData, null, 'interview_id');

        // 关联查询 hr_interview_offer_sign_approve 表（offer签字审批表）， 获取当前offer最新签字状态
        $offer_sign_lasted_data = (new InterviewRepository())->getOfferSignApproveData($interviewIdsArr);


        //查询部门表数据
        $departmentData   = $this->sysList->getDepartmentList();
        $departmentData   = array_column($departmentData, 'name', 'id');

        //查询职位表数据
        $job_title_map = $this->sysList->getJobTitleList();

        //查询城市
        $cityData = $this->sysList->getProvinceList();
        $cityData = array_column($cityData, NULL, 'code');

        //获取市列表，上面那个其实是省列表
        $cityList = (new H5appointRepository())->getCityList();
        $cityListKy = array_column($cityList, 'name', 'code');

        /* 获取工作网点列表 */
        $worknodeIdsArr = array_unique(array_merge(array_column($resumeData["dataList"], "worknode_id"), array_column($resumeData["dataList"], "store_id"),array_column($resumeData["dataList"], "recommend_store_id")));
        $worknodeIds    = getIdsStr($worknodeIdsArr);
        $worknode       = $this->sysList->getStoreList([
            "ids" => $worknodeIds,
        ]);
        $worknodeData   = array_column($worknode, null, 'id');

        //获取JD名称 匹配用的 ids
        $alternative_job_ids_str = array_column($resumeData['dataList'], 'alternative_job_ids');
        $alternative_job_ids = [];
        foreach ($alternative_job_ids_str as $idsStr) {
            $alternative_job_ids = array_merge($alternative_job_ids, explode(',', $idsStr));
            $alternative_job_ids = array_unique($alternative_job_ids);
        }

        $jobIdsArr   = array_unique(
            array_merge(array_column($resumeData["dataList"], "hc_job_id"),
                array_column($resumeData["dataList"], "job_id"),
                $alternative_job_ids
            )
        );
        $jobIds      = getIdsStr($jobIdsArr);
        $jobList     = $this->sysList->getJobList([
            "ids" => $jobIds,
        ]);
        $jobListData = array_column($jobList, 'job_name', 'job_id');

        //获取黑名单 匹配用的
        $blacklistIdsArr = array_unique(array_column($resumeData["dataList"], "credentials_num"));
        $blacklistIds    = getIdsStr($blacklistIdsArr);
        $blackList       = $this->sysList->getBlacklist([
            "ids" => $blacklistIds,
        ]);
        $blackListData   = array_column($blackList, null, 'identity');

        /* 获取员工id对应名字数组 */
        $interviewer_ids = array_filter(array_column($interviewSubscribeData, "interviewer_id"));
        $resume_provider_ids = array_filter(array_column($interviewSubscribeData, "resume_provider_id"));
        $recommender_staff_ids = array_filter(array_column($resumeData["dataList"], "recommender_staff_id")); //by推荐人
        $staffIdsArr = array_unique(array_merge($interviewer_ids, $resume_provider_ids,$recommender_staff_ids));
        /* 获取员工信息列表 */
        $staffList = $this->sysList->getStaffListForBi($staffIdsArr);
        $staffData = array_column($staffList, NULL, 'staff_info_id');

        //招聘渠道
        $recruitChannel = (new SysListServer())->getRecruitChannelList();
        $recruitChannel = array_column($recruitChannel, 'name', 'key');


        /* 获取入职员工的工号 */
        $resumeIds       = array_unique(array_column($resumeData["dataList"], "resume_id"));
        $resumeIds       = getIdsStr($resumeIds);
        $resumeStaffList = $this->entry->getEntryStaffInfo([
            'resume_ids' => $resumeIds,
        ]);
        $resumeStaffList = array_column($resumeStaffList, null, 'resume_id');

        $resumeIds       = array_values(array_filter(array_unique(array_column($resumeData["dataList"], "resume_id"))));
        $historyHcs = $this->getHistoryHc($resumeIds);

        /**
         *  获取 简历 薪资审批状态*/
        $approvalList = $this->getLastSalaryApproval(array_unique(array_column($resumeData["dataList"], "resume_id")));

        $approvalList= array_column($approvalList, null, 'resume_id');
        $approveJobs = [];
        if ($approvalList) {
            $jobDatas = $this->getDI()->get("db_rby")->fetchAll("select * from hr_job_title where id in ( " . implode(",", array_column($approvalList, 'job_id')) . ")");
            $approveJobs = array_column($jobDatas, null, 'id');
        }

        $server = new ResumeServer();

        $shift_list = (new SysListRepository())->getAllShiftList();
        $shift_list = array_column($shift_list, null, 'id');

        $child_department_list = (new SysListRepository())->getChildDepartmentListById(25);
        $child_department_ids = array_column($child_department_list,'id');

        //简历附件权限
        //offer附件权限
        $this->annex_ofer_permission = $this->isHaveOfferAnnexPermission($paramIn['staff_id']);

        //背调附件权限
        $this->annex_bg_permission = $this->isHaveBgAnnexPermission($paramIn['staff_id']);

        //薪资审批表单、offer表单 工号ids
        $salaryOfferStaffIds = SettingEnvServer::getSetVal('salary_permission_staff');
        $salaryOfferStaffIds = explode(',', $salaryOfferStaffIds);

        //薪资审批表单、offer表单 工号ids
        $salaryOfferRoles = SettingEnvServer::getSetVal('salary_permission_roles');
        $salaryOfferRoles = explode(',', $salaryOfferRoles);

        $config = $this->getDI()->getConfig();
        //获取sub_id
        $operation_info_record = $this->getSubID($resumeData["dataList"]);
        $new_operations = array_column($this->getNewOperationState(array_values($operation_info_record)),'state','interview_sub_id');

        //获取具体信息
        $resume_ids = array_column($resumeData['dataList'],'resume_id');
        $resume_specific_types = (new CustomerresumeRepository())->getResumeSpecificTypes($resume_ids);

        $offerIds = array_column($interviewOfferData, 'offer_id');
        $offerIds = $this->hasBgCheckAnnexOffer($offerIds);

        $resume_last_operator_staff_ids = array_values(array_unique(array_column($resumeData["dataList"],'resume_last_operator')));
        $resume_last_operator_staff_res = (new StaffServer())->getStaffListByIds($resume_last_operator_staff_ids,['staff_info_id','name','nick_name']);
        $resume_last_operator_staff_list = array_column($resume_last_operator_staff_res, null, 'staff_info_id');

        //获取大区数据
        $regionList = (new SysManageRegionRepository())->getListFromCache(['id', 'name']);
        $regionList = array_column($regionList, 'name', 'id');

        //获取片区数据
        $pieceList = (new SysManagePieceRepository())->getListFromCache(['id', 'name']);
        $pieceList = array_column($pieceList, 'name', 'id');

        //获取多个配置文件
        $updateHcSetting = (new SettingEnvServer())->getMultiEnvByCode([
            'replace_hc_jobids',
            'replace_hc_hire_type',
            'replace_hc_staff_ids'
        ]);

        $updateHcSettingParams = [
            'setting_job_title'     => $updateHcSetting['replace_hc_jobids'] ? explode(',', $updateHcSetting['replace_hc_jobids']) : [],
            'setting_hire_type'     => $updateHcSetting['replace_hc_hire_type'] ? explode(',', $updateHcSetting['replace_hc_hire_type']) : [],
            'setting_staff_info_id' => $updateHcSetting['replace_hc_staff_ids'] ? explode(',', $updateHcSetting['replace_hc_staff_ids']) : [],
        ];

        //业务处理
        foreach ($resumeData["dataList"] as $k => $v) {
            $resumeData["dataList"][$k]["resume_last_operator_name"] = StaffServer::getStaffNameView($resume_last_operator_staff_list[$v['resume_last_operator']] ?? [],2);

            //赋值ope_id
            if(!empty($v['sub_id']) && $operation_info_record) {
                $resumeData["dataList"][$k]["ope_id"] = $operation_info_record[$v['sub_id']];
            }else{
                $resumeData["dataList"][$k]["ope_id"] = '';
            }

            $phone_area_code = !empty($v['phone_area_code']) ? '+'.$v['phone_area_code']: '+66';
            if (strlen($v['phone']) == 11 && strpos($v['phone'], "0") === 0) {
                $v['phone'] = substr($v['phone'], 1, 11);
            }
            $resumeData["dataList"][$k]["phone"] = $phone_area_code.' '.$v['phone'];
            //发送offer时间
            if ($v['state'] != 25 && $v['state'] != 40) {
                $resumeData["dataList"][$k]["send_time"] = "";
            }
            //offer提交人
            $submitter_id = $interviewOfferData[$v["interview_id"]]["submitter_id"] ?? "";

            //offer附件权限
            if((!empty($submitter_id) && $submitter_id == $paramIn['staff_id']) || $this->annex_ofer_permission){
                $resumeData["dataList"][$k]['permissions_attach_offer'] = 1;
            }else{
                $resumeData["dataList"][$k]['permissions_attach_offer'] = 0;
            }

            //背调附件权限
            if((!empty($submitter_id) && $submitter_id == $paramIn['staff_id']) || $this->annex_bg_permission){
                $resumeData["dataList"][$k]['permissions_attach_bg'] = 1;
            }else{
                $resumeData["dataList"][$k]['permissions_attach_bg'] = 0;
            }

            //面试-详细地址
            $resumeData["dataList"][$k]["detail_address"] = $interviewSubscribeData[$v["interview_id"]]["detail_address"] ?? "";
            //面试-subscribeId
            $resumeData["dataList"][$k]["subscribe_id"] = $interviewSubscribeData[$v["interview_id"]]["id"] ?? "";
            //面试-subscribeId-简历提供人
            $resumeData["dataList"][$k]["resume_provider_id"] = $interviewSubscribeData[$v["interview_id"]]["resume_provider_id"] ?? "";
            //面试-subscribeId-简历提供人名字
            if ($interviewSubscribeData[$v["interview_id"]]['resume_provider_id'] == "-1") {
                $resumeData["dataList"][$k]["resume_provider_name"] = $this->getTranslation()->_('recruit_channels_2');
            } elseif ($interviewSubscribeData[$v["interview_id"]]['resume_provider_id'] == "-2") {
                $resumeData["dataList"][$k]["resume_provider_name"] = $this->getTranslation()->_('null');
            } else {
                $resume_provider_id = $interviewSubscribeData[$v["interview_id"]]["resume_provider_id"] ?? 0;
                $resumeData["dataList"][$k]["resume_provider_name"] = StaffServer::getStaffNameView($staffData[$resume_provider_id] ?? [],2,$resume_provider_id);
            }

            //面试-subscribeId-interview_time
            $add_hour = $this->getDI()['config']['application']['add_hour'];
            $_now_time = gmdate('Y-m-d H:i:s', time()+$add_hour * 3600);

            $resumeData["dataList"][$k]["interview_time"] = $interviewSubscribeData[$v["interview_id"]]["interview_time"] ?? "";
            //面试反馈等待时长
            $resumeData["dataList"][$k]["wating_feedback"] = '';
            if ($resumeData["dataList"][$k]['interview_time']) {
                $_interview_time = $resumeData["dataList"][$k]['interview_time'];
                $resumeData["dataList"][$k]["wating_feedback"] = $this->getWaitFeedbackTime($_interview_time,$_now_time);
            }

            //获取简历筛选反馈等待时长
            $resumeData["dataList"][$k]["filter_wating_feedback"] = $this->getWaitFeedbackTime($v['recommend_time'],$v['feedback_time']);
            //推荐时间转换成对应时区日期显示
            $resumeData["dataList"][$k]["recommend_time"] = date('Y-m-d H:i:s',(strtotime($v['recommend_time']) +  $add_hour * 3600)) ;

            $resumeData["dataList"][$k]["is_offer_submitter"] = ($submitter_id == $userinfo['id']) ? 1 : 0;//是否offer提交人

            $resumeData["dataList"][$k]["offer_id"] = $interviewOfferData[$v["interview_id"]]["offer_id"] ?? "";
            //off附件上传时间
            $resumeData["dataList"][$k]["offer_annex_upload_time"] = $interviewOfferData[$v["interview_id"]]["offer_annex_upload_time"] ?? "";
            //offer-最后更新时间
            $resumeData["dataList"][$k]["offer_updated_at"] = $interviewOfferData[$v["interview_id"]]["offer_updated_at"] ?? "";
            //offer-预计到岗时间
            $resumeData["dataList"][$k]["offer_work_time"] = isset($interviewOfferData[$v["interview_id"]]) ? ($interviewOfferData[$v["interview_id"]]["offer_work_time"] ?: '') : strval($v['expected_arrivaltime']);
            //offer-基本工资
            $resumeData["dataList"][$k]["offer_money"] = $interviewOfferData[$v["interview_id"]]["money"] ?? "";
            //offer-职位津贴
            $resumeData["dataList"][$k]["offer_position"] = $interviewOfferData[$v["interview_id"]]["position"] ?? "";
            //offer-经验津贴
            $resumeData["dataList"][$k]["offer_exp"] = $interviewOfferData[$v["interview_id"]]["exp"] ?? "";
            //offer-餐补
            $resumeData["dataList"][$k]["offer_food"] = $interviewOfferData[$v["interview_id"]]["food"] ?? "";
            //offer-危险区域津贴
            $resumeData["dataList"][$k]["offer_dangerously"] = $interviewOfferData[$v["interview_id"]]["dangerously"] ?? "";
            //offer-电脑补贴
            $resumeData["dataList"][$k]["offer_computer"] = $interviewOfferData[$v["interview_id"]]["computer"] ?? "";
            //offer-租房津贴
            $resumeData["dataList"][$k]["offer_renting"] = $interviewOfferData[$v["interview_id"]]["renting"] ?? "";
            //offer-租车津贴
            $resumeData["dataList"][$k]["offer_rental"] = $interviewOfferData[$v["interview_id"]]["rental"] ?? "";
            //offer-设备费
//            $resumeData["dataList"][$k]["offer_equipment"] = $interviewOfferData[$v["interview_id"]]["equipment_cost"] ?? "";

            // 备注
            $resumeData["dataList"][$k]["other"] = $interviewOfferData[$v["interview_id"]]["other"] ?? "";
            //offer附件
            $resumeData["dataList"][$k]['file_offer_url'] = $interviewOfferData[$v["interview_id"]]["offer_annex_url"] ?? "";
            //背调附件
            $resumeData["dataList"][$k]['file_backgroud_check_url'] = $interviewOfferData[$v["interview_id"]]["bg_check_annex_url"] ?? "";
            // 是否有背调附件
            $resumeData["dataList"][$k]['is_has_file_backgroud_check'] = isset($interviewOfferData[$v["interview_id"]]) && in_array($interviewOfferData[$v['interview_id']]['offer_id'], $offerIds) ? 1 : 0;

            //员工雇佣类型
            $resumeData["dataList"][$k]['hire_type_text'] = $v['hire_type'] ? $this->getTranslation()->_('hire_type_' . $v['hire_type']) : '';

            //面试官 staff_id
            $resumeData["dataList"][$k]["interviewer_id"] = $interviewSubscribeData[$v["interview_id"]]["interviewer_id"] ?? "";
            //面试官 姓名
            $interviewer_id = $interviewSubscribeData[$v["interview_id"]]["interviewer_id"] ?? 0;
            $resumeData["dataList"][$k]["interviewer_name"] = StaffServer::getStaffNameView($staffData[$interviewer_id] ?? [],2,$interviewer_id);

            //hc-岗位名称
            if ($v['hc_job_id']) {
                $resumeData["dataList"][$k]['hc_job_name'] = $jobListData[$v['hc_job_id']];
            } else {
                $resumeData["dataList"][$k]['hc_job_name'] = '';
            }
            //没有反馈信息的，不纳入PC操作下的查看面试反馈项
            if (isset($result[$v['interview_id']]) && !empty(array_filter($result[$v['interview_id']]))) {
                $interview_ids = implode(',', $result[$v['interview_id']]);

                $sql = "select interview_info_id from hr_interview_info where interview_info_id in ({$interview_ids}) and evaluate is not null";
                $db = $this->getDI()->get('db_rby');
                $interview_ids_res = $db->fetchAll($sql, \Phalcon\Db::FETCH_ASSOC);

                if ($interview_ids_res && !empty($interview_ids_res)) {
                    $resumeData["dataList"][$k]['interview_msg'] = array_column($interview_ids_res, 'interview_info_id');
                } else {
                    $resumeData["dataList"][$k]['interview_msg'] = [];
                }
            } else {
                $resumeData["dataList"][$k]['interview_msg'] = [];
            }

            //添加专属客服客服 客服处理简历进度,终止处理原因  @info shenxn
            $exclusiveCustomerInfo = $v['exclusive_customer'] ? $this->staff->getStaffInfoById($v['exclusive_customer']) : [];
            $resumeData["dataList"][$k]['exclusive_customer'] = $exclusiveCustomerInfo['name'] ?? '';
            $resumeData["dataList"][$k]['process_status_text'] = !empty( enums::$process_status[$v['process_status']] ) ? $this->getTranslation()->_(enums::$process_status[$v['process_status']]) : '';
            $resumeData["dataList"][$k]['reason_text'] = !empty( enums::$termination_reason[$v['reason']] ) ? $this->getTranslation()->_(enums::$termination_reason[$v['reason']]) : '';
            $resumeData["dataList"][$k]['specific_type_text'] = $resume_specific_types[$v['resume_id']] ?? '';

            //是否已有反馈
            $resumeData["dataList"][$k]["is_feedback"] = intval($new_operations[$v['sub_id']] ?? 0);


            $resumeData["dataList"][$k]['is_salary_approve'] =  $this->isHasSalaryPermission($this->userInfo) ? 1 : 0;

            // 备选岗位
            if ($v['alternative_job_ids']) {

                $resumeData["dataList"][$k]['alternative_job_names'] = $this->formatAlternativeJobs($v['alternative_job_ids'], $jobListData);
            } else {

                $resumeData["dataList"][$k]['alternative_job_names'] = '';
            }

            // 历史hc
            $historyHcsStr = '';
            if (isset($historyHcs[$v['resume_id']])) {
                $historyHcsStr = implode(", ", array_diff($historyHcs[$v['resume_id']], [$v['hc_id']]));
            }
            $resumeData['dataList'][$k]['history_hcs'] = $historyHcsStr;


            //hc-部门名称
            if ($v['hc_department_id']) {
                $resumeData["dataList"][$k]['hc_department_name'] = $departmentData[$v['hc_department_id']] ?? '';
                $resumeData["dataList"][$k]['is_express_line'] = $this->isExpressLine($v['hc_department_id']) ? 1 : 0;
            } else {
                $resumeData["dataList"][$k]['hc_department_name'] = '';
                $resumeData["dataList"][$k]['is_express_line'] = 0;
            }
            $resumeData["dataList"][$k]['interview_msg'] = isset($result[$v['interview_id']]) ? $result[$v['interview_id']] : [];
            //jd-部门名称
            $resumeData["dataList"][$k]['department_name'] = $departmentData[$v['department_id']] ?? '';
            //简历-状态
            $resumeData["dataList"][$k]['state_name'] = $state_array[$v['state']] ?? '';


            //追加显示简历offer签字状态字段
            if($offer_sign_lasted_data){
                $pdf_domain_prefix = $this->getDI()->getConfig()->application['img_prefix'];
                $offer_sign_submitter = $offer_sign_lasted_data[$v["interview_id"]]['submitter_id'] ?? 0;//offer签字发起人
                $offer_sign_state = $offer_sign_lasted_data[$v["interview_id"]]['approve_state'] ?? 0;
                $offer_sign_state_name = $offer_sign_state ? $this->getTranslation()->_('offer_sign_state_'.$offer_sign_state) : '';
                $offer_sign_pdf_path = $offer_sign_lasted_data[$v["interview_id"]]['pdf_path'] ?? '';
                $resumeData['dataList'][$k]['offer_sign_status']= $offer_sign_state;
                $resumeData['dataList'][$k]['offer_sign_status_text']= $offer_sign_state_name;
                $resumeData['dataList'][$k]['offer_sign_pdf_path']= $offer_sign_pdf_path ? $pdf_domain_prefix.$offer_sign_pdf_path : '';
                $resumeData['dataList'][$k]['is_offersign_submitter']= ($offer_sign_submitter == $userinfo['id']) ? 1 : 0 ;//是否是offer签字发起人，1-是，0-否，该字段与is_can_revoke结合使用，都是1是可撤销offer签字
            }

            //来源
            $resumeData['dataList'][$k]['source'] = $v['source'] == 2
            && isset($submitterInfos[$v['submitter_id']])
            && $submitterInfos[$v['submitter_id']]['department_id'] != 7 ? "3" : ($v['source'] ? $v['source'] : '0');
            // 员工id
            $resumeData['dataList'][$k]['submitter_id'] = $v['submitter_id'];

            //追加工作城市 work_city
            $provinceName = $cityData[$v['work_code']]['name'] ?? '';
            //期望工作城市-市，MY显示输入内容不进行转义，省还用上变address_id
            $cityName = $cityListKy[$v['work_city_id']] ?? '';
            $resumeData["dataList"][$k]['work_city'] = $provinceName.' '.$cityName;


            //hc-网点一级行政区
            $resumeData["dataList"][$k]['work_name'] = isset($cityData[$v['province_code']]['name']) ? $cityData[$v['province_code']]['name'] : '';
            //hc-网点名称
            $resumeData["dataList"][$k]['worknode_name'] = $worknodeData[$v['worknode_id']]["name"];
            //hc-网点所属区域
            $resumeData["dataList"][$k]['sorting_no'] = $worknodeData[$v['worknode_id']]["sorting_no"];
            //hc-网点地址
            $resumeData["dataList"][$k]['hc_detail_address'] = $worknodeData[$v['worknode_id']]["detail_address"];
            //简历-姓名
            $resumeData["dataList"][$k]['name'] = $v['first_name'] . " " . $v['middle_name'] . " " . $v['last_name'] . " " . $v['suffix_name'];
            //简历-姓名
            $resumeData["dataList"][$k]['name_en'] = $v['first_name_en'] . " " . $v['last_name_en'];
            //简历-email
            $resumeData["dataList"][$k]['email'] = $v['email'] ?? '';
            //简历-招聘渠道
            $resumeData["dataList"][$k]['recruit_channel_name'] = $recruitChannel[$v['recruit_channel']];
            //检测是否在黑名单
            $resumeData["dataList"][$k]['is_blacklist'] = $blackListData[$v["credentials_num"]] ? 1 : 0;
            //检测性别
            //简历-所属网点
            $resumeData["dataList"][$k]['store_name'] = $worknodeData[$v["store_id"]]["name"] ?? "";
            $resumeData["dataList"][$k]['sex_title']  = enums::$sex_title[$v['sex']] ?? '';
            //简历-期望岗位
            $resumeData["dataList"][$k]['expect_job_name'] = $jobListData[$v['expect_job_id']] ?? '';
            //入职-员工工号
            $resumeData["dataList"][$k]['staff_id'] = $resumeStaffList[$v['resume_id']]['staff_id'] ?? '';
            //简历入库时间
            $resumeData["dataList"][$k]['resume_created_at'] = $v['resume_created_at'] ?? '';
            //入职-办理时间
            $resumeData["dataList"][$k]['operate_date'] = $resumeStaffList[$v['resume_id']]['operate_date'] ?? '';
            // 薪资审批状态
            $resumeData["dataList"][$k]['approval_status'] = isset($approvalList[$v['resume_id']]) ? $approvalList[$v['resume_id']]['status'] : '';
            $resumeData["dataList"][$k]['approval_status_text'] = isset($approvalList[$v['resume_id']]) ? $this->translationTransfer($approvalList[$v['resume_id']]['status']) : '';
            // 基本薪资  通过试用期后薪资
            $resumeData["dataList"][$k]['basic_salary'] = isset($approvalList[$v['resume_id']]) ? $approvalList[$v['resume_id']]['basic_salary'] : '';
            $resumeData["dataList"][$k]['trial_salary'] = isset($approvalList[$v['resume_id']]) ? $approvalList[$v['resume_id']]['trial_salary'] : '';
            $resumeData["dataList"][$k]['renting'] = isset($approvalList[$v['resume_id']]) ? $approvalList[$v['resume_id']]['renting'] : '';
            $resumeData["dataList"][$k]['job_title_grade'] = isset($approvalList[$v['resume_id']]) ? $approvalList[$v['resume_id']]['job_title_grade'] : '';
            $resumeData["dataList"][$k]['xinghuo_allowance'] = isset($approvalList[$v['resume_id']]) ? $approvalList[$v['resume_id']]['xinghuo_allowance'] : '';
            $resumeData["dataList"][$k]['deminimis_benefits'] = isset($approvalList[$v['resume_id']]) ? $approvalList[$v['resume_id']]['deminimis_benefits'] : '';
            $resumeData["dataList"][$k]['other_non_taxable_allowance'] = isset($approvalList[$v['resume_id']]) ? $approvalList[$v['resume_id']]['other_non_taxable_allowance'] : '';
            $resumeData["dataList"][$k]['approve_job_id'] = $v['job_title_id'] ?? '';
            $resumeData["dataList"][$k]['approve_job_name'] = $approveJobs[$v['job_title_id']]['job_name'] ?? '';
//            $resumeData['dataList'][$k]['salary_view_permission']  = isset($approvalList[$v['resume_id']]) && isset($paramIn['staff_id'])
//            && $approvalList[$v['resume_id']]['status'] != 4 &&  !($approvalList[$v['resume_id']]['submitter_id'] == $paramIn['staff_id'] || $userinfo['salary_structure_permission'] == enums::$salary_structure_permission['on'])
//                ? 0 : 1;

            $resumeData['dataList'][$k]['salary_view_permission'] =
                isset($approvalList[$v['resume_id']]) && $approvalList[$v['resume_id']]['submitter_id'] == $userinfo['id']
                || in_array($userinfo['id'], $salaryOfferStaffIds)
                || array_intersect(explode(',', $userinfo['position_category']), $salaryOfferRoles) ? 1 : 0;


            $resumeData['dataList'][$k]['is_can_revoke'] = $approvalList[$v['resume_id']]['submitter_id'] == $userinfo['id'] ? 1 : 0;

            //简历年龄
            if (isset($v['date_birth']) && $v['date_birth']) {
                if ((int)date("Y") < (int)date('Y', strtotime($v['date_birth']))) {
                    $age = (int)(date("Y-m-d") - ($v['date_birth'] - 543));
                } else {
                    $age = (int)(date("Y-m-d") - $v['date_birth']);
                }
            } else {
                $age = '';
            }
            $resumeData["dataList"][$k]['age'] = $age;
            //简历银行卡号
            $resumeData["dataList"][$k]['bank_no'] = $staffData[$resumeData["dataList"][$k]['staff_id']]['bank_no'] ?? '';

            $hc_job_title_name = $job_title_map[$v['job_title_id']] ?? '';
            $resumeData["dataList"][$k]['hc_job_title_name'] = $hc_job_title_name;

            //职位名称
            $resumeData["dataList"][$k]['job_title_name'] = !empty($v['job_title_id']) ? sprintf("(%s) %s", $v['job_title_id'],$hc_job_title_name) : '';
            //增加是否是一线员工职位标识字段
            $resumeData["dataList"][$k]["is_front_line_job"] = $this->isFirstLineJob($v['hc_department_id'], $v['job_title_id']) ? 1: 0;

            //根据部门+职位 判断是否是一线员工职位标识（简历筛选、薪资审批使用该逻辑）
            $resumeData["dataList"][$k]["is_first_line_job"] =  empty($v['hc_id']) ? null : $this->isFirstLineJob($v['hc_department_id'], $v['job_title_id']);

            //验证是否可以编辑简历hc
            $updateHcSettingParams['staff_info_id']            = $this->userInfo['id'] ?? 0;
            $updateHcSettingParams['job_title']                = $v['job_title_id'] ?? 0;
            $updateHcSettingParams['hire_type']                = $v['hire_type'] ?? 0;
            $updateHcSettingParams['is_first_line_job']        = $resumeData["dataList"][$k]["is_first_line_job"];
            $resumeData["dataList"][$k]["is_update_resume_hc"] = InterviewHelper::isUpdateResumeHc($updateHcSettingParams);

            //HC不是HCM-设置中心-系统设置-WinHR-non_recommend_resume_ids配置职位：操作列显示“推荐简历”
            //https://flashexpress.feishu.cn/wiki/JQ7TwB5POicjAqk6bHXcxg6gnTf
            $resumeData["dataList"][$k]["is_recommended"] = $this->isRecommendedResume([
                'hc_id'         => $v['hc_id'],
                'department_id' => $v['hc_department_id'],
                'job_title'     => $v['job_title_id'],
            ]);

            //offer-班次 - 默认值
            $shift_id = 0;
            if($v['worknode_id'] != -1) {
                if(!in_array($resumeData["dataList"][$k]['state'],[1, 5, 10, 20])){
                    $shift_id = $interviewOfferData[$v["interview_id"]]["shift_id"];
                }
                if(in_array($v['hc_department_id'], $child_department_ids)) {
                    $shift_id = 7;
                } else {
                    switch ($v['hc_department_id']) {
                        case 65:
                        case 51:
                            $shift_id = 7;
                            break;
                        case 32:
                            $shift_id = 5;
                            break;
                        default:
                            $shift_id = 7;
                            break;
                    }
                }
                if(isset($shift_list[$shift_id])) {
                    $resumeData["dataList"][$k]["shift_id"] = $shift_id;
                    //班次类型
                    $shift_type = $shift_list[$shift_id]['type'] ?? '';
                    $resumeData['dataList'][$k]['shift_type'] = $shift_type;
                    $resumeData['dataList'][$k]['shift_type_text'] = empty($shift_type) ? '' : $this->getTranslation()->_('shift_'.$shift_type);
                    $resumeData['dataList'][$k]['shift_text'] = ($shift_list[$shift_id]['start'] ?? '').'-'.($shift_list[$shift_id]['end'] ?? '');
                } else {
                    $resumeData["dataList"][$k]["shift_id"] = $shift_id;
                    $resumeData['dataList'][$k]['shift_type'] = '';
                    $resumeData['dataList'][$k]['shift_type_text'] = '';
                    $resumeData['dataList'][$k]['shift_text'] = '';
                }
            } else {
                $resumeData["dataList"][$k]["shift_id"] = $shift_id;
                $resumeData['dataList'][$k]['shift_type'] = '';
                $resumeData['dataList'][$k]['shift_type_text'] = '';
                $resumeData['dataList'][$k]['shift_text'] = '';
            }

            //by 推荐人字段信息

            //简历来源
            $resumeData["dataList"][$k]['source_text'] = empty($v['source']) ? '' :$this->getTranslation()->_('resume_src_'.$v['source']) ;
            //by推荐网点
            if($v["recommend_store_id"]){
                $resumeData["dataList"][$k]['recommend_store_name'] = $worknodeData[$v['recommend_store_id']]["name"]?? '';
            }
            //by推荐人
            if($v["recommender_staff_id"]){
                $current_recommender = $staffData[$v["recommender_staff_id"]] ?? [];
                $resumeData["dataList"][$k]["recommender_name"] = ($current_recommender['name'] ?? '') . "({$v["recommender_staff_id"]})";
                $resumeData["dataList"][$k]["recommender_department_name"] = $departmentData[$current_recommender['node_department_id']] ?? '';
                $resumeData["dataList"][$k]["recommender_job_name"] = $job_title_map[$current_recommender['job_title']] ?? '';
            }
            //by推荐简历审核状态
            if($v['approve_state']){
                $resumeData["dataList"][$k]["approve_state_text"] = $this->getTranslation()->_('resume_approve_state_'.$v['approve_state']);
            }

            //简历期望薪资查看权限
            $is_have_salary_permission = $server->isHaveSalaryOrAnnexPermission(
                $v['resume_id'],
                $v['filter_state'],
                $v['state'],
                $v['recruiter_id'],
                $this->userInfo['id'],
                $this->userInfo['expected_salary_permission']
            );
            if(!$is_have_salary_permission){
                $resumeData['dataList'][$k]['entry_salary'] = '*****';
                $resumeData['dataList'][$k]['current_salary'] = '*****';
            }
            if($server->checkIsNetworkFirstLineJD($v['job_id'])){
                $resumeData['dataList'][$k]['entry_salary'] = '';
                $resumeData['dataList'][$k]['current_salary'] = '';
            }

            //投递日期
            $resumeData["dataList"][$k]["delivery_time"]     = show_time_zone($v['delivery_time']);
            $resumeData["dataList"][$k]["ai_score"]          = is_null($v['ai_score']) ? '' : $v['ai_score'].$this->getTranslation()->_('unit_fen');;

            //大区片区
            $manageRegionId                                   = $worknodeData[$v['worknode_id']]['manage_region'] ?? '';
            $resumeData["dataList"][$k]['manage_region_name'] = $regionList[$manageRegionId] ?? '';
            $managePieceId                                    = $worknodeData[$v['worknode_id']]['manage_piece'] ?? '';
            $resumeData["dataList"][$k]['manage_piece_name']  = $pieceList[$managePieceId] ?? '';

            //优先级
            $resumeData["dataList"][$k]["priority_name"] = $this->getTranslation()->_(HrhcModel::$priority_id_list[$v['priority_id']] ?? '');
        }

        $this->getDI()->get('logger')->write_log("interviewResumeListAction resumeData:" . json_encode($resumeData['pagination'], JSON_UNESCAPED_UNICODE), 'info');
        $returnData['data'] = $resumeData;

        return $this->checkReturn($returnData);
    }

    /**
     * 1. 支持配置发起薪资审批的部门和工号
     * 判断是否有发起薪资审批提交按钮权限
     *
     * @param $userinfo
     *
     */
    public function isHasSalaryPermission($userinfo)
    {
        $departmentIds = SettingEnvServer::getSetVal('salary_offer_departments');
        $departmentIds = explode(',', $departmentIds);

        $staffIds = SettingEnvServer::getSetVal('salary_offer_staffs');
        $staffIds = explode(',', $staffIds);

        return in_array($userinfo['id'], $staffIds) || in_array($userinfo['node_department_id'], $departmentIds);
    }


    /**
     * 获取非一线职位发送offer 邮件模板
     *
     * @param $resumeId
     *
     *
     */
    public function getNotFrontLineJobSendOfferEmailTpl($resumeId)
    {

        $this->sysList      = new SysListRepository();

        $resumeInfo = (new ResumeServer())->getResumeBaseInfo($resumeId);
        $result = ['resume_id'=>$resumeId];
        $tmp = "";
        if ($resumeInfo) {
            $hr_interview = $this->getInterviewInfoByResumeid($resumeId,$resumeInfo["hc_id"]);
            $hc_id = $hr_interview['hc_id'] ?? 0;
            $hc_info = (new OfferSignServer())->getHcInfo($hc_id);

            //面试人信息
            $call_en = enums::$call_en;
            $jobTitle = $this->sysList->getPositionList(["id" => $hc_info['job_title']]);//面试人职位
            $interviewer_job_title = $jobTitle[0]['job_name'] ?? '';
            $interviewer_name = $resumeInfo['call_name'] ? $call_en[$resumeInfo['call_name']] : '';
            $interviewer_name .=  " " .  $resumeInfo['first_name'] ." " . $resumeInfo['last_name']; //面试人姓名
            //获取招聘人信息
            $offer_sign_data = (new InterviewRepository())->getLastOfferSignData($resumeId);
            $recruiter_id = $offer_sign_data['submitter_id'];
            $recruiter_obj = HrStaffInfoModel::findFirst([
                'conditions' => "staff_info_id = :staff_info_id:",
                'bind' => ['staff_info_id' => $recruiter_id],
                'columns' => ['name','mobile','job_title']
            ]);

            if (!empty($recruiter_obj)) {
                $recruiter_info = $recruiter_obj->toArray();
                $recruiter_job_title = $this->sysList->getPositionList(["id" => $recruiter_info['job_title']]);

            }
            $recruiter_job_title = $recruiter_job_title[0]['job_name'] ?? '-';


            $tmp .= "Dear {$interviewer_name},\r\n";
            $tmp .= "\r\n";
            $tmp .= "Congratulations to you! I am happy to inform you that Flash Express would like to offer you the position of {$interviewer_job_title} \r\n";
            $tmp .= "We consider you as the potential candidate for the position after selection of all candidates. \r\n";
            $tmp .= "Please kindly find the Offer Letter attached and sign back to us as your confirmation of acceptance of this offer.\r\n";
            $tmp .= "\r\n";
            $tmp .= "Best Regards\r\n";
            $tmp .= "{$recruiter_info['name']}\r\n";
            $tmp .= "{$recruiter_job_title}\r\n";
            $tmp .= "Tel:{$recruiter_info['mobile']}\r\n";
            $tmp .= "Flash Express Co.,Ltd.\r\n";
            if (!in_array($hc_info['hire_type'], HrStaffInfoModel::$agentTypeTogether)) {
                $tmp .= "11 floor rbbinsons Cyber Sigma bldg. Lawton Ave. Mckinley West. Taguig City\r\n";
            } else {
                $tmp .= "10 floor rbbinsons Cyber Sigma bldg. Lawton Ave. Mckinley West. Taguig City\r\n";
            }
            $tmp .= "Philippines \r\n";


            //接口返回信息
            $result['credentials_category'] = $resumeInfo['credentials_category'];
            $result['phone_area_code'] = !empty($resumeInfo['phone_area_code']) ? $resumeInfo['phone_area_code'] : '66';
            $result['email'] = $resumeInfo['email'];
            $result['phone'] = $resumeInfo['phone'];
            $result['name'] = $interviewer_name;
            $result['content'] = $tmp;
        }

        return $result;
    }

    public function infoInterviewOffer($paramIn,$userinfo)
    {
        $this->offer        = new OfferRepository();
        $this->sysList      = new SysListRepository();
        $this->log          = new LogServer();

        //[2]校验是否存在
        $validations = [
            'id' => "Required|Int",
        ];
        $this->validateCheck($paramIn, $validations);


        $dataList             = $this->offer->infoInterviewOffer($paramIn);
        $offerStaffJobType    = (new SysListServer())->getOfferStaffJobType();
        $offerStaffJobTypeArr = array_column($offerStaffJobType, "name", "key");
        if ($dataList) {
            $shift_info = $this->getShiftInfo($dataList['shift_id']);
            $dataList = array_merge($dataList,$shift_info);
            /* 获取部门列表 */
            $departmentDataList = $this->sysList->getDepartmentList();
            $departmentDataList = array_column($departmentDataList, 'name', 'id');

            $dataList['department_name']     = $departmentDataList[$dataList['department_id']];
            $dataList['staff_job_type_name'] = isset($offerStaffJobTypeArr[$dataList['staff_job_type']]) ? $offerStaffJobTypeArr[$dataList['staff_job_type']] : '';
            $dataList['hire_type_text'] = $dataList['hire_type'] ? $this->getTranslation()->_('hire_type_' . $dataList['hire_type']) : '';

            $dataList['job_title_name']   = !empty($dataList['job_title_id']) ? sprintf("(%s) %s", $dataList['job_title_id'], $dataList['job_title_name']) : '';
            $dataList['job_title_grade'] = $dataList['job_title_grade'];
            $dataList['in_salary_range_text'] = $dataList['in_salary_range'] && isset($dataList['in_salary_range']) ? $this->getTranslation()->_('qa3002' . $dataList['in_salary_range']) : '';
            $dataList['is_express_line'] = $this->isExpressLine($dataList['department_id']) ? 1 : 0;

            //offer页面薪资查看权限
            $dataList['salary_view_permission']  = ($userinfo['salary_structure_permission'] == enums::$salary_structure_permission['on'] || ($dataList['submitter_id'] && $dataList['submitter_id'] == $userinfo['id']) ) ? 1: 0;

            // 字段 null =》 0
            $dataList['gdl_allowance'] =  $dataList['gdl_allowance'] ?? 0;
            $dataList['xinghuo_allowance'] =  $dataList['xinghuo_allowance'] ?? 00;
            $dataList['site_allowance'] =  $dataList['site_allowance'] ?? 00;

            $paramLog                         = [
                'module_id'   => $dataList['interview_id'],
                'module_type' => 2,
            ];
            $historyData                      = $this->log->infoLog($paramLog);
            $dataList['infoInterviewHistory'] = $historyData;
            //试用期薪资根据权限判断是否隐藏
            //1 代表 没有权限

            $dataList['salary_items'] = null;
            if (isCountry('Id')) {
                $offerSendSalary =
                    OfferSendSalaryModel::findFirst([
                        'conditions' => ' offer_id = :offer_id:',
                        'bind' => ['offer_id' => $dataList['id']]
                    ]);
                if ($offerSendSalary) {
                    $offerSendSalary = $offerSendSalary->toArray();

                    $money = [
                        'money' => $dataList['money'],
                        'basic_salary' => $dataList['basic_salary'],
                        'welfare_allowance' => $dataList['welfare_allowance'],
                        'variable_allowance' => $dataList['variable_allowance'],
                        'food_allowance' => $dataList['food'],
                        'attendance_allowance' => $dataList['attendance_allowance'],
                        'transport_allowance' => $dataList['transport_allowance'],
                        'house_rental' => $dataList['renting'],
                        'xinghuo_allowance' => $dataList['xinghuo_allowance'],
                        'language_allowance' => $dataList['language_allowance'],
                    ];

                    $trial_salary = [
                        'money' =>$offerSendSalary['money'],
                        'basic_salary' => $offerSendSalary['basic_salary'],
                        'welfare_allowance' => $offerSendSalary['welfare_allowance'],
                        'variable_allowance' => $offerSendSalary['variable_allowance'],
                        'food_allowance' => $offerSendSalary['food_allowance'],
                        'attendance_allowance' => $offerSendSalary['attendance_allowance'],
                        'transport_allowance' => $offerSendSalary['transport_allowance'],
                        'house_rental' => $offerSendSalary['house_rental'],
                        'xinghuo_allowance' => $offerSendSalary['xinghuo_allowance'],
                        'language_allowance' => $offerSendSalary['language_allowance'],
                    ];

                    $dataList['salary_items'] = [
                        'money' => $money,
                        'trial_salary' => $trial_salary,
                    ];
                }

            }


            if ($dataList['submitter_id'] != $userinfo['id']) {
                // 不是发送offer的人
                if (
                    $dataList['salary_view_permission'] == 0
                ) {
                    $dataList['money'] = '*****';//试用期工资
                    $dataList['trial_salary'] = '*****';//通过试用期工资
                    $dataList['position'] = '*****';//职位津贴
                    $dataList['xinghuo_allowance'] = '*****';//星火激励津贴
                    $dataList['food'] = '*****';//餐补
                    $dataList['rental'] = '*****';//租车津贴
                    $dataList['renting'] = '*****';//租房津贴
                    $dataList['dangerously'] = '*****';//危险区域津贴
//                    $dataList['equipment_cost'] = '*****';//设备费
                    $dataList['computer'] = '*****';//电脑补贴
                    $dataList['exp'] = '*****';//经验津贴
                    $dataList['gasoline_allowance'] = '*****';//油费补贴
                    $dataList['island_allowance'] = '*****';//海岛补贴
                    $dataList['deminimis_benefits'] = '*****';//无税金
                    $dataList['performance_allowance'] = '*****';//绩效补贴
                    $dataList['recommended'] = '*****';//推荐补贴
                    $dataList['other_taxable_allowance'] = '*****';//其他纳税
                    $dataList['other_non_taxable_allowance'] = '*****';//其他无税金补贴
                    $dataList['phone_subsidy'] = '*****';//电话补贴
                    $dataList['gdl_allowance'] = '*****';//gdl补贴
                    $dataList['mobile_allowance'] = '*****';//手机津贴
                    $dataList['attendance_allowance'] = '*****';//出勤津贴
                    $dataList['fuel_allowance'] = '*****';//g油补津贴
                    $dataList['car_allowance'] = '*****';//私家车车补津贴
                    $dataList['vehicle_allowance'] = '*****';//货车车补津贴
                    $dataList['site_allowance'] = '*****';//货车车补津贴
                    $dataList['internship_salary'] = '*****';//实习期工资
                }
            }
        }

        //查询操作日志
        $dataList['operator_salary_log'] = (new OfferServer())->getOfferSalaryOperatorLogList($dataList['id'], $dataList['resume_id']);
        $data['dataList'] = $dataList;

        return $this->checkReturn(['data' => $data]);
    }


}