<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>
      Voluntary Salary, Wage, Other Income Deduction Authorization Form
    </title>
  </head>
  <style>
    .box {
      width: 210mm;
      font-size: 3.5mm;
      margin: 0 auto;
      font-family: Arial Unicode MS, thailand, SimSun;
      padding: 0 20mm;
      box-sizing: border-box;
    }
    .title {
      height: 15mm;
      line-height: 15mm;
      font-size: 6.5mm;
      font-weight: 700;
      padding-bottom: 5mm;
      border-bottom: 1mm solid #000;
      display: flex;
      justify-content: space-between;
    }
    .title img {
      height: 16mm;
      width: auto;
    }
    p {
      margin: 1mm 0;
    }
    .title-t {
      margin: 5mm auto;
      font-size: 4mm;
      text-align: center;
    }
    .t-r {
      text-align: right;
      font-weight: 600;
    }
    .t-w {
      display: inline-block;

      border-bottom: 0.1mm solid #000;
      padding: 1mm;
      text-align: center;
    }

    .t-50 {
      min-width: 32mm;
      text-align: center;
    }
    .t-80 {
      width: 80mm;
      text-align: left;
    }
    .t-60 {
      min-width: 40mm;
      text-align: left;
    }
    .t-160 {
      width: 110mm;
      text-align: left;
    }
    .t-180 {
      width: 160mm;
      text-align: left;
    }
    .t-l {
      text-align: left;
      font-weight: 600;
    }
    .m-1 {
      margin-top: 5mm;
    }
    .m-1-p {
      text-indent: 12mm;
    }
    .m-3 table {
      margin-top: 20mm;
      width: 100%;
      text-align: center;
    }
    .t-img img {
      width: 50mm;
    }
    .address-slot {
      text-indent: 0;
      display: flex;
      flex-direction: row;
    }
    .w-50{
      width: 90%;
      text-align: center;
      box-sizing: border-box;
    }
    .left-tr{
      text-align: left;
    }
  </style>
  <body>
    <div class="box">
      <div class="title">
        <span>Flash Express Co., Ltd.</span>
        <img
          src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAABkCAMAAAAL3/3yAAAAilBMVEUAAAAAAAAAAAAOCggjGBUjGBUAAAAAAAAAAAAZEQ8RDAojGBUjGBUAAAD/6jMjGBX/6jMAAAAjGBUAAAAjGBUjGBUjGBUAAAD/6jP/6jP/6jMjGBUAAAD/6jMAAAD/6jMjGBX/6jP/6jP/6jP/6jMAAAD/6jP/6jP/6jMjGBX/6jMAAAD/6jMjGBU5DR95AAAAK3RSTlMAQIAggEDvEN8wUBDvn+9gEL+fYM/fcHAwz7+/j4Cvn69QIGDfz0Bwj4+vUJZWPwAABm5JREFUeNrs2O2uojAQBuABEkATCMYfggGNoGBO5/6vb3V/bDUtzIw9uuck81wAmX7QvlNQSimllFJKqZ/iBIqpOk6gePqmAcVzKnEExdIhHkFxVF+IZQWKoT4j4gCKYSgRcQ+K4Yp3NShS1eDdFRSpLhFRT3eWCW/0dOc1OHijpztH3SCinu7cBgdRT3eWDm/0dOeo9viPPmMxGhw93bkNjtWDWnDEBx38D1sjkcNfqXm0ArbE+eJB0OBY5wq81k61jpDyN0Zi55ngDfDFxhEBx1jioxNrNFtwBJW/MhJpBncXuiRqTa2c3eDQp3viVOsIKb8wEgXcRcRghf98zHkSfdazRtOCI6j8xIgknpJi4LvMf5VucOjTPUuJ74aVHxuJjW+CM2CL5j9LNzj06Q474uoILD81EmvPBBcQ/s+vySdR1ukOOefmaJ/L/z25wZ7EZINjfQEE5Ibs5fIvH80NrZnTkg2OVfbw/txA3wsxQZIb6DXlLPCArg54uQH8clb54fdCcG6YdyEaHOvMHc17c0P2wgTvgMdd09RQH+ob9Gi6sQ/IDYcXyqevNVlGlm/JlXmSJrMNjqvcX4dxOTcUkvLluWEXLZNNML2m7XJDPSGlOXZjNbdx48ir+FBuIPa6eEtmudNQOw0Ordx3vc0NQsm7csP2W3ND4Q4vz8Cqu32JPJPk9SQgN8jfG3IyeQvats1yQ92fODM22I0rtX7Xe0MbnhvcJjMl/gp6xspaNJpPvTcEXLyzWzImfws7Y+hzru3GlZDnBonD9+YGO4XcPD2hR1PZ3MAkL1++FBG7YRdlwYhbf/WnnfNtShwGwvhiUm+SVALY9hT0RMd/OOP3/3rH7rZsmRTbUnGuXH4vylKIytPt5tm02HQqLme1xO3CSH1DxVVYGTs3Pg+AnNo3hIfi6uJrJt/uGyTBu6xBvbY01PMun2b+TesNxwg8GeAbhN9dGmp4brAMA9cb7IAachKBQ0XCY/rSYYq6Dy3D64+vN0gN6S/w9SDfIIQNdcDTNLRX/dcb5sf6hrsh6w2MnXTgV0P5vZxUGECChjpgGVqGn1lvOH5auzt6xpkfcjqqU41/DC3DEbX0m9Yb5n0EHmplw9/c3FALs+fDlqFh/MuA/Bg8L4jA/Zm0tW0W2hrq8FLYe//rFOElxZP7ht68tKYkZ5H6yi7eNFqGgb5hcmrf0J/r9rbtorWhXu5Pg4/HXaewP+wbenPZJSUNJd/hhvqtyTIM9Q3Xp/YN/bno0rbNDzfUYQN9+wTI/+cb9o/ynwMN9UODvRrsG+7+Pd9w1Z6ScphVs9l6DS3Dz/oGM6mjuok16Y/ikR1dvgkGI89tWoEKhrX++QbOEmqg4zfAWgga6GnUqoWlaBW/ANbCo0yDNa30DoUbQAp+ihRUkRzHibxKKADnN6t14QBxfrFaezgHZtMmy+A/K1aAW/zYCwz8otrvAHQZZ9vY7EbkADbd7YdiF4+f9900OANh9VnhKU4qrVxavaABdvGCX2YKjjmsx6Pnpsky7KfJBpVx2XabWkomJuP0Y0DtwtQVkpa19y9g9NzKfR8CpUORIIpPyRVpZQDy7UOSWJIR99kkIbE0KpogBlLKI9y/cRzbs8is5i/YK9JCniGslecUsfiAaqwB+CHFlAIioZQCm29UFftcKxg7s2mjZdD4EfUWD0hZoVVZyxTX7GSNG/AYW49v0AiL9Zlrkq7g+ByKO3zIfR9CrXBvpNpnjpOmYqMkXkNeq0yqSkQpfvkZ9D6v+5Yh9A1q9zR3XMtElI1o5UTERMbnlKRVPHqogf4IVmQyUUQE4upVkhbgUolhLdPfFpXwGAPgqnj0Jeu+cZWBK3Y5FUoymTJNstVae8fpQ1OkYxGzcoRdpBpA5TikFo+9aj2xZQjgws2wVhnP/I6nPIYUwE35Ji9zg+ValWv+QeYcTsNl84qMwnTRiIfSihZ8Snry7QSbB9LV8BmpCdCUlp72cFxgPPbW8JHu+wioCrfYdkMnZsbJpGr9kCFnYGnLLOoOfy8eN7Np44oMpwljKtsO+IwkW9TSb8XlTUMuI2QOyJzEq7FXrHeyDCGFpEOKG0Pzo5hQRKoU6mc/63OhJ7FzLx4kH/s5CDd830eISvZwgBiMcAMlFPOjkRGKUtNqb4BxXvsz8KO38b+0dubtHiJdif8pORKJRCKRSCQSifTgLwXp9rfv6GcVAAAAAElFTkSuQmCC"
          alt=""
        />
      </div>
      <p class="title-t">
        <strong
          >Voluntary Salary, Wage, Other Income Deduction Authorization
          Form</strong
        >
      </p>
      <div class="t-r">
        <p>Made atFlash Express Co., Ltd.</p>
        <p><span> Date</span> <span class="t-w t-50"> ${entry_date}</span></p>
      </div>
      <div class="t-l">
        <p>
          <span>Dear </span
          ><span style="margin-left: 5mm">Human Resources Manager</span>
        </p>
        <p>Flash Express Company Limited(“Company”)</p>
      </div>
      <div class="m-1">
        <p class="m-1-p">
          <span>I</span><span class="t-w t-60"> ${staff_name}</span
          ><span>ID card number</span><span class="t-w t-60"> ${identity}</span>

          <span>as Flash Express Co., Ltd. Employee, Position</span
          ><span class="t-w t-160"> ${job_title}</span> <span>. Located at</span>
        </p>
        <div>
          <div class="address-slot">
            <div class="t-w t-180" style="height: 4mm">${address_details} &nbsp ${regional_details}</div>
          </div>
          <div class="t-w t-180" style="height: 4mm"></div>
          .
        </div>
        <p class="m-1-p">
          I hereby sign on this form to evidence that if I cause any damages,
          and/or owe debts to the Company, or cause any damages to Company,
          whether intentionally or recklessly, or engage in any actions that
          cause the Company to be liable for damages to others, I consent to be
          liable for the above-mentioneddamages and debts by authorizing the
          Company to deduct salary, wages, and other income that I am entitled
          to receive from the Company to pay for all debts and/or damages with
          immediate effect until the Company and the aggrieved party receive the
          full amount without having to receive notice from the Company. In the
          event that I resign, retire, or terminate as an employee for any
          reason, I authorize the Company to deduct all salary, wages, and/or
          other income I have been entitled to receive form this Company to pay
          for the debts and damages I owe the Company until it is fully paid
          off.
        </p>
        <p class="m-1-p">
          IN WITNESS WHEREOF, this Authorization is made voluntarily and I have
          read and understood the content hereof and thereby affix the
          signature. This Authorization Form shall form part of my Employment
          Contract and I agree not to withdraw my consent regardless of whatever
          reasons.
        </p>
      </div>
      <div class="m-3">
        <table
          border="0"
          style="border-collapse: separate; border-spacing: 5mm 2mm"
        >
          <tr valign="bottom" class="left-tr">
            <td width="50%">
              <span>Sign</span
              ><span class="t-w t-50 t-img">
                <img
                  src="${config_sign_img}"
                  alt="" /></span
              ><span> Company</span>
            </td>
            <td width="50%">
              <span>Sign</span>
              <span class="t-w t-50 t-img">
                &nbsp;  <#if staff_sign_img??>
                  <img src="${staff_sign_img}" alt="" />
                 </#if>
              </span
              ><span> Employee</span>
            </td>
          </tr>
          <tr valign="bottom" class="left-tr">
            <td width="50%">
            ( <span class="t-w w-50"> ${config_sign_name_en}</span>)
            </td>
            <td width="50%">(<span class="t-w w-50"> ${staff_name}</span>)</td>
          </tr>
        </table>
      </div>
    </div>
  </body>
</html>
