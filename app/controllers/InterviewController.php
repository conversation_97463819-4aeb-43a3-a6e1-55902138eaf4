<?php

namespace FlashExpress\bi\App\Controllers;

use FlashExpress\bi\App\Controllers;

use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\library\Tools;
use FlashExpress\bi\App\Models\backyard\HrSmsLogModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\WinhrExcelTaskModel;
use FlashExpress\bi\App\Repository\CustomerresumeRepository;
use FlashExpress\bi\App\Server\DownLoadTaskService;
use FlashExpress\bi\App\Server\InterviewHcServer;
use FlashExpress\bi\App\Server\InterviewServer;
use FlashExpress\bi\App\Server\FlashJdServer;
use FlashExpress\bi\App\Server\HcServer;
use FlashExpress\bi\App\Server\MailServer;
use FlashExpress\bi\App\Server\ResumeServer;
use FlashExpress\bi\App\library\Excel;
use FlashExpress\bi\App\Repository\ResumeRepository;
use Exception;
use FlashExpress\bi\App\Server\PublicServer;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Repository\InterviewRepository;
use FlashExpress\bi\App\Server\StaffServer;
use FlashExpress\bi\App\Server\SysListServer;
use WebGeeker\Validation\Validation;

class InterviewController extends Controllers\ControllerBase
{
    protected $server;
    protected $paramIn;

    public function initialize()
    {
        parent::initialize();

        $this->biUrl   = $this->config->hr->bi_url;
        $this->paramIn = $this->request->getPost();
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
    }

    public function onConstruct()
    {
        parent::onConstruct();
    }


    /**
     * 面试管理 工资列表
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function wagelistAction()
    {
        //[1]入参
        $paramIn = $this->paramIn;
        $this->getDI()->get('logger')->write_log("paramIn".json_encode($paramIn, JSON_UNESCAPED_UNICODE), 'info');
        //[2]业务处理
        $returnArr = (new InterviewServer())->getWageList($paramIn);
        $this->getDI()->get('logger')->write_log("wagelistAction：".json_encode($returnArr, JSON_UNESCAPED_UNICODE),
            'info');
        //[3]数据返回
        $this->jsonReturn($returnArr);
    }


    /**
     * 面试管理 状态列表
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function interviewStateListAction()
    {
        //[2]业务处理
        //$returnArr = (new InterviewServer())->getStateList();

        $returnArr = (new InterviewServer())->getStateList();

        //[3]数据返回
        $this->jsonReturn($returnArr);
    }


    /**
     * 面试管理 简历列表
     * @Access  public
     * @Param   request
     * @Return  jsonData
     * @Permission(action='Interview.interviewResumeList')
     */
    public function interviewResumeListAction()
    {
        //[1]入参
        $paramIn = $this->paramIn;
        $this->getDI()->get('logger')->write_log("paramIn".json_encode($paramIn, JSON_UNESCAPED_UNICODE), 'info');
        $paramIn['staff_id'] = $this->userInfo['id'];
        unset($paramIn['no_page']);
        // 入参校验
        $validations = [];
        if (!empty($paramIn['resume_last_operator'])) {
            $validations['resume_last_operator'] = 'IntLe:99999999|>>>:'.$this->getTranslation()->_('input_staff_id_error');
        }
        $this->validateCheck($paramIn, $validations);

        $returnArr = (new InterviewServer())->interviewList($paramIn, $this->userInfo);
        $this->getDI()->get('logger')->write_log("interviewResumeListAction".json_encode($returnArr,
                JSON_UNESCAPED_UNICODE), 'info');

        //[3]数据返回
        $this->jsonReturn($returnArr);
    }


    /**
     *
     * 是否可以发起薪资审批
     * @Access  public
     * @Param   request
     * @Return  jsonData
     * @Permission(action='Interview.interviewResumeList')
     */
    public function isCanSubmitSalaryApproveAction()
    {
        $paramIn = $this->paramIn;
        $this->getDI()->get('logger')->write_log("paramIn".json_encode($paramIn, JSON_UNESCAPED_UNICODE), 'info');

        $validation = [
            'department_id'   => 'Required|Int', // 部门   快递业务线
            'job_id'          => 'Required|Int', // 职位  是否一线职位
            'in_salary_range' => 'Required|IntIn:1,2', // 是否在薪酬范围内
            'salary'          => 'Required|Int', // 货币
        ];
        $this->validateCheck($paramIn, $validation);

        $interviewServer = new InterviewServer();
        $return          = $interviewServer->isCanSubmitSalaryApprove($paramIn['department_id'], $paramIn['job_id'],
            $paramIn['in_salary_range'], $paramIn['salary']);
        $this->getDI()->get('logger')->write_log("isCanSubmitSalaryApproveAction:".json_encode($return,
                JSON_UNESCAPED_UNICODE), 'info');

        $this->jsonReturn($this->checkReturn(['data' => $return]));
    }

    public function approveSalaryItemsAction()
    {
        $paramIn    = $this->paramIn;
        $validation = [
            'resume_id' => 'Required|Int',
        ];
        $this->validateCheck($paramIn, $validation);
        $this->getDI()->get('logger')->write_log("paramIn".json_encode($paramIn, JSON_UNESCAPED_UNICODE), 'info');
        $interviewServer = new InterviewServer();
        $returnArr       = $interviewServer->approveSalaryItems($paramIn['resume_id']);
        $this->jsonReturn($this->checkReturn($returnArr));
    }


    /**
     * 印尼在用
     */
    public function approveSalaryV2Action()
    {
        $paramIn    = $this->paramIn;
        $validation = [
            'hc_id' => 'Required|Int', // hc id
        ];
        $this->validateCheck($paramIn, $validation);
        $this->getDI()->get('logger')->write_log("paramIn".json_encode($paramIn, JSON_UNESCAPED_UNICODE), 'info');

        $interviewServer = new InterviewServer();
        $returnArr       = $interviewServer->approveSalaryV2($this->userInfo, $paramIn);
        $this->jsonReturn($this->checkReturn($returnArr));
    }

    /**
     *
     * 面试管理 待发offer 申请薪资审批
     */
    public function approveSalaryAction()
    {
        $paramIn    = $this->paramIn;

        $validation = [
            'hc_id' => 'Required|Int', // hc id
        ];
        $this->validateCheck($paramIn, $validation);
        $this->getDI()->get('logger')->write_log("paramIn".json_encode($paramIn, JSON_UNESCAPED_UNICODE), 'info');

        $interviewServer = new InterviewServer();

        $returnArr = $interviewServer->approveSalary($this->userInfo, $paramIn);
        //hr操作淘汰 若客服处理进度等于待处理、延期处理、待审核
        //      点击淘汰简历、将客服处理进度改成=处理终止，并将处理终止原因标记为“招聘HR已处理”
        if (env('country_code') == 'PH') {
            (new CustomerresumeRepository())->getCustomerResumeById($paramIn['resume_id']);
        }
        $this->getDI()->get('logger')->write_log("approveSalaryAction:".json_encode($returnArr, JSON_UNESCAPED_UNICODE),
            'info');

        $this->jsonReturn($returnArr);
    }

    /**
     *
     * 面试管理 撤销 薪资审批
     *
     */
    public function revokeSalaryAction()
    {
        $paramIn = $this->paramIn;

        $returnArr = (new InterviewServer())->revokeSalary($this->userInfo, $paramIn['resume_id'],
            $paramIn['revoke_remark']);

        $this->jsonReturn($returnArr);
    }

    /**
     *
     * 面试管理  获取审批详情
     */
    public function approvalDetailAction()
    {
        $paramIn = $this->paramIn;
        $this->getDI()->get('logger')->write_log("paramIn".json_encode($paramIn, JSON_UNESCAPED_UNICODE), 'info');

        $returnArr = (new InterviewServer())->approvalDetail($this->userInfo, $paramIn['resume_id']);
        $this->getDI()->get('logger')->write_log("approvalDetailAction".json_encode($returnArr, JSON_UNESCAPED_UNICODE),
            'info');

        $this->jsonReturn($returnArr);
    }

    /**
     * 面试管理 简历列表 导出
     * @Access  public
     * @Param   request
     * @Return  jsonData
     * @Permission(action='Interview.interviewResumeListExplot')
     */
    public function interviewResumeListExplotAction()
    {
        $paramIn = $this->paramIn;
        $paramIn['userinfo'] = $this->userinfo;
        $paramIn['lang'] = $this->lang;//语言
        $InterviewServer = Tools::reBuildCountryInstance(new InterviewServer($this->lang));
        $returnCount = $InterviewServer->interviewList(array_merge($paramIn,['is_count' => 1,'page_num'=>1,'page_size'=>1,'no_page'=>false]), $this->userinfo);
        if ($returnCount > enums::EXPORT_SYNC_MAX_NUM_LIMIT) {
            throw new ValidationException($this->t->_('asyn_download_max_num', ['num' => enums::EXPORT_SYNC_MAX_NUM_LIMIT]));
        }
        $paramIn["no_page"] = 1;
        $paramIn['export'] = 1;

        //获取文件名
        $file_name   = 'interviewResume'.'_'.date('Ymd-His').'.xlsx';
        $file_name   = str_replace(' ', '_', $file_name);
        $action_name = 'interview'.WinhrExcelTaskModel::ACTION_NAME_SPACE_SEPARATOR.'interviewResumeListExport';
        //入 task 表 走队列导出
        $data = (new DownLoadTaskService())->insertTask( $this->userinfo['id'], $action_name, $paramIn,$file_name);

        return $this->jsonReturn($data);

    }


    /**
     * 面试管理 简历列表 导出
     * @Access  public
     * @Param   request
     * @Return  jsonData
     * @Permission(action='Interview.interviewResumeListExplot')
     */
    public function interviewResumeFilterListExplotAction()
    {
        //[1]入参
        $paramIn            = $this->paramIn;
        $paramIn["no_page"] = 1;

        //[2]业务处理
        $returnArr = (new InterviewServer())->interviewList($paramIn, $this->userInfo);

        $data = $returnArr['data']['dataList'];
        if (empty($data)) {
            $this->jsonReturn($this->checkReturn(-3, 'empty data'));
        }
        foreach ($data as $k => $v) {
//
            $HcId                  = $v['hc_id'] ?? '';
            $cvId                  = $v['resume_id'] ?? '';
            $historyCvId           = $v['history_hcs'] ?? '';
            $name                  = $v['name'] ?? '';
            $name_en               = $v['name_en'] ?? '';
            $sex                   = $v['sex_title'] ?? '';
            $age                   = $v['age'] ?? '';
            $credentialsNum        = $v['credentials_num'] ?? '';
            $phone                 = $v['phone'] ?? '';
            $job_name              = $v['hc_job_name'] ?? '';
            $alternative_job_names = $v['alternative_job_names'] ?? '';
            $department_name       = $v['hc_department_name'] ?? '';
            $area_name             = $v['sorting_no'] ?? '';
            $work_name             = $v['work_name'] ?? '';
            $worknode_name         = $v['worknode_name'] ?? '';
            $email                 = $v['email'] ?? '';
            $stateName             = $v["state_name"] ?? '';
            $expectJobName         = $v['expect_job_name'] ?? '';
            $resumeCreatedAt       = $v['resume_created_at'] ?? '';
            $submitterId           = $v['submitter_id'] && in_array($v['source'], [2, 3]) ? $v['submitter_id'] : '';
            $recruit_channel_name  = $v['recruit_channel_name'] ? $v['recruit_channel_name'] : '';

            $filter_staff_id   = $v['filter_staff_id'] ?? '';
            $filter_staff_name = $v['filter_staff_name'] ?? '';
            $resume_last_operator_name = $v['resume_last_operator_name'] ?? '';
            $priority_text = $v['priority_text'] ?? '';                                // hc 优先级

            $_data[$k] = [
                $cvId,                      //CVID
                $HcId,                      //HCID
                $historyCvId,               //历史HCID
                $name,                      //姓名
                $name_en,                   //英文名
                $sex,                       //性别
                $age,                       //年龄
                $credentialsNum,            //证件号
                $phone,                     //手机号
                $email,                     //电子邮箱
                $expectJobName,             //期望岗位
                $alternative_job_names,     //备选 岗位
                $stateName,                 //状态
                $job_name,                  //JD名称
                $department_name,           //所属部门
                $area_name,                 //所属区域
                $work_name,                 //工作城市
                $worknode_name,             //所属网点
                $resumeCreatedAt,           //入库时间
                $recruit_channel_name,      //招聘渠道
                $filter_staff_name,          //简历筛选面试官姓名
                $filter_staff_id,            //简历筛选面试官工号
                $submitterId,               //简历创建人
            ];

            if (isCountry('PH') || isCountry('MY')) {
                $_data[$k][] = $resume_last_operator_name;
            }

        }
        $field      = [
            'CVID',
            'HCID',
            $this->getTranslation()->_('8048'),//历史hc
            $this->getTranslation()->_('7022'),//姓名
            $this->getTranslation()->_('8059'),//英文名
            $this->getTranslation()->_('7023'),//性别
            $this->getTranslation()->_('7045'),//年龄
            $this->getTranslation()->_('7503'),//证件号
            $this->getTranslation()->_('7505'),//手机号
            $this->getTranslation()->_('8050'),//电子邮箱
            $this->getTranslation()->_('8051'),//期望岗位
            $this->getTranslation()->_('8049'),//备选岗位

            $this->getTranslation()->_('resume_status'),//状态
            $this->getTranslation()->_('7001'),//JD名称
            $this->getTranslation()->_('7002'),//所属部门
            $this->getTranslation()->_('7031'),//所属区域
            $this->getTranslation()->_('7010'),//工作城市
            $this->getTranslation()->_('7043'),//所属网点

            $this->getTranslation()->_('7047'),//入库时间

            $this->getTranslation()->_('42003'),//招聘渠道
            $this->getTranslation()->_('filter_staff_name'),//简历筛选面试官姓名
            $this->getTranslation()->_('filter_staff_id'),//简历筛选面试官工号
            $this->getTranslation()->_('8116'),//简历创建人,
        ];

        if (isCountry('PH') || isCountry('MY')) {
            $field[] = $this->getTranslation()->_('principal_staff_name');
        }

        $file_path  = Excel::exportExcel($field, $_data, 'resumeFilterRecommendlist'.time());
        $file_path  = $file_path ? $file_path['object_url'] : '';
        $returnData = [
            'code' => 1,
            'msg'  => '',
            'data' => [
                'file_url' => $file_path,
            ],
        ];

        //[3]数据返回
        $this->jsonReturn($returnData);
    }


    /**
     * 面试管理 HC列表
     * @Access  public
     * @Param   request
     * @Return  jsonData
     * @Permission(action='Interview.interviewHcList')
     */
    public function interviewHcListAction()
    {
        //[1]入参
        $paramIn = $this->paramIn;
        $this->getDI()->get('logger')->write_log("paramIn".json_encode($paramIn, JSON_UNESCAPED_UNICODE), 'info');

        $paramIn['staff_id'] = $this->userinfo['staff_id'];

        // 入参校验
        if (isset($paramIn['hc_id']) && $paramIn['hc_id']) {
            $validations = [
                "hc_id" => "Required|Int|>>>:".$this->getTranslation()->_('HcID error'),
            ];
            $this->validateCheck($paramIn, $validations);
        }

        $validations = [
            'staff_id'                 => "Int|>>>:staff_id",
            'page_size'                => "Int|>>>:page_size",
            'page_num'                 => "Int|>>>:page_num",
            'department_id'            => "Int|>>>:department_id",
            'job_id'                   => "Int|>>>:job_id",
            'city_code'                => "StrLenGeLe:1, 100|>>>:city_code",
            'state_code'               => "Int|>>>:state_code",
            'order_field'              => "StrLenGeLe:1, 100|>>>:order_field",
            'sort'                     => "Int|>>>:sort",
            'store_id'                 => "StrLenGeLe:1, 100|>>>:store_id",
            'export'                   => "Int|>>>:export",
            'reason_type'              => "Int|>>>:reason_type",
            'job_title_id'             => "Int|>>>:job_title_id",
            'filter_job_title_is_null' => "Int|>>>:filter_job_title_is_null",
        ];

        if (isset($paramIn['no_page'])) {
            $validations['no_page'] = "BoolSmart|>>>:no_page";
        }

        $this->validateCheck($paramIn, $validations);

        //[2]业务处理
        $returnArr = (new InterviewServer())->interviewHcList($paramIn);
        $this->getDI()->get('logger')->write_log("interviewHcListAction".json_encode($returnArr,
                JSON_UNESCAPED_UNICODE), 'info');

        //[3]数据返回
        return $this->jsonReturn($returnArr);
    }

    /**
     * 面试管理 HC列表 导出
     * @Access  public
     * @Param   request
     * @Return  jsonData
     * @Permission(action='Interview.interviewHcListExport')
     */
    public function interviewHcListExportAction()
    {
        //[1]入参
        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        $paramIn['export']   = 1;

        //[2]业务处理
        $returnArr = (new InterviewServer())->interviewHcList($paramIn);

        //[3]数据返回
        $fileName = 'InterviewHClist.'.time().rand(1000, 9999);
        $data     = $returnArr['data']['dataList'];
        if (empty($data)) {
            $this->jsonReturn($this->checkReturn(-3, 'empty data'));
        }
        foreach ($data as $k => $v) {
            //hcid
            $_hcId = $v["hc_id"] ?? "";
            // 雇佣类型
            $_hire_type_text = $v["hire_type_text"] ?? "";
            //优先级
            $_priorityName = $v["priority_name"] ?? "";
            //JD名称
            $_jobName = $v["job_name"] ?? "";
            //所属部门
            $_departmentName = $v["department_name"] ?? "";
            //工作城市
            $_cityName = $v["city_name"] ?? "";
            //所属网点
            $_worknodeName = $v["worknode_name"] ?? "";
            //所属区域
            $_areaName = $v["sorting_no"] ?? "";
            //HC创建时间
            $_hcCreateTime = $v["createtime"] ?? "";
            //截止日期
            $_expirationdate = $v["expirationdate"] ?? "";
            //期望人数
            $_demandnumber = $v["demandnumber"] ?? "";
            //已招人数
            $_successCou = $v["success_cou"] ?? "";
            //空缺人数
            $_surplusnumber = $v["surplusnumber"] ?? "";
            //面试中人数
            $_subscribeCou = $v["subscribe_cou"] ?? "";
            //审批完成时间
            $_hcApprovalCompletionTime = $v["approval_completion_time"] ?? "";
            //已招满时间
            $_hcFullTime = $v["hc_full_time"] ?? "";
            //淘汰人数
            $_weedOut = $v["weed_out"] ?? "";
            //状态
            $_stateName = $v["state_name"] ?? "";

            $_working_day_rest_type_text = $v['working_day_rest_type'] ?? '';
            $working_day_rest_type_text  = '';
            if (!empty($_working_day_rest_type_text)) {
                $working_day_rest_type = explode(',', $_working_day_rest_type_text);
                array_walk($working_day_rest_type, function (&$val) {
                    $val = $this->getTranslation()->_('working_day_rest_type_'.$val);
                });
                $working_day_rest_type_text = implode(',', $working_day_rest_type);
            }

            $_data[$k] = [
                $_hcId,
                $_priorityName,
                $_jobName,
                $v["job_title_name"] ?? "", //职位名称
                $_departmentName,
                $_cityName,
                $_worknodeName,
                $v["manage_region_name"] ?? "", //大区
                $v["manage_piece_name"] ?? "", //片区
                $_hcCreateTime,
                $_expirationdate,
                $working_day_rest_type_text,
                $_demandnumber,
                $_successCou,
                $_surplusnumber,
                $_subscribeCou,
                $_hcApprovalCompletionTime,
                $_hcFullTime,
                $_weedOut,
                $_stateName,
            ];
            if (isCountry(["MY","TH","PH"])) {
                $_data[$k][] = $_hire_type_text;
            }
        }
        $field      = [
            'HCID',
            $this->getTranslation()->_('4032'),//优先级
            $this->getTranslation()->_('7001'),//JD名称
            $this->getTranslation()->_('current_position'),//职位名称
            $this->getTranslation()->_('7002'),//所属部门
            $this->getTranslation()->_('7010'),//工作城市
            $this->getTranslation()->_('7003'),//所属网点
            $this->getTranslation()->_('area_region'),//所属大区
            $this->getTranslation()->_('area_piece'),//所属片区
            $this->getTranslation()->_('7014'),//HC创建时间
            $this->getTranslation()->_('7004'),//截止日期
            $this->getTranslation()->_('working_day_rest_type'),//工作天数&轮休规则
            $this->getTranslation()->_('7009'),//期望人数
            $this->getTranslation()->_('7011'),//已招人数
            $this->getTranslation()->_('7012'),//空缺人数
            $this->getTranslation()->_('7013'),//面试中人数
            $this->getTranslation()->_('7015'),//审批完成时间
            $this->getTranslation()->_('7016'),//已招满时间
            $this->getTranslation()->_('7018'),//淘汰人数
            $this->getTranslation()->_('7008'),//状态
        ];
        if (isCountry(["MY","TH","PH"])){
            $field[] = $this->getTranslation()->_('hire_type');
        }
        $header     = $field;
        $data       = $_data;
        $file_path  = Excel::exportExcel($header, $data, 'interviewHcList'.time());
        $fileUrl    = $file_path ? $file_path['object_url'] : '';
        $returnData = [
            'code' => 1,
            'msg'  => '',
            'data' => [
                'file_url' => $fileUrl,
            ],
        ];

        return $this->jsonReturn($returnData);
    }

    /**
     * 获取城市接口
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function getAddressListAction()
    {
        //[1]入参
        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];

        //[2]业务处理
        $returnArr = (new InterviewServer())->getAddressList($paramIn);

        //[3]数据返回
        $this->jsonReturn($returnArr);
    }

    /**
     * 获取岗位名称
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function getJobNameListAction()
    {
        //[1]入参
        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];

        //[2]业务处理
        $returnArr = (new FlashJdServer())->getJobList($paramIn);

        //[3]数据返回
        $this->jsonReturn($returnArr);
    }

    /**
     * 面试HC获取状态接口
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function getStateListAction()
    {
        //[1]入参
        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];

        //[2]业务处理
        $returnArr = (new InterviewServer())->getStateList($paramIn);

        //[3]数据返回
        $this->jsonReturn($returnArr);
    }


    /**
     *
     * 文本短信发送
     *
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function sendSmsAction()
    {
        $paramIn = $this->paramIn;
        //面试link
        $interview_url        = $this->processingDefault($paramIn, 'interview_url', 1);
        $paramIn['hr_remark'] = $this->processingDefault($paramIn, 'hr_remark', 1);
        $paramIn['ope_id']    = $this->processingDefault($paramIn, 'ope_id', 1);
        //验证简历ID
        $this->validateCheck($paramIn, ["resume_id" => "Required|Int"]);

        if (isset($paramIn['hr_remark']) && !empty($paramIn['hr_remark'])) {
            $this->validateCheck($paramIn,
                ["hr_remark" => "StrLenGeLe:0,200|>>>: {$this->getTranslation()->_('remark_error')}"]);
        }

        $phoneEmail = isset($paramIn['phone_email']) ? $paramIn['phone_email'] : '';//1:邮件 2:短信 3:邮件&&短信

        $automsg_category = isset($paramIn['automsg_category']) ? $paramIn['automsg_category'] : '';//1:offer  0 面试通知

        //发送offer通知
        if ($automsg_category == 1) {
            $this->sendOffer($paramIn);
        } else {
            $this->validateCheck($paramIn, ["subscribe_id" => "Required|Int"]);
        }

        //邮箱验证
        if (!empty($paramIn['email'])) {
            //

            if (!filter_var($paramIn['email'], FILTER_VALIDATE_EMAIL)) {
                return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('email_is_error')));
            }
        }

        //必须选择一种发送方式
        if (!isset($phoneEmail) && empty($phoneEmail)) {
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('send_type')));
        }
        //邮箱验证
        if ($paramIn['email']) {
            if (!filter_var($paramIn['email'], FILTER_VALIDATE_EMAIL)) {
                return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('email_is_error')));
            }
        }

        if ($phoneEmail == 2 or $phoneEmail == 3) {
            //校验参数
            $validations = [
                "phone_area_code" => "IfExist:mobile|Required|StrLenGe:1|>>>:"."miss phone Area Code",
            ];
            $this->validateCheck($paramIn, $validations);
        }

        $currentModelAreaCode = $this->getDI()->get('config')['application']['mobile_area_code'];
        $phoneAreaCode        = $paramIn['phone_area_code'] ?? "";
        $hasMobile            = isset($paramIn['mobile']) && $paramIn['mobile'] && $phoneAreaCode == $currentModelAreaCode;
        $hasEmail             = isset($paramIn['email']) && $paramIn['email'];
        //短信||邮箱必选其一
        if (!$hasEmail && !$hasMobile) {
            return $this->jsonReturn(self::checkReturn(-3, "Send msg to email or mobile Required"));
        }


        $stat = [];
        $str  = '';
        //求职者 sendtype 仅发送求职者 3：两者都发送
        if ($paramIn['sendtype'] == 1 || $paramIn['sendtype'] == 3) {
            //发送邮件
            if ($hasEmail && $phoneEmail != 2) {
                $tomail    = $paramIn['email'];
                $title     = "flashexpress company sends you an invitation";
                $content   = str_replace(' ', '&nbsp;', $paramIn['content']);
                $content   = str_replace("\r\n", "<br/>", $content);
                $content   .= '<br/><br/> ['.$this->userInfo['name'].']';
                $sendEmail = (new MailServer())->send_mail('default', $tomail, $title, $content);
                if (!(isset($sendEmail) && $sendEmail)) {
                    $this->getDI()->get('logger')->write_log("sendSms: 发送邮件失败-".json_encode($paramIn,
                            JSON_UNESCAPED_UNICODE), 'notice');
                    $stat['email'] = 0;
                    $str           .= " email:{$paramIn['email']} failed  ";
                } else {
                    $this->getDI()->get('logger')->write_log("sendSms: 发送邮件成功-".json_encode($paramIn,
                            JSON_UNESCAPED_UNICODE), 'info');
                    $stat['email'] = 1;
                    $str           .= " email:{$paramIn['email']} success  ";
                }

                //是否需要保存email信息
                $_temp['id']    = $paramIn['resume_id'];
                $_temp['email'] = $paramIn['email'];
                $this->saveToResume($_temp);
            }
            //发送短信
            if ($hasMobile && $phoneEmail != 1) {
                $InterviewServer = new InterviewServer();
                // 保存短信中的内容
                $hrSmsModel              = new HrSmsLogModel();
                $hrSmsModel->sms_type    = HrSmsLogModel::SMS_TYPE_INTERVIEW;
                $hrSmsModel->origin_id   = $paramIn['resume_id'];
                $hrSmsModel->area_code   = $phoneAreaCode;
                $hrSmsModel->mobile      = $paramIn['mobile'];
                $hrSmsModel->content     = $paramIn['content'];
                $hrSmsModel->operator_id = $paramIn['ope_id'];
                $hrSmsModel->save();
                // 拼接短链接短信内容
                $content = $InterviewServer->smsContent($paramIn['resume_id'], $paramIn['type'] ?? 'EN',
                    $hrSmsModel->id);

                $result = ['MY 预约面试（通知候选人） 不发送短信'];//21180【MY】SMS短信去掉短链接
                if(!isCountry('MY')) {
                    $result  = $InterviewServer->sendSms($paramIn['mobile'], $content ?? $paramIn['content']);

                }

                $this->getDI()->get("logger")->write_log("sendSms: ".json_encode($paramIn,
                        JSON_UNESCAPED_UNICODE).json_encode($result, JSON_UNESCAPED_UNICODE), "info");
                if ($result) {
                    $stat['mobile'] = 1;
                    $str            .= " mobile:{$paramIn['mobile']} success  ";
                } else {
                    $str            .= " mobile:{$paramIn['mobile']} failed  ";
                    $stat['mobile'] = 0;

                    $this->getDI()->get("logger")->write_log("sendSms: 短信发送失败-".json_encode($paramIn,
                            JSON_UNESCAPED_UNICODE), "notice");
                }
            }
        }

        //sendtype:2 仅发送面试官  3:两者都发送 backyard消息
        if ($paramIn['sendtype'] == 2 || $paramIn['sendtype'] == 3) {
            //backyard 消息发送
            $msg['staff_info_id'] = $paramIn['interviewer_info']['interviewer_id'];  //面试官staff_id

            if (isset($paramIn['is_edit']) && $paramIn['is_edit'] == 1) {
                $msg['message_title'] = $this->getTranslation()->_('edit_interview_msg');
                $interview_set        = $this->getTranslation()->_('interview_update_set');
            } else {
                $msg['message_title'] = $this->getTranslation()->_('interview_arrange_title');
                $interview_set        = $this->getTranslation()->_('interview_set');
            }

            //查询面试记录
            $subscribe_detail = (new InterviewRepository)->subscribeDetail($paramIn);
            //获取HR相关信息
            $hr_info = (new StaffRepository())->getStaffInfoById($subscribe_detail['staff_id']);
            //求职者信息
            $resume_info = (new InterviewRepository())->getResumeByInterviewId($subscribe_detail['interview_id']);
            $resume_info = $resume_info ? $resume_info[0] : [];
            //
            $subscribe_and_title = (new \FlashExpress\bi\App\Repository\HrInterviewSubscribeRepository())->getSubscribeDetail($this->paramIn['subscribe_id']);
            //发送内容
            $msg_name               = $hr_info['nick_name'] ?: $hr_info['name'];
            $s_name                 = empty($resume_info['name']) ? $resume_info['first_name_en'].' '.$resume_info['last_name_en'] : $resume_info['name'];
            $msg['message_content'] = str_replace(
                ["{nick_name}", "{staff_info_id}", "{name}", "{cvid}", "{job_name}"],
                [
                    $msg_name,
                    $hr_info['staff_info_id'],
                    $s_name,
                    $resume_info['cvid'],
                    $subscribe_and_title['job_name'],
                ],
                $interview_set
            );
            //push 屏幕通知
            $msg['message_params'] = 'message_list'; //通知跳转地址
            $push_res              = (new PublicServer())->pushMessageToScreen($msg);
            //发送站内信
            $msg['message_content'] .= "|||{$subscribe_detail['interview_id']},{$this->paramIn['subscribe_id']},{$this->paramIn['ope_id']}";
            $msg['type']            = $paramIn['interviewer_info']['interviewer_id'];

            $msg['category']      = 41; //面试通知
            $msg['category_code'] = 0; //通知类型

            $res = (new PublicServer())->pushMessageToInterviewer($msg);

            //记录操作
            $this->getDI()->get("logger")->write_log("sendSms-backyard：pushMessageToScreen".json_encode($msg,
                    JSON_UNESCAPED_UNICODE)." push res :".json_encode($push_res, JSON_UNESCAPED_UNICODE), 'info');


            $level  = $res ? 'info' : 'notice';
            $by_msg = $res ? 1 : 0;

            if ($by_msg == 0) {
                $str .= " interviewer:{$msg['staff_info_id']} failed  ";
            } else {
                $str .= " interviewer:{$msg['staff_info_id']} success  ";
            }
            //记录操作
            $stat['interviewer'] = $by_msg;
            $this->getDI()->get("logger")->write_log("sendSms-backyard ".json_encode($paramIn, JSON_UNESCAPED_UNICODE),
                $level);
        }

        $code = in_array(0, array_values($stat)) ? 0 : 1;

        //修改hr备注
        $update_remark = (new InterviewRepository())->updateSubscribeById($paramIn['subscribe_id'],
            ['hr_remark' => $paramIn['hr_remark']]);
        if (!$update_remark) {
            $this->getDI()->get("logger")->write_log("hr添加预约面试备注-失败 ".json_encode($paramIn, JSON_UNESCAPED_UNICODE),
                'notice');
        }

        //面试官安排记录
        $subscribe_and_title        = (new \FlashExpress\bi\App\Repository\HrInterviewSubscribeRepository())->getSubscribeDetail($this->paramIn['subscribe_id']);
        $params['interview_sub_id'] = $this->paramIn['subscribe_id']; //面试ID;
        $params['interviewer_id']   = $subscribe_and_title['interviewer_id'];//面试官ID;
        $params['interview_url']    = $interview_url;//面试官ID;
        $params['hr_remark']        = $paramIn['hr_remark'];//hr备注;
        $updateInterviewInfo        = (new InterviewRepository())->updateInterviewerInfo($params);
        if (!$updateInterviewInfo) {
            $this->getDI()->get("logger")->write_log("hr_interviewer_operation error".json_encode($params,
                    JSON_UNESCAPED_UNICODE), 'notice');
        }


        $data = ['code' => $code, 'msg' => $str, 'data' => $stat];
        return $this->jsonReturn($data);
    }


    /**
     * 发送offer操作相关通知
     */
    private function sendOffer($paramIn)
    {
        //校验参数
        $validations = [
            "phone_area_code" => "IfExist:mobile|Required|StrLenGe:1|>>>:"."miss phone Area Code",
        ];
        $this->validateCheck($paramIn, $validations);

        $currentModelAreaCode = $this->getDI()->get('config')['application']['mobile_area_code'];
        $phoneAreaCode        = $paramIn['phone_area_code'] ?? "";
        $hasMobile            = isset($paramIn['mobile']) && $paramIn['mobile'] && $phoneAreaCode == $currentModelAreaCode;
        $hasEmail             = isset($paramIn['email']) && $paramIn['email'];

        if (!$hasMobile && $hasEmail) {
            $tomail    = $paramIn['email'];
            $title     = "flashexpress company sends you an invitation";
            $content   = $paramIn['content'];
            $sendEmail = (new MailServer())->send_mail('default', $tomail, $title, $content);
            if (!(isset($sendEmail) && $sendEmail)) {
                $this->getDI()->get('logger')->write_log("radar send email,email:{$tomail},type:{$title} 发送邮件失败，请检查邮箱是否有效！",
                    'notice');
                $data = [
                    'code' => -3,
                    'msg'  => "Send msg to email {$tomail} failed  ",
                    'data' => $sendEmail,
                ];
                return $this->jsonReturn($data);
            } else {
                $this->getDI()->get('logger')->write_log("radar send email 发送邮件成功", 'info');
                $data = [
                    'code' => 1,
                    'msg'  => "Send msg to email {$tomail} success  ",
                ];
                return $this->jsonReturn($data);
            }
        } elseif ($hasMobile) {
            $InterviewServer = new InterviewServer();
            // 保存短信中的内容
            $hrSmsModel              = new HrSmsLogModel();
            $hrSmsModel->sms_type    = HrSmsLogModel::SMS_TYPE_OFFER;
            $hrSmsModel->origin_id   = $paramIn['resume_id'];
            $hrSmsModel->area_code   = $phoneAreaCode;
            $hrSmsModel->mobile      = $paramIn['mobile'];
            $hrSmsModel->content     = $paramIn['content'];
            $hrSmsModel->operator_id = $paramIn['ope_id'];
            $hrSmsModel->save();

            $content = $InterviewServer->offerSmsContent($paramIn['resume_id'], $hrSmsModel->id);
            $result = ['MY send offer 不发送短信'];//21180【MY】SMS短信去掉短链接
            if(!isCountry('MY')) {
                $result  = $InterviewServer->sendSms($paramIn['mobile'], $content ?? $paramIn['content']);
            }

            $this->getDI()->get("logger")->write_log("sendSmsContent ".$content,'info');
            
            $this->getDI()->get("logger")->write_log("sendSms ".json_encode($paramIn,
                    JSON_UNESCAPED_UNICODE).json_encode($result, JSON_UNESCAPED_UNICODE), "info");
            $data = [
                'code' => 1,
                'msg'  => "Send msg to mobile {$paramIn['mobile']} success  ",
            ];
            return $this->jsonReturn($data);
        } else {
            return $this->jsonReturn(self::checkReturn(-3, "Send msg to email or mobile Required"));
        }
    }


    /**
     * 保存email至简历
     * @param type $paramIn
     *
     */
    private function saveToResume($paramIn)
    {
        $resumeRepository = new ResumeRepository();
        //获取简历信息
        $resumeInfo = $resumeRepository->resumeInfo($paramIn['id']);
        if ($resumeInfo && empty($resumeInfo['emial'])) {
            if ($resumeRepository->updateResumeInfo($paramIn)) {
                $this->getDI()->get('logger')->write_log("saveToResume 保存email至简历ID: {$paramIn['id']}", 'info');
            }
        }
    }

    /**
     *
     * 发送offer短信模板
     *
     * @Access  public
     * @Param   request
     * @Return  jsonData
     *
     */
    public function getSendOfferSmsTplAction()
    {
        $paramIn = $this->paramIn;
        $this->validateCheck($paramIn, ["resume_id" => "Required|Int"]);
        if (isset($paramIn['offer_type']) && $paramIn['offer_type'] == 'not_front_line_job') {
            $result = (new InterviewServer())->getNotFrontLineJobSendOfferEmailTpl($paramIn['resume_id']);
        } else {
            $result = (new InterviewServer())->getSendOfferSmsTpl($paramIn['resume_id']);
        }

        $this->jsonReturn(self::checkReturn(["data" => $result]));
    }


    /**
     *
     * 获取预约面试短信通知模板
     *
     * @Access  public
     * @Param   request
     * @Return  jsonData
     *
     *
     */
    public function getSmsTplAction()
    {
        $paramIn = $this->paramIn;

        $this->validateCheck($paramIn, ["resume_id" => "Required|Int"]);
        $result = (new InterviewServer())->getReserveSmsTpl($paramIn['resume_id']);


        $this->jsonReturn(self::checkReturn(["data" => $result]));
    }

    /**
     * 面试管理搜索导航
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function interviewSearchBarAction()
    {
        //[1]入参
        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];

        //[2]业务处理
        $returnArr = (new InterviewServer())->interviewSearchBar($paramIn);

        //[3]数据返回
        $this->jsonReturn($returnArr);
    }

    /**
     * 预约面试添加
     * @Access  public
     * @Param   request
     * @Return  jsonData
     * @Permission(action='Interview.addInterviewAppointment')
     */
    public function addInterviewAppointmentAction()
    {
        //[1]入参
        $paramIn = $this->paramIn;
        $this->getDI()->get('logger')->write_log("paramIn".json_encode($paramIn, JSON_UNESCAPED_UNICODE), 'info');

        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        $validations         = [
            "staff_id"       => "Required|Int",
            "hc_id"          => "Required|Int|>>>:".$this->getTranslation()->_('4412'),
            "resume_id"      => "Required|Int|>>>:".$this->getTranslation()->_('4413'),
            "interview_time" => "Required|DateTime|>>>:".$this->getTranslation()->_('4402'),
            "detail_address" => "Required|StrLenGeLe:1,500|>>>:".$this->getTranslation()->_('4411'),
        ];
        $this->validateCheck($paramIn, $validations);

        $lock_key = $paramIn['hc_id'].'-'.$paramIn['resume_id'];
        //申请操作
        $returnArr = $this->atomicLock(function () use ($paramIn) {
            return (new InterviewServer())->addOrUpdateInterviewAppointment($paramIn);
        }, 'addInterviewAppointment:'.$lock_key, 300);

        if ($returnArr === false) { //没有获取到锁
            $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('5202')));
        }

        //[3]数据返回
        $this->jsonReturn($returnArr);
    }

    /**
     * 预约面试修改
     * @Access  public
     * @Param   request
     * @Return  jsonData
     * @Permission(action='Interview.editInterviewAppointment')
     */
    public function editInterviewAppointmentAction()
    {
        //[1]入参
        $paramIn = $this->paramIn;
        $this->getDI()->get('logger')->write_log("paramIn".json_encode($paramIn, JSON_UNESCAPED_UNICODE), 'info');

        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        $validations         = [
            "staff_id"       => "Required|Int",
            "hc_id"          => "Required|Int|>>>:".$this->getTranslation()->_('4412'),
            "resume_id"      => "Required|Int|>>>:".$this->getTranslation()->_('4413'),
            "interview_time" => "Required|DateTime|>>>:".$this->getTranslation()->_('4402'),
            "detail_address" => "Required|StrLenGeLe:1,500|>>>:".$this->getTranslation()->_('4411'),
            "subscribe_id"   => "Required|Int|>>>:".$this->getTranslation()->_('4418'),
        ];
        $this->validateCheck($paramIn, $validations);

        //[2]业务处理
        $returnArr = (new InterviewServer())->addOrUpdateInterviewAppointment($paramIn);

        //[3]数据返回
        $this->jsonReturn($returnArr);
    }

    /**
     * 预约详情
     * @Access  public
     * @Param   request
     * @Return  jsonData
     * @Permission(action='Interview.interviewAppointmentDetail')
     */
    public function interviewAppointmentDetailAction()
    {
        //[1]入参
        $paramIn = $this->paramIn;
        $this->getDI()->get('logger')->write_log("paramIn".json_encode($paramIn, JSON_UNESCAPED_UNICODE), 'info');

        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        $validations         = [
            "staff_id"     => "Required|Int",
            "subscribe_id" => "Required|Int|>>>:".$this->getTranslation()->_('4418'),
        ];
        $this->validateCheck($paramIn, $validations);

        //[2]业务处理
        $returnArr = (new InterviewServer())->interviewAppointmentDetail($paramIn);

        //[3]数据返回
        $this->jsonReturn($returnArr);
    }

    /**
     * 取消面试
     * @Access  public
     * @Param   request
     * @Return  jsonData
     * @Permission(action='Interview.interviewAppointmentCancel')
     */
    public function interviewAppointmentCancelAction()
    {
        //[1]入参
        $paramIn = $this->paramIn;
        $this->getDI()->get('logger')->write_log("paramIn".json_encode($paramIn, JSON_UNESCAPED_UNICODE), 'info');

        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        $validations = [
            "staff_id"       => "Required|Int",
            "subscribe_id"   => "Required|Int|>>>:" . $this->getTranslation()->_('4418'),
//            "ope_id"         => "Int",
            "interview_back" => "Int",
        ];
        $this->validateCheck($paramIn, $validations);

        //[2]业务处理
        $returnArr = (new InterviewServer())->interviewAppointmentStatus($paramIn);

        //[3]数据返回
        $this->jsonReturn($returnArr);
    }

    /**
     * 操作历史
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function operatingHistoryAction()
    {
        //[1]入参
        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        $validations         = [
            "staff_id"     => "Required|Int",
            "interview_id" => "Required|Int|>>>:".$this->getTranslation()->_('4420'),
        ];
        $this->validateCheck($paramIn, $validations);
        //[2]业务处理
        $returnArr = (new InterviewServer())->operatingHistory($paramIn);

        //[3]数据返回
        $this->jsonReturn($returnArr);
    }


    /**
     * 发送offer 第二版本 印尼在使用
     * @return void
     * @throws Exception
     * @Permission(action='Interview.sendOffer')
     */
    public function sendOfferV2Action()
    {
        $paramIn = $this->paramIn;
        $this->getDI()->get('logger')->write_log("paramIn ".json_encode($paramIn, JSON_UNESCAPED_UNICODE), "info");

        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        $redisKey            = 'lock_send_offer_'.$paramIn['resume_id'];
        $validations         = [
            "interview_id"    => "Required|Int",
            "resume_id"       => "Required|Int",
            'hc_id'           => "Required|Int",
            'job_title_grade' => "Required|IntGe:0",//职级
            "work_time"       => "Required|DateTime|>>>:work_time".$this->getTranslation()->_('4702'), // 到岗时间
            'in_salary_range' => 'Required|IntIn:1,2', // 是否在薪酬范围内
            'money'           => "Required",
            'trial_salary'    => "Required",
        ];

        if (isset($paramIn['hire_type']) && $paramIn['hire_type']) {
            // 雇佣类型
            $hire_type = (new SysListServer())->getHireType();
            $hire_type_arr = array_column($hire_type,"key");
            $validations['hire_type'] = "Required|IntIn:".implode(',',$hire_type_arr);
        }
        $this->validateCheck($paramIn, $validations);
        $returnArr = $this->atomicLock(function () use ($paramIn) {
            return (new InterviewServer())->sendOfferV2($paramIn);
        }, $redisKey, 60);

        if ($returnArr === false) {
            $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('please try again')));
        }
        $this->jsonReturn($returnArr);
    }


    /**
     * 发送offer
     * @return mixed
     * @Permission(action='Interview.sendOffer')
     */
    public function sendOfferAction()
    {
        //[1]入参
        $paramIn = $this->paramIn;
        $this->getDI()->get('logger')->write_log("paramIn".json_encode($paramIn, JSON_UNESCAPED_UNICODE), 'info');

        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        $redisKey            = 'lock_send_offer_'.$paramIn['resume_id'];

        $validations = [
            "interview_id"    => "Required|Int",
            "resume_id"       => "Required|Int",
            'hc_id'           => "Required|Int",
//                'money'          => "Required|Int",
//                'staff_job_type' => "Required|Int",
//            'job_title_grade' => "Required|IntGe:0",//职级
//                'train_place'    => "Required",
            "work_time"       => "Required|DateTime|>>>:work_time".$this->getTranslation()->_('4702'),
//                'in_salary_range' => 'Required|IntIn:1,2', // 是否在薪酬范围内
        ];
        if (isset($paramIn['hire_type']) && $paramIn['hire_type'] != HrStaffInfoModel::HIRE_TYPE_AGENT) {
            $validations['job_title_grade'] = "Required|IntGe:0";
        }
        if (isset($paramIn['hire_type']) && $paramIn['hire_type']) {
            // 雇佣类型
            $hire_type = (new SysListServer())->getHireType();
            $hire_type_arr = array_column($hire_type,"key");
            $validations['hire_type'] = "Required|IntIn:".implode(',',$hire_type_arr);
        }
        $this->validateCheck($paramIn, $validations);


        $cache = $this->getDI()->get('redis');
        if ($cache->get($redisKey)) {
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('please try again')));
        }
        $cache->save($redisKey, 1, 60); //加锁


        //[2]业务处理
        $returnArr = (new InterviewServer())->sendOffer($paramIn);

        //释放锁
        $cache->delete($redisKey);
        //[3]数据返回
        $this->jsonReturn($returnArr);
    }

    /**
     * 取消offer
     * @Access  public
     * @Param   request
     * @Return  jsonData
     * @Permission(action='Interview.cancelSendOffer')
     */
    public function cancelSendOfferAction()
    {
        //[1]入参
        $paramIn = $this->paramIn;
        $this->getDI()->get('logger')->write_log("paramIn".json_encode($paramIn, JSON_UNESCAPED_UNICODE), 'info');

        $paramIn['staff_id'] = $this->userInfo['staff_id'];
        $validations         = [
            "id" => "Required|Int",
        ];
        $this->validateCheck($paramIn, $validations);

        $redisKey = 'lock_cancel_offer_'.$paramIn['id'];
        $cache    = $this->getDI()->get('redis');
        if ($cache->get($redisKey)) {
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('please try again')));
        }
        $cache->save($redisKey, 1, 60); //加锁

        //[2]业务处理
        $returnArr = (new InterviewServer())->cancelSendOffer($paramIn);

        //释放锁
        $cache->delete($redisKey);

        //[3]数据返回
        $this->jsonReturn($returnArr);
    }

    /**
     * offer详情
     * @Access  public
     * @Param   request
     * @Return  jsonData
     * @Permission(action='Interview.infoInterviewOffer')
     */
    public function infoInterviewOfferAction()
    {
        //[1]入参
        $paramIn = $this->paramIn;
        $this->getDI()->get('logger')->write_log("paramIn".json_encode($paramIn, JSON_UNESCAPED_UNICODE), 'info');

        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        $validations         = [
            "id" => "Required|Int",
        ];
        $this->validateCheck($paramIn, $validations);

        //[2]业务处理
        $returnArr = (new InterviewServer())->infoInterviewOffer($paramIn, $this->userinfo);
        $this->getDI()->get('logger')->write_log("infoInterviewOfferAction".json_encode($returnArr,
                JSON_UNESCAPED_UNICODE), 'info');

        //[3]数据返回
        $this->jsonReturn($returnArr);
    }

    /**
     * offer修改
     * @Access  public
     * @Param   request
     * @Return  jsonData
     * @Permission(action='Interview.offerEdit')
     */
    public function offerEditV2Action()
    {
        $paramIn = $this->paramIn;
        $this->getDI()->get('logger')->write_log("paramIn".json_encode($paramIn, JSON_UNESCAPED_UNICODE), 'info');

        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        $validations         = [
            "id"              => "Required|Int",
            "interview_id"    => "Required|Int",
            "resume_id"       => "Required|Int",
            'hc_id'           => "Required|Int",
            'job_title_grade' => "Required|IntGe:0",//职级
            "work_time"       => "Required|DateTime|>>>:work_time".$this->getTranslation()->_('4702'),
            'in_salary_range' => 'Required|IntIn:1,2', // 是否在薪酬范围内
            'money'           => "Required",
            'trial_salary'    => "Required",
        ];

        if (isset($paramIn['hire_type']) && $paramIn['hire_type']) {
            $validations['hire_type'] = "IntIn:1,2,3,4,5,13";
        }

        $this->validateCheck($paramIn, $validations);
        //[2]业务处理
        $returnArr = (new InterviewServer())->offerEditV2($paramIn);
        //[3]数据返回
        $this->jsonReturn($returnArr);
    }


    /**
     * offer修改
     * @Access  public
     * @Param   request
     * @Return  jsonData
     * @Permission(action='Interview.offerEdit')
     */
    public function offerEditAction()
    {
        //[1]入参
        $paramIn = $this->paramIn;
        $this->getDI()->get('logger')->write_log("paramIn".json_encode($paramIn, JSON_UNESCAPED_UNICODE), 'info');

        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        // 优化一下提示 群里bug
        if ($paramIn['money'] == '*****'){
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('no_permission')));
        }
        $validations         = [
            "id"              => "Required|Int",
            "interview_id"    => "Required|Int",
            "resume_id"       => "Required|Int",
            'hc_id'           => "Required|Int",
//            'money'          => "Required|Int",
//            'job_title_grade' => "Required|IntGe:0",
            'staff_job_type'  => "Int",
            "work_time"       => "Required|DateTime|>>>:work_time".$this->getTranslation()->_('4702'),
        ];

        if (isset($paramIn['hire_type']) && $paramIn['hire_type'] != HrStaffInfoModel::HIRE_TYPE_AGENT){
            $validations['job_title_grade'] = "Required|IntGe:0";
        }

        if (isset($paramIn['hire_type']) && $paramIn['hire_type']) {
            // 雇佣类型
            $hire_type = (new SysListServer())->getHireType();
            $hire_type_arr = array_column($hire_type,"key");
            $validations['hire_type'] = "Required|IntIn:".implode(',',$hire_type_arr);
        }

        $this->validateCheck($paramIn, $validations);

        //[2]业务处理
        $returnArr = (new InterviewServer())->offerEdit($paramIn);

        //[3]数据返回
        $this->jsonReturn($returnArr);
    }

    /**
     * 编辑记录
     *
     * @Access  public
     * @Permission(action='Interview.interviewResumeList')
     */
    public function editBgCheckAnnexLogAction()
    {
        $paramIn     = $this->paramIn;
        $validations = [
            "offer_id" => "Required|Int", // offer id
        ];
        $this->validateCheck($paramIn, $validations);
        $returnArr = $this->atomicLock(function () use ($paramIn) {
            return (new InterviewServer())->bgCheckAnnexLogs($paramIn['offer_id']);
        }, 'edit_bg_check_annex_' . $paramIn['offer_id']);
        $this->jsonReturn($returnArr);
    }

    /**
     * 上传背调附件
     *
     * @Access  public
     * @Permission(action='Interview.interviewResumeList')
     */
    public function uploadBgCheckAnnexAction()
    {
        $paramIn     = $this->paramIn;
        $validations = [
            "offer_id"   => "Required|Int", // offer id
            "annex_name" => "Required|StrLenGeLe:1,250", // 附件名称
            "annex_url"  => "Required|StrLenGeLe:1,250", // 附件地址
        ];
        $this->validateCheck($paramIn, $validations);

        $paramIn['operator_id'] = $this->userInfo['id'];
        $returnArr = $this->atomicLock(function () use ($paramIn) {
            return (new InterviewServer())->uploadBgCheckAnnex($paramIn);
        }, 'upload_bg_check_annex_' . $paramIn['offer_id']);
        $this->jsonReturn($returnArr);

    }

    /**
     * 背调附件查询接口
     *
     * @Access  public
     * @Permission(action='Interview.interviewResumeList')
     */
    public function bgCheckAnnexListAction()
    {
        $paramIn     = $this->paramIn;
        $validations = [
            "offer_id"   => "Required|Int", // offer id
        ];
        $this->validateCheck($paramIn, $validations);

        $returnArr = (new InterviewServer())->bgCheckAnnexList($paramIn['offer_id']);
        $this->jsonReturn($returnArr);
    }

    /**
     * 删除背调附件
     *
     * @Access  public
     * @Permission(action='Interview.interviewResumeList')
     */
    public function delBgCheckAnnexAction()
    {
        $paramIn     = $this->paramIn;
        $validations = [
            "id"   => "Required|Int", //  id
        ];
        $this->validateCheck($paramIn, $validations);
        $returnArr = $this->atomicLock(function () use ($paramIn) {
            return (new InterviewServer())->delBgCheckAnnex($paramIn['id'], $this->userInfo['id']);
        }, 'del_bg_check_annex_' . $paramIn['id']);

        if ($returnArr === false) { //没有获取到锁
            $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('5202')));
        }
        $this->jsonReturn($returnArr);
    }


    /**
     * offer相关附件上传
     * offer附件、背调附件
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function uploadOfferAnnexAction()
    {
        $paramIn     = $this->paramIn;
        $validations = [
            "offer_id"   => "Required|Int",  //offerID
            'annex_type' => "Required|IntIn:1,2", //附件类型 1：offer附件，2：背调附件
            'annex_url'  => "Required|StrLenGeLe:1,200",//附件地址
        ];

        $this->validateCheck($paramIn, $validations);

        $returnArr = (new InterviewServer())->offerAnnexUploadedDo($paramIn);

        return $this->jsonReturn($returnArr);
    }

    /**
     * offer附件、背调附件删除
     * @Access  public
     * @Param   request
     * @Return  jsonData
     * @Permission(action='Interview.offerEdit')
     */
    public function delOfferAnnexAction()
    {
        $paramIn     = $this->paramIn;
        $validations = [
            "offer_id"   => "Required|Int",  //offerID
            'annex_type' => "Required|IntGe:0", //附件类型 1：offer附件，2：背调附件
        ];

        $this->validateCheck($paramIn, $validations);

        $returnArr = (new InterviewServer())->delOfferAnnex($paramIn['offer_id'], $paramIn['annex_type']);

        return $this->jsonReturn($returnArr);
    }

    /**
     * 面试官列表
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function interviewerListAction()
    {
        //[1]入参
        $paramIn = $this->paramIn;
        $this->getDI()->get('logger')->write_log("paramIn".json_encode($paramIn, JSON_UNESCAPED_UNICODE), 'info');

        $paramIn['staff_id'] = $this->userinfo['staff_id'];

        //[2]业务处理
        $returnArr = (new InterviewServer())->interviewerList($paramIn);
        //$this->getDI()->get('logger')->write_log("interviewerListAction".json_encode($returnArr, JSON_UNESCAPED_UNICODE), 'info');

        //[3]数据返回
        $this->jsonReturn($returnArr);
    }


    /**
     * 简历提供人列表
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function resumeSupplyListAction()
    {
        //[1]入参
        $paramIn = $this->paramIn;
        $this->getDI()->get('logger')->write_log("paramIn".json_encode($paramIn, JSON_UNESCAPED_UNICODE), 'info');

        $paramIn['staff_id'] = $this->userinfo['staff_id'];

        //[2]业务处理
        $returnArr = (new InterviewServer())->supplyerList($paramIn);
        $this->getDI()->get('logger')->write_log("resumeSupplyListAction".json_encode($returnArr,
                JSON_UNESCAPED_UNICODE), 'info');

        //[3]数据返回
        $this->jsonReturn($returnArr);
    }

    /**
     * 下拉框默认值逻辑
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function comboboxDefaultAction()
    {
        //[1]入参
        $paramIn = $this->paramIn;

        $paramIn['staff_id'] = $this->userinfo['staff_id'];

        //[2]业务处理
        $returnArr = (new InterviewServer())->comboboxDefault($paramIn);

        //[3]数据返回
        $this->jsonReturn($this->checkReturn($returnArr));
    }

    /**
     * 面试反馈
     * @Access  public
     * @Param   request
     * @Return  jsonData
     * @Permission(action='Interview.feedback')
     */
    public function feedbackAction()
    {
        // 接参
        $paramIn = $this->paramIn;
        $this->getDI()->get('logger')->write_log("paramIn".json_encode($paramIn, JSON_UNESCAPED_UNICODE), 'info');

        // 验证
        $validations = [
            "interview_id"          => "Required|StrLenGeLe:1, 40|>>>:Refusal to access ！", // 反馈id"
            "hc_id"                 => "Required|StrLenGeLe:1, 40|>>>:Refusal to access ！",
            "state"                 => "Required|StrLenGeLe:1, 40|>>>:Refusal to access ！", // 是否通过
            "ope_id"                => "Required|StrLenGeLe:1, 40|>>>:Refusal to access ！", // 面试官操作ID
            'manager_id'            => "Int|>>>: Manager Id Error", //直线上级id
            'manager_name'          => "Str|>>>: Manager Name Error", //直线上级名称
            "working_day_rest_type" => "Int|>>>:Working Day Rest Type Error", //工作天数&轮休规则
        ];
        $this->validateCheck($paramIn, $validations);

        // 业务处理
        $recordFeedback = (new InterviewServer())->recordFeedback($paramIn);
        // 数据返回
        $this->jsonReturn($recordFeedback);
    }


    /**
     * 面试反馈修改
     * @Access  public
     * @Param   request
     * @Return  jsonData
     * @Permission(action='Interview.feedbackUpdate')
     */
    public function feedbackUpdateAction()
    {
        // 接参
        $paramIn = $this->paramIn;
        $this->getDI()->get('logger')->write_log("paramIn".json_encode($paramIn, JSON_UNESCAPED_UNICODE), 'info');

        // 验证
        $validations = [
            "interview_info_id"     => "Required|StrLenGeLe:1, 40|>>>:Refusal to access ！", // 反馈id"
            "interview_id"          => "Required|StrLenGeLe:1, 40|>>>:Refusal to access ！", // 面试id"
            "hc_id"                 => "Required|StrLenGeLe:1, 40|>>>:Refusal to access ！",
            "state"                 => "Required|StrLenGeLe:1, 40|>>>:Refusal to access ！", // 是否通过
            'manager_id'            => "Int|>>>: Manager Id Error", //直线上级id
            'manager_name'          => "Str|>>>: Manager Id Error", //直线上级名称
            "working_day_rest_type" => "Int|>>>:Working Day Rest Type Error", //工作天数&轮休规则
        ];
        $this->validateCheck($paramIn, $validations);

        // 业务处理
        $recordFeedback = (new InterviewServer())->updateFeedback($paramIn);

        // 数据返回
        $this->jsonReturn($recordFeedback);
    }


    /**
     * 面试反馈查看
     * @Access  public
     * @Param   request
     * @Return  jsonData
     * @Permission(action='Interview.feedbackList')
     */
    public function feedbackListAction()
    {
        // 接参
        $paramIn = $this->paramIn;
        $this->getDI()->get('logger')->write_log("paramIn".json_encode($paramIn, JSON_UNESCAPED_UNICODE), 'info');

        // 验证
        $validations = [
            "interview_info_id" => "Required|StrLenGeLe:1, 40|>>>:Refusal to access ！", // 反馈id"
        ];
        $this->validateCheck($paramIn, $validations);

        // 业务处理
        $recordFeedback = (new InterviewServer())->feedbackList($paramIn);

        // 数据返回
        $this->jsonReturn($recordFeedback);
    }

    /**
     * 职位列表
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function positionListAction()
    {
        //[1]入参
        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];

        //[2]业务处理
        $returnArr = (new InterviewServer())->positionList($paramIn);

        //[3]数据返回
        $this->jsonReturn($returnArr);
    }

    /**
     * 员工工资信息
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function wagesInfoAction()
    {
        //[1]入参
        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        $paramIn['bi_url']   = $this->biUrl;

        //[2]业务处理
        $returnArr = (new InterviewServer())->wagesInfo($paramIn);

        //[3]数据返回
        $this->jsonReturn($returnArr);
    }

    /**
     * 网点列表
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function worknodeListAction()
    {
        $paramIn         = $this->paramIn;
        $paramIn['type'] = 2;
        $returnArr       = (new HcServer())->worknodeList($paramIn);
        return $this->jsonReturn($returnArr);
    }

    /**
     * 待筛选简历
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function screenedListAction()
    {
        $paramIn   = $this->paramIn;
        $returnArr = (new InterviewServer())->screenedList($paramIn);
        return $this->jsonReturn($returnArr);
    }


    /**
     * 指派网点
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function interviewResumeUpdateStoreAction()
    {
        $paramIn = $this->paramIn;
        // 非空验证
        if (empty($paramIn)) {
            $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('4108')));
        }
        // 入参校验
        $validations = [
            "id" => "Required|Int|>>>:".$this->getTranslation()->_('4413'),  // 简历id
        ];
        $this->validateCheck($paramIn, $validations);

        if (empty($paramIn['store_id'])) {
            $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('miss_args')));
        }

        //只要这两个参数
        $newParam             = [];
        $newParam['id']       = $paramIn['id'];
        $newParam['store_id'] = $paramIn['store_id'];

        $returnArr = (new ResumeServer())->updateResume($newParam);
        // 数据返回
        $this->jsonReturn($returnArr);
    }

    /**
     * 获取班次list
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function shiftListAction()
    {
        $paramIn = $this->paramIn;
        $this->getDI()->get('logger')->write_log("paramIn".json_encode($paramIn, JSON_UNESCAPED_UNICODE), 'info');

        $returnArr = (new InterviewServer())->getAllShift($paramIn['department_id'], $paramIn['job_title_id'],
            $paramIn['store_id']);
        $this->getDI()->get('logger')->write_log("shiftListAction".json_encode($returnArr, JSON_UNESCAPED_UNICODE),
            'info');

        return $this->jsonReturn($returnArr);
    }

    /**
     * 员工搜索
     */
    public function searchStaffAction()
    {
        $paramIn = $this->paramIn;
        $this->getDI()->get('logger')->write_log("paramIn".json_encode($paramIn, JSON_UNESCAPED_UNICODE), 'info');
        $search_name = $paramIn['search_name'];
        $returnArr   = (new StaffServer())->searchStaffList($search_name);
        return $this->jsonReturn($returnArr);
    }

    /**
     * 批量修改最新简历操作人
     * @return void
     * @Permission(action='Update.resume.last.operator')
     */
    public function batchUpdateResumeLastOperatorAction()
    {
        $paramIn = $this->paramIn;
        // 入参校验
        $validations = [
            "resume_last_operator_id" => "Required|StrLenGeLe:1,15|>>>:".$this->getTranslation()->_('resume_last_operator_id_fail'),
            "cvids"                   => "Required|Arr|ArrLenGeLe:1,100",
            'cvids[*]'                => 'Required|IntGe:0',
        ];
        $this->validateCheck($paramIn, $validations);
        $returnArr = (new InterviewServer())->batchUpdateResumeLastOperator($paramIn);
        // 数据返回
        $this->jsonReturn($returnArr);
    }

    /**
     *
     * 按照工号模糊搜索员工信息【拥有网络TA角色，在职，正式员工】
     * @return void|null
     */
    public function searchStaffInfoAction()
    {
        $paramIn = $this->paramIn;
        $this->getDI()->get('logger')->write_log("paramIn".json_encode($paramIn, JSON_UNESCAPED_UNICODE), 'info');
        $paramIn['q'] = !empty($paramIn['q']) ? trim($paramIn['q']) : "";
        $returnArr    = (new StaffServer())->searchStaffInfo($paramIn);
        return $this->jsonReturn($returnArr);
    }


    /**
     * 面试取消+关联hc
     */
    public function interview_cancel_and_related_hcAction()
    {
        $paramIn     = $this->paramIn;
        $validations = [
            "interview_id" => "Required|Int",  // 面试id
            "hc_id" => "Required|Int",
        ];
        $this->validateCheck($paramIn, $validations);
        $returnArr = (new InterviewServer())->interview_cancel_and_related_hc($paramIn);
        return $this->jsonReturn($returnArr);
    }


    /**
     * 修改hc
     * @return null
     * @throws Exception
     */
    public function updateResumeHcAction()
    {
        $paramIn     = $this->paramIn;
        $validations = [
            "hc_id"        => "Required|IntGt:0|>>>:" . $this->getTranslation()->_('4412'),
            'interview_id' => "Required|IntGt:0|>>>:" . $this->getTranslation()->_('4415'),
            "resume_id"    => "Required|IntGt:0|>>>:" . $this->getTranslation()->_('4413'),
            'job_title_id' => "Required|IntGt:0|>>>:" . $this->getTranslation()->_('3010'),
        ];

        $this->validateCheck($paramIn, $validations);

        $returnArr = $this->atomicLock(function () use ($paramIn) {
            return (new InterviewHcServer())->updateResumeHc($paramIn);
        }, 'updateResumeHcList:' . $paramIn['resume_id'], 5);

        if ($returnArr === false) { //没有获取到锁
            $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('5202')));
        }


        return $this->jsonReturn($returnArr);
    }

}
