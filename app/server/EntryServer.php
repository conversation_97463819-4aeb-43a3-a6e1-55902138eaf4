<?php


namespace FlashExpress\bi\App\Server;

use Exception;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;

use FlashExpress\bi\App\library\enumsTh;
use FlashExpress\bi\App\library\Exception\BusinessException;

use FlashExpress\bi\App\Models\backyard\HrAnnexModel;
use FlashExpress\bi\App\library\Exception\ValidationException;

use FlashExpress\bi\App\Models\backyard\HrEntryModel;
use FlashExpress\bi\App\Models\backyard\HrhcModel;
use FlashExpress\bi\App\Models\backyard\HrInterviewOfferModel;
use FlashExpress\bi\App\Models\backyard\HrJobDepartmentRelationModel;
use FlashExpress\bi\App\Models\backyard\HrResumeModel;
use FlashExpress\bi\App\Models\backyard\HrStaffAnnexInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffContractModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\RolesModel;
use FlashExpress\bi\App\Models\backyard\SendEmailRecordModel;
use FlashExpress\bi\App\Models\backyard\SysDepartmentModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Models\backyard\SysManagePieceModel;
use FlashExpress\bi\App\Models\backyard\SysManageRegionModel;
use FlashExpress\bi\App\Models\backyard\WhrPermissionModel;
use FlashExpress\bi\App\Models\backyard\WorkdaySettingModel;
use FlashExpress\bi\App\Models\backyard\WorkdaySettingSplitModel;
use FlashExpress\bi\App\Models\backyard\WorkdaySettingV2Model;
use FlashExpress\bi\App\Models\HrResumeBuddyModel;
use FlashExpress\bi\App\Repository\EntryRepository;
use FlashExpress\bi\App\Repository\PermissionRepository;
use FlashExpress\bi\App\Repository\ResumeExtendRepository;
use FlashExpress\bi\App\Repository\FlashJdRepository;
use FlashExpress\bi\App\Repository\ResumeRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use  FlashExpress\bi\App\Repository\DepartmentRepository;
use FlashExpress\bi\App\Repository\SysDepartmentRepository;
use  FlashExpress\bi\App\Repository\SysListRepository;
use FlashExpress\bi\App\Server\PermissionServer;
use FlashExpress\bi\App\library\RocketMQ;


class EntryServer extends BaseServer
{
    protected $re;
	//薪资查看权限工号    这里的工号会被替换掉!  薪资结构
//	public $salary_view_permission = [21358, 33287, 31856, 17758, 28228, 56780, 23357, 21899, 30555, 24715, 56207, 55578];
    // 到岗确认新员工：符合绑定辅导员的部门 和 职位
    protected static $counselor_department_id = [4];
    protected static $counselor_position_ids = [enumsTh::JOB_TITLE_BIKE_COURIER,37,110,452, enumsTh::JOB_TITLE_EV_COURIER,enumsTh::JOB_TITLE_VAN_COURIER_PROJECT,enumsTh::JOB_TITLE_TRICYCLE_COURIER];
    public $entry;
    public $staff;
    public $department;
    public $sysList;
    public $resume;
    public $log;
    public $interview;

    public function __construct()
    {
        parent::__construct();

//	    $this->salary_view_permission = (new EntryRepository())->getAuthoritySetVal();
    }


    protected function getShiftList($shift_id, $shift_extend_id = 0)
    {
        $shift_list = (new SysListRepository())->getAllShiftListFromCache();
        $shift_list = array_column($shift_list, null, 'id');

        if (isset($shift_list[$shift_id])) {
            //班次类型
            $shift_type                   = $shift_list[$shift_id]['type'] ?? '';
            $shiftInfo['shift_type']      = $shift_type;
            $shiftInfo['shift_type_text'] = empty($shift_type) ? '' : $this->getTranslation()->_('shift_'.$shift_type);
            $shiftInfo['shift_text']      = ($shift_list[$shift_id]['start'] ?? '').'-'.($shift_list[$shift_id]['end'] ?? '');
        } else {
            $shiftInfo['shift_type']      = '';
            $shiftInfo['shift_type_text'] = '';
            $shiftInfo['shift_text']      = '';
        }
        $shiftInfo["shift_id"] = $shift_id;
        return $shiftInfo;
    }

    /**
     * 入职列表
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function listEntry($paramIn = [])
    {
        $this->entry      = new EntryRepository();
        $this->sysList    = new SysListRepository();
        $this->resume     = new ResumeRepository();

        $interviewServer = new InterviewServer();
        $sysListServer   = new SysListServer();

        try {
            $pageSize = $this->processingDefault($paramIn, 'page_size', 2, 10);
            $pageNum  = $this->processingDefault($paramIn, 'page_num', 2, 1);
            $sort     = $this->processingDefault($paramIn, 'sort', 1);
            /* 地址列表 */
            $addressList = $this->sysList->getAddressList(['address_type' => 2]);
            $addressList = array_column($addressList, 'name', 'code');

            /* 获取部门列表 */
            $departmentDataList = $this->sysList->getDepartmentList();
            $departmentDataList = array_column($departmentDataList, 'name', 'id');


            /* 获取工作网点列表 */
            $worknodeData = $this->sysList->getStoreList();
            $worknodeData = array_column($worknodeData, 'name', 'id');

            /* 获取职位列表 */
            $positionList = $this->sysList->getPositionList();
            $positionList = array_column($positionList, 'job_name', 'id');
            //权限
	        $authority_department_ids= $paramIn["authority_department_ids"] ?? []; //权限
	        $authority_stores_ids = $paramIn["authority_stores_ids"] ?? []; //权限
	        $is_admin = $paramIn["is_admin"] ?? enums::$is_admin['on']; //权限 1 超管

            /* 检查搜索条件 */
            $parpam      = [
                'name',
                'job_name',
                'position_id',
                'worknode_id',
                'address_id',
                'phone',
                'time_start',
                'time_end',
                'entry_time_start',
                'entry_time_end',
                'work_time_start',
                'work_time_end',
                'resume_id',
                'staff_id',
                'status',
                'record_entry',
                'hc_department_id',
                'resume_last_operator',
                'hire_type',
                'is_sub_department',
                'onboarding_status',//在职状态 多选
                'identity_status',//身份证确认状态 多选
            ];
            $searchWhere = checkParam($parpam, $paramIn);

            //最新操作人类型  0 全部 1 为空 2 不为空
            $searchWhere['last_operator_query_type'] = isset($paramIn['last_operator_query_type']) ? intval($paramIn['last_operator_query_type']) : 0;

            //最新操作人
            if (!empty($paramIn['resume_last_operator'])) {
                $searchWhere['resume_last_operator'] = intval($paramIn['resume_last_operator']);
            }

            /* 获取入职列表数据 */
            $paramHc = [
                'searchWhere' => $searchWhere,
                'page_size'   => $pageSize,
                'page_num'    => $pageNum,
                'sort'        => $sort,
                'is_admin'=>$is_admin,
                'authority_department_ids'=>$authority_department_ids,
                'authority_stores_ids'=>$authority_stores_ids,
            ];
            $data    = $this->entry->entryList($paramHc);
            $this->getDI()->get('logger')->write_log("entryList:" . json_encode($data), 'info');

            /*获取入职列表数组 */
            $hcData = $data['data'] ?? [];

            /* 获取分页数组 */
            $pageArr = $data['pagination'];

            if(empty($hcData)){
                $returnArr['data']['dataList']   = [];
                $returnArr['data']['pagination'] = $pageArr;
                return $this->checkReturn($returnArr);
            }
            //获取简历相关问题答案
            $resume_id_list = array_column($hcData,'resume_id');
            $disability_qa = $this->resume->get_disability_qa($resume_id_list);
            $disability_pic = $this->resume->get_disability_pic($resume_id_list);

            $statusTransfer = [
                1 => $this->getTranslation()->_('4903'),
                2 => $this->getTranslation()->_('4904'),
                3 => $this->getTranslation()->_('4905'),
            ];

            //称谓
            $call_th = enums::$call_th;
            $call_en = enums::$call_en;

            //获取每个简历对应的薪资审批数据
            $resumeIds = array_filter(array_column($hcData, 'resume_id'));
            $salary_approvals = (new InterviewServer())->getLastSalaryApproval($resumeIds);

            $resume_last_operator_staff_ids = array_values(array_unique(array_column($hcData,'resume_last_operator')));
            $resume_last_operator_res = (new StaffServer())->getStaffListByIds($resume_last_operator_staff_ids,['staff_info_id','name','nick_name']);
            $resume_last_operator_list = array_column($resume_last_operator_res, null, 'staff_info_id');
            
            // 编辑入职表单 角色默认值
            $onboarding_default_roles_arr = $this->get_onboarding_default_roles();
            /* 业务处理 */
            foreach ($hcData as $k => $v) {
                $phone_area_code = !empty($v['phone_area_code']) ? '+'.$v['phone_area_code']: '+66';
                $hcData[$k]['resume_last_operator_name'] = StaffServer::getStaffNameView($resume_last_operator_list[$v['resume_last_operator']] ?? [],2);
                $hcData[$k]['code_phone'] =  $phone_area_code.' '. ($v['phone'] ?? '');
                $departmentHierarchy                  = $this->sysList->getDepartmentList([
                    "id"        => $v["department_id"],
                    "hierarchy" => 1,
                ]);
                $departmentHierarchyIds               = explode(",", $departmentHierarchy);
                $hcData[$k]["departmentHierarchyIds"] = count($departmentHierarchyIds) > 1 ? array_splice($departmentHierarchyIds, 1) : $departmentHierarchyIds;
                if ($v['sex'] == 1) {
                    $hcData[$k]['sex_name'] = $this->getTranslation()->_('4900');
                    $hcData[$k]['sex']      = 1;
                } else if ($v['sex'] == 2) {
                    $hcData[$k]['sex_name'] = $this->getTranslation()->_('4901');
                    $hcData[$k]['sex']      = 2;
                } else {
                    $hcData[$k]['sex_name'] = $this->getTranslation()->_('4902');
                    $hcData[$k]['sex']      = 0;
                }
                $hcData[$k]['address_name']    = $addressList[$v['address_id']];
                $hcData[$k]['status_value']    = $statusTransfer[$v['status']] ?? '';
                $hcData[$k]['department_name'] = $departmentDataList[$v['department_id']];
                if ($v['date_birth']) {
                    if ((int)date("Y") < (int)date('Y', strtotime($v['date_birth']))) {
                        $hcData[$k]['age'] = (int)(date("Y-m-d") - ($v['date_birth'] - 543));
                    } else {
                        $hcData[$k]['age'] = (int)(date("Y-m-d") - $v['date_birth']);
                    }
                } else {
                    $hcData[$k]['age'] = '';
                }
                $recordEntry = $v['record_entry'] ? json_decode($v['record_entry'], true) : [];
                $hcData[$k]['hire_type_text'] = $v['hire_type'] ? $this->getTranslation()->_('hire_type_' . $v['hire_type']) : '';
                if ($recordEntry && isset($recordEntry['hire_type'])) {
                    $hcData[$k]['hire_type']  = $recordEntry['hire_type'];
                    $hcData[$k]['hire_times'] = $recordEntry['hire_times'];

                    if (in_array($recordEntry['hire_type'], [3, 4])) {
                        $hcData[$k]['hire_times_text'] = $recordEntry['hire_times'] . $this->getTranslation()->_('daily');
                    } else {
                        if (in_array($recordEntry['hire_type'], [2])) {
                            $hcData[$k]['hire_times_text'] = $recordEntry['hire_times'] . $this->getTranslation()->_('monthly');
                        }
                    }
                } else {
                    if (in_array($v['hire_type'], [3, 4])) {
                        $hcData[$k]['hire_times_text'] = $v['hire_times'] . $this->getTranslation()->_('daily');
                    } else {
                        if (in_array($v['hire_type'], [2])) {
                            $hcData[$k]['hire_times_text'] = $v['hire_times'] . $this->getTranslation()->_('monthly');
                        }
                    }
                }

                $name    = $v['first_name'] . " " . $v['last_name'];
                $name_en = $v['first_name_en'] . " " . $v['last_name_en'];
                if(env('country_code') == 'TH' && isset($call_th[$v['call_name']])){
                    $hcData[$k]['name']          = $call_th[$v['call_name']] . " " . $name; // 泰文名称
                    $hcData[$k]['name_en']       = $call_en[$v['call_name']] . " " . $name_en; // 英文名称
                }else{
                    $hcData[$k]['name']          = $name; // 泰文名称
                    $hcData[$k]['name_en']       = $name_en; // 英文名称
                }


                $hcData[$k]['worknode_name'] = $worknodeData[$v['worknode_id']];
                $hcData[$k]['position_name'] = $positionList[$v['position_id']];
                $hcData[$k]['nickname']    = $v['nickname'] ;

                if ($v['last_name_en'] && $v['first_name_en']) {
                    if ($v['nationality'] == 2) {
                        $hcData[$k]['company_email'] = $v['first_name_en'] . $v['last_name_en'];
                    } else {
                        $hcData[$k]['company_email'] = $v['first_name_en'] . "_" . mb_substr($v['last_name_en'], 0, 3);
                    }
                }
                //班次信息
                $shift_extend_id = $recordEntry['shift_extend_id']??0;
                $shift_id = $recordEntry['shift_id']??$hcData[$k]['shift_id'];

                $hcData[$k] = array_merge($hcData[$k],$this->getShiftList($shift_id,$shift_extend_id));

                //入职管理信息是否添加字段
                if ($v['status'] == enums::$entry_status['entry_hired']) { //已入职
                    $hcData[$k]['entryform_submit_status'] = enums::$entry_submit_form['entry_do_not_submit'];
                } else {
                    unset($recordEntry['shift_extend_id']);
                    $hcData[$k]['record_entry'] = $recordEntry ? json_encode($recordEntry,JSON_UNESCAPED_UNICODE) : null;
                    $hcData[$k]['entryform_submit_status'] = $recordEntry
                        ? enums::$entry_submit_form['entry_has_submit']
                        : enums::$entry_submit_form['entry_do_not_submit'];
                }
                //残疾人字段信息
                $img_prefix = env("img_prefix", "http://fle-staging-asset-internal.oss-ap-southeast-1.aliyuncs.com/");
                $is_disability = $disability_qa[$v['resume_id']]['4']['question_value']  == 1 ? 1 : 0;
                $hcData[$k]['disability'] = [
                    'is_disability' => $is_disability ,//是否残疾? 1-是，0-否
                    'is_have_disability_certificate'   => 0, //是否有残疾人证? 1-是，0-否
                    'disability_certificate'        => '', //残疾人证明编号
                    'disability_certificate_front' => '', //残疾人证照片-正面
                    'disability_certificate_reverse'  => '', //残疾人证照片-反面
                    'img_prefix'  => $img_prefix, //图片路径前缀
                ];
                if($is_disability){
                    $certificate_pic_front = $disability_pic[$v['resume_id']]['17']['object_key'] ?? '';
                    $certificate_pic_back  = $disability_pic[$v['resume_id']]['18']['object_key'] ?? '';
                    $is_have_disability_certificate = $disability_qa[$v['resume_id']]['14']['question_value'] == 1 ? 1 : 0;
                    $hcData[$k]['disability'] = [
                        'is_disability'         => $is_disability ,//是否残疾? 1-是，0-否
                        'is_have_disability_certificate'   => $is_have_disability_certificate, //是否有残疾人证? 1-是，0-否
                        'disability_certificate'        => $disability_qa[$v['resume_id']]['14']['question_text'], //残疾人证明编号
                        'disability_certificate_front' => $certificate_pic_front ?? '', //残疾人证照片-正面
                        'disability_certificate_reverse'  => $certificate_pic_back ?? '', //残疾人证照片-反面
                        'img_prefix'  => $img_prefix, //图片路径前缀

                    ];

                }


                $hcData[$k]['recruit_channel'] = $v['recruit_channel'];
                $hcData[$k]['recruit_channel_text'] = !empty($v['recruit_channel']) ? $sysListServer->getRecruitChannelList($v['recruit_channel']) : '';

                $hcData[$k]['work_days'] = $salary_approvals[$v['resume_id']]['work_days'] ?? null;
                //增加是否是一线员工职位标识字段
                $hcData[$k]['is_front_line_job'] = $interviewServer->isFrontLineJob($v['position_id']);

                $hcData[$k]["is_first_line_job"] = $interviewServer->isFirstLineJob($v['department_id'], $v['position_id']);

                //$hcData[$k]['higher_id'] = !empty($hcData[$k]['higher_id']) ? $hcData[$k]['higher_id'] : '';
                //$hcData[$k]['higher_name'] = !empty($hcData[$k]['higher_name']) ? $hcData[$k]['higher_name'] : '';
                //$hcData[$k]['working_day_rest_type'] = !empty($hcData[$k]['working_day_rest_type']) ? $hcData[$k]['working_day_rest_type'] : '';

                // 编辑入职表单 角色默认值
                $hcData[$k]['position_category_default'] = $onboarding_default_roles_arr[$v['position_id']] ?? [];
                
                if (isCountry('MY')){
                    // 这是一部分逻辑 前端会根据当前选择的网点 角色 职级 进行联动展示 企业邮箱
                    $deptIds = SysServer::getDepartmentConditionByParams(['department_id'=>318,'is_sub_department'=>1]);
                    $hcData[$k]['is_email_department'] = !empty($v['department_id']) && in_array($v['department_id'],$deptIds);
                }
                // 身份证确认状态
                $hcData[$k]['identity_status_text'] = isset(HrResumeModel::$identity_validate_status_text[$v['identity_status']]) ? $this->getTranslation()->_(HrResumeModel::$identity_validate_status_text[$v['identity_status']]) : '';
                $hcData[$k]['identity_status']      = $v['identity_status'];
            }

            $this->getDI()->get('logger')->write_log("entryList hcData:" . json_encode($hcData), 'info');

            /* 返回格式 */
            $returnArr['data']['dataList']   = $hcData ?? [];
            $returnArr['data']['pagination'] = $pageArr;

            $this->getDI()->get('logger')->write_log("entrylist结果:" . print_r($hcData,true), 'info');
            return $this->checkReturn($returnArr);
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("entry listEntry 异常信息:" . $e->getMessage(), 'error');
            return $this->checkReturn(-3, $this->getTranslation()->_('4008'));
        }
    }

    
    public function get_onboarding_default_roles()
    {
        $return_data = [];
        // 职位ID,职位ID|角色ID,角色ID#职位ID|角色ID
        $onboarding_default_roles_arr = SettingEnvServer::getSetValToArray('onboarding_default_roles','#');
        if (empty($onboarding_default_roles_arr)){
            return $return_data;
        }
        foreach ($onboarding_default_roles_arr as $v){
            $_data = explode('|',$v);
            if (empty($_data[0]) || empty($_data[1])){
                continue;
            }
            foreach (explode(',',$_data[0]) as $val){
                $return_data[$val] = array_values(array_unique(array_merge($return_data[$val] ?? [],explode(',',$_data[1]))));
            }
        }
        return $return_data;
    }

    /**
     * 根据职位名称获取车辆类型
     * @param $staffJobTitle
     * @return string
     */
    public function getCarTypeByJotTitle($staffJobTitle): string
    {
        $car_type = '';
        if (isCountry('TH')) {
            //pickup_driver 默认为VAN
            if (in_array($staffJobTitle, [enums::$job_title['pickup_driver'], enums::$job_title['Van Courier']])) {
                $car_type = 'Van';
            }
            if (in_array($staffJobTitle, [enums::$job_title['tricycle_courier'], enums::$job_title['Bike Courier']])) {
                $car_type = 'Bike';
            }
            return $car_type;
        }
        if ($staffJobTitle == enums::$job_title['Bike Courier']) {
            $car_type = 'Bike';
        } elseif ($staffJobTitle == enums::$job_title['Van Courier']) {
            $car_type = 'Van';
        }
        return $car_type;
    }

    /**
     * 入职列表导出
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function listEntryExport($paramIn, &$is_have_data,$is_superman)
    {
        $this->entry      = new EntryRepository();
        $this->sysList    = new SysListRepository();
        $pageSize = $this->processingDefault($paramIn, 'page_size', 2, 10);
        $pageNum  = $this->processingDefault($paramIn, 'page_num', 2, 1);

        /* 检查搜索条件 */
        $parpam      = [
            'name',
            'job_name',
            'position_id',
            'worknode_id',
            'address_id',
            'phone',
            'time_start',
            'time_end',
            'entry_time_start',
            'entry_time_end',
            'work_time_start',
            'work_time_end',
            'resume_id',
            'staff_id',
            'status',
            'hc_department_id',
            'hire_type',
            'is_sub_department',
            'onboarding_status',//在职状态 多选
            'identity_status',//身份证确认状态 多选
        ];
        $searchWhere = checkParam($parpam, $paramIn);

        if(empty($searchWhere['worknode_id'])){
            unset($searchWhere['worknode_id']);
        }

        //最新操作人类型  0 全部 1 为空 2 不为空
        $searchWhere['last_operator_query_type'] = isset($paramIn['last_operator_query_type']) ? intval($paramIn['last_operator_query_type']) : 0;

        //最新操作人
        if (!empty($paramIn['resume_last_operator'])) {
            $searchWhere['resume_last_operator'] = intval($paramIn['resume_last_operator']);
        }


        /* 获取入职列表数据 */
        $paramHc = [
            'searchWhere' => $searchWhere,
            'page_size'   => $pageSize,
            'page_num'    => $pageNum,
            'no_page'     => 1,
            'is_admin'    =>  $paramIn['userinfo']['is_admin'],   //1是超管
            'authority_stores_ids'    =>  $paramIn['userinfo']['permission_stores_ids'],  //网点权限
            'authority_department_ids' => $paramIn['userinfo']['permission_department_ids'],   //部门权限
        ];
        $data = $this->entry->entryList($paramHc);
        if(empty($data['data'])){
            $is_have_data = 0;
            return [];
        }
        $resumeIds = array_filter(array_column($data['data'], 'resume_id'));
        $resumeApprovals = (new InterviewServer())->getLastSalaryApproval($resumeIds);
        /*获取入职列表数组 */
        $hcData = $data['data'];

        /* 获取分页数组 */
        $pageArr = $data['pagination'];
        /* 业务处理 */
        /* 地址列表 */
        $addressList = $this->sysList->getAddressList(['address_type' => 2]);
        $addressList = array_column($addressList, 'name', 'code');

        // 查询部门名称 ids
        $departmentDataAllList = (new SysDepartmentRepository())->getDepartmentListFromCache(['id', 'name','ancestry','ancestry_v3','type','level','company_id','company_name']);
        $departmentDataList = array_column($departmentDataAllList, null, 'id');


        /* 获取工作网点列表 */
        $worknode          = $this->sysList->getStoreList();
        $worknodeData      = array_column($worknode, 'name', 'id');
        if (isCountry('MY')) {
            $geographyCodeData = array_column($worknode, 'sorting_no', 'id');
        } else {
            $geographyCodeData = array_column($worknode, 'manage_geography_code', 'id');
        }

        /* 获取职位列表 */
        $positionList = $this->sysList->getPositionList();
        $positionList = array_column($positionList, 'job_name', 'id');
        /* 获取员工信息列表 */
        $staffList = (new EntryRepository())->getStaffEntryDataList(array_column($data['data'],'staff_id'));
        $staffList = array_column($staffList, null, 'staff_info_id');
        /* 获取大区对应关系 */
        $areasArr = UC('entry')['areasArr'];

        //todo 获取 省市乡map列表
        //4月份后winhr中 户籍地和居住地增加了国家字段：
        //泰国的省市乡信息存储的是code值，需要对应行政区域表找出对应名称。
        //非泰国的省市乡信息存储的用户手动录入内容，不需要返回，直接使用。

        $resume = new \FlashExpress\bi\App\Repository\ResumeRepository();

        //获取省 code=>name 列表
        $province     = array_filter(array_map(function($arr){
            if($arr['register_country'] == 1){return $arr['register_government'];}
            },
            $hcData));
        $provinceList = $resume->getLocation(['address_type' => 2, 'code' => getIdsStr(array_unique(array_filter($province)))]);
        $provinceList = array_column($provinceList, 'name', 'code');

        //获取市 code=>name 列表
        $city         = array_filter(array_map(function($arr){
            if($arr['register_country'] == 1){return $arr['register_city'];}
        },$hcData));

        $cityList     = $resume->getLocation(['address_type' => 3, 'code' => getIdsStr(array_unique(array_filter($city)))]);
        $cityList     = array_column($cityList, 'name', 'code');

        //获取乡 code=>name 列表
        $district     = array_filter(array_map(function($arr){
            if($arr['register_country'] == 1){return $arr['register_town'];}
        },$hcData));
        $districtList = $resume->getLocation(['address_type' => 4, 'code' => getIdsStr(array_unique(array_filter($district)))]);
        $districtList = array_column($districtList, 'name', 'code');

        //入职状态
        $statusTransfer = [
            1 => $this->getTranslation()->_('4903'),
            2 => $this->getTranslation()->_('4904'),
            3 => $this->getTranslation()->_('4905'),
        ];

        $resume_last_operator_staff_ids = array_values(array_unique(array_column($hcData,'resume_last_operator')));
        $resume_last_operator_res = (new StaffServer())->getStaffListByIds($resume_last_operator_staff_ids,['staff_info_id','name','nick_name']);
        $resume_last_operator_list = array_column($resume_last_operator_res, null, 'staff_info_id');

        $_data = [];
        foreach ($hcData as $k => $v) {
            //简历id
            $cvId = $v['resume_id'] ? $v['resume_id'] : '';
            $hcId = $v['hc_id'] ? $v['hc_id'] : '';
            //员工号
            $staffId = $v['staff_id'] ? $v['staff_id'] : '';
            //称呼
            if ($v['call_name'] && env('country_code') == 'TH') {
                if ($v['call_name'] == 1) {
//                        $call_name = 'Mr';
                    $call_name = $this->getTranslation()->_('Mr');
                } elseif ($v['call_name'] == 2) {
//                        $call_name = 'Mrs';
                    $call_name = $this->getTranslation()->_('Mrs');
                } else {
//                        $call_name = 'Miss';
                    $call_name = $this->getTranslation()->_('Miss');
                }
            } else {
                $call_name = '';
            }
            //名
            $first_name = $v['first_name'] ?? '';
            //姓
            $last_name = $v['last_name'] ?? '';
            // 中名
            $middle_name = $v['middle_name'] ?? '';
            // 后缀
            $suffix_name = $v['suffix_name'] ?? '';
            //全称
            $name = $first_name . " " . $last_name;
            if (isCountry('Ph')) {
                $name = $first_name . " " . $middle_name . " " . $last_name . " " . $suffix_name;
            }
            $name = $call_name ? $call_name . " " .$name : $name;
            $phone_area_code = !empty($v['phone_area_code']) ? '+'.$v['phone_area_code']: '+66';


            //手机号
            $mobile = $phone_area_code.' '. ($v['phone'] ?? '');
            //个人邮箱
            $email = $v['email'] ? $v['email'] : '';
            //职位
            $job_title = $positionList[$v['position_id']] ? $positionList[$v['position_id']] : '';
            //网点
            $sys_store_id = $worknodeData[$v['worknode_id']] ? $worknodeData[$v['worknode_id']] : '';
            //生日
            $birth_date = $v['date_birth'] ?? '';
            //年龄
            if ($v['date_birth']) {
                if ((int)date("Y") < (int)date('Y', strtotime($v['date_birth']))) {
                    $age = (int)(date("Y-m-d") - ($v['date_birth'] - 543));
                } else {
                    $age = (int)(date("Y-m-d") - $v['date_birth']);
                }
            } else {
                $age = '';
            }
            $nationality = $v['nationality'] ? $this->getTranslation()->_('nationality_' . $v['nationality']) : '';
            $working_country = $v['working_country'] ? $this->getTranslation()->_('working_country_' . $v['working_country']) : '';
            //工作城市
            $address_id = $addressList[$v['address_id']] ? $addressList[$v['address_id']] : '';
            //入库时间
            $createtime = $v['created_at'] ? date("Y-m-d", strtotime($v['created_at'])) : '';
            //预计入职时间
            $work_time = $v['work_time'] ? date("Y-m-d", strtotime($v['work_time'])) : '';
            //入职时间
            $entry_date = $v['entry_date'] ? date("Y-m-d", strtotime($v['entry_date'])) : '';
            //办理时间
            $operate_date = $v['operate_date'] ? date("Y-m-d H:i:s", strtotime($v['operate_date'])) : '';

            //职等
            $jobTitleGrads = [
                1 => 'Staff',
                2 => 'Supervisor',
                3 => 'Manager',
                4 => 'Executive',
            ];

            //【员工信息】
            //英文姓名
            $bi_name_en = $staffList[$staffId]['name_en'] ? $staffList[$staffId]['name_en'] : '';
            //身份证/护照号
            $bi_identity = $staffList[$staffId]['identity'] ? $staffList[$staffId]['identity'] : '';
            //部门
            $department_id = $v['department_id'] ?? '';
            $bi_department = $departmentDataList[$department_id]['name'] ? $departmentDataList[$department_id]['name'] : '';

            //户口所在地
            $register_locale = $this->getResumeRegisterAddress($v,$provinceList,$cityList,$districtList);

            //设备费
//            $equipment_cost = $staffList[$staffId]['equipment_cost'] ? $staffList[$staffId]['equipment_cost'] : '';
            //职位
            $bi_job_title = $positionList[$staffList[$staffId]['job_title']] ? $positionList[$staffList[$staffId]['job_title']] : '';
            //职等
            //$bi_job_title_grad = $jobTitleGrads[$staffList[$staffId]['job_title_grade']] ?? null;
            //职级
            $bi_job_title_level = $staffList[$staffId]['job_title_grade_v2'] ? 'F'.$staffList[$staffId]['job_title_grade_v2'] : null;
            //所属区域
            if (isCountry('MY')) {
                $bi_area = $geographyCodeData[$v['worknode_id']] ?? '';
            } else {
                $bi_area = $geographyCodeData[$staffList[$staffId]['sys_store_id']] ? $areasArr[$geographyCodeData[$staffList[$staffId]['sys_store_id']]] : '';
            }
            //网点
            $bi_sys_store_id = $worknodeData[$staffList[$staffId]['sys_store_id']] ? $worknodeData[$staffList[$staffId]['sys_store_id']] : '';
            //入职时间
            $bi_hire_date = $staffList[$staffId]['hire_date'] ? date("Y-m-d", strtotime($staffList[$staffId]['hire_date'])) : '';
            //工作天数
            $bi_work_day = $staffList[$staffId]['week_working_day'] ?? null;
            //银行卡号
            $bi_bank_no = $staffList[$staffId]['bank_no'] ? $staffList[$staffId]['bank_no'] : '';

            $currency = '';
            if (isCountry('th')) {
                $currency = $v['currency'] == HrInterviewOfferModel::CURRENCY_THB ? $this->getTranslation()->_('currency_thb') : $this->getTranslation()->_('currency_sgd');
            }

            //基本薪资
            $money_cost = $v['money'] > 0 ? bcdiv($v['money'], 100, 0).$currency : '';
            // 通过试用期工资
            $trial_salary = $v['trial_salary'] > 0 ? bcdiv($v['trial_salary'], 100, 0).$currency : '';
            //职位补贴
            $position_cost = $v['position'] > 0 ? bcdiv($v['position'], 100, 0).$currency : '';
            //工作经验补贴
            $exp_cost = $v['exp'] > 0 ? bcdiv($v['exp'], 100, 0).$currency : '';
            //租车津贴
            $rentalcost = $v['rental'] > 0 ? bcdiv($v['rental'], 100, 0).$currency : '';
            //餐补
            $food_cost = $v['food'] > 0 ? bcdiv($v['food'], 100, 0).$currency : '';
            //危险区域津贴
            $dangerously_cost = $v['dangerously'] > 0 ? bcdiv($v['dangerously'], 100, 0).$currency : '';
            $staffJobTitle = $staffList[$staffId]['job_title'] ?? 0;
            //车辆类型
            $car_type = $this->getCarTypeByJotTitle($staffJobTitle);
            //车牌号
            $car_number = $v['car_number'] ?? '';
            //上牌地点
            $place_cards = $v['place_cards'] ?? '';
            $car_engine_number = $v['car_engine_number'] ?? '';
            $recordEntry = json_decode($v['record_entry'], true);

            if(isset($recordEntry['is_disability'])){
                $is_disability =  $recordEntry['is_disability'] == 1? $this->getTranslation()->_('qa30141') : $this->getTranslation()->_('qa30142');
                $disability_certificate_no = $recordEntry['disability_certificate']?? '';//残疾证证书编号
            }
            // 最新操作人
            $resume_last_operator_name = StaffServer::getStaffNameView($resume_last_operator_list[$v['resume_last_operator']] ?? [],2);

            // 部门链
            $department_level = (new DepartmentServer())->getDepartmentLevel($department_id, $departmentDataList);

            $hire_type_text = $v['hire_type'] ? $this->getTranslation()->_('hire_type_' . $v['hire_type']) : '';

            $_data[$k][] = $cvId;
            $_data[$k][] = $hcId;
            $_data[$k][] = $staffId;//工号
            $_data[$k][] = $call_name;//称呼
            $_data[$k][] = $first_name;//名
            $_data[$k][] = $last_name;//姓
            $_data[$k][] = $name;//全名
            $_data[$k][] = $bi_name_en;//英文名(员工信息)
            $_data[$k][] = $mobile;//手机号
            $_data[$k][] = $email;//个人邮箱
            $_data[$k][] = $job_title;//职位
            $_data[$k][] = $hire_type_text;//雇佣类型
            $_data[$k][] = $sys_store_id;//所属网点
            $_data[$k][] = $birth_date;//出生日期
            $_data[$k][] = $age;//年龄
            $_data[$k][] = $nationality;//国籍
            $_data[$k][] = $working_country;//工作国家
            $_data[$k][] = $address_id;//工作城市
            $_data[$k][] = $is_disability ?? ''; //是否残疾
            $_data[$k][] = $disability_certificate_no ?? '';//残疾人证书编号
            $_data[$k][] = $createtime;//入库时间
            $_data[$k][] = $work_time;//预计入职时间
            $_data[$k][] = $entry_date;//入职时间
            $_data[$k][] = $operate_date;//办理时间
//            $_data[$k][] = $equipment_cost;//设备费 21083 删除设备费相关
            // 身份证确认状态
            if (isCountry('TH') || isCountry('PH') || isCountry('MY')) {
                $_data[$k][] = isset(HrResumeModel::$identity_validate_status_text[$v['identity_status']]) ? $this->getTranslation()->_(HrResumeModel::$identity_validate_status_text[$v['identity_status']]) : '';
            }
            $_data[$k][] = $statusTransfer[$v['status']];//状态

            $_data[$k][] = $bi_identity;//身份证/护照(员工信息)
            $_data[$k][] = $register_locale;//户口所在地
            $_data[$k][] = $bi_department;//部门(员工信息)
            $_data[$k][] = $department_level[0] ?? '-';
            $_data[$k][] = $department_level[1] ?? '-';
            $_data[$k][] = $department_level[2] ?? '-';
            $_data[$k][] = $department_level[3] ?? '-';
            $_data[$k][] = $department_level[4] ?? '-';
            $_data[$k][] = $department_level[5] ?? '-';
            $_data[$k][] = $department_level[6] ?? '-';
            $_data[$k][] = $bi_job_title;//职位(员工信息)
            $_data[$k][] = $bi_job_title_level;//职级
            $_data[$k][] = $bi_area;//所属区域
            $_data[$k][] = $bi_sys_store_id;//所属网点(员工信息)
            $_data[$k][] = $bi_work_day;//工作天数
            $_data[$k][] = $bi_bank_no;//银行卡号(员工信息)
            $_data[$k][] = (isset($resumeApprovals[$v['resume_id']]) && $resumeApprovals[$v['resume_id']]['submitter_id'] == $paramIn['userinfo']['id']) || $paramIn['userinfo']['salary_structure_permission'] == enums::$salary_structure_permission['on'] ? $money_cost : '-';//基本薪资
            $_data[$k][] = (isset($resumeApprovals[$v['resume_id']]) && $resumeApprovals[$v['resume_id']]['submitter_id'] == $paramIn['userinfo']['id']) || $paramIn['userinfo']['salary_structure_permission'] == enums::$salary_structure_permission['on'] ? $trial_salary: '-';//通过试用期薪资
            $_data[$k][] = (isset($resumeApprovals[$v['resume_id']]) && $resumeApprovals[$v['resume_id']]['submitter_id'] == $paramIn['userinfo']['id']) || $paramIn['userinfo']['salary_structure_permission'] == enums::$salary_structure_permission['on'] ? $position_cost: '-';// 职位补贴
            $_data[$k][] = (isset($resumeApprovals[$v['resume_id']]) && $resumeApprovals[$v['resume_id']]['submitter_id'] == $paramIn['userinfo']['id']) || $paramIn['userinfo']['salary_structure_permission'] == enums::$salary_structure_permission['on'] ? $exp_cost: '-';// 工作经验补贴
            $_data[$k][] = (isset($resumeApprovals[$v['resume_id']]) && $resumeApprovals[$v['resume_id']]['submitter_id'] == $paramIn['userinfo']['id']) || $paramIn['userinfo']['salary_structure_permission'] == enums::$salary_structure_permission['on'] ? $rentalcost: '-';// 租车津贴
            $_data[$k][] = (isset($resumeApprovals[$v['resume_id']]) && $resumeApprovals[$v['resume_id']]['submitter_id'] == $paramIn['userinfo']['id']) || $paramIn['userinfo']['salary_structure_permission'] == enums::$salary_structure_permission['on'] ? $food_cost: '-';// 餐补

            $_data[$k][] = $dangerously_cost;//危险区域津贴
            $_data[$k][] = $car_type;//车辆类型
            $_data[$k][] = $car_number;//车牌号
            $_data[$k][] = $place_cards;//上牌地点
            $_data[$k][] = $car_engine_number;//发动机号

            if (isCountry('TH') || isCountry('PH') || isCountry('MY')) {
                $_data[$k][] = $resume_last_operator_name;
            }
        }
        /* 返回格式 */
        return $_data;
    }



    /**
     * 职级查询
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function level_grade($paramIn = [])
    {
        try {
            $department_id = $paramIn['department_id'] ?? 0 ;
            $job_title_id  = $paramIn['job_title_id'] ?? 0;
            $returnData = [
                'code' => 1,
                'msg'  => 'ok',
                'data' => [],
            ];
            $relationModel = HrJobDepartmentRelationModel::findFirst([
                'columns'=>'job_level',
                'conditions'=>'department_id = :department_id: and job_id = :job_id:',
                'bind' => ['department_id'=>$department_id,'job_id' => $job_title_id],
            ]);
            if($relationModel){
                $job_level = explode(',',$relationModel->job_level);
                foreach ($job_level as $value){
                    $returnData['data'][] = ['id'=>$value,'text'=>'F'.$value];
                }


            }
            return $returnData;
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("level_grade 异常信息:" . $e->getMessage(), 'error');
            return $this->checkReturn(-3, $this->getTranslation()->_('4008'));
        }
    }



    /**
     * 取消入职
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function cancelEntry($paramIn = [])
    {
        //[1]获取参数
        $resume_id  = $this->processingDefault($paramIn, 'resume_id');
        $entry_id   = $this->processingDefault($paramIn, 'entry_id');
        $staffId    = $this->processingDefault($paramIn, 'staff_id');
        $is_add_blk = $this->processingDefault($paramIn, 'is_add_blacklist', 2, 0);

        //入职取消类型记录
        $entry_cancel_type           = $this->processingDefault($paramIn, 'cancel_type', 2, 1);
        $entry_cancel_reason_type    = $this->processingDefault($paramIn, 'cancel_reason_type', 2, 0);
        $entry_cancel_reason_content = $this->processingDefault($paramIn, 'cancel_reason_content');

        //[2]验证入职
        $entrySql  = "--
                        select * from hr_entry 
                        where deleted = 0 and entry_id = {$entry_id} ";
        $data      = $this->getDI()->get('db')->query($entrySql);
        $entryData = $data->fetch(\Phalcon\Db::FETCH_ASSOC);
        if (!empty($entryData)) {
            $offerIds = $entryData['interview_offer_id'];
        } else {
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('err_msg_offer_not_exist')));
        }

        if ($entryData['status'] == enums::$entry_status['entry_hired']) { //验证已入职
            throw new BusinessException($this->getTranslation()->_('4012'), 4012);
        }

        //[3]取消offer
        $returnArr = (new InterviewServer($this->lang))->cancelSendOffer([
            'id'                          => $offerIds,
            'cancel_type'                 => 9, //未到岗
            'cancel_reason'               => 'not on board',
            'is_blacklist'                => $is_add_blk ?? 0,
            'entry_cancel_type'           => $entry_cancel_type,
            'entry_cancel_reason_type'    => $entry_cancel_reason_type,
            'entry_cancel_reason_content' => $entry_cancel_reason_content,
        ], 'entry_list');


        if ($returnArr) {
            if (isCountry('PH')) {
                $cancelEntry                          = [];
                $cancelEntry['cancel_type']           = $entry_cancel_type;
                $cancelEntry['cancel_reason_type']    = $entry_cancel_reason_type;
                $cancelEntry['cancel_reason_content'] = $entry_cancel_reason_content;
            }

            //添加日志
            (new \FlashExpress\bi\App\Repository\LogRepository())->addLog([
                'module_id'     => $entryData['interview_id'],
                'module_type'   => enums::$log_module_type['entry'],
                'action'        => enums::$log_option['confirmed'],
                'staff_info_id' => $staffId,
                'module_status' => enums::$log_status['missing'],
                'data_after'    => json_encode($cancelEntry),
            ]);
        }
        return $returnArr;
    }


    /**
     * 获取系统配置
     * @return mixed
     * @throws Exception
     */
    protected function getSysInfoFromHRIS($params)
    {
        //hris系统配置
        $ac = new ApiClient('hr_rpc', '', 'sys_info', $this->lang);
        $ac->setParams($params);
        $result = $ac->execute();
        if (!isset($result['result']['code']) || $result['result']['code'] != 0) {
            throw new Exception('获取sys_info异常 ');
        }
        return $result['result'];
    }


    /**
     * 获取入职员工信息
     * @param $staff_info_id
     * @return mixed
     * @throws Exception
     */
    protected function getStaffInfoFromHRIS($staff_info_id)
    {
        $ac = new ApiClient('hr_rpc', '', 'staff_view', $this->lang);
        $ac->setParams([
            'staff_info_id' => $staff_info_id,
        ]);
        $result = $ac->execute();
        if (!isset($result['result']['code']) || $result['result']['code'] != 0) {
            throw new Exception('获取staff_view异常');
        }
        return $result['result'];
    }

    /**
     * 验证是否能编辑入职信息
     * @param $position_category
     * @param $staffInfo
     * @return int
     */
    protected function checkIsEdit($position_category, $staffInfo): int
    {
        // 在职
        if (isset($staffInfo['state']) && isset($staffInfo['wait_leave_state']) && $staffInfo['state'] == HrStaffInfoModel::STAFF_STATE_IN && $staffInfo['wait_leave_state'] == 0) {
            if (array_intersect($position_category, [RolesModel::HRIS_MANAGER, RolesModel::PERSONNEL_MANAGER])) {
                $is_edit_state = 1;
            } else {
                $is_edit_state = 0;
            }
        } else {
            $is_edit_state = 1;
        }
        return $is_edit_state;
    }


    /**
     * 入职详情
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function infoEntry($paramIn = [])
    {
        $this->entry      = new EntryRepository();
        $SysListServer    = new SysListServer();

        $position_category = $paramIn['userinfo']['position_category'];
        try {

            /* 获取staff_id */
            $entry_info    = $this->entry->getEntryInfo($paramIn);
            if(empty($entry_info)){
                throw new ValidationException($this->getTranslation()->_('8402'));
            }
            $staff_info_id = $entry_info['staff_id'];

            $data = $this->getSysInfoFromHRIS(['store_position', 'header_office_position']);

            $positionsData  = array_merge($data['body']['header_office_position'], $data['body']['store_position']);

            $data = $this->getStaffInfoFromHRIS($staff_info_id);
            $data['body']['nickname'] = $data['body']['nick_name'] ?? '';
            $data['body']['other'] = $data['body']['remarks'] ?? '';
            unset($data['body']['remarks']);

            $data['body']['nickname'] = $data['body']['nick_name'] ?? '';
            $data['body']['other'] = $data['body']['remarks'] ?? '';
            unset($data['body']['remarks']);
            /* 业务处理 */
            //性别
            if ($data['body']['sex'] == 0) {
                $data['body']['sex_name'] = $this->getTranslation()->_('4902');
                $data['body']['sex']      = 0;
            } else if ($data['body']['sex'] == 1) {
                $data['body']['sex_name'] = $this->getTranslation()->_('4900');
                $data['body']['sex']      = 1;
            } else {
                $data['body']['sex_name'] = $this->getTranslation()->_('4901');
                $data['body']['sex']      = 2;
            }

            foreach ($data['body']['position_category'] as $kk => $vv) {
                $data['body']['position_category_value'][$kk] = $positionsData[$vv];
            }
            foreach ($data['body']['position_category'] as $kk => $vv) {
                $data['body']['position_category'][$kk] = (int)$vv;
            }

            //返回mobile_area_code字段
            $entryDetail = isset($entry_info['record_entry']) && $entry_info['record_entry'] ? json_decode($entry_info['record_entry'], true) : [];

            if (!empty($entryDetail)) {
                $workingDayRestType = $entryDetail['working_day_rest_type'] ?? '';
                $data['body']['rest_type']= str_split($workingDayRestType)[1] ?? 0;
                $data['body']['week_working_day']= $entryDetail['week_working_day'] ?? 0;
                $data['body']['working_day_rest_type']= $workingDayRestType;
                $data['body']['working_day_rest_type_text']= $entryDetail['working_day_rest_type'] ? static::$t->_('working_day_rest_type_'.$entryDetail['working_day_rest_type']) : '';
                $data['body']['default_rest_day_date']= $entryDetail['default_rest_day_date'] ?? [];
                $data['body'] = array_merge($data['body'], ['mobile_area_code' => $entryDetail['mobile_area_code']]);

                if (isCountry()) {
                    $data['body']['project_num']= !empty($entryDetail['project_num']) ? $entryDetail['project_num'] : null;
                }

                //contract_rm_num1 & contract_rm_num2
                $data['body']['contract_rm_num1']= $entryDetail['contract_rm_num1'] ?? '';
                $data['body']['contract_rm_num2']= $entryDetail['contract_rm_num2'] ?? '';
            }

            $shiftInfo = $this->getStaffShiftInfo($staff_info_id,$entryDetail);

            $resume_id = $entry_info['resume_id'];
            /* 获取简历详情 */
            $resumeInfo = (new ResumeRepository())->resumeInfo($resume_id);
            $data['body']['recruit_channel'] = $resumeInfo['recruit_channel'];
            $data['body']['recruit_channel_text'] = empty($resumeInfo['recruit_channel']) ? $SysListServer->getRecruitChannelList($resumeInfo['recruit_channel']) : '';

            /* 返回格式 */
            $returnArr['data'] = array_merge($data['body'],$shiftInfo);
            $returnArr['data']['departments'] = [];
            $returnArr['data']['is_edit_state'] = $this->checkIsEdit(explode(',',$position_category),$data['body']);

        } catch (ValidationException $e) {
            return $this->jsonReturn($this->checkReturn(-3,$e->getMessage()));
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("entry infoEntry 异常信息:" . $e->getMessage(), 'error');
            return $this->checkReturn(-3, $this->getTranslation()->_('4008'));
        }
        return $this->checkReturn($returnArr);

    }

    public function getStaffShiftInfo($staff_info_id,$entryDetail)
    {

        $data['shift_id'] = 0;
        $data['shift_type'] = '';
        $data['shift_type_text'] = '';
        $data['shift_text'] = '';

        //获取工号当前班次
        $ac = new ApiClient('hr_rpc', '', 'staff_shift', $this->lang);
        $ac->setParams([
            'staff_info_id' => $staff_info_id,
        ]);
        $shift_data = $ac->execute();
        if (isset($shift_data['result']) && $shift_data['result']) {
            $shift_list = (new SysListRepository())->getAllShiftList();
            $shift_list = array_column($shift_list, null, 'id');
            $shift_id = $shift_data['result']['shift_id'];
            $shift_type = $shift_data['result']['shift_type'];
            $data['shift_id'] = $shift_id;
            $data['shift_type'] = $shift_type;
            $data['shift_type_text'] = empty($shift_type) ? '' : $this->getTranslation()->_('shift_'.$shift_type);
            $data['shift_text'] = ($shift_list[$shift_id]['start'] ?? '').'-'.($shift_list[$shift_id]['end'] ?? '');
        }
        return $data;

    }


    /**
     * sysinfo
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function sysinfo($paramIn = [])
    {
        try {
            //不区分用户
            $redisKey = 'winhr-sysinfo-v2_2'.$this->lang;
            $cache      = $this->getDI()->get('redis');
            if ($returnData = $cache->get($redisKey)) {
                return json_decode($returnData,true);
            }

            /* 获取token */
            $userId        = $paramIn['userinfo']['id'];
            $bi_url        = $paramIn['bi_url'];
            $time          = time();
            $pwd           = md5('fbi' . $userId . $time) . $time;
            $tokenUrl      = $bi_url . '/v1/login?';
            $tokenPostData = [
                'username' => $userId,
                'password' => $pwd,
            ];
            $data          = post_curl($tokenUrl, $tokenPostData, [], $this->lang);
            $data          = json_decode($data, true);
            $token         = '';
            if ($data['code'] == 0) {
                $token = $data['body']['token'];
            }
            $log_data = [
                'tokenurl'=>$tokenUrl,
                'post_data'=>$tokenPostData,
            ];
            $this->getDI()->get('logger')->write_log("entry-sysinfo-hris-login:request:".json_encode($log_data).",response:".json_encode($data) , 'info');

            /* 获取各id对应值 */
            $headers           = ['Authorization: Bearer ' . $token];
            $infoUrl           = $bi_url . '/v1/staffs/sysinfo?';
            $data              = get_curl($infoUrl, $headers, $this->lang);
            $request_log = ['url'=>$infoUrl,'header'=>$headers,'lang'=>$this->lang];
            $this->getDI()->get('logger')->write_log("entry-sysinfo:" . $data.",request:".json_encode($request_log), 'info');
            $data              = json_decode($data, true);

            $returnArr['data'] = $data['body'];
            //获取hc优先级
            $priorityArr = UC('hc')['priorityArr'];
            foreach ($priorityArr as $k => $v) {
                $priority[$k]['id']   = $k;
                $priority[$k]['text'] = $this->getTranslation()->_($v);
            }
            $returnArr['data']['priority'] = $priority;
            $result = $this->checkReturn($returnArr);
            $cache->save($redisKey, json_encode($result), 120);
            return $result;
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("entry sysinfo 异常信息:" . $e->getMessage(), 'error');
            return $this->checkReturn(-3, $this->getTranslation()->_('4008'));
        }
    }

    /**
     * 已发offer列表
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function offerStaffList($paramIn = [])
    {
        $this->entry      = new EntryRepository();

        try {
            $staffData         = $this->entry->offerStaffList();
            $returnArr['data'] = $staffData;
            return $this->checkReturn($returnArr);
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("entry offerStaffList 异常信息:" . $e->getMessage(), 'error');
            return $this->checkReturn(-3, $this->getTranslation()->_('4008'));
        }
    }


    public function manager($paramIn = [])
    {
        try {
            $redisKey = 'manager:'.md5(json_encode($paramIn));
            $cache      = $this->getDI()->get('redis');
            if ($returnData = $cache->get($redisKey)) {
                return $this->checkReturn(json_decode($returnData,true));
            }

            /* 获取token */
            $type              = $paramIn['type'];
            $name              = $paramIn['name'];
            $sys_store_id      = $paramIn['sys_store_id'];
            $sys_department_id = $paramIn['sys_department_id'];
            $positions         = $paramIn['positions'] ? $paramIn['positions'] : [];

            $param             = [
                'type'              => $type,
                'name'              => $name,
                'sys_store_id'      => $sys_store_id,
                'sys_department_id' => $sys_department_id,
                'positions'         => $positions,
            ];

            $ac = new ApiClient('hr_rpc', '', 'staffs-manager', $this->lang);
            $ac->setParams($param);
            $data = $ac->execute();
            if ($data && $data['result']) {
                $data = $data['result'];
            }

            $returnArr['data'] = $data['body'];
            $cache->save($redisKey, json_encode($returnArr), 3600);
            return $this->checkReturn($returnArr);
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("entry manager 异常信息:" . $e->getMessage(), 'error');
            return $this->checkReturn(-3, $this->getTranslation()->_('4008'));
        }
    }

    /**
     * 记录入职信息
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function entryRecEdit($paramIn = [])
    {
        $this->entry      = new EntryRepository();
        $this->log        = new LogServer();

        try {
            $entry_id = intval($paramIn['entry_id']);
            $entryPic = $paramIn['profile_object_key'];
            $other = $paramIn['other'];
            if(strlen($other) > 200){
                return $this->checkReturn(-3, $this->getTranslation()->_('remark_error'));
            }
            if(isset($paramIn['job_title_level'])){ //删除职等信息
                unset($paramIn['job_title_level']);
            }

            $validations = [
            ];
            //企业邮箱
            if(isset($paramIn['email']) && !empty($paramIn['email'])){
                //过滤邮箱中特殊不可见字符
                $paramIn['email'] = nameSpecialCharsRepalce($paramIn['email'] ?? '');
                $validations['email'] = "Required|Email|>>>:" .$this->getTranslation()->_('7028').':'. $this->getTranslation()->_('4109');
            }

            if(isset($paramIn['personal_email']) && !empty($paramIn['personal_email'])){
                //过滤邮箱中特殊不可见字符
                $paramIn['personal_email'] = nameSpecialCharsRepalce($paramIn['personal_email'] ?? '');
                $validations['personal_email'] = "Required|Email|>>>:" .$this->getTranslation()->_('7027').':'.$this->getTranslation()->_('4109');
            }
            if($validations){
                $this->validateCheck($paramIn, $validations);
            }

            //校验是否已入职
            $entrySql  = "--
                select 
                    *
                from hr_entry 
                where hr_entry.entry_id=" . $entry_id;
            $data      = $this->getDI()->get('db')->query($entrySql);
            $entryData = $data->fetch(\Phalcon\Db::FETCH_ASSOC);
            if (!$entryData) {
                return $this->checkReturn(-3, $this->getTranslation()->_('4012'));
            }

            if (!empty($entryData['hc_id'])){
                (new HcServer())->checkHcHireType($entryData['hc_id'],$this->lang);
            }
            
            // 验证 证件号和手机号是否重复
            $resumeServerObj = new ResumeServer();
            $resumeServerObj->checkCredentialsNumANDPhoneNum($paramIn['identity'], $paramIn['mobile'] ,'whr', $entryData['resume_id']);
            (new OutsourcingBlackListServer())->check($paramIn['identity'],'winhr',true,$this->lang);

            //校验黑名单
//            $isBalcklist = (new BlacklistRepository())->blacklistCheck([
//                "resume_id" => $entryData['resume_id'],
//            ]);
//            if ($isBalcklist > 0) {
//                return $this->checkReturn(-3, $this->getTranslation()->_('7500'));
//            }

            //校验班次id是否正确
            /*
            $shift_list = (new SysListRepository())->getAllShiftList();
            $shift_list = array_column($shift_list, null, 'id');
            if(!isset($shift_list[$paramIn['shift_id']])) {
                return $this->checkReturn(-3, 'shift ID error');
            }
            */

            //拦截 车辆来源不是公司车辆，无法办理入职
            if (
                isCountry()
                && !empty($paramIn['position_id'])
                && !empty($paramIn['vehicle_source'])
                && ($paramIn['position_id'] == enumsTh::JOB_TITLE_EV_COURIER)
                && ($paramIn['vehicle_source'] != 2)) //不用枚举 客户端枚举错误
            {
                throw new ValidationException($this->getTranslation()->_('vehicle_source_error_not_entry'));
            }

            //记录传入参数
            $this->getDI()->get('logger')->write_log("entryRecEdit 传入参数:" . json_encode($paramIn), 'info');

            //拼装头像图片
            $paramIn['avatar_pic'] = $entryPic ? env('img_prefix') . $entryPic : "";

            //校验办理入职接口参数信息-世龙
            //$svc_call = env('hr_rpc');
            //$ret      = (new \FlashExpress\bi\App\Controllers\ControllerBase)->GetApiDatass('staff_info_create_vaildate', $svc_call, $paramIn);
            $apiClient = (new ApiClient('hr_rpc', '', 'staff_info_create_vaildate', $this->lang));
            $apiClient->setParams($paramIn);
            $ret = $apiClient->execute();

            if($ret["result"] != 1){
                return $this->checkReturn(-3, $ret["result"]);
            }

            $identityUrl = $this->getIdentityFrontUrl($entryData['resume_id']);
            if(!empty($identityUrl) && isCountry('TH')) {
                $identityData['resume_id']    = $entryData['resume_id'];
                $identityData['identity_url'] = $identityUrl;
                (new ResumeServer())->getIdentityFrontAiMosaic($identityData);
            }

            unset($paramIn["userinfo"]["permission_department_ids"]);
            unset($paramIn["userinfo"]["permission_stores_ids"]);
            //写入入职信息，也要更新入职时间
            $paramEntry = [
                "id"         => $entry_id,
                "updateData" => [
                    "record_entry" => json_encode($paramIn),
                    "entry_date"   => $paramIn['hire_date'],
                    "manager"   => $paramIn['manager'],
                ],
            ];
            if (isset($paramIn['hire_type'])){
                $paramEntry['updateData']['hire_type'] = $paramIn['hire_type'];
            }
            $this->entry->changeEntry($paramEntry);
            $interview_param = [
                'interview_offer_id' => $entryData['interview_offer_id'],
                'updateData' => [
                    'shift_id' => $paramIn['shift_id'],
                ],
            ];
            $this->entry->changeHrInterviewOffer($interview_param);
            $resumeParam['id'] = $entryData['resume_id'];
            $resumeParam['recruit_channel'] = $paramIn['recruit_channel'];
            (new ResumeRepository())->updateResumeInfo($resumeParam);//更新渠道
            //添加日志
            $this->log->addLog([
                'module_id'   => $entryData['interview_id'],
                'module_type' => enums::$log_module_type['handle_employment_info'],
                'action'      => enums::$log_option['submit'],
            ]);

            return $this->checkReturn([]);
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("entry recordEntry 异常信息:" . $e->getMessage(), 'info');
            return $this->checkReturn(-3, $e->getMessage());
        }
    }

    /**
     * 记录入职信息
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function entryRecDetail($paramIn = [])
    {
        $this->entry      = new EntryRepository();

        try {
            $entry_id = $paramIn['entry_id'];

            $result = $this->entry->getEntryDetail(['entry_id' => $entry_id]);
            if ($result) {
                $data = json_decode($result['record_entry'], true);

                if (!isset($data['hire_type'])) {

                    $data['hire_type'] = $result['hire_type'];
                    $data['hire_times'] = $result['hire_times'];

                }
                $data['hire_type_text'] = $data['hire_type'] ? $this->getTranslation()->_('hire_type_' . $data['hire_type']) : '';
                if (in_array($data['hire_type'], [3, 4])) {

                    $data['hire_times_text'] = $data['hire_times'] . $this->getTranslation()->_('daily');
                } else if (in_array($data['hire_type'], [2, HrhcModel::HIRE_TYPE_CONTRACT_LABOUR])) {
                    $data['hire_times_text'] = $data['hire_times'] . $this->getTranslation()->_('monthly');
                } else {

                    $data['hire_times_text'] = '';
                }

                $data['shift_id'] = $data['shift_id'] ?? 0;
                $data['shift_type'] = '';
                $data['shift_type_text'] = '';
                $data['shift_text'] = '';
                $shift_list = (new SysListRepository())->getAllShiftList();
                $shift_list = array_column($shift_list, null, 'id');
                if(isset($shift_list[$data['shift_id']])) {
                    $data['shift_type'] = $shift_list[$data['shift_id']]['type'] ?? '';
                    $data['shift_type_text'] = empty($data['shift_type']) ? '' : $this->getTranslation()->_('shift_'.$data['shift_type']);
                    $data['shift_text'] = ($shift_list[$data['shift_id']]['start'] ?? '').'-'.($shift_list[$data['shift_id']]['end'] ?? '');
                }
                if(isset($data['job_title_level'])){ //删除职等信息
                    unset($data['job_title_level']);
                }
	            //工作所在州
	            $data['staff_province_code'] = $data['staff_province_code'] ?? '';
                //验证权限附件  没有权限 把残疾人证 隐藏
                $resume_id = $result['resume_id'];
                $interview_id = $result['interview_id'];
                $resumeServer = new ResumeServer();
                $resume_info = $resumeServer->getResumeBaseInfo($resume_id);
                $interveiw_info = (new InterviewServer())->getInterviewInfo($interview_id);
                $is_have_annex_permission = $resumeServer->isHaveSalaryOrAnnexPermission(
                    $resume_id,
                    $resume_info['filter_state'],//入职页面简历筛选阶段已走过，无需在判断
                    $interveiw_info['interview_state'],
                    $resume_info['recruiter_id'],
                    $this->userInfo['id'],
                    $resumeServer->checkIsNetworkFirstLineJD($resume_info['job_id'])?1:$this->userInfo['resume_attachment_permission']
                );
                if(!$is_have_annex_permission){
                    $data['disability_certificate_front'] = $this->getTranslation()->_('unauthorized_picture');
                    $data['disability_certificate_reverse'] = $this->getTranslation()->_('unauthorized_picture');
                }

                return $this->checkReturn(["data" => $data]);
            }
            return $this->checkReturn([]);
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("entry recordEntry 异常信息:" . $e->getMessage(), 'error');
            return $this->checkReturn(-3, $e->getMessage());
        }
    }

    /**
     * 记录入职信息
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function entryRecDetailForBy($paramIn = [], $lang = 'zh')
    {
        $this->entry      = new EntryRepository();
        $this->staff      = new StaffRepository();
        $this->sysList    = new SysListRepository();
        $this->lang = $lang;

        //[1]获取参数
        $resume_id = $this->processingDefault($paramIn, 'resume_id');

        //[2]获取详情
        $result = $this->entry->getEntryInfoByEntryId(['resume_id' => $resume_id]);
        if (empty($result)) {
            throw new BusinessException($this->getTranslation()->_('4012'));
        }

        //查询网点名称 ids
        $worknodeIdsArr = [$result['worknode_id']] ?? '';
        $worknodeIds    = getIdsStr($worknodeIdsArr);
        $worknodeData   = $this->sysList->getStoreList([
            "ids" => $worknodeIds,
        ]);
        $worknodeData   = array_column($worknodeData, 'name', 'id');

        //查询部门名称 ids
        $departmentIdsArr   = [$result['department_id']];
        $departmentIds      = getIdsStr($departmentIdsArr);
        $departmentData     = $this->sysList->getDepartmentList([
            "ids" => $departmentIds,
        ]);
        $departmentDataList = array_column($departmentData, 'name', 'id');

        //查询职位名称 ids
        //暂时先不显示主管的职位
        $positionIdsArr = [$result['position_id']];
        $positionIds    = getIdsStr($positionIdsArr);
        $positionData   = $this->sysList->getPositionList([
            "ids" => $positionIds,
        ]);
        $positionList   = array_column($positionData, 'job_name', 'id');

        //岗位名称
        $jobIdsArr   = [$result['job_id']];
        $jobIds      = getIdsStr($jobIdsArr);
        $jobList     = $this->sysList->getJobList([
            "ids" => $jobIds,
        ]);
        $jobListData = array_column($jobList, 'job_name', 'job_id');

        //计算年龄
        if ($result['date_birth']) {
            if ((int)date("Y") < (int)date('Y', strtotime($result['date_birth']))) {
                $age = (int)(date("Y-m-d") - ($result['date_birth'] - 543));
            } else {
                $age = (int)(date("Y-m-d") - $result['date_birth']);
            }
        } else {
            $age = '';
        }

        //获取网点主管
        $managerInfo = $this->staff->getStoreManager($result['worknode_id']);

        //性别
        $sex = (new \FlashExpress\bi\App\Server\ResumeServer())->getResumeSex($result['sex'], $result['call_name']);

        //获取简历相关照片-选择半身照
        $annexObj = HrAnnexModel::find([
            'conditions' => 'type = 1 and file_type in ({file_type:array}) and oss_bucket_key = :resume_id: and deleted = 0 ',
            'bind'       => [
                'resume_id' => $resume_id,
                'file_type' => [HrAnnexModel::FILE_TYPE_PERSON_HALF_PIC, HrAnnexModel::FILE_TYPE_IDENTITY_FRONT],
            ],
        ])->toArray();
        $avator = $identityFrontUrl = '';
        foreach ($annexObj as $one) {
            if($one['file_type'] == HrAnnexModel::FILE_TYPE_PERSON_HALF_PIC) {
                $avator = env('img_prefix') . $one['object_key'];
            }

            if($one['file_type'] == HrAnnexModel::FILE_TYPE_IDENTITY_FRONT) {
                $identityFrontUrl = env('img_prefix') . $one['object_key'];
            }
        }

        // 是否展示辅导员项
        // 展示条件：Network部门的 DC officer、Bike Courier、Van Courier三种职位
        $is_show_counselor = $this->isShowCounselor($result);

        // 获取该员工辅导员信息
        $counselor = '';
        if ($is_show_counselor && $result['staff_id']) {
            // 该员工
            $staff_info = $this->staff->getStaffInfoById($result['staff_id']);

            // 辅导员
            if ($staff_info['instructor_id']) {
                $counselor_info = $this->staff->getStaffInfoById($staff_info['instructor_id']);
                $counselor = "（{$counselor_info['staff_info_id']}）{$counselor_info['name']}";
            }
        }


        $config_shift_list = [];

        $shift_type = '';
        $shift_type_text ='';
        $shift_text = '';
        $this->lang = $lang;
        if($result['worknode_id'] != '-1') {
            $config_shift_list = (new InterviewServer())->getAllShift($result['department_id'], $result['position_id'], $result['worknode_id']);

            $shift_list = (new SysListRepository())->getAllShiftList();
            $shift_list = array_column($shift_list, null, 'id');
            //班次信息
            $shift_id = $result['shift_id'];
            if(isset($shift_list[$shift_id])) {
                $shift_type = $shift_list[$shift_id]['type'] ?? '';
                $shift_type_text = empty($shift_type) ? '' : $this->getTranslation()->_('shift_'.$shift_type);
                $shift_text = ($shift_list[$shift_id]['start'] ?? '').'-'.($shift_list[$shift_id]['end'] ?? '');
            }
        }

        $recordEntry = !empty($result['record_entry']) ? json_decode($result['record_entry'], true) : [];

        $resumeExtendInfo = (new ResumeExtendRepository())->getInfoByResumeId($resume_id);

        $identity_code_url = !empty($resumeExtendInfo['identity_code_url']) ? $resumeExtendInfo['identity_code_url'] : '';
        //PH 没有打码的身份证图片
        if(isCountry('PH')) {
            $identity_code_url = $identityFrontUrl;
        }
        //非待入职：身份证确认状态为：待确认 展示为 未确认，已通过-》与本人相同，未通过-》与本人不同
        $identity_personal_verification_status_text = isset(HrResumeModel::$identity_validate_status_by_text[$result['identity_validate_status']]) ? $this->getTranslation()->_(HrResumeModel::$identity_validate_status_by_text[$result['identity_validate_status']]) : '';
        //待入职，身份证确认状态为：待确认 展示为 请确认
        if($result['status'] == HrEntryModel::STATUS_TO_BE_EMPLOYED && in_array($result['identity_validate_status'], [HrResumeModel::IDENTITY_VALIDATE_STATUS_PENDING])) {
            $identity_personal_verification_status_text = $this->getTranslation()->_('please_confirm');
        }
        
        //获取到岗确认操作人姓名。
        $operate_name = $this->getOperationInfo($result['status'], $result['interview_id']);
        $returnData = [
            'resume_id'               => $result['resume_id'],
            'name'                    => $result['name'],
            'store_name'              => $worknodeData[$result['worknode_id']] ?? '',
            'department_id'           => $result['department_id'],
            'department_name'         => $departmentDataList[$result['department_id']] ?? '',
            'identity'                => $result['credentials_num'],
            'phone_area_code'         => $result['phone_area_code'],
            'mobile'                  => $result['phone'],
            'sex'                     => $sex,
            'age'                     => $age,
            'store_manager_id'        => $managerInfo['staff_info_id'] ?? '',
            'store_manager_name'      => $managerInfo['name'] ?? '',
            'avator'                  => $avator,
            'entry_id'                => $result['entry_id'] ?? null,
            'entryform_submit_status' => $result['entryform_submit_status'] ?? 2,
            'status'                  => $result['status'] ?? 3,
            'job_title_id'            => $result['job_id'],
            'job_name'                => $jobListData[$result['job_id']] ?? '',
            'position_name'           => $positionList[$result['position_id']] ?? '',
            'work_time'               => $result['entry_date'] == '0000-00-00 00:00:00' || empty($result['entry_date']) ? $result['work_time'] : $result['entry_date'],
            'worknode_id'             => $result['worknode_id'],
            'is_show_counselor'       => $is_show_counselor,
            'counselor'               => $counselor,
            'counselor_id'            => isset($counselor_info['staff_info_id']) && $counselor_info['staff_info_id']
                                        ? $counselor_info['staff_info_id']: '',
            'shift_id'                => $result['shift_id'],
            'shift_type'              => $shift_type,
            'shift_type_text'         => $shift_type_text,
            'shift_text'              => $shift_text,
            'config_shift_list'       => $config_shift_list,
            'operator_name'           => $operate_name,
            'staff_id'                => $result['staff_id'] ?? '',//员工id
            'position_id'             => $result['position_id'] ?? '',//职位id
            'hire_type'               => $recordEntry['hire_type'] ?? '',//员工类型
            'job_title'               => $result['job_title'],

            'identity_personal_verification_status'       => $result['identity_validate_status'],
            'identity_personal_verification_status_text'  => $identity_personal_verification_status_text,
            'identity_code_url'  => $identity_code_url,//身份证图片
            'hand_identity_url'  => !empty($resumeExtendInfo['hand_identity_url']) ? $resumeExtendInfo['hand_identity_url'] : '',//身份证打码图片,//手持身份证照片(仅ph 有 其他国家返回空)
        ];

        if (isCountry('ph')) {
            $returnData['working_day_rest_type'] = $result['working_day_rest_type'] ?? 0;
            $returnData['working_day_rest_name'] = '';
            if ($returnData['working_day_rest_type']) {
                $returnData['working_day_rest_name'] = $this->getTranslation($lang)->_('working_day_rest_type_'.$returnData['working_day_rest_type']);
            }
            $returnData['default_rest_day_date'] = $result['default_rest_day_date'] ?? [];
            $returnData['default_rest_day_list'] = (new SysListServer())->getDefaultRestDay($lang);

            //添加取消列
            $returnData['cancel_type_list'] = [];
            foreach (HrEntryModel::$cancel_type_list as $k => $v) {
                $info = ['name' => $k, 'value' => self::$t->_($v)];
                $returnData['cancel_type_list'][] = $info;
            }

            $returnData['cancel_reason_type_list'] = [];
            foreach (HrEntryModel::$cancel_reason_type_list as $k => $v) {
                $info = ['name' => $k, 'value' => self::$t->_($v)];
                $returnData['cancel_reason_type_list'][] = $info;
            }

            $returnData["is_first_line_job"] = (new InterviewServer())->isFirstLineJob($result['department_id'], $result['job_title']);

            $returnData['cancel_type']      = $returnData['cancel_reason_type'] = 0;
            $returnData['cancel_type_name'] = $returnData['cancel_reason_type_name'] = $returnData['cancel_reason_content'] = '';
            if ($result['status'] == HrEntryModel::STATUS_NOT_EMPLOYED) {
                $returnData['cancel_type']             = $result['cancel_type'];
                $returnData['cancel_reason_type']      = $result['cancel_reason_type'];
                $returnData['cancel_type_name']        = self::$t->_(HrEntryModel::$cancel_type_list[$result['cancel_type']] ?? '');
                $returnData['cancel_reason_type_name'] = self::$t->_(HrEntryModel::$cancel_reason_type_list[$result['cancel_reason_type']] ?? '');
                $returnData['cancel_reason_content']   = $result['cancel_reason_content'] ?? '';
            }
            //新增 自由轮休配置天数
            $freeSetting                    = $this->getFreeLeaveConfig($returnData['work_time'],$result['worknode_id'], $result['job_title']);
            $returnData['free_workday_num'] = $freeSetting ? $freeSetting->days_num : null;
            $returnData['max_free_workday_num'] = $freeSetting ? $freeSetting->max_days_num : null;
        }

        if ($result) {
            return $this->checkReturn(["data" => $returnData]);
        }

        return $this->checkReturn([]);
    }

    /**
     * 是否展示辅导员
     * @param $staffInfo
     * @return int
     */
    public function isShowCounselor($staffInfo)
    {
        // 是否展示辅导员项
        // 展示条件：Network部门的 DC officer、Bike Courier、Van Courier三种职位
        $is_show_counselor = 0;
        // 获取Network部门清单
        $network_dep_ids = (new DepartmentRepository())->getDepartmentChildInfo(static::$counselor_department_id);
        if (in_array($staffInfo['department_id'], $network_dep_ids) && in_array($staffInfo['position_id'], static::$counselor_position_ids)) {
            $is_show_counselor = 1;
        }
         return $is_show_counselor;
    }

    /**
     * 获取到岗确认操作人信息
     * @param $status
     * @param $interview_id
     * @return string
     */
    public function getOperationInfo($status, $interview_id)
    {
        $operate_name = '';
        if($status != 2) {
            $log_status = $status == 1 ? enums::$log_status['arrived'] : enums::$log_status['missing'];
            $binds = [
                'interview_id' => $interview_id,
                'module_type' => enums::$log_module_type['entry'],
                'action' => enums::$log_option['confirmed'],
                'module_status' => $log_status,
            ];
            $conditions = ' module_id = :interview_id: and module_type = :module_type: and action = :action: and module_status = :module_status:';
            //查询有无action为关联hc_id 的log
            $logInfo = (new LogServer())->getLogOneInfo($conditions, $binds);
            if($logInfo) {
                $operateInfo = HrStaffInfoModel::findFirst([
                    'conditions' => "staff_info_id = :staff_info_id:",
                    "bind"       => ['staff_info_id' => $logInfo['staff_info_id']],
                    "columns"    => 'staff_info_id,name',
                ]);
                $operate_name = '(' . $logInfo['staff_info_id'] .')' . $operateInfo['name'];
            }
        }
        return $operate_name;
    }

    /**
     * 验证办理入职表格
     */
    public function checkEntry($paramIn = [])
    {
        $this->entry      = new EntryRepository();

        $entry_id       = $paramIn['entry_id'] ?? '';
        $department_id  = $paramIn['sys_department_id'] ?? '';
        $node_department_id = $paramIn['node_department_id'] ?? '';
        $store_id       = $paramIn['sys_store_id'] ?? '';
        $job_title      = $paramIn['job_title'] ?? '';

        if (empty($entry_id)) {
            return false;
        }
        $info = $this->entry->checkEntry($entry_id);
        if (!empty($info)) {
            if ($info['worknode_id'] == $store_id  &&
                $info['position_id'] == $job_title &&
                ($department_id == $info['department_id'] || $node_department_id == $info['department_id'])
            ) {
                return true;
            } else {
                return false;
            }
        }
    }

    /**
     * 补入职数据
     */
    public function fixEntryData()
    {
        $this->entry      = new EntryRepository();

        $entryData = $this->entry->checkEntryData();

        if (empty($entryData)) {
            return [];
        }

        $this->getDI()->get('logger')->write_log("补入职数据:" . json_encode($entryData), 'info');
        $this->getDI()->get('db')->begin();
        $this->entry->batch_insert('hr_entry', $entryData);
        $this->getDI()->get('db')->commit();
        return $entryData;
    }


    /**
     * 入职办理: 添加辅导员校验 [Network部门的 DC officer、Bike Courier、Van Courier三种职位 辅导员 必填]
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function entryAddCounselorCheck($paramIn = [])
    {
        $this->entry      = new EntryRepository();
        $this->department = new DepartmentRepository();

        $entry_id  = $paramIn['entry_id'];
        $instructor_id  = $paramIn['instructor_id'];

        try {
            $returnData = [
                'code' => 1,
                'msg'  => '',
                'data' => [],
            ];

            // 入职员工信息
            $info = $this->entry->checkEntry($entry_id);
            if (empty($info)) {
                $returnData['code'] = 0;
                $returnData['msg'] = $this->getTranslation()->_('err_msg_3');
            }

            // 校验
            // 获取Network部门清单
            $network_dep_ids = $this->department->getDepartmentChildInfo(static::$counselor_department_id);
            if (in_array($info['department_id'], $network_dep_ids) && in_array($info['position_id'], static::$counselor_position_ids) && empty($instructor_id)) {
                $returnData['code'] = 0;
                $returnData['msg'] = $this->getTranslation()->_('counselor_option_cannot_null');
            }

            return $returnData;
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("entry entryAddCounselorCheck 异常信息:" . $e->getMessage(), 'error');
            return $this->checkReturn(-3, $e->getMessage());
        }
    }

    /**
     * 验证残疾人信息是否完整
     * @param $paramIn
     * @return bool 是否完整
     */
    public function validate_disability_error($paramIn)
    {
        $is_disability = $paramIn['is_disability'] ?? -1;
        if ($is_disability == -1) {
            return true;
        }

        if (isCountry('TH') && ($paramIn['hire_type'] == HrStaffInfoModel::HIRE_TYPE_AGENT)) {
            return false;
        }

        if ($is_disability == 1) {//如果残疾
            if (!isset($paramIn['is_have_disability_certificate'])) {
                return true;
            }
            if ($paramIn['is_have_disability_certificate'] == 1) {//如果有残疾人证
            	//验证权限附件
	            if(isset($paramIn['userinfo']['resume_attachment_permission']) && $paramIn['userinfo']['resume_attachment_permission'] == enums::$resume_attachment_permission['off']){
		            $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('4009')));
	            }
                //残疾人证书编号必填，且是长度13的数字
                if (!isset($paramIn['disability_certificate']) || empty($paramIn['disability_certificate']) || !preg_match('/^[0-9]{13}$/', $paramIn['disability_certificate'])) {
                    return true;
                }

                if (!isset($paramIn['disability_certificate_front']) || empty($paramIn['disability_certificate_front'])) {//残疾人证 正面
                    return true;
                }
                if (!isset($paramIn['disability_certificate_reverse']) || empty($paramIn['disability_certificate_reverse'])) {//残疾人证 反面
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 获取简历信息中户籍地 地址详情
     * @param $resume_info
     * @param $provinceList 省 key value map列表
     * @param $cityList 市 key value map列表
     * @param $districtList 乡 key value map列表
     * @return string
     */
    public function getResumeRegisterAddress($resume_info,$provinceList,$cityList,$districtList){
        $register_country = $resume_info['register_country'] ?? '1'; //户口所在地

        $register_house_num   = $resume_info['register_house_num'] ?? '';
        $register_village_num = $resume_info['register_village_num'] ?? '';
        $register_village     = $resume_info['register_village'] ?? '';
        $register_alley       = $resume_info['register_alley'] ?? '';
        $register_street      = $resume_info['register_street'] ?? '';
        $district_name        = $register_country == '1' ? ($districtList[$resume_info['register_town']] ?? '') : ($resume_info['register_town'] ?? '');
        $city_name            = $register_country == '1' ? ($cityList[$resume_info['register_city']] ?? '') : ($resume_info['register_city'] ?? '');
        $province_name        = $register_country == '1' ? ($provinceList[$resume_info['register_government']] ?? '') : ($resume_info['register_government'] ?? '');

        //户籍地址-详情：门牌号+村号+村庄+巷+街道+乡+市+省+邮编
        if($register_country == '1'){
            //泰国国家 户籍地址格式：
            if (strtolower($resume_info['register_government']) == 'th01') {
                //泰国国家且省份是曼谷（后缀名不一样）

                $register_address = "บ้านเลขที่ {$register_house_num} หมู่ {$register_village_num} หมู่บ้าน {$register_village} ซอย {$register_alley} ถนน {$register_street} แขวง {$district_name} เขต {$city_name} จังหวัด {$province_name}";

            } else {
                //非曼谷省份后缀名不一样）
                $register_address = "บ้านเลขที่ {$register_house_num} หมู่ {$register_village_num} หมู่บ้าน {$register_village} ซอย {$register_alley} ถนน {$register_street} ตำบล {$district_name} อำเภอ {$city_name}  จังหวัด {$province_name}";

            }


        }else{
            //非泰国国家户籍地址格式：

            $register_address = "{$register_house_num} {$register_village_num} {$register_village} {$register_alley} {$register_street} {$district_name} {$city_name} {$province_name}";

        }

        return $register_address;
    }


    //获取 oa 组织架构下的网点数据   2. 若未给员工配置管辖范围，且员工是部门负责人&助理&AM&DM&网点主管，那么员工在WHR内可查看的数据则为其OA组织架构及其下级的所有数据；

	/**
	 * @description: 若未给员工配置管辖范围，且员工是部门负责人&助理&AM&DM&网点主管，那么员工在WHR内可查看的数据则为其OA组织架构及其下级的所有数据；
	 *             判断是否可以获取权限
	 *
	 * @param null
	 *
	 * @return     :
	 * <AUTHOR> L.J
	 * @time       : 2021/8/23 19:14
	 */
	public function getIsOaDepartmentStore($staff_info_id,$position_category)
	{
		if (empty($staff_info_id)) return false;
		//判断是不是 部门负责人 & 部门负责人的助理
		$deptManager = SysDepartmentModel::findFirst([
			                                             'conditions' => '(manager_id = :manager_id: or assistant_id = :assistant_id:) and type IN(2,3) and deleted = 0',
			                                             'bind'       => [
				                                             'manager_id'   => $staff_info_id,
				                                             'assistant_id' => $staff_info_id,
			                                             ],
		                                             ]);
		if (!empty($deptManager)) {
			$this->getDI()->get("logger")->write_log("getIsOaDepartmentStore  用户是部门负责人 & 部门负责人的助理  用户为:".$staff_info_id,"info");
			return true;
		}
		//判断是不是网点主管信息
		$cur_pos = $position_category;
		$tArr    = explode(",", $cur_pos);
		if (in_array(HcServer::ROLE_DOT_ADMIN, $tArr)) {
			$this->getDI()->get("logger")->write_log("getIsOaDepartmentStore  用户是网点主管  用户为:".$staff_info_id,"info");
			return true;
		}
		//判断是不是AM  大区负责人  sys_manage_region.manager_id
		$am = SysManageRegionModel::findFirst([
			                                      'conditions' => 'manager_id = :manager_id:  and deleted = 0',
			                                      'bind'       => [
				                                      'manager_id' => $staff_info_id,
			                                      ],
		                                      ]);
		if (!empty($am)) {
			$this->getDI()->get("logger")->write_log("getIsOaDepartmentStore  用户是AM  大区负责人  用户为:".$staff_info_id,"info");
			return true;
		}

		//判断是不是dM   片区负责人 sys_manage_piece.manager_id
		$dm = SysManagePieceModel::findFirst([
			                                     'conditions' => 'manager_id = :manager_id:  and deleted = 0',
			                                     'bind'       => [
				                                     'manager_id' => $staff_info_id,
			                                     ],
		                                     ]);
		if (!empty($dm)) {
			$this->getDI()->get("logger")->write_log("getIsOaDepartmentStore  用户是DM  片区负责人  用户为:".$staff_info_id,"info");
			return true;
		}

        //查询出管辖的网点
        $store = SysStoreModel::findFirst([
            'manager_id=:manager_id: ',
            'bind' => ['manager_id' => $staff_info_id],
            'columns' => ['id'],
        ]);

        if (!empty($store)) {
            $this->getDI()->get("logger")->write_log("getIsOaDepartmentStore  用户是网点负责人  用户为:".$staff_info_id,"info");
            return true;
        }

		$this->getDI()->get("logger")->write_log("getIsOaDepartmentStore  用户没有查询 OA 的权限:".$staff_info_id,"info");
		return false;

	}

    /**
     * 获取入职信息
     * @param $resumeId
     * @return null
     */
    public function getInfoByResumeId($resumeId, $columns = 'status,entry_id,staff_id')
    {
        if (!$resumeId) {
            return [];
        }

        $dataObj = HrEntryModel::findFirst([
            'columns'    => $columns,
            'conditions' => 'resume_id=:resume_id: and deleted = :deleted:',
            'bind'       => [
                'resume_id' => $resumeId,
                'deleted'   => enums::IS_DELETED_NO,
            ],
            'order'      => 'entry_id DESC',
        ]);

        return $dataObj ? $dataObj->toArray() : [];
    }

	/**
	 * @description:1. 登录WHR的用户，只能查看用户管辖范围内的数据
	1. 若给员工配置了管辖范围，那么员工在WHR内可查看的数据则为管辖范围内的数据；
	1. 管辖部门，决定了用户可查看部门下网点为Head officer的数据；
	2. 管辖大区、片区、网点，决定了用户可查看大区下的网点、片区下的网点、单个网点下的数据；
	2. 若未给员工配置管辖范围，且员工是部门负责人&助理&AM&DM&网点主管，那么员工在WHR内可查看的数据则为其OA组织架构及其下级的所有数据；
	 * 目前这个方法 就登录时用了
	 * @param $staff_info_id  用户 id
	 * @param $department_id  部门 id
	 * @return     :
	 * <AUTHOR> L.J
	 * @time       : 2021/8/23 19:43
	 */
	public function getAuthorityStoreIds($staff_info_id,$department_id,$position_category){
		$data =['stores_ids'=>[],'department_ids'=>[],'priority_ids' => [],'is_whr_permission' => false];
		if(empty($staff_info_id) && empty($department_id)){
			return $data;
		}

        // 获取whr的配置的权限
        if (isCountry(['th','ph','my'])) {
            [$priority_ids, $stores_ids] = (new PermissionServer())->getWhrDataPermission($staff_info_id);
            if (!empty($priority_ids) && !empty($stores_ids)) {
                $data['priority_ids'] = $priority_ids;
                $data['stores_ids']   = $stores_ids;
                $data['is_whr_permission'] = true;
                return $data;
            }
        }

		//获取管辖区域权限
		$areas_stores = $this->getStaffManageDepartmentsAreasStores($staff_info_id);
		$this->getDI()->get("logger")->write_log("获取管辖区域权限 getAuthorityStoreIds ".json_encode($areas_stores) ." 用户为:".$staff_info_id." 部门:".$department_id,"info");

		$data =['stores_ids'=>$areas_stores['stores_ids'],'department_ids'=>$areas_stores['department_ids']];

		//如果没有权限 && 并且可以获取 oa 权限
		if(empty($areas_stores['stores_ids']) && empty($areas_stores['department_ids']) && $this->getIsOaDepartmentStore($staff_info_id,$position_category)){
			$this->getDI()->get("logger")->write_log("用户可以获取OA数据权限 getAuthorityStoreIds ".json_encode($areas_stores) ." 用户为:".$staff_info_id." 部门:".$department_id,"info");
			//获取 oa 权限
			//oa 只有这些部门关联了网点
			$department = [25,57,58,59,106,65,95];
			//获取部门以及子部门
            //staff_info_id 修改查看范围为oa组织架构及其子部门
            $department_list = SysDepartmentModel::find([
                'conditions' => '(manager_id = :manager_id: or assistant_id = :assistant_id:) and type IN(2,3) and deleted = 0',
                'bind'       => [
                    'manager_id'   => $staff_info_id,
                    'assistant_id' => $staff_info_id,
                ],
            ])->toArray();

            $departmentIds = [];
            foreach ($department_list as $departmentInfo) {
                $departmentInfoIds = (new DepartmentRepository())->getDepartmentListById($departmentInfo['id']);
                $departmentIds = array_merge($departmentIds, $departmentInfoIds);
            }

            $data['department_ids'] = array_merge($data['department_ids'], $departmentIds);
			//获取交集
			$store_department = array_intersect($department_list,$department);
			$this->getDI()->get("logger")->write_log("用户可以获取OA数据权限的部门 getAuthorityStoreIds ".json_encode($store_department) ." 用户为:".$staff_info_id." 部门:".$department_id,"info");
			//如果包含这些部门 循环获取网点信息
			if(!empty($store_department)){
				foreach($store_department as $value){
					$oa_areas_stores = (new SysStoreServer())->getStoreList($value);
					//oa 部门下的网点
					$oa_areas_stores = array_column($oa_areas_stores,'id');
					$data['stores_ids'] = array_merge($data['stores_ids'], $oa_areas_stores);
				}
				$this->getDI()->get("logger")->write_log("获取oa权限结构 getAuthorityStoreIds ".json_encode($data) ." 用户为:".$staff_info_id." 部门:".$department_id,"info");
			}
			//大区负责人 或者片区负责人 获取大区//片区下的网点
			//查询出本人所管辖的大区
			$region_list =  SysManageRegionModel::find([
				                                    'manager_id=:manager_id: and deleted=0 ',
				                                    'bind' => ['manager_id' => $staff_info_id],
				                                    'columns' => ['id'],
			                                    ])->toArray();
			$mange_region_ids     = array_values(array_filter(array_unique(array_column($region_list, 'id'))));  //管辖大区
			//查询出本人所管辖的片区
			$piece_list =  SysManagePieceModel::find([
				                                           'manager_id=:manager_id: and deleted=0 ',
				                                           'bind' => ['manager_id' => $staff_info_id],
				                                           'columns' => ['id'],
			                                           ])->toArray();
			$mange_piece_ids      = array_values(array_filter(array_unique(array_column($piece_list, 'id'))));   //管辖片区
			$this->getDI()->get("logger")->write_log("获取oa权限结构查询出大区和片区 getAuthorityStoreIds 大区.".json_encode($mange_region_ids)." 片区" .json_encode($mange_piece_ids)." 用户为:".$staff_info_id." 部门:".$department_id,"info");
			//查询出管辖的网点
			$stores_ids = SysStoreModel::find([
				                    'manager_id=:manager_id: ',
				                    'bind' => ['manager_id' => $staff_info_id],
				                    'columns' => ['id'],
			                    ])->toArray();

			$stores_ids      = array_values(array_filter(array_unique(array_column($stores_ids, 'id'))));   //管辖片区

            if(!empty($stores_ids)){
				$data['stores_ids'] = array_merge($data['stores_ids'], $stores_ids);  //管辖网点
				$this->getDI()->get("logger")->write_log("获取oa权限结构查询管辖网点 getAuthorityStoreIds .".json_encode($stores_ids)." 用户为:".$staff_info_id." 部门:".$department_id,"info");
			}

			//管辖片区
			if (!empty($mange_piece_ids)) {

				//todo 获取片区下所有网点
				$store_ids              = (new SysListServer())->getStoreListByPieceNew($mange_piece_ids);
				$data['stores_ids'] = array_merge($data['stores_ids'], $store_ids);
			}

			//管辖大区
			if (!empty($mange_region_ids)) {
				//todo 获取大区下所有网点
				$store_ids              = (new SysListServer())->getStoreListByRegionNew($mange_region_ids);
				$data['stores_ids'] = array_merge($data['stores_ids'], $store_ids);
			}

			$this->getDI()->get("logger")->write_log("获取oa权限结构查询出大区和片区的网点 getAuthorityStoreIds .".json_encode($data)." 用户为:".$staff_info_id." 部门:".$department_id,"info");

		}
		$data['stores_ids']  = array_unique($data['stores_ids']);
		$data['department_ids']= array_unique($data['department_ids']);

		return $data;
	}


	/**
	 * 获取指定工号 管辖的 部门、大区、片区、网点 信息   获取所有信息
     * @param $staff_info_id 员工ID
	 * @param
	 *return ['store_ids'=>array_unique($manage_where_store_ids),'department_ids'=>$mange_department_ids]; store_ids  网点 id ,department_ids 部门 id
	 */
    public function getStaffManageDepartmentsAreasStores($staff_info_id)
    {
        $return_data = ['stores_ids' => [], 'department_ids' => []];
        if (empty($staff_info_id)) {
            return $return_data;
        }


        $manage_where_store_ids = [];

        $relations = (new StaffServer())->setExpire(60*5)->getStaffJurisdictionFromCache($staff_info_id);
        //获取管辖的字段信息
	    $return_data['stores_ids'] = $mange_store_ids            = $relations['stores'] ?? [];           //管辖网点
        $mange_piece_ids            = $relations['pieces'] ?? [];           //管辖片区
        $mange_region_ids           = $relations['regions'] ?? [];          //管辖大区
        $mange_store_categories_ids = $relations['store_categories'] ?? []; //管辖网点类型
	    $return_data['department_ids'] = $mange_department_ids       = $relations['departments'] ?? [];      //管辖部门
        //如果 大区片区网点类型 都没选 就不用往下走了
        if ( empty($mange_piece_ids) && empty($mange_region_ids) && empty($mange_store_categories_ids)) {
            return $return_data;
        }


        //管辖网点
        if (!empty($mange_store_ids)) {
            $manage_where_store_ids = array_merge($manage_where_store_ids, $mange_store_ids);
        }

        // -2 是全部网点 如果选择了   就不继续找 大区和片区的网点了
        if (!in_array('-2', $manage_where_store_ids)) {
            $builder = $this->modelsManager->createBuilder();
            $builder->columns('id');
            $builder->from(['s' => SysStoreModel::class]);
            //管辖大区
            if (!empty($mange_region_ids)) {
                $builder->orWhere('manage_region in ({manage_region:array})', ['manage_region' => $mange_region_ids]);
            }
            //管辖片区
            if (!empty($mange_piece_ids)) {
                $builder->orWhere('manage_piece in ({manage_piece:array})', ['manage_piece' => $mange_piece_ids]);
            }
            //管辖网点类型
            if (!empty($mange_store_categories_ids)) {
                $builder->orWhere('category in ({store_categories:array})',
                    ['store_categories' => $mange_store_categories_ids]);
            }
            $store_list = $builder->getQuery()->execute()->toArray();
            if (!empty($store_list)) {
                $manage_where_store_ids = array_merge($manage_where_store_ids, array_column($store_list, 'id'));
            }
        }


        //
        return ['stores_ids'     => array_values(array_unique($manage_where_store_ids)),
                'department_ids' => $mange_department_ids,
        ];
    }

    /**
     * 入职提醒邮件
     * @return int
     */
    public function entryRemindEmail(): int
    {
        $send_date = date('Y-m-d', strtotime('+2 day'));
        $t = $this->getTranslation('zh');
        $departmentRepository = new DepartmentRepository();
        $d_ids = array_values(array_unique(array_merge($departmentRepository->getDepartmentListById(15006),$departmentRepository->getDepartmentListById(20001))));
        
        $builder   = $this->modelsManager->createBuilder();
        $builder->columns([
            'resume.name',
            'resume.email',
            'offer.work_time',
            'offer.hc_id',
            'hc.worknode_id',
            'entry.status',
            'entry.resume_id',
            'entry.record_entry',
        ])->from(['entry' => HrEntryModel::class]);
        $builder->leftjoin(HrResumeModel::class, " entry.resume_id = resume.id", "resume");
        $builder->leftjoin(HrInterviewOfferModel::class,
            " entry.resume_id = offer.resume_id and entry.interview_offer_id = offer.id", "offer");
        $builder->leftjoin(HrhcModel::class, " offer.hc_id = hc.hc_id", "hc");
        $builder->where('entry.deleted = ' . HrResumeModel::IS_DELETE_NO . ' and entry.status = ' . HrEntryModel::STATUS_TO_BE_EMPLOYED . ' and hc.worknode_id =\'-1\'');
        $builder->andWhere("offer.status = :status: and offer.work_time = :send_date: and hc.department_id not in ({department_ids:array}) and resume.work_city_id = 'MY0601'",
            ['send_date' => $send_date, 'status' => HrInterviewOfferModel::STATUS_FINISH, 'department_ids' => $d_ids]);
        $data = $builder->getQuery()->execute()->toArray();
        $i    = 0;
        foreach ($data as $v) {
            $recordEntry = $v['record_entry'] ? json_decode($v['record_entry'], true) : [];
            
            $send_email_record = SendEmailRecordModel::find([
                'conditions' => 'business_id = :resume_id:',
                'bind'       => [
                    'resume_id' => $v['resume_id'],
                ],
            ])->toArray();
            $send_email_record = array_column($send_email_record, null, 'type');
            if (!empty($send_email_record[SendEmailRecordModel::type_entry_remind]) && show_time_zone($send_email_record[SendEmailRecordModel::type_entry_remind]['created_at'],
                    'Y-m-d') == date('Y-m-d')) {
                continue;
            }
            $email = !empty($send_email_record[SendEmailRecordModel::type_send_offer]) ? $send_email_record[SendEmailRecordModel::type_send_offer]['email'] : ($recordEntry['personal_email'] ?? '');
            if (empty($email)) {
                continue;
            }
            $work_date             = date('d M Y', strtotime($v['work_time']));
            $content               = $t->_('entry_email_remind_content',
                ['name' => $v['name'] ?? '', 'work_date' => $work_date]);
            $offer_attachment_path = BASE_PATH . '/public/pdf/' . md5(time()) . '.pdf';
            file_put_contents($offer_attachment_path,
                file_get_contents('https://tc-static-asset-internal.flashexpress.com/workOrder/1732700835-dc34b4b3f5874d21b2c2d562eb6e09ef.pdf'));

            $title     = $t->_('entry_email_remind_title');
            $sendEmail = (new MailServer())->send_mail('default', $email, $title, $content, $offer_attachment_path,
                'letter for company new joiner first day registration for visitor card.pdf');
            if (!$sendEmail) {
                $this->getDI()->get('logger')->write_log("入职提醒发送email失败:{$email}，请检查邮箱是否有效！", 'error');
                continue;
            }
            // 邮件发送记录
            $send_email_record              = new SendEmailRecordModel();
            $send_email_record->type        = SendEmailRecordModel::type_entry_remind;
            $send_email_record->business_id = $v['resume_id'];
            $send_email_record->email       = $email;
            $send_email_record->title       = $title;
            $send_email_record->content     = $content;
            $send_email_record->save();

            //删除临时文件
            unlink($offer_attachment_path);
            $i++;
        }
        return $i;
    }

    /**
     * 未入职提醒-飞书
     * @return int
     */
    public function entryRemindFeishu()
    {
        $entryDate = date('Y-m-d 00:00:00');
        $departmentRepository = new DepartmentRepository();
        $d_ids = array_values(array_unique(array_merge($departmentRepository->getDepartmentListById(15006),$departmentRepository->getDepartmentListById(20001))));
        $builder   = $this->modelsManager->createBuilder();
        $builder->columns([
            'entry.resume_id',
        ])->from(['entry' => HrEntryModel::class]);
        $builder->leftjoin(HrhcModel::class, " entry.hc_id = hc.hc_id", "hc");
        $builder->where('entry.deleted = ' . HrResumeModel::IS_DELETE_NO . ' and entry.status != ' . HrEntryModel::STATUS_EMPLOYED);
        $builder->andWhere("entry.entry_date = :entry_date: and hc.department_id not in ({department_ids:array})",
            ['entry_date' => $entryDate,'department_ids' => $d_ids]);
        $data = $builder->getQuery()->execute()->toArray();
        if ($data){
            $resume_ids_str = implode(',', array_values(array_unique(array_column($data,'resume_id'))));
            $content  = "提醒类型: 未入职员工提醒" . "\n";
            $content .= sprintf("CVID: %s",  $resume_ids_str). "\n";
            (new PublicServer())->push($content);
        }
        return count($data);
    }

    /**
     * 给总部员工自动发送合同
     * @return array
     * @throws Exception
     */
    public function send_contract($params)
    {
        $departmentRepository = new DepartmentRepository();
        $d_ids                = array_values(array_unique(array_merge($departmentRepository->getDepartmentListById(15006),
            $departmentRepository->getDepartmentListById(20001))));
        $builder              = $this->modelsManager->createBuilder();
        $builder->columns([
            'staff.staff_info_id',
            'staff.node_department_id',
            'staff.job_title',
            'annex.face_check_state',
        ])->from(['entry' => HrEntryModel::class]);
        $builder->leftjoin(HrStaffInfoModel::class, " entry.staff_id = staff.staff_info_id", "staff");
        $builder->leftjoin(HrStaffAnnexInfoModel::class, " entry.staff_id = annex.staff_info_id and type = 1", "annex");
        $builder->where('entry.deleted = ' . HrResumeModel::IS_DELETE_NO . ' and entry.status = ' . HrEntryModel::STATUS_EMPLOYED);
        $builder->andWhere("staff.state = 1 and staff.is_sub_staff = :is_sub_staff: and staff.formal = :formal: and staff.hire_type not in ({hire_type:array}) and staff.node_department_id not in ({department_ids:array}) and annex.audit_state = :audit_state:",
            ['hire_type'      => HrStaffInfoModel::$agentTypeTogether,
             'department_ids' => $d_ids,
             "audit_state"    => HrStaffAnnexInfoModel::AUDIT_STATE_PASS,
             "formal"         => HrStaffInfoModel::FORMAL_1,
             "is_sub_staff"   => HrStaffInfoModel::IS_SUB_STAFF_NO,
            ]);
        if (!empty($params[0])) {
            $builder->andWhere("staff.staff_info_id = :staff_id:", ['staff_id' => $params[0]]);
        }
        $data = $builder->getQuery()->execute()->toArray();
        $this->logger->write_log(['send_contract' =>empty($data) ? 'no data' : 'has data'], 'info');
        $staff_ids = [];
        $addEntryServer = new AddEntryServer();
        $interviewServer = new InterviewServer();
        foreach ($data as $v) {
            $is_front_line_job = $interviewServer->isFirstLineJob($v['node_department_id'], $v['job_title']);
            if ($is_front_line_job === true) {
                $this->logger->write_log(['send_contract' => $v, 'is_front_line_job' => true], 'info');
                continue;
            }
            /**
             * - 1 属于【配置项-Blackface_verification_position和配置项- Blackface_verification_department并集】:
             * - 2 首次录入底片时：若录入成功，且HCM-员工信息审核的员工身份信息审核状态为“通过“，发送合同
             * - 3 HCM-员工信息审核的员工身份信息审核状态变更为“通过“：若首次录入底片成功，发送合同
             * 所以 2、3点通过face_check_state控制
             */
            if ($addEntryServer->notAutoSendContractJobTitleDepartment($v['node_department_id'],
                    $v['job_title']) && in_array($v['face_check_state'],
                    [HrStaffAnnexInfoModel::FACE_CHECK_STATE_DEFAULT, HrStaffAnnexInfoModel::FACE_CHECK_STATE_HIT])) {
                $this->logger->write_log(['send_contract' => $v, 'notAutoSendContractJobTitleDepartment' => '1'],
                    'info');
                continue;
            }
            $contract_data = HrStaffContractModel::find([
                'conditions' => 'staff_id = :staff_id: and contract_type = :contract_type:',
                'bind'       => [
                    'staff_id'      => $v['staff_info_id'],
                    'contract_type' => enums::CONTRACT_LDHT,
                ],
                'order'      => 'id desc',
            ])->toArray();
            if (count($contract_data) > 1) {
                $this->logger->write_log(['send_contract' => $v, 'count_contract_data' => '>1'], 'info');

                // 包含已删除的 历史劳动合同数据大于1条的 就代表发送过
                continue;
            }
            if ($contract_data[0]['contract_is_deleted'] == enums::IS_DELETED_YES || $contract_data[0]['contract_status'] != enums::CONTRACT_STATUS_ADD) {
                $this->logger->write_log(['send_contract' => $v, 'contract_is_deleted' => true], 'info');
                continue;
            }
            //电子合同入队列
            $data = ['type' => 'send_contract', 'staff_info_id' => $v['staff_info_id']];
            $rmq  = new RocketMQ('contract-add');
            $rid  = $rmq->sendToMsg($data);
            $this->logger->write_log('hr-contract-send-contract rid:' . $rid . 'data:' . json_encode($data), $rid ? 'info' : 'error');
            $staff_ids[] = $v['staff_info_id'];
        }
        return $staff_ids;
    }

    /**
     * 身份证确认状态红点
     * @param $paramIn
     * @return int
     */
    public function getIdentityConfirmedNum($paramIn)
    {
        //只有这几个国家有，其他国家直接返回0
        if(!isCountry(['TH', 'PH', 'MY'])) {
            return 0;
        }
        $staffId = $this->userInfo['id'];
        $permissionData = (new PermissionRepository())->getPermissionRelation($staffId, true);
        if(empty($permissionData)) {
            return 0;
        }

        $permissionDataToIds = array_column($permissionData, 'id');

        if(!in_array(WhrPermissionModel::PERMISSION_ENTRY_LIST, $permissionDataToIds)) {
            return 0;
        }

        return (new EntryRepository())->getIdentityConfirmed($paramIn);
    }

    /**
     * 获取 身份证正面照
     * @param $resumeId
     * @return mixed|string
     */
    public function getIdentityFrontUrl($resumeId)
    {
        if(empty($resumeId)) {
            return '';
        }
        $data     = (new ResumeRepository())->annexInfo($resumeId);
        //简历附件，提示语
        $identity_front_url = '';
        foreach ($data as $v) {
            //身份证正面照编辑简历附件
            if($v['file_type'] == HrAnnexModel::FILE_TYPE_IDENTITY_FRONT) {
                $identity_front_url = $v['file_url'];
            }
        }

        return $identity_front_url;
    }
    /**
     * 获取办理入职配置信息
     * @param $entry_id
     * @return string[]
     * @throws ValidationException
     */
    public function getEntryConfig($paramIn): array
    {
        $entry_date = $paramIn['entry_date'] ?? date('Y-m-d');
        $return_data = [];
        $entry_id = $paramIn['entry_id'];
        $t         = $this->getTranslation();
        $entryInfo = HrEntryModel::findFirst($entry_id);
        if (empty($entryInfo)) {
            throw new ValidationException($t->_('data_error'));
        }

        $hcInfo = HrhcModel::findFirst($entryInfo->hc_id);
        if (empty($hcInfo)) {
            throw new ValidationException($t->_('data_error'));
        }
        //$jdInfo = (new FlashJdRepository())->jdDetail(['job_id' => $hcInfo->job_id]);
        $setting = (new SettingEnvServer())->getMultiEnvByCodeToArray([
            'edit_hire_type_staff',
        ]);
        // 是否可以编辑雇佣类型 false= 不可以编辑,true=可以编辑
        $is_edit_hire_type = false;
        if (
            !empty($setting['edit_hire_type_staff']) &&
            !empty($paramIn['staff_id']) &&
            in_array($paramIn['staff_id'], $setting['edit_hire_type_staff'])
        ){
            $is_edit_hire_type = true;
        }
        $return_data['is_edit_hire_type'] = $is_edit_hire_type;
        $return_data['jd_type'] = $jdInfo['type'] ?? '3';

        if (isCountry('PH')) {
            $freeSetting                         = $this->getFreeLeaveConfig($entry_date,$hcInfo->worknode_id, $hcInfo->job_title);
            $return_data['free_workday_num']     = $freeSetting ? $freeSetting->days_num : null;
            $return_data['max_free_workday_num'] = $freeSetting ? $freeSetting->max_days_num : null;
        }
        return $return_data;
    }

    //自由轮休 根据网点和职位获取对应配置天数
    public function getFreeLeaveConfig($date_at,$store_id, $job_title)
    {
        if (empty($store_id) || empty($job_title)) {
            return false;
        }
        if($store_id == enums::HEAD_OFFICE_ID){
            return false;
        }
        return WorkdaySettingV2Model::findFirst([
            'conditions' => 'store_id = :store_id: and job_title = :job_title: and is_delete = 0 and (max_days_num > 0 or max_days_num is null) and if(ISNULL(end_date),true,end_date != effect_date)  and  (end_date >= :end_date: or end_date is null) and effect_date <= :effect_date:',
            'bind'       => [
                'store_id'   => $store_id,
                'job_title'   => $job_title,
                'end_date'    => $date_at,
                'effect_date' => $date_at,
            ],
            'order'      => 'effect_date desc',
        ]);
    }

    public function checkCompanyEmail($params)
    {
        $t         = $this->getTranslation();
        $entryInfo = HrEntryModel::findFirst($params['entry_id']);
        if (empty($entryInfo)) {
            throw new ValidationException($t->_('data_error'));
        }

        $data['is_display'] = $this->checkIsDisplay($params);
        $data['is_required'] = $this->checkIsRequired($params);

        $email = '';
        $resume = (new ResumeRepository())->getResumeInfo(['id' => $entryInfo->resume_id]);
        $record_entry = !empty($entryInfo->record_entry) ? json_encode($entryInfo->record_entry, true) : [];
        if(!empty($resume) && $data['is_display'] && empty($record_entry['email'])){
            $email = $this->getCompanyEmail($resume['first_name'] . '.' . $resume['last_name']);
        }

        $data['email'] = $email;

        return $data;
    }

    /**
     * 检验是否展示
     * @param $params
     * @return bool
     */
    public function checkIsDisplay($params)
    {
        if($params['sys_store_id'] == enums::HEAD_OFFICE_ID) {
            return true;
        }

        $staff_email_roles  = (new SettingEnvServer())->getSetValToArray('staff_email_roles');
        if(!empty($params['role_id']) && array_intersect($params['role_id'], $staff_email_roles)) {
            return true;
        }

        $email_position  = (new SettingEnvServer())->getSetValToArray('email_position');
        if(in_array($params['job_title'], $email_position)) {
            return true;
        }

        return false;
    }

    /**
     * 检验是否必填
     * @param $params
     * @return bool
     */
    public function checkIsRequired($params)
    {
        if($params['sys_store_id'] == enums::HEAD_OFFICE_ID) {
            return true;
        }

        $staff_email_roles  = (new SettingEnvServer())->getSetValToArray('staff_email_roles');
        if(!empty($params['role_id']) && array_intersect($params['role_id'], $staff_email_roles)) {
            return true;
        }

        return false;
    }

    public function getCompanyEmail($name)
    {
        return '';
    }

}
