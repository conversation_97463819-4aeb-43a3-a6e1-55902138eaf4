<?php


namespace FlashExpress\bi\App\Server;


use DateInterval;
use FlashExpress\bi\App\library\DateTime;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\ErrCodeEnums;
use FlashExpress\bi\App\library\Excel;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\BackyardBaseModel;
use FlashExpress\bi\App\Models\backyard\HireTypeImportListModel;
use FlashExpress\bi\App\Models\backyard\HrEntryModel;
use FlashExpress\bi\App\Models\backyard\HrhcModel;
use FlashExpress\bi\App\Models\backyard\HrJobDepartmentRelationModel;
use FlashExpress\bi\App\Models\backyard\HrJobTitleModel;
use FlashExpress\bi\App\Models\backyard\HrSmsLogModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\StaffResignModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Models\SettingEnvModel;
use FlashExpress\bi\App\Repository\BaseRepository;
use FlashExpress\bi\App\Repository\HrJobTitleRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Repository\SysDepartmentRepository;
use FlashExpress\bi\App\Repository\SysManagePieceRepository;
use FlashExpress\bi\App\Repository\SysManageRegionRepository;

use function Complex\negative;


/**
 * Class HireTypeServer
 * @package FlashExpress\bi\App\Server
 */
class HireTypeServer extends BaseServer
{
    const MAX_IMPORT_ROWS = 500; //最大导入条数
    public $sysLang;

    function __construct($lang = '')
    {
        parent::__construct();
        $this->sysLang = $lang ? $lang: $this->lang;
    }

    /**
     * @description 获取list
     * @param $params
     * @return array
     */
    public function getList($params): array
    {
        $params['page_num']  = empty($params['page_num']) ? enums::DEFAULT_PAGE_NUM: $params['page_num'];
        $params['page_size'] = empty($params['page_size']) ? enums::DEFAULT_PAGE_SIZE : $params['page_size'];
        $params['page_size'] = min(enums::MAX_SIZE_PER_PAGE, $params['page_size']);

        //获取Builder
        $builder = $this->getQueryBuilder($params);

        //获取count
        $total = $builder->columns('count(1) as cou')->getQuery()->getSingleResult()->cou;

        //获取List
        if (empty($params['is_download'])) {
            $builder->limit($params['page_size'], $params['page_size'] * ($params['page_num'] - 1));
        }
        $builder->columns($this->getColumns());
        $builder->orderBy('h.created_at desc');
        $list = $builder->getQuery()->execute()->toArray();
        if ($list) {
            $list = $this->formatListData($list);
        }

        $data['total'] = !empty($total) ? intval($total) : 0;
        $data['list']  = $list;

        return self::checkReturn(['data' => $data]);
    }

    /**
     * @description 获取sql builder
     * @param $params
     * @return mixed
     */
    private function getQueryBuilder($params)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['h' => HireTypeImportListModel::class]);

        //原工号用户数据
        $builder->leftJoin(HrStaffInfoModel::class, 'hsi.staff_info_id = h.old_staff_id', 'hsi');

        //原工号网点数据
        $builder->leftJoin(SysStoreModel::class, 'hsi.sys_store_id = old_store.id', 'old_store');

        $builder->where('h.deleted = 0');
        // 原工号
        if (!empty($params['old_staff_id'])) {
            $builder->andWhere('h.old_staff_id like :old_staff_id:', ['old_staff_id' => $params['old_staff_id'] . '%']);
        }
        // 新工号
        if (!empty($params['new_staff_id'])) {
            $builder->andWhere('h.new_staff_id like :new_staff_id:', ['new_staff_id' => $params['new_staff_id'] . '%']);
        }

        // 原工号职位
        if (!empty($params['old_job_title_id'])) {
            $builder->andWhere('hsi.job_title = :old_job_title_id:', [
                'old_job_title_id' => $params['old_job_title_id'],
            ]);
        }

        // 原工号所属部门
//        if (!empty($params['old_department_id'])) {
//            $builder->andWhere('hsi.node_department_id = :old_department_id:', [
//                'old_department_id' => $params['old_department_id']
//            ]);
//        }
        if (!empty($params['old_department_id'])) {
            $deptIds = SysServer::getDepartmentConditionByParams(['department_id'=>$params['old_department_id'],'is_sub_department'=>$params['is_sub_department'] ?? 2]);
            if (!empty($deptIds)) {
                $builder->inWhere('hsi.node_department_id', $deptIds);
            }
        }

        // 输入职位
        if (!empty($params['job_title_id'])) {
            $builder->andWhere('h.new_staff_job_title = :job_title:', ['job_title' => $params['job_title_id']]);
        }

        // 原工号大区
        if (!empty($params['old_manage_region'])) {
            $builder->andWhere('old_store.manage_region = :old_manage_region:', ['old_manage_region' => $params['old_manage_region']]);
        }

        // 原工号片区
        if (!empty($params['old_manage_piece'])) {
            $builder->andWhere('old_store.manage_piece = :old_manage_piece:', ['old_manage_piece' => $params['old_manage_piece']]);
        }

        // 原工号工作地点
        if (!empty($params['old_store_id'])) {
            $builder->andWhere('hsi.sys_store_id = :old_store_id:', ['old_store_id' => $params['old_store_id']]);
        }

        // 入职状态
        if (!empty($params['entry_state'])) {
            $builder->andWhere('h.entry_state = :entry_state:', ['entry_state' => $params['entry_state']]);
        }

        // 来源
        if (!empty($params['employ_origin'])) {
            $builder->andWhere('h.data_source = :data_source:', ['data_source' => $params['employ_origin']]);
        }

        //数据类型
        if (!empty($params['data_type'])) {
            $builder->andWhere('h.data_type = :data_type:', ['data_type' => $params['data_type']]);
        }

        // 操作人
        if (!empty($params['operator_id'])) {
            $builder->andWhere('h.operator_id = :operator_id:', ['operator_id' => $params['operator_id']]);
        }
        // 预计入职日期
        if (!empty($params['expected_entry_start_date']) && !empty($params['expected_entry_end_date'])) {
            $builder->andWhere('h.expected_entry_date >= :expected_entry_start_date: and h.expected_entry_date <= :expected_entry_end_date:', [
                'expected_entry_start_date' => $params['expected_entry_start_date'],
                'expected_entry_end_date'   => $params['expected_entry_end_date'],
            ]);
        }
        // 实际入职日期
        if (!empty($params['actual_entry_start_date']) && !empty($params['actual_entry_end_date'])) {
            $builder->andWhere('h.actual_entry_date >= :actual_entry_start_date: and h.actual_entry_date <= :actual_entry_end_date:', [
                'actual_entry_start_date' => $params['actual_entry_start_date'],
                'actual_entry_end_date'   => $params['actual_entry_end_date'],
            ]);
        }
        return $builder;
    }

    /**
     * @description 获取返回columns
     * @return array
     */
    private function getColumns(): array
    {
        return [
            'h.new_staff_id',
            'h.old_staff_id',
            'h.expected_entry_date',
            'h.actual_entry_date',
            'h.operator_id',
            'h.entry_state',
            'h.created_at',
            'h.import_fail_reason_id',
            'hsi.node_department_id as old_node_department_id',
            'h.new_staff_job_title as job_title_id',
            'hsi.sys_store_id as old_sys_store_id',
            'h.receive_offer_mobile',
            'h.data_source',
            'old_store.manage_region as old_manage_region',
            'old_store.manage_piece as old_manage_piece',
            'hsi.job_title as old_job_title_id',
        ];
    }

    /**
     * @description 组织列表数据
     * @param $list
     * @return array
     */
    private function formatListData($list): array
    {
        //导入结果
        $server = new SysListServer();
        $importSourceList = $server->getHireTypeImportSource();
        $importSourceList = array_column($importSourceList, 'name', 'key');

        //入职状态
        $server->lang = $this->sysLang;
        $entryStateList = $server->getEntryState();
        $entryStateList = array_column($entryStateList, 'name', 'key');

        //获取工号
        $staffIds = array_merge(array_column($list, 'old_staff_id'), array_column($list, 'operator_id'));
        $staffIds = array_values(array_filter($staffIds));

        $addHour = $this->getDI()['config']['application']['add_hour'];
        $staffInfo = [];
        if (!empty($staffIds)) {
            $staffInfo = (new StaffRepository())->getStaffInfoByIds($staffIds);
            $staffInfo = array_column($staffInfo, 'name', 'staff_info_id');
        }

        //部门
        $departmentDataList = (new SysDepartmentRepository())->getDepartmentListFromCache(['id', 'name']);
        $departmentDataList = array_column($departmentDataList,'name', 'id');

        //职位
        $positionList = (new HrJobTitleRepository())->getJobTitleListFromCache(['id','job_name']);
        $positionList = array_column($positionList, 'job_name', 'id');

        //获取大区数据
        $regionList = (new SysManageRegionRepository())->getListFromCache(['id', 'name']);
        $regionList = array_column($regionList, 'name', 'id');

        //获取片区数据
        $pieceList = (new SysManagePieceRepository())->getListFromCache(['id', 'name']);
        $pieceList = array_column($pieceList, 'name', 'id');

        //网点
        $storeList = [];
        $storeIds = array_values(array_unique(array_column($list, 'old_sys_store_id')));
        if ($storeIds) {
            $storeList = SysStoreModel::find([
                'conditions' => 'id in({ids:array})',
                'bind'       => ['ids' => $storeIds],
                'columns'    => ['id', 'name'],
            ])->toArray();
            $storeList = array_column($storeList, 'name', 'id');
            $storeList[enums::HEAD_OFFICE_ID] = enums::HEAD_OFFICE;
        }

        foreach ($list as &$item) {
            //$item['import_state_name'] = $importStateList[$item['import_state']] ?? '';
            $item['entry_state_name']       = $entryStateList[$item['entry_state']] ?? '';
            $item['staff_name']             = $staffInfo[$item['old_staff_id']] ?? '';
            $item['new_staff_id']           = empty($item['new_staff_id']) ? '' : $item['new_staff_id'];
            $item['operator_name']          = empty($item['operator_id']) ? '' : sprintf('%s (%d)',
                $staffInfo[$item['operator_id']] ?? '', $item['operator_id']);
            $item['created_at']             = date('Y-m-d H:i:s', strtotime($item['created_at']) + $addHour * 3600);
            $item['data_source_name']       = $importSourceList[$item['data_source']] ?? '';
            $item['old_department_name']    = $departmentDataList[$item['old_node_department_id']] ?? '';
            $item['job_title_name']         = $positionList[$item['job_title_id']] ?? '';
            $item['old_store_name']         = $storeList[$item['old_sys_store_id']] ?? '';
            $item['old_job_title_name']     = $positionList[$item['old_job_title_id']] ?? '';
            $item['old_manage_region_name'] = $regionList[$item['old_manage_region']] ?? '';
            $item['old_manage_piece_name']  = $pieceList[$item['old_manage_piece']] ?? '';
        }

        return $list;
    }

    /**
     * @description 导出excel
     * @param $params
     * @return array
     * @throws \FlashExpress\bi\App\library\Exception\BusinessException
     */
    public function export($params): array
    {
        $params['is_download'] = 1;

        //获取列表
        $list = $this->getList($params);
        $this->lang = $this->sysLang;

        $t = $this->getTranslation();

        $header = [
            $t->_('hire_type_staff_id'),             //新工号
            $t->_('hire_type_origin_staff_id'),      //原工号
            $t->_('7504'),                           //姓名
            $t->_('old_staff_job_title'),            //原工号职位
            $t->_('new_staff_job_title'),            //新工号职位
            $t->_('old_department_name'),            //原工号部门
            $t->_('old_staff_manage_region'),        //原工号大区
            $t->_('old_staff_manage_piece'),         //原工号片区
            $t->_('old_staff_store_name'),           //工作地点
            $t->_('mobile'),                         //手机号
            $t->_('hire_type_expected_date'),        //预计入职日期
            $t->_('hire_type_actual_date'),          //实际入职日期
            $t->_('data_source'),                    //返聘来源
            $t->_('hire_type_operator_id'),          //操作人
            $t->_('hire_type_operator_time'),        //操作时间
            $t->_('7033'),                           //入职状态
        ];

        $data = [];
        foreach ($list['data']['list'] as $k => $v) {
            $data[$k] = [
                $v['new_staff_id'],
                $v['old_staff_id'],
                $v['staff_name'],
                $v['old_job_title_name'],
                $v['job_title_name'],
                $v['old_department_name'],
                $v['old_manage_region_name'],
                $v['old_manage_piece_name'],
                $v['old_store_name'],
                $v['receive_offer_mobile'],
                $v['expected_entry_date'],
                $v['actual_entry_date'],
                $v['data_source_name'],
                $v['operator_name'],
                $v['created_at'],
                $v['entry_state_name'],
            ];
        }

        $filePath  = Excel::exportExcel($header, $data, 'ListIndependentContractor_' . date('Y-m-d'). '.xlsx');
        return self::checkReturn(['data' => ['path' => $filePath['object_url'] ?? '']]);
    }

    /**
     * @description 导入个人代理
     * @param $file
     * @param $operator_id
     * @param bool $is_lnt
     * @return array
     * @throws ValidationException
     */
    public function import($file, $operator_id,$is_lnt = false)
    {
        $config = ['path' => ''];
        $excel = new \Vtiful\Kernel\Excel($config);

        $fileInfo = pathinfo(basename($file));
        if (!isset($fileInfo['extension']) || $fileInfo['extension'] !== "xlsx") { //仅支持的文件格式xlsx
            throw new ValidationException($this->getTranslation()->_('11029'));
        }
        $excelData = $excel->openFile($file)
            ->openSheet()
            ->setType([
                \Vtiful\Kernel\Excel::TYPE_INT,
                \Vtiful\Kernel\Excel::TYPE_INT,
                \Vtiful\Kernel\Excel::TYPE_STRING,
                \Vtiful\Kernel\Excel::TYPE_STRING,
            ])
            ->getSheetData();
        if (empty($excelData)) { //数据为空
            throw new ValidationException($this->getTranslation()->_('8402'));
        }

        //超过最大条数
        if (count($excelData) - 2 > self::MAX_IMPORT_ROWS) {
            throw new ValidationException($this->getTranslation()->_('err_msg_over_max_rows', [
                'limit_rows' => self::MAX_IMPORT_ROWS
            ]));
        }

        //导入工号
        $staffInfoDetail = $staffInfo = [];
        $staffInfoIds = array_values(array_filter(array_column($excelData, 0)));
        if (!empty($staffInfoIds)) {
            $staffInfo = (new StaffRepository())->getStaffInfoWithCondition($staffInfoIds);
            $staffInfoDetail = array_column($staffInfo, null,'staff_info_id');
            $staffInfo = array_column($staffInfo, 'staff_info_id');
        }
        //重复的工号
        $duplicateStaffInfo = $this->getRepeatStaffInfoId($staffInfoIds);

        //是否存在返聘
        $reappointment = $this->getReappointment($staffInfoIds);

        //是否在黑名单
        if (isCountry('TH')){
            $black_list = (new BlackgreylistServer())->getBlackList($staffInfoIds);
        }else{
            $black_list = (new BlacklistServer())->getBlackList($staffInfoIds);
        }
        
        //是否存在离职
        $resignList = $this->getStaffResignInfo($staffInfoIds);
        $resignStaffList = array_column($resignList, 'submitter_id');

        //职位ID
        $positionList = (new HrJobTitleRepository())->getJobTitleListFromCache(['id'], ['status = 1']);
        $positionIdsList = array_column($positionList, 'id');

        //获取职位配置
        $validJobTitleConfig = (new SettingEnvServer())->getMultiEnvByCode(['individual_contractor_job_title','lnt_recruitment_job_ids']);
        $validICJobTitleConfigList = !empty($validJobTitleConfig['individual_contractor_job_title']) ? explode(',', $validJobTitleConfig['individual_contractor_job_title']) : [];
        $validLNTJobTitleConfigList = !empty($validJobTitleConfig['lnt_recruitment_job_ids']) ? explode(',', $validJobTitleConfig['lnt_recruitment_job_ids']) : [];

        $insert = [];
        foreach ($excelData as $k => $datum) {

            if (in_array($k, [0, 1])) {

                if ($k == 1) {
                    $excelData[$k][4] = 'Import Result'; //$this->getTranslation()->_('import_result');
                    $excelData[$k][5] = 'Cause of Error'; //$this->getTranslation()->_('import_fail_reason');
                }
                continue;
            }

            $staffInfoId = trim($datum[0]);
            $jobTitleId  = trim($datum[1]);
            $mobile      = trim($datum[2]);
            if (is_numeric(trim($datum[3]))) {
                $date = $this->excelNumToDate(intval($datum[3]));
            } else {
                $date = trim($datum[3]);
            }
            $excelData[$k][3] = $date;

            try {
                // 工号不存在
                if (empty($staffInfoId)) {
                    throw new ValidationException('工号不存在', ErrCodeEnums::HIRE_TYPE_STAFF_ID_NOT_EXIST);
                }

                if (!preg_match( '/^\d{5,6}$/', $staffInfoId)) {
                    throw new ValidationException('工号需为5-6位的数字', ErrCodeEnums::HIRE_TYPE_ERR_STAFF_ID_FORMAT);
                }

                if (empty($staffInfo) || !in_array($staffInfoId, $staffInfo)) {
                    throw new ValidationException('工号不存在', ErrCodeEnums::HIRE_TYPE_STAFF_ID_NOT_EXIST);
                }

                //手机号格式
                if (isCountry('MY')) {
                    $pattern = '/(\d{10})|(\d{11})$/';
                } else {
                    $pattern = '/^\d{10}$/';
                }
                if (!preg_match($pattern, $mobile)) {
                    throw new ValidationException('手机号格式错误', ErrCodeEnums::HIRE_TYPE_ERR_MOBILE_FORMAT);
                }

                if (isCountry('MY')) {
                    if (strpos($mobile, '0') !== 0) {
                        $mobile = str_pad($mobile, 11, "0", STR_PAD_LEFT);
                    }
                } elseif (isCountry('PH')) {
                    $mobile = str_pad($mobile, 11, "0", STR_PAD_LEFT);
                }
                
                // 手机号重复校验
                $commonMobile = HrStaffInfoModel::count([
                    'conditions' => 'is_sub_staff = :is_sub_staff: and state in ({states:array}) and ((staff_info_id != :staff_info_id: and mobile = :mobile:) or mobile_company = :mobile_company:)',
                    'bind'       => [
                        'staff_info_id'  => $staffInfoId,
                        'mobile'         => $mobile,
                        'mobile_company' => $mobile,
                        'is_sub_staff'   => HrStaffInfoModel::IS_SUB_STAFF_0,
                        'states'         => [HrStaffInfoModel::STAFF_STATE_IN, HrStaffInfoModel::STAFF_STATE_STOP],
                    ],
                ]);
                if ($commonMobile) {
                    throw new ValidationException('手机号与其他在职员工重复', ErrCodeEnums::HIRE_TYPE_ERR_MOBILE_ALREADY_EXISTS);
                }

                //时间格式
                if (!preg_match('/^(19|20)\d\d[-\/](0[1-9]|1[012])[-\/](0[1-9]|[12][0-9]|3[01])$/', $date)) {
                    throw new ValidationException('日期格式错误', ErrCodeEnums::HIRE_TYPE_ERR_DATE_FORMAT);
                }

                //职位校验
                if (!in_array($jobTitleId, $positionIdsList)) {
                    throw new ValidationException('职位错误', ErrCodeEnums::HIRE_TYPE_NOT_EXIST_JOB_TITLE);
                }

                //非个人代理职位
                if (!$is_lnt && !in_array($jobTitleId, $validICJobTitleConfigList)) {
                    throw new ValidationException('职位非个人代理职位', ErrCodeEnums::HIRE_TYPE_NOT_INDIVIDUAL_CONTRACTOR);
                }
                //职位非LNT公司职位
                if ($is_lnt && !in_array($jobTitleId, $validLNTJobTitleConfigList)) {
                    throw new ValidationException('职位非LNT公司职位',
                        ErrCodeEnums::HIRE_TYPE_ERR_NOT_LNT_JOB_TITLE);
                }

                $relation = HrJobDepartmentRelationModel::findFirst([
                    'conditions' => 'department_id = :department_id: and job_id = :job_id:',
                    'bind' => [
                        'department_id' => $staffInfoDetail[$staffInfoId]['node_department_id'],
                        'job_id' => $jobTitleId,
                    ]
                ]);
                if (empty($relation)) {
                    throw new ValidationException('职位部门未关联', ErrCodeEnums::HIRE_TYPE_NOT_EXIST_RELATION);
                }

                if (isCountry('PH')) {
                    // 是否存在离职申请
                    if (in_array($staffInfoId, $resignStaffList)) {
                        throw new ValidationException('该员工已提交转个人代理的离职申请', ErrCodeEnums::HIRE_TYPE_EXIST_RESIGN);
                    }
                } else {
                    if (!$is_lnt && isset($staffInfoDetail[$staffInfoId]['state']) && $staffInfoDetail[$staffInfoId]['state'] != HrStaffInfoModel::STAFF_STATE_LEAVE) {
                        throw new ValidationException('该员工还未离职', ErrCodeEnums::HIRE_TYPE_NOT_STAFF_LEAVE);
                    }
                }

                // 重复工号校验
                if (in_array($staffInfoId, $duplicateStaffInfo)) {
                    throw new ValidationException('工号重复', ErrCodeEnums::HIRE_TYPE_IMPORT_STAFF_REPEAT);
                }

                // 是否存在返聘
                if (in_array($staffInfoId, $reappointment)) {
                    throw new ValidationException('存在返聘', ErrCodeEnums::HIRE_TYPE_EXIST_REAPPOINTMENT);
                }

                if (strtotime($date) < 0 || strtotime($date) < strtotime(date('Y-m-d'))) {
                    throw new ValidationException('不能早于当前日期', ErrCodeEnums::HIRE_TYPE_EARLIER_THAN_TODAY);
                }

                // 校验黑名单
                if (isCountry(['MY','TH','PH']) && isset($staffInfoDetail[$staffInfoId]['identity']) && in_array($staffInfoDetail[$staffInfoId]['identity'], $black_list)) {
                    throw new ValidationException('工号存在黑名单', ErrCodeEnums::HIRE_TYPE_ERR_STAFF_ID_BLACK);
                }
                $outsourcingBlackList = (new OutsourcingBlackListServer())->check($staffInfoDetail[$staffInfoId]['identity'] ?? '','winhr',false,$this->lang);
                if ($outsourcingBlackList['is_black']){
                    throw new ValidationException('证件号已被加入外协黑名单', ErrCodeEnums::HIRE_TYPE_ERR_IDENTITY_BLACK);
                }

                //匹配HC
                $params = [
                    'department_id' => $staffInfoDetail[$staffInfoId]['node_department_id'],
                    'job_title_id'  => $jobTitleId,
                    'sys_store_id'  => $staffInfoDetail[$staffInfoId]['sys_store_id'],
                    'is_lnt'        => $is_lnt,
                ];
                $hcId = $this->matchValidHc($params);
                //if (empty($hcId)) {
                //    throw new ValidationException('不存在匹配的HC', ErrCodeEnums::HIRE_TYPE_NOT_EXIST_MATCHED_HC);
                //}

                if (strtotime($date) > strtotime('+1 day')) { //明天以后的，定时发送
                    $sendState = HireTypeImportListModel::SEND_OFFER_TIMED;
                } else { //立即发送
                    $sendState = HireTypeImportListModel::SEND_OFFER_PENDING;
                }

                $_tmp = [
                    'old_staff_id'          => $staffInfoId,
                    'expected_entry_date'   => $date,
                    'hc_id'                 => $hcId,
                    'import_state'          => HireTypeImportListModel::IMPORT_STATE_SUCCESS,
                    'new_staff_job_title'   => $jobTitleId,
                    'receive_offer_mobile'  => $mobile,
                    'operator_id'           => $operator_id,
                    'entry_state'           => HrEntryModel::STATUS_TO_BE_EMPLOYED,
                    'send_offer_state'      => $sendState,
                    'data_type'             => $is_lnt ? HireTypeImportListModel::DATA_TYPE_LNT : HireTypeImportListModel::DATA_TYPE_IC,
                ];
            } catch (ValidationException $e) {
                $this->logger->write_log(sprintf('import err %d, %s', $e->getCode(), $e->getMessage()), 'info');
                $excelData[$k][4] = self::$t->_('contract_import_fail');
                $excelData[$k][5] = $this->getImportErrMessage($e->getCode());
                continue;
            }
            $excelData[$k][4] = self::$t->_('contract_import_success');
            $excelData[$k][5] = '';
            $insert[] = $_tmp;
        }

        //入表
        if (!empty($insert)) {
            //批量导入
            (new HireTypeImportListModel())->batch_insert($insert);
        }

        //上传导入结果
        $fileName = sprintf('CreateIndependentContractor_%s_ImportResult.xlsx', date('Ymd'));
        $url = $this->makeReturnReport($excelData, $fileName, "A1:D1");

        $result = [
            'success_num'        => count($insert),
            'fail_num'           => count($excelData) - count($insert) - 2,
            'import_result_path' => $url['object_url'] ?? '',
        ];

        //返回excel
        return self::checkReturn(['data' => $result]);
    }

    /**
     * @description 返回结果
     * @param $fileData
     * @param $fileName
     * @param string $rowRange
     * @param int $cellHeight
     * @param string $columnRange
     * @param int $width
     * @return array
     * @throws \OSS\Core\OssException
     */
    public function makeReturnReport(
        $fileData,
        $fileName,
        $rowRange = 'A1:G1',
        $cellHeight = 170,
        $columnRange = 'A:G',
        $width = 16
    )
    {
        $excel  = new \Vtiful\Kernel\Excel(['path' => sys_get_temp_dir()]);
        $excel  = $excel->fileName($fileName);
        $wrapStyle = (new \Vtiful\Kernel\Format($excel->getHandle()))
            ->wrap()
            ->align(\Vtiful\Kernel\Format::FORMAT_ALIGN_LEFT, \Vtiful\Kernel\Format::FORMAT_ALIGN_VERTICAL_TOP)
            ->toResource();
        foreach ($fileData as $row => $rowItem) {
            foreach ($rowItem as $column => $colItem) {
                if ($row == 0) {
                    if ($column == 0) {
                        $excel->mergeCells($rowRange, $colItem);
                        $excel->setRow($rowRange, $cellHeight, $wrapStyle);
                        $excel->setColumn($columnRange, $width);
                    } else {
                        break;
                    }
                } else {
                    $excel->insertText($row, $column, $colItem, null);
                }
            }
        }
        $filePath = $excel->output();
        if (env('break_away_from_ms')) {
            $result = (new Excel())->uploadFileHcm($filePath);
        } else {
            $result = (new Excel())->uploadFile($filePath);
        }
        return $result;
    }

    /**
     * @param $excelNum
     * @return string
     */
    private function excelNumToDate($excelNum): string
    {
        // Excel中的起始日期（1900年1月1日）
        $startDate = DateTime::createFromFormat('Y-m-d', '1899-12-31');

        // 在Excel中，1900年被错误地视为闰年，因此需要特别处理
        if ($excelNum > 60) {
            $excelNum--;
        }

        // 将天数加到起始日期上
        $dateInterval = new DateInterval('P' . $excelNum . 'D');
        $startDate->add($dateInterval);

        // 返回格式化后的日期
        return $startDate->format('Y-m-d');
    }

    /**
     * @description 关联HC
     * @param $params
     * @return int
     * @throws ValidationException
     */
    private function matchValidHc($params): int
    {
        $hireType = $params['is_lnt'] ? HrStaffInfoModel::HIRE_TYPE_MONTH : HrStaffInfoModel::HIRE_TYPE_AGENT;
        $hcInfo = HrhcModel::findFirst([
            'conditions' => 'department_id = :department_id: and job_title = :job_title: and 
                worknode_id = :store_id: and state_code = 2 and hire_type = :hire_type: and reason_type in(1,3) and deleted = 1 and surplusnumber > 0 and hire_times = :hire_times:',
            'bind' => [
                'department_id' => $params['department_id'],
                'job_title'     => $params['job_title_id'],
                'store_id'      => $params['sys_store_id'],
                'hire_type'     => $hireType,
                'hire_times'     => 12,
            ],
        ]);
        if (empty($hcInfo)) {

            if (isCountry('PH')) {
                throw new ValidationException('不存在匹配的HC', ErrCodeEnums::HIRE_TYPE_NOT_EXIST_MATCHED_HC);
            }

            $hcModel = new HrhcModel();
            $hcModel->department_id = $params['department_id'];
            $hcModel->job_title = $params['job_title_id'];
            $hcModel->hire_type = $hireType;
            $hcModel->hire_times = 12;
            $hcModel->worknode_id = $params['sys_store_id'];
            $hcModel->expirationdate = date('Y-m-d', strtotime('+1 month'));
            $hcModel->priority_id = 1; //P1优先级
            $hcModel->reason_type = HrhcModel::REASON_TYPE_RESIGNATION;
            $hcModel->demandnumber = 1;
            $hcModel->surplusnumber = 0;
            $hcModel->state_code = HrhcModel::STATE_FULL_RECRUITMENT;
            $hcModel->approval_state_code = 6;
            $hcModel->submitter_id = enums::SYS_ADMIN_id;
            $hcModel->save();

            return $hcModel->hc_id;
        } else {
            if ($hcInfo->surplusnumber == 1) {
                $hcInfo->state_code = HrhcModel::STATE_FULL_RECRUITMENT;
            }
            $hcInfo->surplusnumber = new \Phalcon\Db\RawValue('surplusnumber - 1');
            $hcInfo->save();
        }

        return $hcInfo->hc_id;
    }

    /**
     * @description 查看是否存在返聘
     * @param $staff_info_ids
     * @return array
     */
    private function getReappointment($staff_info_ids): array
    {
        if (empty($staff_info_ids)) {
            return [];
        }

        $info = HireTypeImportListModel::find([
            'conditions' => 'old_staff_id in({staff_id:array}) and entry_state in(1,2)',
            'bind' => [
                'staff_id' => $staff_info_ids,
            ],
            'columns' => 'old_staff_id',
        ])->toArray();
        return array_column($info, 'old_staff_id');
    }

    /**
     * @description 获取重复的工号
     * @param $data
     * @return int[]|string[]
     */
    private function getRepeatStaffInfoId($data)
    {
        $count      = []; // 存储每个工号的出现次数
        $duplicates = []; // 存储重复的工号

        // 遍历二维数组
        foreach ($data as $subArray) {
            if (!empty($subArray)) {
                $staffInfoId = $subArray;

                // 计数工号出现次数
                if (!isset($count[$staffInfoId])) {
                    $count[$staffInfoId] = 0;
                }
                $count[$staffInfoId]++;

                // 如果一个工号出现超过一次，将其添加到重复工号数组中
                if ($count[$staffInfoId] > 1) {
                    $duplicates[$staffInfoId] = $count[$staffInfoId];
                }
            }
        }

        return array_keys($duplicates); // 返回重复工号的数组
    }

    /**
     * @description 发送个人代理Offer
     * @param $list
     * @return bool
     */
    public function sendHireTypeUnPaidOffer($list): bool
    {
        if (empty($list)) {
            return false;
        }

        $server          = new OfferServer();
        $interviewServer = new InterviewServer();

        //获取HC关联的网点信息
        $hcIds  = array_column($list, 'hc_id');
        $hcInfo = $this->getWorkStore($hcIds);
        $storeInfo = array_column($hcInfo, 'store_name', 'hc_id');
        $jobInfo = array_column($hcInfo, 'job_name', 'hc_id');
        $storeManagerIds = array_column($hcInfo, 'manager_id');
        $storeManagerInfo = array_column($hcInfo, 'manager_id', 'hc_id');

        //操作人
        $operatorIds  = array_merge(array_column($list, 'operator_id'), $storeManagerIds);
        $operatorIds  = array_values(array_filter($operatorIds));
        $staffInfo    = (new StaffRepository())->checkoutStaffBatch($operatorIds);
        $staffInfo    = array_column($staffInfo, null, 'staff_info_id');

        foreach ($list as $item) {

            $tran = BackyardBaseModel::beginTransaction($this);
            try {
                if (isset($staffInfo[$item['operator_id']])) {
                    $operatorDetail = sprintf('%s (%s)', $staffInfo[$item['operator_id']]['name'], $staffInfo[$item['operator_id']]['mobile']);
                } else {
                    $operatorDetail = '';
                }

                //根据HC找到网点负责人
                $storeManagerId = $storeManagerInfo[$item['hc_id']] ?? 0;
                if (!empty($storeManagerId)) {
                    $managerDetail = sprintf('%s (%s)', $staffInfo[$storeManagerId]['name'], $staffInfo[$storeManagerId]['mobile']);
                    $managerMobile = $staffInfo[$storeManagerId]['mobile'];
                    $managerName   = $staffInfo[$storeManagerId]['name'];
                } else {
                    $managerDetail = '';
                    $managerMobile = '';
                    $managerName   = '';
                }

                //获取详情内容
                $contentParams = [
                    'staff_name'           => $item['staff_name'],
                    'store_name'           => !empty($storeInfo) ? ($storeInfo[$item['hc_id']] ?? '') : '',
                    'expected_entry_date'  => $item['expected_entry_date'],
                    'job_title_name'       => !empty($jobInfo) ? ($jobInfo[$item['hc_id']] ?? '') : '',
                    'operator_name'        => $operatorDetail,
                    'operator_mobile'      => $staffInfo[$item['operator_id']]['mobile']??'',
                    'store_manager_info'   => $managerDetail,
                    'store_manager_name'   => $managerName,
                    'store_manager_mobile' => $managerMobile,
                    'data_type'            => $item['data_type'],
                ];
                $content = $server->getSendOfferMessage($contentParams);

                //存发送日志
                $hrSmsModel              = new HrSmsLogModel();
                $hrSmsModel->sms_type    = HrSmsLogModel::SMS_TYPE_AGENT_OFFER;
                $hrSmsModel->origin_id   = $item['id'];
                $hrSmsModel->area_code   = $this->getAreaCode();
                $hrSmsModel->mobile      = $item['mobile'];
                $hrSmsModel->content     = $content;
                $hrSmsModel->operator_id = $item['operator_id'];
                $hrSmsModel->save();

                //获取短链
                $fullUrl = sprintf('%s%s/offer/tpl?resume_id=&country_code=%s&id=%d',
                    env("h5_url"),
                    'en', // 这里是语言，详细内容是已经翻译好的，这里随便传一个语言就行
                    env("country_code"),
                    $hrSmsModel->id);
                $shortUrl = $this->getShortUrl($fullUrl);
                $this->logger->write_log(sprintf('sendHireTypeUnPaidOffer,id= %d,short link=%s', $hrSmsModel->id, $shortUrl), 'info');

                //获取短链短信模版
                $shortContent = $server->getSendOfferSmsTemplate($shortUrl);

                //发短信
                //21180【MY】SMS短信去掉短链接---停发
                if(!isCountry('MY')) {
                    $interviewServer->sendSms($item['mobile'], $shortContent);
                }

                //
                $data = HireTypeImportListModel::findFirst($item['id']);
                if (empty($data)) {
                    throw new \Exception('invalid data' . $item['id']);
                }
                $data->send_offer_state = HireTypeImportListModel::SEND_OFFER_HAS_SEND;
                $data->send_offer_content = $shortUrl;
                $data->send_offer_time = gmdate('Y-m-d H:i:s');
                $data->save();

                $tran->commit();
            } catch (\Exception $e) {
                $tran->rollback();
                $this->logger->write_log(sprintf("sendHireTypeUnPaidOffer failure : %s", $e->getMessage() . $e->getTraceAsString()));
            }
        }
        return true;
    }

    /**
     * @return int
     */
    public function getAreaCode(): int
    {
        switch (strtoupper(env('country_code', 'TH'))) {
            case 'MY':
                $areaCode = 60;
                break;
            case 'PH':
                $areaCode = 63;
                break;
            case 'TH':
            default:
                $areaCode = 66;
                break;
        }
        return $areaCode;
    }

    /**
     * @description 获取hc对应的网点的名字
     * @param $hc_ids
     * @return array
     */
    private function getWorkStore($hc_ids): array
    {
        if (empty($hc_ids)) {
            return [];
        }
        $builder = $this->modelsManager->createBuilder();
        $builder->columns(['h.hc_id', 's.name as store_name', 'j.job_name', 's.manager_id']);
        $builder->from(['h' => HrhcModel::class]);
        $builder->leftJoin(SysStoreModel::class, 'h.worknode_id = s.id', 's');
        $builder->leftJoin(HrJobTitleModel::class, 'h.job_title = j.id', 'j');
        $builder->inWhere('h.hc_id', $hc_ids);
        $list = $builder->getQuery()->execute()->toArray();
        if (empty($list)) {
            return [];
        }
        return $list;
    }

    /**
     * 下载导入模版
     */
    public function getDownloadTemplate(): array
    {
        $template = SettingEnvModel::getEnvByCode('download_hire_type_temp');
        $templateList = json_decode($template, true);
        if (empty($templateList)) {
            return self::checkReturn(['data' => []]);
        }

        return self::checkReturn(['data' => $templateList]);
    }

    public function getImportErrMessage($err_code)
    {
        return $this->getTranslation()->_('err_msg.' . $err_code);
    }

    /**
     * @description 添加转型到个人代理
     * @throws ValidationException
     */
    public function addTransferToAgent($params): array
    {
        $staffInfoId   = $params['origin_staff_info_id'];
        $mobile        = $params['mobile'];
        $entryDate     = $params['entry_date'];
        $jobTitleId    = $params['job_title_id'];
        $staffResignId = $params['resign_id'] ?? 0;

        if (empty($staffInfoId) || empty($mobile) || empty($entryDate) || empty($jobTitleId) || empty($staffResignId)) {
            throw new ValidationException('miss args');
        }
        $staffInfo = (new StaffRepository())->getStaffInfoById($staffInfoId);

        $db = $this->getDI()->get('db');
        $db->begin();

        //是否存在winhr导入数据
        $importInfo = HireTypeImportListModel::findFirst([
            'conditions' => 'old_staff_id = :staff_id: and entry_state = :entry_state: and data_source = :data_source:',
            'bind' => [
                'staff_id'    => $staffInfoId,
                'entry_state' => HrEntryModel::STATUS_TO_BE_EMPLOYED,
                'data_source' => HireTypeImportListModel::DATA_SOURCE_WIN_HR,
            ],
            'for_update' => true,
        ]);

        //匹配HC
        $params = [
            'department_id' => $staffInfo['node_department_id'],
            'job_title_id'  => $jobTitleId,
            'sys_store_id'  => $staffInfo['sys_store_id'],
        ];

        $model                       = new HireTypeImportListModel();
        $model->old_staff_id         = $staffInfoId;
        $model->expected_entry_date  = $entryDate;
        $model->import_state         = HireTypeImportListModel::IMPORT_STATE_SUCCESS;
        $model->new_staff_job_title  = $jobTitleId;
        $model->receive_offer_mobile = $mobile;
        $model->entry_state          = HrEntryModel::STATUS_TO_BE_EMPLOYED;
        $model->data_source          = HireTypeImportListModel::DATA_SOURCE_BY;
        $model->operator_id          = $staffInfoId;
        $model->staff_resign_id      = $staffResignId;

        if (empty($importInfo)) {
            $hcId = $this->matchValidHc($params);
            $model->hc_id = $hcId;
            $model->save();
        } else {
            //软删除
            $importInfo->deleted = enums::IS_DELETED_YES;
            $importInfo->save();

            //如职位变更，则归还HC
            if ($jobTitleId != $importInfo->new_staff_job_title) {
                $hcInfo = HrhcModel::findFirst([
                    'conditions' => 'hc_id = :hc_id:',
                    'bind' => [
                        'hc_id' => $importInfo->hc_id,
                    ],
                    'for_update' => true,
                ]);
                $hcInfo->surplusnumber = new \Phalcon\Db\RawValue('surplusnumber + 1');
                $hcInfo->state_code = HrhcModel::STATE_RECRUITING;
                $hcInfo->save();

                $hcId = $this->matchValidHc($params);
                $model->hc_id = $hcId;
                $model->save();
            } else {
                $model->hc_id = $importInfo->hc_id;
                $model->save();
            }
        }

        $db->commit();

        return self::checkReturn([]);
    }

    /**
     * @description 获取离职列表
     * @param array $staffInfoIds
     * @return array
     */
    private function getStaffResignInfo(array $staffInfoIds): array
    {
        if (empty($staffInfoIds)) {
            return [];
        }
        //是否存在“审批中”/“已通过”/“超时”且离职原因=“要去做个人代理”的离职申请
        return StaffResignModel::find([
            //'conditions' => 'submitter_id in({staff_info_ids:array}) and (status in(1,2) or status = 5 and reason = 33)',
            'conditions' => 'submitter_id in({staff_info_ids:array}) and status in(1,2,5) and reason = 33',
            'bind' => [
                'staff_info_ids' => $staffInfoIds
            ]
        ])->toArray();
    }
}