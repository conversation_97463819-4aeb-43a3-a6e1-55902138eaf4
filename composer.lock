{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "5d61663e3c6c297a6c6a8a6ec8ef2a03", "packages": [{"name": "aliyun/aliyun-mns-php-sdk", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/aliyun/aliyun-mns-php-sdk.git", "reference": "f4b32a964d6e411b6dcc0137e00e94c47dd8b3e1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/aliyun/aliyun-mns-php-sdk/zipball/f4b32a964d6e411b6dcc0137e00e94c47dd8b3e1", "reference": "f4b32a964d6e411b6dcc0137e00e94c47dd8b3e1", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"guzzlehttp/guzzle": ">=6.0.0", "php": ">=5.5.0", "psr/http-message": "^1.0"}, "type": "library", "autoload": {"psr-4": {"AliyunMNS\\": "AliyunMNS/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Aliyun MNS Team", "homepage": "http://www.aliyun.com/product/mns"}], "description": "Aliyun Message and Notification Service SDK for PHP, PHP>=5.5.0", "homepage": "https://github.com/aliyun/aliyun-mns-php-sdk", "keywords": ["<PERSON><PERSON><PERSON>", "message", "message service", "mns", "notification"], "time": "2019-06-14T04:35:52+00:00"}, {"name": "aliyunmq/mq-http-sdk", "version": "1.0.4", "source": {"type": "git", "url": "https://github.com/aliyunmq/mq-http-php-sdk.git", "reference": "615f473fe28d9378b518fb4a8449f99e0e514861"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/aliyunmq/mq-http-php-sdk/zipball/615f473fe28d9378b518fb4a8449f99e0e514861", "reference": "615f473fe28d9378b518fb4a8449f99e0e514861", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-dom": "*", "ext-xmlreader": "*", "guzzlehttp/guzzle": ">=6.0.0", "php": ">=5.5.0"}, "type": "library", "autoload": {"psr-4": {"MQ\\": "MQ/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Aliyun Message Queue(MQ) Http PHP SDK, PHP>=5.5.0", "homepage": "https://github.com/aliyunmq/mq-http-php-sdk", "keywords": ["MQ", "alicloud", "<PERSON><PERSON><PERSON>", "message", "message queue", "queue"], "support": {"issues": "https://github.com/aliyunmq/mq-http-php-sdk/issues", "source": "https://github.com/aliyunmq/mq-http-php-sdk/tree/1.0.4"}, "time": "2023-01-10T06:42:49+00:00"}, {"name": "danielst<PERSON>les/stringy", "version": "3.1.0", "source": {"type": "git", "url": "https://github.com/danielstjules/Stringy.git", "reference": "df24ab62d2d8213bbbe88cc36fc35a4503b4bd7e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/danielstjules/Stringy/zipball/df24ab62d2d8213bbbe88cc36fc35a4503b4bd7e", "reference": "df24ab62d2d8213bbbe88cc36fc35a4503b4bd7e", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.4.0", "symfony/polyfill-mbstring": "~1.1"}, "require-dev": {"phpunit/phpunit": "~4.0"}, "type": "library", "autoload": {"psr-4": {"Stringy\\": "src/"}, "files": ["src/Create.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.danielstjules.com"}], "description": "A string manipulation library with multibyte support", "homepage": "https://github.com/danielstjules/Stringy", "keywords": ["UTF", "helpers", "manipulation", "methods", "multibyte", "string", "utf-8", "utility", "utils"], "time": "2017-06-12T01:10:27+00:00"}, {"name": "fguillot/json-rpc", "version": "v1.2.8", "source": {"type": "git", "url": "https://github.com/matasarei/JsonRPC.git", "reference": "f1eef90bf0bb3f7779c9c8113311811ef449ece8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/matasarei/JsonRPC/zipball/f1eef90bf0bb3f7779c9c8113311811ef449ece8", "reference": "f1eef90bf0bb3f7779c9c8113311811ef449ece8", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.4"}, "require-dev": {"phpunit/phpunit": "4.8.*"}, "type": "library", "autoload": {"psr-0": {"JsonRPC": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "description": "Simple Json-RPC client/server library that just works", "homepage": "https://github.com/matasarei/JsonRPC", "support": {"issues": "https://github.com/matasarei/JsonRPC/issues", "source": "https://github.com/matasarei/JsonRPC/tree/v1.2.8"}, "time": "2019-03-23T16:13:00+00:00"}, {"name": "guzzlehttp/guzzle", "version": "6.3.3", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "407b0cb880ace85c9b63c5f9551db498cb2d50ba"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/407b0cb880ace85c9b63c5f9551db498cb2d50ba", "reference": "407b0cb880ace85c9b63c5f9551db498cb2d50ba", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"guzzlehttp/promises": "^1.0", "guzzlehttp/psr7": "^1.4", "php": ">=5.5"}, "require-dev": {"ext-curl": "*", "phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.4 || ^7.0", "psr/log": "^1.0"}, "suggest": {"psr/log": "Required for using the Log middleware"}, "type": "library", "extra": {"branch-alias": {"dev-master": "6.3-dev"}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Guzzle is a PHP HTTP client library", "homepage": "http://guzzlephp.org/", "keywords": ["client", "curl", "framework", "http", "http client", "rest", "web service"], "time": "2018-04-22T15:46:56+00:00"}, {"name": "guzzlehttp/promises", "version": "v1.3.1", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "a59da6cf61d80060647ff4d3eb2c03a2bc694646"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/a59da6cf61d80060647ff4d3eb2c03a2bc694646", "reference": "a59da6cf61d80060647ff4d3eb2c03a2bc694646", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.5.0"}, "require-dev": {"phpunit/phpunit": "^4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Promise\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Guzzle promises library", "keywords": ["promise"], "time": "2016-12-20T10:07:11+00:00"}, {"name": "guzzlehttp/psr7", "version": "1.5.2", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "9f83dded91781a01c63574e387eaa769be769115"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/9f83dded91781a01c63574e387eaa769be769115", "reference": "9f83dded91781a01c63574e387eaa769be769115", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.4.0", "psr/http-message": "~1.0", "ralouphie/getallheaders": "^2.0.5"}, "provide": {"psr/http-message-implementation": "1.0"}, "require-dev": {"phpunit/phpunit": "~4.8.36 || ^5.7.27 || ^6.5.8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.5-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Psr7\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "homepage": "https://github.com/Tobion"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "time": "2018-12-04T20:46:45+00:00"}, {"name": "hprose/hprose", "version": "v2.0.39", "source": {"type": "git", "url": "https://github.com/hprose/hprose-php.git", "reference": "cda46f873c78585ad77bf4be733edd6e2a9a6573"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hprose/hprose-php/zipball/cda46f873c78585ad77bf4be733edd6e2a9a6573", "reference": "cda46f873c78585ad77bf4be733edd6e2a9a6573", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": ">=4.0.0"}, "suggest": {"ext-hprose": "Faster serialize and unserialize hprose extension."}, "type": "library", "autoload": {"files": ["src/init.php"], "psr-4": {"Hprose\\": "src/Hprose"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://hprose.com", "role": "Developer"}], "description": "It is a modern, lightweight, cross-language, cross-platform, object-oriented, high performance, remote dynamic communication middleware. It is not only easy to use, but powerful. You just need a little time to learn, then you can use it to easily construct cross language cross platform distributed application system.", "homepage": "http://hprose.com/", "keywords": ["HTML5", "H<PERSON>rose", "Socket", "ajax", "async", "communication", "cross-domain", "cross-language", "cross-platform", "framework", "future", "game", "http", "json", "jsonrpc", "library", "middleware", "phprpc", "protocol", "rpc", "serialization", "serialize", "service", "tcp", "unix", "web", "webapi", "webservice", "websocket", "xmlrpc"], "time": "2019-06-07T09:30:44+00:00"}, {"name": "markbaker/complex", "version": "1.4.7", "source": {"type": "git", "url": "https://github.com/MarkBaker/PHPComplex.git", "reference": "1ea674a8308baf547cbcbd30c5fcd6d301b7c000"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MarkBaker/PHPComplex/zipball/1ea674a8308baf547cbcbd30c5fcd6d301b7c000", "reference": "1ea674a8308baf547cbcbd30c5fcd6d301b7c000", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^5.6.0|^7.0.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^0.4.3", "phpcompatibility/php-compatibility": "^8.0", "phpdocumentor/phpdocumentor": "2.*", "phploc/phploc": "2.*", "phpmd/phpmd": "2.*", "phpunit/phpunit": "^4.8.35|^5.4.0", "sebastian/phpcpd": "2.*", "squizlabs/php_codesniffer": "^3.3.0"}, "type": "library", "autoload": {"psr-4": {"Complex\\": "classes/src/"}, "files": ["classes/src/functions/abs.php", "classes/src/functions/acos.php", "classes/src/functions/acosh.php", "classes/src/functions/acot.php", "classes/src/functions/acoth.php", "classes/src/functions/acsc.php", "classes/src/functions/acsch.php", "classes/src/functions/argument.php", "classes/src/functions/asec.php", "classes/src/functions/asech.php", "classes/src/functions/asin.php", "classes/src/functions/asinh.php", "classes/src/functions/atan.php", "classes/src/functions/atanh.php", "classes/src/functions/conjugate.php", "classes/src/functions/cos.php", "classes/src/functions/cosh.php", "classes/src/functions/cot.php", "classes/src/functions/coth.php", "classes/src/functions/csc.php", "classes/src/functions/csch.php", "classes/src/functions/exp.php", "classes/src/functions/inverse.php", "classes/src/functions/ln.php", "classes/src/functions/log2.php", "classes/src/functions/log10.php", "classes/src/functions/negative.php", "classes/src/functions/pow.php", "classes/src/functions/rho.php", "classes/src/functions/sec.php", "classes/src/functions/sech.php", "classes/src/functions/sin.php", "classes/src/functions/sinh.php", "classes/src/functions/sqrt.php", "classes/src/functions/tan.php", "classes/src/functions/tanh.php", "classes/src/functions/theta.php", "classes/src/operations/add.php", "classes/src/operations/subtract.php", "classes/src/operations/multiply.php", "classes/src/operations/divideby.php", "classes/src/operations/divideinto.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Class for working with complex numbers", "homepage": "https://github.com/MarkBaker/PHPComplex", "keywords": ["complex", "mathematics"], "time": "2018-10-13T23:28:42+00:00"}, {"name": "markbaker/matrix", "version": "1.1.4", "source": {"type": "git", "url": "https://github.com/MarkBaker/PHPMatrix.git", "reference": "6ea97472b5baf12119b4f31f802835b820dd6d64"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MarkBaker/PHPMatrix/zipball/6ea97472b5baf12119b4f31f802835b820dd6d64", "reference": "6ea97472b5baf12119b4f31f802835b820dd6d64", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^5.6.0|^7.0.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^0.4.3", "phpcompatibility/php-compatibility": "^8.0", "phpdocumentor/phpdocumentor": "2.*", "phploc/phploc": "2.*", "phpmd/phpmd": "2.*", "phpunit/phpunit": "^4.8.35|^5.4.0", "sebastian/phpcpd": "2.*", "squizlabs/php_codesniffer": "^3.3.0"}, "type": "library", "autoload": {"psr-4": {"Matrix\\": "classes/src/"}, "files": ["classes/src/functions/adjoint.php", "classes/src/functions/antidiagonal.php", "classes/src/functions/cofactors.php", "classes/src/functions/determinant.php", "classes/src/functions/diagonal.php", "classes/src/functions/identity.php", "classes/src/functions/inverse.php", "classes/src/functions/minors.php", "classes/src/functions/trace.php", "classes/src/functions/transpose.php", "classes/src/operations/add.php", "classes/src/operations/directsum.php", "classes/src/operations/subtract.php", "classes/src/operations/multiply.php", "classes/src/operations/divideby.php", "classes/src/operations/divideinto.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Class for working with matrices", "homepage": "https://github.com/MarkBaker/PHPMatrix", "keywords": ["mathematics", "matrix", "vector"], "time": "2018-11-04T22:12:12+00:00"}, {"name": "mpdf/mpdf", "version": "v7.1.9", "source": {"type": "git", "url": "https://github.com/mpdf/mpdf.git", "reference": "a0fc1215d2306aa3b4ba6e97bd6ebe4bab6a88fb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mpdf/mpdf/zipball/a0fc1215d2306aa3b4ba6e97bd6ebe4bab6a88fb", "reference": "a0fc1215d2306aa3b4ba6e97bd6ebe4bab6a88fb", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-gd": "*", "ext-mbstring": "*", "myclabs/deep-copy": "^1.7", "paragonie/random_compat": "^1.4|^2.0|9.99.99", "php": "^5.6 || ~7.0.0 || ~7.1.0 || ~7.2.0 || ~7.3.0", "psr/log": "^1.0", "setasign/fpdi": "1.6.*"}, "require-dev": {"mockery/mockery": "^0.9.5", "phpunit/phpunit": "^5.0", "squizlabs/php_codesniffer": "^2.7.0", "tracy/tracy": "^2.4"}, "suggest": {"ext-bcmath": "Needed for generation of some types of barcodes", "ext-xml": "Needed mainly for SVG manipulation", "ext-zlib": "Needed for compression of embedded resources, such as fonts"}, "type": "library", "extra": {"branch-alias": {"dev-development": "7.x-dev"}}, "autoload": {"psr-4": {"Mpdf\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-only"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "role": "<PERSON><PERSON><PERSON>, maintainer"}, {"name": "<PERSON>", "role": "<PERSON><PERSON><PERSON> (retired)"}], "description": "PHP library generating PDF files from UTF-8 encoded HTML", "homepage": "https://mpdf.github.io", "keywords": ["pdf", "php", "utf-8"], "time": "2019-02-06T13:32:19+00:00"}, {"name": "myclabs/deep-copy", "version": "1.9.1", "source": {"type": "git", "url": "https://github.com/myclabs/DeepCopy.git", "reference": "e6828efaba2c9b79f4499dae1d66ef8bfa7b2b72"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/e6828efaba2c9b79f4499dae1d66ef8bfa7b2b72", "reference": "e6828efaba2c9b79f4499dae1d66ef8bfa7b2b72", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.1"}, "replace": {"myclabs/deep-copy": "self.version"}, "require-dev": {"doctrine/collections": "^1.0", "doctrine/common": "^2.6", "phpunit/phpunit": "^7.1"}, "type": "library", "autoload": {"psr-4": {"DeepCopy\\": "src/DeepCopy/"}, "files": ["src/DeepCopy/deep_copy.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Create deep copies (clones) of your objects", "keywords": ["clone", "copy", "duplicate", "object", "object graph"], "time": "2019-04-07T13:18:21+00:00"}, {"name": "paragonie/random_compat", "version": "v9.99.99", "source": {"type": "git", "url": "https://github.com/paragonie/random_compat.git", "reference": "84b4dfb120c6f9b4ff7b3685f9b8f1aa365a0c95"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/random_compat/zipball/84b4dfb120c6f9b4ff7b3685f9b8f1aa365a0c95", "reference": "84b4dfb120c6f9b4ff7b3685f9b8f1aa365a0c95", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7"}, "require-dev": {"phpunit/phpunit": "4.*|5.*", "vimeo/psalm": "^1"}, "suggest": {"ext-libsodium": "Provides a modern crypto API that can be used to generate random bytes."}, "type": "library", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com"}], "description": "PHP 5.x polyfill for random_bytes() and random_int() from PHP 7", "keywords": ["csprng", "polyfill", "pseudorandom", "random"], "time": "2018-07-02T15:55:56+00:00"}, {"name": "paragraph1/php-fcm", "version": "0.7", "source": {"type": "git", "url": "https://github.com/Paragraph1/php-fcm.git", "reference": "****************************************"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Paragraph1/php-fcm/zipball/****************************************", "reference": "****************************************", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"guzzlehttp/guzzle": "^6.2", "php": ">=5.5"}, "require-dev": {"mockery/mockery": "0.9.5", "phpunit/phpunit": "4.8.*", "satooshi/php-coveralls": "dev-master"}, "type": "library", "autoload": {"psr-4": {"paragraph1\\phpFCM\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP application server for google firebase cloud messaging (FCM)", "homepage": "https://github.com/Paragraph1/phpFCM", "keywords": ["FCM", "Firebase Cloud Messaging", "android", "firebase", "gcm", "google", "ios", "notifications", "php"], "time": "2016-08-18T11:47:56+00:00"}, {"name": "phpoffice/phpexcel", "version": "1.8.2", "source": {"type": "git", "url": "https://github.com/PHPOffice/PHPExcel.git", "reference": "1441011fb7ecdd8cc689878f54f8b58a6805f870"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPOffice/PHPExcel/zipball/1441011fb7ecdd8cc689878f54f8b58a6805f870", "reference": "1441011fb7ecdd8cc689878f54f8b58a6805f870", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-mbstring": "*", "ext-xml": "*", "ext-xmlwriter": "*", "php": "^5.2|^7.0"}, "require-dev": {"squizlabs/php_codesniffer": "2.*"}, "type": "library", "autoload": {"psr-0": {"PHPExcel": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-2.1"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "http://blog.maartenballiauw.be"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "http://rootslabs.net"}, {"name": "<PERSON>", "homepage": "http://markbakeruk.net"}], "description": "PHPExcel - OpenXML - Read, Create and Write Spreadsheet documents in PHP - Spreadsheet engine", "homepage": "https://github.com/PHPOffice/PHPExcel", "keywords": ["OpenXML", "excel", "php", "spreadsheet", "xls", "xlsx"], "abandoned": "phpoffice/phpspreadsheet", "time": "2018-11-22T23:07:24+00:00"}, {"name": "phpoffice/phpspreadsheet", "version": "1.6.0", "source": {"type": "git", "url": "https://github.com/PHPOffice/PhpSpreadsheet.git", "reference": "bf00f0cc5f55c354018f9a9ef15e6e3e1a229051"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPOffice/PhpSpreadsheet/zipball/bf00f0cc5f55c354018f9a9ef15e6e3e1a229051", "reference": "bf00f0cc5f55c354018f9a9ef15e6e3e1a229051", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-ctype": "*", "ext-dom": "*", "ext-fileinfo": "*", "ext-gd": "*", "ext-iconv": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-simplexml": "*", "ext-xml": "*", "ext-xmlreader": "*", "ext-xmlwriter": "*", "ext-zip": "*", "ext-zlib": "*", "markbaker/complex": "^1.4", "markbaker/matrix": "^1.1", "php": "^5.6|^7.0", "psr/simple-cache": "^1.0"}, "require-dev": {"doctrine/instantiator": "^1.0.0", "dompdf/dompdf": "^0.8.0", "friendsofphp/php-cs-fixer": "@stable", "jpgraph/jpgraph": "^4.0", "mpdf/mpdf": "^7.0.0", "phpcompatibility/php-compatibility": "^8.0", "phpunit/phpunit": "^5.7", "squizlabs/php_codesniffer": "^3.3", "tecnickcom/tcpdf": "^6.2"}, "suggest": {"dompdf/dompdf": "Option for rendering PDF with PDF Writer", "jpgraph/jpgraph": "Option for rendering charts, or including charts with PDF or HTML Writers", "mpdf/mpdf": "Option for rendering PDF with PDF Writer", "tecnickcom/tcpdf": "Option for rendering PDF with PDF Writer"}, "type": "library", "autoload": {"psr-4": {"PhpOffice\\PhpSpreadsheet\\": "src/PhpSpreadsheet"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-2.1-or-later"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://blog.maartenballiauw.be"}, {"name": "<PERSON>", "homepage": "https://markbakeruk.net"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://rootslabs.net"}], "description": "PHPSpreadsheet - Read, Create and Write Spreadsheet documents in PHP - Spreadsheet engine", "homepage": "https://github.com/PHPOffice/PhpSpreadsheet", "keywords": ["OpenXML", "excel", "gnumeric", "ods", "php", "spreadsheet", "xls", "xlsx"], "time": "2019-01-02T04:42:54+00:00"}, {"name": "psr/http-message", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/f6561bf28d520154e4b0ec72be95418abe6d9363", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "time": "2016-08-06T14:39:51+00:00"}, {"name": "psr/log", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "6c001f1daafa3a3ac1d8ff69ee4db8e799a654dd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/6c001f1daafa3a3ac1d8ff69ee4db8e799a654dd", "reference": "6c001f1daafa3a3ac1d8ff69ee4db8e799a654dd", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "Psr/Log/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "time": "2018-11-20T15:27:04+00:00"}, {"name": "psr/simple-cache", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/simple-cache.git", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/simple-cache/zipball/408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\SimpleCache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interfaces for simple caching", "keywords": ["cache", "caching", "psr", "psr-16", "simple-cache"], "time": "2017-10-23T01:57:42+00:00"}, {"name": "ralouphie/getallheaders", "version": "2.0.5", "source": {"type": "git", "url": "https://github.com/ralouphie/getallheaders.git", "reference": "5601c8a83fbba7ef674a7369456d12f1e0d0eafa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/5601c8a83fbba7ef674a7369456d12f1e0d0eafa", "reference": "5601c8a83fbba7ef674a7369456d12f1e0d0eafa", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3"}, "require-dev": {"phpunit/phpunit": "~3.7.0", "satooshi/php-coveralls": ">=1.0"}, "type": "library", "autoload": {"files": ["src/getallheaders.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "time": "2016-02-11T07:05:27+00:00"}, {"name": "setasign/fpdi", "version": "1.6.2", "source": {"type": "git", "url": "https://github.com/Setasign/FPDI.git", "reference": "a6ad58897a6d97cc2d2cd2adaeda343b25a368ea"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Setasign/FPDI/zipball/a6ad58897a6d97cc2d2cd2adaeda343b25a368ea", "reference": "a6ad58897a6d97cc2d2cd2adaeda343b25a368ea", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "suggest": {"setasign/fpdf": "FPDI will extend this class but as it is also possible to use \"tecnickcom/tcpdf\" as an alternative there's no fixed dependency configured.", "setasign/fpdi-fpdf": "Use this package to automatically evaluate dependencies to FPDF.", "setasign/fpdi-tcpdf": "Use this package to automatically evaluate dependencies to TCPDF."}, "type": "library", "autoload": {"classmap": ["filters/", "fpdi.php", "fpdf_tpl.php", "fpdi_pdf_parser.php", "pdf_context.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.setasign.com"}], "description": "FPDI is a collection of PHP classes facilitating developers to read pages from existing PDF documents and use them as templates in FPDF. Because it is also possible to use FPDI with TCPDF, there are no fixed dependencies defined. Please see suggestions for packages which evaluates the dependencies automatically.", "homepage": "https://www.setasign.com/fpdi", "keywords": ["fpdf", "fpdi", "pdf"], "time": "2017-05-11T14:25:49+00:00"}, {"name": "symfony/polyfill-ctype", "version": "v1.11.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-ctype.git", "reference": "82ebae02209c21113908c229e9883c419720738a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/82ebae02209c21113908c229e9883c419720738a", "reference": "82ebae02209c21113908c229e9883c419720738a", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.3"}, "suggest": {"ext-ctype": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.11-dev"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "time": "2019-02-06T07:57:58+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.11.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "fe5e94c604826c35a32fa832f35bd036b6799609"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/fe5e94c604826c35a32fa832f35bd036b6799609", "reference": "fe5e94c604826c35a32fa832f35bd036b6799609", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.3"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.11-dev"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "time": "2019-02-06T07:57:58+00:00"}, {"name": "vlucas/phpdotenv", "version": "v2.6.1", "source": {"type": "git", "url": "https://github.com/vlucas/phpdotenv.git", "reference": "2a7dcf7e3e02dc5e701004e51a6f304b713107d5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/2a7dcf7e3e02dc5e701004e51a6f304b713107d5", "reference": "2a7dcf7e3e02dc5e701004e51a6f304b713107d5", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.9", "symfony/polyfill-ctype": "^1.9"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.6-dev"}}, "autoload": {"psr-4": {"Dotenv\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.vancelucas.com"}], "description": "Loads environment variables from `.env` to `getenv()`, `$_ENV` and `$_SERVER` automagically.", "keywords": ["dotenv", "env", "environment"], "time": "2019-01-29T11:11:52+00:00"}, {"name": "webgeeker/validation", "version": "0.3", "source": {"type": "git", "url": "https://github.com/photondragon/webgeeker-validation.git", "reference": "dceb68de499706df2dc80e69fc69a09f64d33f32"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/photondragon/webgeeker-validation/zipball/dceb68de499706df2dc80e69fc69a09f64d33f32", "reference": "dceb68de499706df2dc80e69fc69a09f64d33f32", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.6"}, "require-dev": {"phpunit/phpunit": "^5.7"}, "type": "library", "autoload": {"psr-4": {"WebGeeker\\Validation\\": "src/Validation/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "photondragon", "email": "<EMAIL>"}], "description": "Validation for WebGeeker", "time": "2018-09-28T21:03:05+00:00"}], "packages-dev": [], "aliases": [], "minimum-stability": "stable", "stability-flags": [], "prefer-stable": false, "prefer-lowest": false, "platform": {"php": "^7.2", "ext-json": "*", "ext-phalcon": "*"}, "platform-dev": [], "plugin-api-version": "2.3.0"}