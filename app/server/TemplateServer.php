<?php
namespace FlashExpress\bi\App\Server;


use FlashExpress\bi\App\library\enumsTh;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Repository\SysDepartmentRepository;

class TemplateServer extends BaseServer
{
    public function __construct($lang = '')
    {
        parent::__construct();
    }


    /**
     * 替换短信模板内容
     * @param $resumeInfo
     * @param array $content
     * @return array
     */
    public function replaceInterviewTpl($resumeInfo, $content = [])
    {
        $data['department_id'] = $resumeInfo['hr_hc_department_id'] ?? 0;
        $data['job_title_id']  = $resumeInfo['hc_job_title'] ?? 0;
        $data['hire_type']     = $resumeInfo['hire_type'] ?? 0;

        if (isCountry('TH') && $this->checkThFulfillmentInternship($data)) {
            return $this->getFulfillmentInternshipInterviewTpl($content);
        }

        return $content;
    }

    /**
     * 获取offer模板
     */
    public function getFulfillmentInternshipOfferTpl($resumeInfo)
    {
        $staff  = isset($resumeInfo['hr_interview_offer']['submitter']) && $resumeInfo['hr_interview_offer']['submitter'] ? (string)$resumeInfo['hr_interview_offer']['submitter']['name'] . "(" . (string)$resumeInfo['hr_interview_offer']['submitter']['mobile_company'] . ")" : "";

        $tmp   = "SMS ยืนยันการจ้างงาน\r\n";
        $tmp   .= "ยินดีต้อนรับท่านเข้าสู่ครอบครัว แฟลช ฟูลฟิลล์เม้นท์ \r\n";
        $tmp   .= "ทางบริษัทฯ ขอนัดหมายคุณ : " . $resumeInfo['first_name'] . " " . $resumeInfo['last_name'] . "\r\n";
        $tmp   .= "เริ่มงานที่ : " . (isset($resumeInfo['hr_hc']['store_name']) ? $resumeInfo['hr_hc']['store_name'] : '') . "\r\n";
        $tmp   .= "ตำแหน่งที่ฝึกงาน : " . (isset($resumeInfo['hr_interview_offer']['hr_job_title']['job_name']) ? $resumeInfo['hr_interview_offer']['hr_job_title']['job_name'] : '') . "\r\n";
        $tmp   .= "ค่าเบี้ยเลี้ยง : " . (isset($resumeInfo['hr_interview_offer']['internship_salary']) ? number_format($resumeInfo['hr_interview_offer']['internship_salary'] / 100) : '') . " บาท/วัน \r\n";
        $tmp   .= "ค่าอาหาร : " . (isset($resumeInfo['hr_interview_offer']['food']) ? number_format($resumeInfo['hr_interview_offer']['food'] / 100) : '') . " บาท/วัน \r\n";
        $tmp   .= "ค่าเช่าบ้าน : " . (isset($resumeInfo['hr_interview_offer']['renting']) ? number_format($resumeInfo['hr_interview_offer']['renting'] / 100) : '') . " บาท/วัน \r\n";
        $tmp   .= "วันที่เริ่มงาน : " . (isset($resumeInfo['hr_interview_offer']['work_time']) ? date("Y/m/d", strtotime($resumeInfo['hr_interview_offer']['work_time'])) : '') . "\r\n";
        $tmp   .= "ติดต่อผู้รับผิดชอบ :" . $staff . "\r\n";

        return $tmp;
    }

    /**
     * 获取实习生模板
     * 小改动不改模板
     */
    public function getFulfillmentInternshipInterviewTpl($content = [])
    {
        //短信内容
        if (isset($content['send_interview_th_msg_content'])) {
            $content['send_interview_th_msg_content'] = "แจ้งนัดสัมภาษณ์งาน \r\n บริษัท แฟลช ฟูลฟิลล์เม้นท์ \r\n คุณ : %s \r\n กรุณาตรวจสอบรายละเอียดตามลิงก์  %s";
        }

        ////泰文
        if (isset($content['th_applicant_email'])) {
            $content['th_applicant_email'] = "แจ้งนัดสัมภาษณ์งาน \r\n บริษัท แฟลช ฟูลฟิลล์เม้นท์ \r\n คุณ :  %s %s \r\n โทร : %s\r\n ตำแหน่ง : %s \r\n แผนก :  %s \r\n"
                . "ประจำสาขา : %s \r\n วันที่นัดสัมภาษณ์ : %s \r\n สถานที่นัดสัมภาษณ์ : %s \r\n  ติดต่อผู้รับผิดชอบสาขา : %s \r\n";
        }

        //英文线上
        if (isset($content['en_applicant_online_email'])) {
            $content['en_applicant_online_email'] = "Dear %s: [%s %s] \r\n \r\n Good day. We are pleased to inform that you have been shortlisted for an interview for [%s] with [%s]\r\n \r\n"
                . "Please find the appended information below for your perusal:- \r\n \r\n  Flash Fulfillment Co., Ltd：%s \r\n 【Date / Time】%s \r\n \r\n"
                . "【Interview Platform】 \r\n 【Interview Access】 \r\n 【Contact Person】%s：%s \r\n 【Interviewers】%s \r\n 【Registration Link】%s \r\n \r\n"
                . "Please note that you would be able to access to the meeting via the website, or you could consider downloading the application as well. \r\n \r\n"
                . "Should you require further clarification, please feel free to contact us.\r\n \r\n"
                . "Thank you and we look forward to meeting you soon.\r\n \r\n"
                . "Best regards,\r\n";
        }

        //英文线下
        if (isset($content['en_applicant_email'])) {
            $content['en_applicant_email'] =  "Dear %s  [%s %s] \r\n \r\n Good day. We are pleased to inform that you have been shortlisted for an interview for [%s] with [%s] \r\n \r\n"
            . "Please find the appended information below for your perusal:- \r\n \r\n 【Flash Fulfillment Co., Ltd】%s \r\n 【Location】%s \r\n"
            . "【Date / Time】%s \r\n 【Interview Venue】 \r\n 【Contact Person】%s %s \r\n 【Interviewers】%s \r\n 【Documents to bring along】\r\n 1. Identification Card / Passport \r\n 2. Testimonials (if any) \r\n 3. Latest Payslip \r\n"
            . "【Registration Link】%s \r\n \r\n Please kindly contact the contact person once you have reached our office; do note that you will be required to register yourself at our security section (located at Level 7) prior to the interview session. \r\n \r\n"
            . "Should you require further clarification, please feel free to contact us.\r\n \r\n Thank you and we look forward to meeting you soon.\r\n \r\n Best regards,\r\n";
        }

        //面试官泰文
        if (isset($content['th_interview_email'])) {
            $content['th_interview_email'] = "แจ้งนัดสัมภาษณ์งาน \r\n บริษัท แฟลช ฟูลฟิลล์เม้นท์ \r\n คุณ : %s \r\n  โทร : %s \r\n HCID: %s \r\n ตำแหน่ง : %s \r\n แผนก : %s \r\n ประจำสาขา : %s \r\n วันที่นัดสัมภาษณ์ : %s \r\n"
                . "สถานที่นัดสัมภาษณ์ : %s \r\n ติดต่อผู้รับผิดชอบสาขา : %s \r\n";
        }

        return $content;
    }

    /**
     * 检查是为实习生
     */
    public function checkThFulfillmentInternship($data = [])
    {
        if (!isCountry('TH') || empty($data)) {
            return 0;
        }

        $departmentInfo = (new SysDepartmentRepository())->getDepartmentInfoById($data['department_id']);

        $departmentIds  = explode('/', $departmentInfo['ancestry_v3'] ?? '');

        if (
            $data['hire_type'] == HrStaffInfoModel::HIRE_TYPE_OTHER &&
            $data['job_title_id'] == enumsTh::JOB_TITLE_INTERNSHIP &&
            in_array(enumsTh::DEPARTMENT_TH_FULFILLMENT, $departmentIds)
        ) {
            return 1;
        }

        return 0;
    }

    /**
     * 检查是为Thailand Fulfillment[243]部门及其子部门下
     */
    public function checkThFulfillment($data = [])
    {
        if (!isCountry('TH') || empty($data)) {
            return 0;
        }

        $departmentInfo = (new SysDepartmentRepository())->getDepartmentInfoById($data['department_id']);
        $departmentIds  = explode('/', $departmentInfo['ancestry_v3'] ?? '');
        if (in_array(enumsTh::DEPARTMENT_TH_FULFILLMENT, $departmentIds)) {
            return 1;
        }
        return 0;
    }
}