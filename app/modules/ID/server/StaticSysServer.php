<?php

namespace FlashExpress\bi\App\Modules\ID\Server;

use FlashExpress\bi\App\Repository\ResumeRepository;

class StaticSysServer extends BaseServer
{
    /**
     * 获取offer-根据职位id显示薪资补贴
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getWageSubsidyList($paramIn,$unit_text = 'IDR')
    {



        $resumeId = $paramIn['resume_id'] ?? 0;
        $resumeRepository = new ResumeRepository();
        $resumeData = $resumeRepository->resumeData($resumeId);

        $interviewServer = new \FlashExpress\bi\App\Server\InterviewServer();
        $isFirstLineJob = $interviewServer->isFirstLineJob($resumeData['department_id'], $resumeData['job_title']);

        if(!$isFirstLineJob){
            $offer_salary_item = [
                [
                    "name" => $this->getTranslation()->_('7804'),
                    "key"  => "money",
                    "unit" => "{$unit_text}/Month",
                ],
                [
                    "name" => $this->getTranslation()->_('7803'),
                    "key"  => "trial_salary",
                    "unit" => "{$unit_text}/Month",
                ],
                [
                    "name" => $this->getTranslation()->_('7707'),
                    "key"  => "renting",
                    "unit" => "{$unit_text}/Month",
                ],
                [
                    "name" => $this->getTranslation()->_('7702'),
                    "key"  => "position",
                    "unit" => "{$unit_text}/Month",
                ],
                [
                    "name" => $this->getTranslation()->_('xinghuo_allowance'),
                    "key"  => "xinghuo_allowance",
                    "unit" => "{$unit_text}/Month",
                ],
                [
                    "name" => $this->getTranslation()->_('7704'),
                    "key"  => "food",
                    "unit" => "{$unit_text}/Day",
                ],
                [
                    "name" => $this->getTranslation()->_('7705'),
                    "key"  => "dangerously",
                    "unit" => "{$unit_text}/Day",
                ]

            ];

        }else{

            $offer_salary_item = [
                [
                    "name" => $this->getTranslation()->_('7804'),
                    "key"  => "money",
                    "unit" => "{$unit_text}/Month",
                ],
                [
                    "name" => $this->getTranslation()->_('7803'),
                    "key"  => "trial_salary",
                    "unit" => "{$unit_text}/Month",
                ],
                [
                    "name" => $this->getTranslation()->_('7707'),
                    "key"  => "renting",
                    "unit" => "{$unit_text}/Month",
                ],
                [
                    "name" => $this->getTranslation()->_('7704'),
                    "key"  => "food",
                    "unit" => "{$unit_text}/Day",
                ],
                [
                    "name" => $this->getTranslation()->_('7705'),
                    "key"  => "dangerously",
                    "unit" => "{$unit_text}/Day",
                ]

            ];

        }

        //返回薪资项结构
        $wageSubsidyArr = [
            [
                "ids"  => "default",
                "data" => [],
            ],

            //其他
            [
                //职位除 a,b,c 以外
                "ids"  => 'other',
                "is_get_salary" => 0,
                "data" =>$offer_salary_item
            ],
        ];

        return $wageSubsidyArr;
    }

    /**
     * 获取offer-根据职位id显示薪资补贴
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getWageSubsidyListV2($paramIn,$unit_text = 'IDR')
    {
        $resumeId = $paramIn['resume_id'] ?? 0;
        $resumeRepository = new ResumeRepository();
        $resumeData = $resumeRepository->resumeData($resumeId);

        $salaryItems = [ // 薪资项目
            'money' => [
                "name" => $this->getTranslation()->_('7711'), // 薪酬包
                "key"  => "money",
                "unit" => "{$unit_text}/Month",
            ],
            'basic_salary' => [
                "name" => $this->getTranslation()->_('7701'), // 基本工资
                "key"  => "basic_salary",
                "unit" => "{$unit_text}/Month",
            ],
            'welfare_allowance' => [
                "name" => $this->getTranslation()->_('welfare_allowance'), // 福利工资
                "key"  => "welfare_allowance",
                "unit" => "{$unit_text}/Month",
            ],
            'variable_allowance' => [
                "name" => $this->getTranslation()->_('variable_allowance'), // 非固定津贴
                "key"  => "variable_allowance",
                "unit" => "{$unit_text}/Day",
            ],
            'food_allowance' => [
                "name" => $this->getTranslation()->_('7704'), // 餐补
                "key"  => "food_allowance",
                "unit" => "{$unit_text}/Day",
            ],
            'attendance_allowance' => [
                "name" => $this->getTranslation()->_('attendance_allowance'), // 出勤津贴
                "key"  => "attendance_allowance",
                "unit" => "{$unit_text}/Day",
            ],
            'transport_allowance' => [
                "name" => $this->getTranslation()->_('7811'), // 交通津贴
                "key"  => "transport_allowance",
                "unit" => "{$unit_text}/Day",
            ],
            'house_rental' => [
                "name" => $this->getTranslation()->_('7707'), // 租房津贴
                "key"  => "house_rental",
                "unit" => "{$unit_text}/Month",
            ],
            'xinghuo_allowance' => [
                "name" => $this->getTranslation()->_('xinghuo_allowance'), // 星火津贴
                "key"  => "xinghuo_allowance",
                "unit" => "{$unit_text}/Month",
            ],
            'language_allowance' => [
                "name" => $this->getTranslation()->_('language_allowance'), // 语言津贴
                "key"  => "language_allowance",
                "unit" => "{$unit_text}/Month",
            ],
        ];




        if ($resumeData['nationality'] == 2) {
            // 中国
            unset($salaryItems['welfare_allowance']);
            unset($salaryItems['xinghuo_allowance']);
            unset($salaryItems['language_allowance']);
        }
        return [
            'nationality' => $resumeData['nationality'],
            'money' => $salaryItems, // 试用期
            'trial_salary' => $salaryItems, // 通过试用期
        ];
    }
}
