<?php
/**
 * Created by <PERSON><PERSON><PERSON>torm.
 * User: z<PERSON><PERSON>
 * Date: 2021/9/16
 * Time: 17:31
 */

namespace FlashExpress\bi\App\Modules\MY\Controllers;

use FlashExpress\bi\App\Controllers\InterviewController as GlobalInterviewController;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Modules\MY\Server\OfferSignServer;
use FlashExpress\bi\App\Modules\MY\Server\StaticSysServer;
use FlashExpress\bi\App\Modules\MY\Server\InterviewServer;
use FlashExpress\bi\App\Models\backyard\HrhcModel;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use Exception;
use FlashExpress\bi\App\Server\SysListServer;

class InterviewController extends GlobalInterviewController
{

    /**
     * 获取发送offer页面薪资接口
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getOfferSalaryItemAction()
    {
        $validations['resume_id'] = "Required|StrLenGeLe:1, 10";
        $this->validateCheck($this->paramIn, $validations);
        //获取国家差异化的静态资源
        $specialStaticServer = new StaticSysServer(); //差异化资源放到该server
        $static_data['wageSubsidyList'] = $specialStaticServer->getWageSubsidyList($this->paramIn);

        $returnArr['data']['dataList'] = $static_data;
        return $this->jsonReturn($this->checkReturn($returnArr));
    }


    /**
     * 发送offer
     * @return mixed
     * @Permission(action='Interview.sendOffer')
     */
    public function sendOfferAction()
    {
        //[1]入参
        $paramIn = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        $redisKey            = 'lock_send_offer_' . $paramIn['resume_id'];

        $validations         = [
            "interview_id"   => "Required|Int",
            "resume_id"      => "Required|Int",
            'hc_id'          => "Required|Int",
//            'money'          => "Required|Int",
//                'staff_job_type' => "Required|Int",
//            'job_title_grade' => "Required|IntGe:0",//职级
//            'train_place'    => "Required",
            "work_time"      => "Required|DateTime|>>>:work_time" . $this->getTranslation()->_('4702'),
        ];

        if (isset($paramIn['hire_type']) && $paramIn['hire_type']) {
            // 雇佣类型
            $hire_type                = (new SysListServer())->getHireType();
            $hire_type_arr            = array_column($hire_type, "key");
            $validations['hire_type'] = "Required|IntIn:" . implode(',', $hire_type_arr);
        }

        if (isset($paramIn['hire_type']) && !in_array($paramIn['hire_type'], HrStaffInfoModel::$agentTypeTogether)) {
            $validations['money']           = "Required|Int";
            $validations['train_place']     = "Required";
            $validations['job_title_grade'] = "Required|IntGe:0";
        }

        $this->validateCheck($paramIn, $validations);

        $cache      = $this->getDI()->get('redis');
        if ($cache->get($redisKey)) {
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('please try again')));
        }
        $cache->save($redisKey, 1, 60); //加锁

        //[2]业务处理
        $returnArr = (new InterviewServer())->sendOffer($paramIn);

        //释放锁
        $cache->delete($redisKey);
        //[3]数据返回
        $this->jsonReturn($returnArr);
    }

    /**
     * 简历提供人列表
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function resumeSupplyListAction()
    {
        //[1]入参
        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        //[2]业务处理
        $returnArr = (new InterviewServer())->supplyerList($paramIn);

        //[3]数据返回
        $this->jsonReturn($returnArr);
    }

    /**
     * 面试管理 简历列表
     * @Access  public
     * @Param   request
     * @Return  jsonData
     * @Permission(action='Interview.interviewResumeList')
     */
    public function interviewResumeListAction()
    {
        //[1]入参
        $paramIn = $this->paramIn;
        $paramIn['staff_id'] = $this->userInfo['id'];
        unset($paramIn['no_page']);

        // 入参校验
        $validations = [];
        if (!empty($paramIn['resume_last_operator'])) {
            $validations['resume_last_operator'] = 'IntLe:99999999|>>>:'.$this->getTranslation()->_('input_staff_id_error');
        }
        $this->validateCheck($paramIn, $validations);
        $returnArr = (new InterviewServer())->interviewList($paramIn, $this->userInfo);

        //[3]数据返回
        $this->jsonReturn($returnArr);
    }

    /**
     * 非一线员工职位 发送offer签字接口
     * @Permission(action='Interview.sendOffer')
     */
    public function sendOfferSignAction()
    {
        //[1]入参
        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $this->userInfo['id'];
        $redisKey            = 'lock_send_offer_' . $paramIn['resume_id'];

        $validations         = [
            "interview_id"   => "Required|Int",
            "resume_id"      => "Required|Int",
            'hc_id'          => "Required|Int",
            'money'          => "Required|Int",
//                'staff_job_type' => "Required|Int",
            'job_title_grade' => "Required|IntGe:0",//职级
            'train_place'    => "Required",
            'in_salary_range'    => "Required|Int",
//                'higher_id'    => "Required|Int",
            "work_time"      => "Required|DateTime|>>>:work_time" . $this->getTranslation()->_('4702'),
        ];

        if (isset($paramIn['hire_type']) && $paramIn['hire_type']) {
            if (in_array($paramIn['hire_type'], HrStaffInfoModel::$agentTypeTogether)){
                return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('hire_type_agent_not_offer_sign_error')));
            }

            $validations['hire_type'] = 'Required|IntIn:1,2,3,4,5';
            //实习生
            if ($paramIn['hire_type'] == HrhcModel::HIRE_TYPE_PRACTICE) {
                $validations['internship_start'] = 'Required|Date';
                $validations['internship_end']   = 'Required|Date';
                $validations['company']          = 'Required|Str';
                unset($validations['money']);
                unset($validations['train_place']);
            }
        }

        $this->validateCheck($paramIn, $validations);

        $offerSignServer = new OfferSignServer();
        $hcInfo = $offerSignServer->getHcInfo($paramIn['hc_id']);

        //todo 判断职位ID是否是一线职位，1-是，0-否
        $is_front_line_job = (new InterviewServer())->isFirstLineJob($hcInfo['department_id'], $paramIn['position_id']);
        if($is_front_line_job === true && $paramIn['hire_type'] !=  HrhcModel::HIRE_TYPE_PRACTICE){
            //一线职位不允许访问
            return $this->jsonReturn(self::checkReturn(-3, 'not allow access'));
        }

        $cache      = $this->getDI()->get('redis');
        if ($cache->get($redisKey)) {
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('please try again')));
        }
        $cache->save($redisKey, 1, 60); //加锁

        //[2]业务处理
        $returnArr = $offerSignServer->sendOfferSign($paramIn);

        //释放锁
        $cache->delete($redisKey);
        //[3]数据返回
        $this->jsonReturn($returnArr);

    }

    /**
     * offer修改到岗日期-重新生成offer pdf
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function getOfferPdfByWorkTimeAction()
    {
        //[1]入参
        $paramIn             = $this->paramIn;
        $this->getDI()->get('logger')->write_log("paramIn".json_encode($paramIn, JSON_UNESCAPED_UNICODE), 'info');
        $validations         = [
            "offer_id"  => "Required|Int",
            "work_time" => "Required|DateTime|>>>:work_time" . $this->getTranslation()->_('4702'),
        ];
        $this->validateCheck($paramIn, $validations);
        $return = (new OfferSignServer())->getOfferPdfBy($paramIn);

        //[3]数据返回
        $this->jsonReturn($return);
    }

    /**
     * offer列表-撤销薪资审批
     * @Access  public
     * @Param   request
     * @Return  jsonData
     * @Permission(action='Interview.interviewResumeList')
     */
    public function revokeSalaryAction()
    {
        $paramIn = $this->paramIn;
        $InterviewServer = (new InterviewServer());
        $returnArr = $InterviewServer->revokeSalary($this->userInfo, $paramIn['resume_id'], $paramIn['revoke_remark']);
        if ($returnArr['code'] == 1) {
            //判断是否是非一线职位
            $offer_data = $InterviewServer->getOfferInfo($paramIn['resume_id']);

            $is_front_line_job = $InterviewServer->isFrontLineJob($offer_data['position_id']);
            if(!$is_front_line_job){
                //如果是非一线职位，则更新offer 签字按钮状态
                $offerSignServer = new OfferSignServer();
                $offerSignServer->revokeOfferSignBySalaryRevoke($paramIn['resume_id']);
            }
        }
        $this->jsonReturn($returnArr);
    }

    /**
     * offer签字详情
     * @Access  public
     * @Param   request
     * @Return  jsonData
     * @Permission(action='Interview.interviewResumeList')
     */
    public function offerSignDetailAction()
    {
        $paramIn = $this->paramIn;
        $validations         = [
            "resume_id"     => "Required|Int",
        ];

        $this->validateCheck($paramIn, $validations);
        $returnArr = (new OfferSignServer())->offerSignDetail( $paramIn['resume_id'],$this->userInfo);

        $this->jsonReturn($returnArr);
    }

    /**
     * 发送非一线offer
     * @Access  public
     * @Param   request
     * @Return  jsonData
     * @Permission(action='Interview.sendOffer')
     */
    public function sendOfferSignOfferAction(){
        $paramIn = $this->paramIn;
        $validations         = [
            "resume_id"     => "Required|Int",
            'phone'         => "Required|Int",
            "send_email"    => "Required|Email|>>>:" . $this->getTranslation()->_('4109'),
            'offer_pdf_url' => "Required|Url",
            'send_content'       => "Required|Str",//邮件内容
        ];

        $this->validateCheck($paramIn, $validations);
        $redisKey            = 'lock_send_offer_' . $paramIn['resume_id'];
        $cache      = $this->getDI()->get('redis');
        if ($cache->get($redisKey)) {
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('please try again')));
        }
        $cache->save($redisKey, 1, 10); //加锁

        $returnArr = (new OfferSignServer())->sendOfferSignOffer($this->userInfo, $paramIn);

        //释放锁
        $cache->delete($redisKey);

        $this->getDI()->get('logger')->write_log("sendOfferSignOffer 参数信息:" . json_encode($paramIn), 'info');
        $this->jsonReturn($returnArr);
    }

    /**
     * 重新发送非一线offer
     * 与首次发送offer区别是：只发送邮件内容
     * @Access  public
     * @Param   request
     * @Return  jsonData
     * @Permission(action='Interview.sendOffer')
     */
    public function sendOfferSignOfferEmailAction(){
        $paramIn = $this->paramIn;
        $validations         = [
            "resume_id"     => "Required|Int",
            "send_email"    => "Required|Email|>>>:" . $this->getTranslation()->_('4109'),
            'offer_pdf_url' => "Required|Url",
            'send_content'       => "Required|Str",//邮件内容
        ];

        $this->validateCheck($paramIn, $validations);
        $redisKey            = 'lock_send_offer_again_' . $paramIn['resume_id'];
        $cache      = $this->getDI()->get('redis');
        if ($cache->get($redisKey)) {
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('please try again')));
        }
        $cache->save($redisKey, 1, 10); //加锁
        //重新发送offer邮件内容,不做数据变更
        $returnArr = (new OfferSignServer())->sendOfferSignOfferEmail($this->userInfo, $paramIn);

        //释放锁
        $cache->delete($redisKey);

        $this->getDI()->get('logger')->write_log("sendOfferSignOfferEmailAction 参数信息:" . json_encode($paramIn), 'info');
        $this->jsonReturn($returnArr);
    }

    /**
     * 撤销offer签字
     * @Access  public
     * @Param   request
     * @Return  jsonData
     * @Permission(action='Interview.interviewResumeList')
     */
    public function revokeOfferSignAction()
    {
        $paramIn = $this->paramIn;
        $validations         = [
            "revoke_remark"  => "Required|StrLenGeLe:1,200",
            "resume_id"      => "Required|Int",
        ];

        $this->validateCheck($paramIn, $validations);

        $returnArr = (new OfferSignServer())->revokeOfferSignMain( $paramIn['resume_id'], $paramIn['revoke_remark'],4);

        $this->jsonReturn($returnArr);
    }

    /**
     * 取消offer
     * @Access  public
     * @Param   request
     * @Return  jsonData
     * @Permission(action='Interview.cancelSendOffer')
     */
    public function cancelSendOfferAction()
    {
        //[1]入参
        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $this->userInfo['staff_id'];
        $validations         = [
            "id" => "Required|Int",
        ];
        $this->validateCheck($paramIn, $validations);

        $redisKey            = 'lock_cancel_offer_' . $paramIn['id'];
        $cache      = $this->getDI()->get('redis');
        if ($cache->get($redisKey)) {
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('please try again')));
        }
        $cache->save($redisKey, 1, 60); //加锁

        //[2]业务处理
        $returnArr = (new InterviewServer())->cancelSendOffer($paramIn);

        //释放锁
        $cache->delete($redisKey);

        //[3]数据返回
        $this->jsonReturn($returnArr);
    }

    /**
     *
     * 发送offer短信模板
     *
     * @Access  public
     * @Param   request
     * @Return  jsonData
     *
     */
    public function getSendOfferSmsTplAction()
    {
        $paramIn = $this->paramIn;
        $this->validateCheck($paramIn, ["resume_id" => "Required|Int"]);
        if (isset($paramIn['offer_type']) && $paramIn['offer_type'] == 'not_front_line_job') {
            $result = (new InterviewServer())->getNotFrontLineJobSendOfferEmailTpl($paramIn['resume_id']);
        } else {
            $result = (new InterviewServer())->getSendOfferSmsTpl($paramIn['resume_id'], $paramIn['offer_id'] ?? 0);
        }

        $this->jsonReturn(self::checkReturn(["data" => $result]));
    }

    /**
     * offer修改
     * @Access  public
     * @Param   request
     * @Return  jsonData
     * @Permission(action='Interview.offerEdit')
     */
    public function offerEditAction()
    {
        //[1]入参
        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        // 优化一下提示 群里bug
        if ($paramIn['money'] == '*****'){
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('no_permission')));
        }
        $validations         = [
            "id"             => "Required|Int",
            "interview_id"   => "Required|Int",
            "resume_id"      => "Required|Int",
            'hc_id'          => "Required|Int",
//            'money'          => "Required|Int",
//            'job_title_grade' => "Required|IntGe:0",
            'staff_job_type' => "Int",
            "work_time"      => "Required|DateTime|>>>:work_time" . $this->getTranslation()->_('4702'),
        ];
        if (isset($paramIn['hire_type']) && !in_array($paramIn['hire_type'], HrStaffInfoModel::$agentTypeTogether)){
            $validations['money'] = "Required|Int";
            $validations['job_title_grade'] = "Required|IntGe:0";
        }

        if (isset($paramIn['hire_type']) && $paramIn['hire_type']) {
            $hire_type                = (new SysListServer())->getHireType();
            $hire_type_arr            = array_column($hire_type, "key");
            $validations['hire_type'] = "Required|IntIn:" . implode(',', $hire_type_arr);

            //实习生
            if ($paramIn['hire_type'] == HrhcModel::HIRE_TYPE_PRACTICE) {
                $validations['internship_start']  = 'Required|Date';
                $validations['internship_end']    = 'Required|Date';
                $validations['company']           = 'Required|Str';
                $validations['internship_salary'] = "Required|Int|>>>:internship_salary ".$this->t->_('internship_salary_error');
                unset($validations['money']);
                unset($validations['train_place']);
            }
        }


        $this->validateCheck($paramIn, $validations);

        //[2]业务处理
        $returnArr = (new InterviewServer())->offerEdit($paramIn);

        //[3]数据返回
        $this->jsonReturn($returnArr);
    }

    /**
     * offer详情
     * @Access  public
     * @Param   request
     * @Return  jsonData
     * @Permission(action='Interview.infoInterviewOffer')
     */
    public function infoInterviewOfferAction()
    {
        //[1]入参
        $paramIn             = $this->paramIn;
        $this->getDI()->get('logger')->write_log("paramIn".json_encode($paramIn, JSON_UNESCAPED_UNICODE), 'info');

        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        $validations         = [
            "id" => "Required|Int",
        ];
        $this->validateCheck($paramIn, $validations);

        //[2]业务处理
        $returnArr = (new InterviewServer())->infoInterviewOffer($paramIn,$this->userinfo);
        $this->getDI()->get('logger')->write_log("infoInterviewOfferAction".json_encode($returnArr, JSON_UNESCAPED_UNICODE), 'info');

        //[3]数据返回
        $this->jsonReturn($returnArr);
    }
}