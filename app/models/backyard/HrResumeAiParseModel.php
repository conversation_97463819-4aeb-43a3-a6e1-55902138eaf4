<?php
/**
 * 简历AI解析模型
 * Author: shenxn
 * Date: 2024-04-22
 * Description: 用于管理简历AI解析的状态和类型
 */
namespace FlashExpress\bi\App\Models\backyard;

class HrResumeAiParseModel extends BackyardBaseModel
{
    protected $table_name = 'hr_resume_ai_parse';

    // 解析状态
    const STATUS_PENDING = 0;    // 待解析
    const STATUS_SUCCESS = 1;    // 解析成功
    const STATUS_FAILED = 2;     // 解析失败
    const STATUS_PARSING = 3;    // 解析中


    // 解析来源
    const SOURCE_UPLOAD = 1;     // 手动上传
    const SOURCE_EMAIL  = 2;     // 邮件导入

    // 操作来源
    const OPERATOR_SOURCE_WINHR_CREATE = 1; //简历创建
    const OPERATOR_SOURCE_WINHR_UPDATE = 2; //简历编辑
}