<?php
/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON>.
 * User: z<PERSON><PERSON>
 * Date: 2021/9/16
 * Time: 17:31
 */

namespace FlashExpress\bi\App\Modules\TH\Controllers;

use FlashExpress\bi\App\Controllers\InterviewController as GlobalInterviewController;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Excel;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\HrInterestedDepartmentModel;
use FlashExpress\bi\App\Models\backyard\HrInterviewOfferModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;

use FlashExpress\bi\App\Models\backyard\WinhrExcelTaskModel;
use FlashExpress\bi\App\Modules\TH\Server\InterviewServer;
use FlashExpress\bi\App\Modules\TH\Server\ResumeServer;
use FlashExpress\bi\App\Modules\TH\Server\StaticSysServer;
use FlashExpress\bi\App\Repository\ResumeRepository;
use FlashExpress\bi\App\Server\DownLoadTaskService;
use FlashExpress\bi\App\Server\InterestedDepartmentServer;
use FlashExpress\bi\App\Server\OfferSignServer;
use FlashExpress\bi\App\Server\SchoolServer;
use FlashExpress\bi\App\Server\SysListServer;


class InterviewController extends GlobalInterviewController
{

    /**
     * 获取发送offer页面薪资接口
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getOfferSalaryItemAction(){
        //获取国家差异化的静态资源
        $specialStaticServer = new StaticSysServer(); //差异化资源放到该server
        $static_data['wageSubsidyList'] = $specialStaticServer->getWageSubsidyList($this->paramIn);

        $returnArr['data']['dataList'] = $static_data;
        return $this->jsonReturn($this->checkReturn($returnArr));
    }

    /**
     * offer列表-撤销薪资审批
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function revokeSalaryAction()
    {
        $paramIn = $this->paramIn;
        $InterviewServer = (new InterviewServer());
        $returnArr = $InterviewServer->revokeSalary($this->userInfo, $paramIn['resume_id'], $paramIn['revoke_remark']);
        if($returnArr['code'] == 1){
            //判断是否是非一线职位
            $offer_data = $InterviewServer->getOfferInfo($paramIn['resume_id']);
            $is_front_line_job = $InterviewServer->isFrontLineJob($offer_data['position_id']);

            //非一线定义 泰国
            $resumeInfo = (new ResumeRepository())->resumeInfo($paramIn['resume_id']);
            if ((new InterviewServer())->isHeadOfferRecruit($resumeInfo)) {
                $is_front_line_job = false;
            }

            if(!$is_front_line_job){
                //如果是非一线职位，则更新offer 签字按钮状态
                (new OfferSignServer())->revokeOfferSignBySalaryRevoke($paramIn['resume_id']);
            }
        }
        $this->jsonReturn($returnArr);
    }

    /**
     * 取消offer
     * @Access  public
     * @Param   request
     * @Return  jsonData
     * @Permission(action='Interview.cancelSendOffer')
     */
    public function cancelSendOfferAction()
    {
        //[1]入参
        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $this->userInfo['staff_id'];
        $validations         = [
            "id" => "Required|Int",
        ];
        $this->validateCheck($paramIn, $validations);

        $redisKey            = 'lock_cancel_offer_' . $paramIn['id'];
        $cache      = $this->getDI()->get('redis');
        if ($cache->get($redisKey)) {
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('please try again')));
        }
        $cache->save($redisKey, 1, 60); //加锁

        //[2]业务处理
        $returnArr = (new InterviewServer())->cancelSendOffer($paramIn);

        //释放锁
        $cache->delete($redisKey);

        //[3]数据返回
        $this->jsonReturn($returnArr);
    }


    /**
     * offer签字申请-非一线员工职位
     * @Permission(action='Interview.sendOffer')
     */
    public function sendOfferSignAction()
    {
        //[1]入参
        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $this->userInfo['id'];
        $redisKey            = 'lock_send_offer_' . $paramIn['resume_id'];

        $validations = [
            "interview_id"    => "Required|Int",
            "resume_id"       => "Required|Int",
            'hc_id'           => "Required|Int",
            'money'           => "Required|Int",
            'job_title_grade' => "Required|IntGe:0",//职级
            'train_place'     => "Required",
            'in_salary_range' => "Required|Int",
            "work_time"       => "Required|DateTime|>>>:work_time".$this->getTranslation()->_('4702'),
            'currency'        => 'Required|IntIn:'.implode_array_keys(HrInterviewOfferModel::$currency_list),
        ];

        if (isset($paramIn['currency']) && $paramIn['currency'] == HrInterviewOfferModel::CURRENCY_SGD) {
            $validations['subsidy_type'] = 'Required|IntIn:'.implode_array_keys(HrInterviewOfferModel::$subsidy_List);
        }

        if (isset($paramIn['hire_type']) && $paramIn['hire_type']) {
            $validations['hire_type'] = 'Required|IntIn:'.implode_array_keys(HrStaffInfoModel::$hire_type_list);
        }

        $this->validateCheck($paramIn, $validations);

        $offerSignServer = new OfferSignServer();
        $hcInfo          = $offerSignServer->getHcInfo($paramIn['hc_id']);
        if ($hcInfo['hire_type'] == HrStaffInfoModel::HIRE_TYPE_AGENT) {
            return $this->jsonReturn(self::checkReturn(-3, 'not allow access'));
        }

        //判断是否为为打标签的简历
        $resume                           = (new  ResumeServer())->getResumeBaseInfo($paramIn['resume_id']);
        $is_front_line_job                = (new InterviewServer())->isFrontLineJob($paramIn['position_id']);
        $hcInfo['is_head_office_recruit'] = $resume['is_head_office_recruit'] ?? 0;
        $is_head_office_recruit           = (new InterviewServer())->isHeadOfferRecruit($hcInfo);

        if (!$is_head_office_recruit && ($is_front_line_job === 1)) {
            //一线职位不允许访问
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('front_line_job_not_offer_sign')));
        }

        $cache      = $this->getDI()->get('redis');
        if ($cache->get($redisKey)) {
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('please try again')));
        }
        $cache->save($redisKey, 1, 60); //加锁

        //[2]业务处理
        $returnArr = (new OfferSignServer())->sendOfferSign($paramIn);

        //释放锁
        $cache->delete($redisKey);
        //[3]数据返回
        $this->jsonReturn($returnArr);
    }


    /**
     * 撤销offer签字
     * @Access  public
     * @Param   request
     * @Return  jsonData
     * @Permission(action='Interview.interviewResumeList')
     */
    public function revokeOfferSignAction()
    {
        $paramIn = $this->paramIn;
        $validations         = [
            "revoke_remark"  => "Required|StrLenGeLe:1,200",
            "resume_id"      => "Required|Int",
        ];

        $this->validateCheck($paramIn, $validations);

        $returnArr = (new OfferSignServer())->revokeOfferSignMain( $paramIn['resume_id'], $paramIn['revoke_remark'],4);

        $this->jsonReturn($returnArr);
    }


    /**
     * offer签字详情
     * @Access  public
     * @Param   request
     * @Return  jsonData
     * @Permission(action='Interview.interviewResumeList')
     */
    public function offerSignDetailAction()
    {
        $paramIn = $this->paramIn;
        $validations         = [
            "resume_id"     => "Required|Int",
        ];

        $this->validateCheck($paramIn, $validations);
        $returnArr = (new OfferSignServer())->offerSignDetail( $paramIn['resume_id'],$this->userInfo);

        $this->jsonReturn($returnArr);
    }


    /**
     * 发送非一线offer
     * @Access  public
     * @Param   request
     * @Return  jsonData
     * @Permission(action='Interview.sendOffer')
     */
    public function sendOfferSignOfferAction(){
        $paramIn = $this->paramIn;
        $validations         = [
            "resume_id"     => "Required|Int",
            'phone'         => "Required|Int",
            "send_email"    => "Required|Email|>>>:" . $this->getTranslation()->_('4109'),
            'offer_pdf_url' => "Required|Url",
            'send_content'       => "Required|Str",//邮件内容
        ];

        $this->validateCheck($paramIn, $validations);
        $redisKey            = 'lock_send_offer_' . $paramIn['resume_id'];
        $cache      = $this->getDI()->get('redis');
        if ($cache->get($redisKey)) {
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('please try again')));
        }
        $cache->save($redisKey, 1, 10); //加锁

        $returnArr = (new OfferSignServer())->sendOfferSignOffer($this->userInfo, $paramIn);

        //释放锁
        $cache->delete($redisKey);

        $this->getDI()->get('logger')->write_log("sendOfferSignOffer 参数信息:" . json_encode($paramIn), 'info');
        $this->jsonReturn($returnArr);
    }


    /**
     * 预约面试添加
     * @Access  public
     * @Param   request
     * @Return  jsonData
     * @Permission(action='Interview.addInterviewAppointment')
     */
    public function addInterviewAppointmentAction()
    {
            //[1]入参
            $paramIn             = $this->paramIn;
            $paramIn['staff_id'] = $this->userinfo['staff_id'];
            $validations         = [
                "staff_id"       => "Required|Int",
                "hc_id"          => "Required|Int|>>>:" . $this->getTranslation()->_('4412'),
                "resume_id"      => "Required|Int|>>>:" . $this->getTranslation()->_('4413'),
                "interview_time" => "Required|DateTime|>>>:" . $this->getTranslation()->_('4402'),
                "detail_address" => "Required|StrLenGeLe:1,500|>>>:" . $this->getTranslation()->_('4411'),
            ];
            $this->validateCheck($paramIn, $validations);
            $lock_key = $paramIn['hc_id'].'-'.$paramIn['resume_id'];

            //[2]业务处理
            $returnArr = $this->atomicLock(function () use ($paramIn) {
                return (new InterviewServer())->addOrUpdateInterviewAppointment($paramIn);
            }, 'addInterviewAppointment:' . $lock_key, 300);

            if ($returnArr === false) { //没有获取到锁
                $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('5202')));
            }

            //[3]数据返回
            $this->jsonReturn($returnArr);
    }


    /**
     * 面试管理 简历列表
     * @Access  public
     * @Param   request
     * @Return  jsonData
     * @Permission(action='Interview.interviewResumeList')
     */
    public function interviewResumeListAction()
    {
        //[1]入参
        $paramIn = $this->paramIn;
        $paramIn['staff_id'] = $this->userInfo['id'];
        unset($paramIn['no_page']);
        // 入参校验
        $validations = [];
        if (!empty($paramIn['resume_last_operator'])) {
            $validations['resume_last_operator'] = 'IntLe:99999999|>>>:'.$this->getTranslation()->_('input_staff_id_error');
        }
        $this->validateCheck($paramIn, $validations);
        $returnArr = (new InterviewServer())->interviewList($paramIn, $this->userInfo);

        //[3]数据返回
        $this->jsonReturn($returnArr);
    }

    /**
     * 面试管理 简历列表 导出
     * @Access  public
     * @Param   request
     * @Return  jsonData
     * @Permission(action='Interview.interviewResumeListExplot')
     */
    public function interviewResumeFilterListExplotAction()
    {
        //[1]入参
        $paramIn            = $this->paramIn;
        $paramIn["no_page"] = 1;

        //[2]业务处理
        $returnArr = (new InterviewServer())->interviewList($paramIn, $this->userInfo);

        $data = $returnArr['data']['dataList'];
        if (empty($data)) {
            $this->jsonReturn($this->checkReturn(-3, 'empty data'));
        }
        $currentSchoolids   = array_unique(array_column($data, 'current_school_id'));
        $currentSchoolNames = (new SchoolServer())->getSchoolNamesbyIds($currentSchoolids);

        $interestedDepartmentIds   = array_unique(array_column($data, 'interested_department_id'));
        $interestedDepartmentNames = (new InterestedDepartmentServer())->getInterestedDepartmentsbyIds($interestedDepartmentIds);


        foreach ($data as $k => $v) {

            $interestedDepartmentName = '';

            if (!empty($v['interested_department_id'])) {
                if ($v['interested_department_id'] == HrInterestedDepartmentModel::DEPARTMENT_OTHER) {
                    $interestedDepartmentName = $v['interested_department_other'];
                } else {
                    $interestedDepartmentName = $interestedDepartmentNames[$v['interested_department_id']] ?? '';
                }
            }

            $HcId                  = $v['hc_id'] ?? '';
            $cvId                  = $v['resume_id'] ?? '';
            $historyCvId           = $v['history_hcs'] ?? '';
            $name                  = $v['name'] ?? '';
            $name_en               = $v['name_en'] ?? '';
            $sex                   = $v['sex_title'] ?? '';
            $age                   = $v['age'] ?? '';
            $credentialsNum        = $v['credentials_num'] ?? '';
            $phone                 = $v['phone'] ?? '';
            $job_name              = $v['hc_job_name'] ?? '';
            $alternative_job_names = $v['alternative_job_names'] ?? '';
            $department_name       = $v['hc_department_name'] ?? '';
            $area_name             = $v['sorting_no'] ?? '';
            $work_name             = $v['work_name'] ?? '';
            $worknode_name         = $v['worknode_name'] ?? '';
            $email                 = $v['email'] ?? '';
            $stateName             = $v["state_name"] ?? '';
            $expectJobName         = $v['expect_job_name'] ?? '';
            $resumeCreatedAt       = $v['resume_created_at'] ?? '';
            $submitterId           = $v['submitter_id'] && in_array($v['source'], [2, 3]) ? $v['submitter_id'] : '';
            $recruit_channel_name  = $v['recruit_channel_name'] ? $v['recruit_channel_name'] : '';

            $filter_staff_id   = $v['filter_staff_id'] ?? '';
            $filter_staff_name = $v['filter_staff_name'] ?? '';

            $tutor_phone_area_code = !empty($v['tutor_phone_area_code']) ? '+' . $v['tutor_phone_area_code'] : '';
            $tutor_info =  !empty($v['tutor_phone']) ? $tutor_phone_area_code.' '.$v['tutor_phone'] : '';
            $resume_last_operator_name = $v['resume_last_operator_name'] ?? '';
            $priority_text = $v['priority_text'] ?? '';                                // hc 优先级


            $_data[$k] = [
                $cvId,                                                            //CVID
                $HcId,                                                            //HCID
                $historyCvId,                                                     //历史HCID
                $name,                                                            //姓名
                $name_en,                                                         //英文名
                $sex,                                                             //性别
                $age,                                                             //年龄
                $currentSchoolNames[$v['current_school_id']] ?? '',               //学校名称
                $v['current_college'] ?? '',                                      //学院
                $v['current_major'] ?? '',                                        //专业
                $this->t->_(enums::$gradeList[$v['current_grade_id']] ?? ''),     //年级
                $v['work_start_date'] ?? '',                                      //实习期间
                $v['work_end_date'] ?? '',                                        //实习期间
                $interestedDepartmentName,                                        //感兴趣的部门
                $v['recruit_type_name'] ?? '',                                    //招聘类型
                $tutor_info,                                                      //导师电话
                $v['tutor_email'] ?? '',                                          //导师邮箱
                $v['line_id'] ?? '',                                              //Line ID
                $credentialsNum,                                                  //证件号
                $phone,                                                           //手机号
                $email,                                                           //电子邮箱
                $expectJobName,                                                   //期望岗位
                $alternative_job_names,                                           //备选 岗位
                $stateName,                                                       //状态
                $job_name,                                                        //JD名称
                $department_name,                                                 //所属部门
                $area_name,                                                       //所属区域
                $work_name,                                                       //工作城市
                $worknode_name,                                                   //所属网点
                $resumeCreatedAt,                                                 //入库时间
                $recruit_channel_name,                                            //招聘渠道
                $filter_staff_name,                                               //简历筛选面试官姓名
                $filter_staff_id,                                                 //简历筛选面试官工号
                $submitterId,                                                     //简历创建人
                $resume_last_operator_name,                                       // 最新操作人
            ];
        }
        $field      = [
            'CVID',
            'HCID',
            $this->t->_('8048'),//历史hc
            $this->t->_('7022'),//姓名
            $this->t->_('8059'),//英文名
            $this->t->_('7023'),//性别
            $this->t->_('7045'),//年龄
            $this->t->_('current_school_name'),       //学校名称
            $this->t->_('current_college'),           //学院
            $this->t->_('current_major'),             //专业
            $this->t->_('current_grade_name'),        //年级
            $this->t->_('work_start_date'),           //实习期间
            $this->t->_('work_end_date'),             //实习期间
            $this->t->_('interested_department_name'),//感兴趣的部门
            $this->t->_('recruit_type'),              //招聘类型
            $this->t->_('tutor_phone'),               //导师电话
            $this->t->_('tutor_email'),               //导师邮箱
            $this->t->_('line_id'),                   //Line ID
            $this->t->_('7503'),//证件号
            $this->t->_('7505'),//手机号
            $this->t->_('8050'),//电子邮箱
            $this->t->_('8051'),//期望岗位
            $this->t->_('8049'),//备选岗位

            $this->t->_('resume_status'),//状态
            $this->t->_('7001'),//JD名称
            $this->t->_('7002'),//所属部门
            $this->t->_('7031'),//所属区域
            $this->t->_('7010'),//工作城市
            $this->t->_('7043'),//所属网点

            $this->t->_('7047'),//入库时间

            $this->t->_('42003'),//招聘渠道
            $this->t->_('filter_staff_name'),//简历筛选面试官姓名
            $this->t->_('filter_staff_id'),//简历筛选面试官工号
            $this->t->_('8116'),//简历创建人,
            $this->t->_('principal_staff_name'),
        ];
        $file_path  = Excel::exportExcel($field, $_data, 'resumeFilterRecommendlist'.time());
        $file_path  = $file_path ? $file_path['object_url'] : '';
        $returnData = [
            'code' => 1,
            'msg'  => '',
            'data' => [
                'file_url' => $file_path,
            ],
        ];

        //[3]数据返回
        $this->jsonReturn($returnData);
    }

    /**
     * 重新发送非一线offer
     * 与首次发送offer区别是：只发送邮件内容
     * @Access  public
     * @Param   request
     * @Return  jsonData
     * @Permission(action='Interview.sendOffer')
     */
    public function sendOfferSignOfferEmailAction(){
        $paramIn = $this->paramIn;
        $validations         = [
            "resume_id"     => "Required|Int",
            "send_email"    => "Required|Email|>>>:" . $this->getTranslation()->_('4109'),
            'offer_pdf_url' => "Required|Url",
            'send_content'       => "Required|Str",//邮件内容
        ];

        $this->validateCheck($paramIn, $validations);
        $redisKey            = 'lock_send_offer_again_' . $paramIn['resume_id'];
        $cache      = $this->getDI()->get('redis');
        if ($cache->get($redisKey)) {
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('please try again')));
        }
        $cache->save($redisKey, 1, 10); //加锁
        //重新发送offer邮件内容,不做数据变更
        $returnArr = (new OfferSignServer())->sendOfferSignOfferEmail($this->userInfo, $paramIn);

        //释放锁
        $cache->delete($redisKey);

        $this->getDI()->get('logger')->write_log("sendOfferSignOfferEmailAction 参数信息:" . json_encode($paramIn), 'info');
        $this->jsonReturn($returnArr);
    }

    /**
     * offer修改到岗日期-重新生成offer pdf
     * @Access  public
     * @Param   request
     * @Return  jsonData
     * @Permission(action='Interview.offerEdit')
     */
    public function getOfferPdfByWorkTimeAction()
    {
        //[1]入参
        $paramIn             = $this->paramIn;

        $validations         = [
            "offer_id"  => "Required|Int",
            "work_time" => "Required|DateTime|>>>:work_time" . $this->getTranslation()->_('4702'),
        ];

        $this->validateCheck($paramIn, $validations);
        $return = (new OfferSignServer())->getOfferPdfByWorkTime($paramIn['offer_id'],$paramIn['work_time']);

        //[3]数据返回
        $this->jsonReturn($return);
    }

    /**
     * offer详情
     * @Access  public
     * @Param   request
     * @Return  jsonData
     * @Permission(action='Interview.infoInterviewOffer')
     */
    public function infoInterviewOfferAction()
    {
        //[1]入参
        $paramIn             = $this->paramIn;
        $this->getDI()->get('logger')->write_log("paramIn".json_encode($paramIn, JSON_UNESCAPED_UNICODE), 'info');

        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        $validations         = [
            "id" => "Required|Int",
        ];
        $this->validateCheck($paramIn, $validations);

        //[2]业务处理
        $returnArr = (new InterviewServer())->infoInterviewOffer($paramIn,$this->userinfo);
        $this->getDI()->get('logger')->write_log("infoInterviewOfferAction".json_encode($returnArr, JSON_UNESCAPED_UNICODE), 'info');

        //[3]数据返回
        $this->jsonReturn($returnArr);
    }

    /**
     * 发送offer
     * @return mixed
     * @Permission(action='Interview.sendOffer')
     */
    public function sendOfferAction()
    {
        //[1]入参
        $paramIn = $this->paramIn;
        $this->logger->write_log("paramIn".json_encode($paramIn, JSON_UNESCAPED_UNICODE), 'info');

        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        $redisKey            = 'lock_send_offer_'.$paramIn['resume_id'];

        $validations = [
            "interview_id"    => "Required|Int",
            "resume_id"       => "Required|Int",
            'hc_id'           => "Required|Int",
//            'money'           => "Required|Int",
//            'staff_job_type' => "Required|Int",
//            'job_title_grade' => "Required|IntGe:0",//职级
//            'train_place'     => "Required",
            "work_time"       => "Required|DateTime|>>>:work_time".$this->getTranslation()->_('4702'),
//            'in_salary_range' => 'Required|IntIn:1,2',
            'currency'        => 'Required|IntIn:'.implode_array_keys(HrInterviewOfferModel::$currency_list),
        ];

        if (isset($paramIn['currency']) && $paramIn['currency'] == HrInterviewOfferModel::CURRENCY_SGD) {
            $validations['subsidy_type'] = 'Required|IntIn:'.implode_array_keys(HrInterviewOfferModel::$subsidy_List);
        }

        if (isset($paramIn['hire_type']) && $paramIn['hire_type']) {
            // 雇佣类型
            $hire_type = (new SysListServer())->getHireType();
            $hire_type_arr = array_column($hire_type,"key");
            $validations['hire_type'] = "Required|IntIn:".implode(',',$hire_type_arr);
        }

        if (isset($paramIn['hire_type']) && $paramIn['hire_type'] != HrStaffInfoModel::HIRE_TYPE_AGENT) {
            $validations['job_title_grade'] = "Required|IntGe:0";
        }

        //项目期数
        if (!empty($paramIn['project_num'])) {
            $validations['project_num'] = 'Required|IntIn:'.implode_array_keys(HrInterviewOfferModel::$van_courier_project_num_list);
        }

        $this->validateCheck($paramIn, $validations);

        $cache = $this->getDI()->get('redis');
        if ($cache->get($redisKey)) {
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('please try again')));
        }
        $cache->save($redisKey, 1, 60); //加锁

        //[2]业务处理
        $returnArr = (new InterviewServer())->sendOffer($paramIn);

        //释放锁
        $cache->delete($redisKey);
        //[3]数据返回
        $this->jsonReturn($returnArr);
    }

    /**
     * offer修改
     * @Access  public
     * @Param   request
     * @Return  jsonData
     * @Permission(action='Interview.offerEdit')
     */
    public function offerEditAction()
    {
        //[1]入参
        $paramIn             = $this->paramIn;
        $this->getDI()->get('logger')->write_log("paramIn".json_encode($paramIn, JSON_UNESCAPED_UNICODE), 'info');

        $paramIn['staff_id'] = $this->userinfo['staff_id'];
        // 优化一下提示 群里bug
        if ($paramIn['money'] == '*****'){
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('no_permission')));
        }
        $validations         = [
            "id"             => "Required|Int",
            "interview_id"   => "Required|Int",
            "resume_id"      => "Required|Int",
            'hc_id'          => "Required|Int",
//            'money'          => "Required|Int",
//            'job_title_grade' => "Required|IntGe:0",
            'staff_job_type' => "Int",
            "work_time"      => "Required|DateTime|>>>:work_time" . $this->getTranslation()->_('4702'),
            'currency'        => 'Required|IntIn:'.implode_array_keys(HrInterviewOfferModel::$currency_list),
        ];

        if (isset($paramIn['currency']) && $paramIn['currency'] == HrInterviewOfferModel::CURRENCY_SGD) {
            $validations['subsidy_type'] = 'Required|IntIn:'.implode_array_keys(HrInterviewOfferModel::$subsidy_List);
        }

        if (isset($paramIn['hire_type']) && $paramIn['hire_type'] != HrStaffInfoModel::HIRE_TYPE_AGENT){
            $validations['job_title_grade'] = "Required|IntGe:0";
        }

        if (isset($paramIn['hire_type']) && $paramIn['hire_type']) {
            // 雇佣类型
            $hire_type = (new SysListServer())->getHireType();
            $hire_type_arr = array_column($hire_type,"key");
            $validations['hire_type'] = "Required|IntIn:".implode(',',$hire_type_arr);
        }

        //项目期数
        if (!empty($paramIn['project_num'])) {
            $validations['project_num'] = 'Required|IntIn:'.implode_array_keys(HrInterviewOfferModel::$van_courier_project_num_list);
        }

        $this->validateCheck($paramIn, $validations);

        //[2]业务处理
        $returnArr = (new InterviewServer())->offerEdit($paramIn);

        //[3]数据返回
        $this->jsonReturn($returnArr);
    }
}