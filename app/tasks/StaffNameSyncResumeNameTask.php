<?php
/**
 *AddEntryToBiTask.php
 * Created by: Lqz.
 * Description:
 * User: Administrator
 * CreateTime: 2020/11/6 0006 13:55
 */

use FlashExpress\bi\App\Models\backyard\HrResumeModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\HrEntryModel;


class StaffNameSyncResumeNameTask extends BaseTask
{

    /**
     * @param $argv
     * 将hr_staff_info表中name同步到hr_resume的first_name，并将last_name置为空
     * 注意：只执行一次
     * 注意：只执行一次
     */
    public function syncAction($argv){
        if(env('country_code') != 'MY'){
            exit('not allow ') ;
        }

        $builder = $this->modelsManager->createBuilder();
        $builder->columns('hsi.staff_info_id, hsi.name, he.resume_id');
        $builder->from(['hsi' => HrStaffInfoModel::class]);
        $builder->leftJoin(HrEntryModel::class, 'hsi.staff_info_id=he.staff_id', 'he');
        $staff_list = $builder->getQuery()->execute()->toArray();

        $db = $this->getDI()->get('db');

        foreach($staff_list as $key=>$staff){
            if( !empty($staff['name']) && !empty($staff['resume_id']) ){
                $result = $db->updateAsDict('hr_resume', ['first_name'=>$staff['name'],'last_name'=>''], 'id = '.$staff['resume_id']);
                if($result){
                    echo 'staff_info_id='.$staff['staff_info_id'] . ',resume_id='.$staff['resume_id'] . ' success'.PHP_EOL;
                }else{
                    echo 'staff_info_id='.$staff['staff_info_id'] . ',resume_id='.$staff['resume_id'] . 'fail'.PHP_EOL;
                }
            }else{
                echo 'staff_info_id='.$staff['staff_info_id']. ' name or resume_id is null'.PHP_EOL;
            }
        }
    }

    /**
     * 将马来hr_resume表中 last_name合并到first_name中，并清空last_name
     */
    function createNameAction(){
        if(env('country_code') != 'MY'){
            exit('not allow ') ;
        }
        $list = HrResumeModel::find([
            'conditions' => "last_name !=''",
            'columns' => "id, name, first_name, last_name"
        ])->toArray();
        //print_r($list);die;
        $db = $this->getDI()->get('db');

        foreach ($list as $key=>$val){
            if( !empty($val['first_name']) && !empty($val['last_name']) ){
                $result = $db->updateAsDict('hr_resume', ['first_name'=>$val['first_name'].' '.$val['last_name'],'last_name'=>''], 'id = '.$val['id']);
                if($result){
                    echo 'id='.$val['id'] . ' success'.PHP_EOL;
                }else{
                    echo 'id='.$val['id'] . 'fail'.PHP_EOL;
                }
            }else{
                echo 'null' . PHP_EOL;
            }
        }
    }
}