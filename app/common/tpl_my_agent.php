<?php

return array(
    //求职者
    'th_applicant_email' => "แจ้งนัดสัมภาษณ์งาน \r\n บริษัท แฟลช เอ็กเพรส \r\n คุณ :  %s %s \r\n โทร : %s\r\n ตำแหน่ง : %s \r\n แผนก :  %s \r\n"
    . "ประจำสาขา : %s \r\n วันที่นัดสัมภาษณ์ : %s \r\n สถานที่นัดสัมภาษณ์ : %s \r\n  ติดต่อผู้รับผิดชอบสาขา : %s \r\n",
    
    'en_applicant_online_email' => "Dear %s [%s %s] \r\n \r\nGood day. We are pleased to inform you that you are shortlisted for an interview for the position of [%s] with [%s]\r\n \r\n"
    . "Please find the appended information below for your perusal:- \r\n \r\nFlash Express Co., Ltd：%s \r\n【Date / Time】%s \r\n"
    . "【Interview Platform】 \r\n【Interview Link】 \r\n【Contact Person】%s：%s \r\n【Interviewer】%s\r\n"
    . "%s\r\n"
    . "Kindly download VOOV app in advance. \r\n \r\n"
    . "Should you require further clarification, please feel free to contact us.\r\n \r\n"
    . "Thank you and we are looking forward to meeting you soon.",
    
    'en_applicant_email' => "Dear %s  [%s %s] \r\n \r\nGood day. We are pleased to inform you that you are shortlisted for an interview for the position of [%s] with [%s] \r\n \r\n"
    . "Please find the appended information below for your perusal:- \r\n \r\n【Flash Express Co., Ltd】%s \r\n【Location】%s \r\n"
    . "【Date / Time】%s \r\n【Interview Venue】\r\n【Contact Person】%s %s \r\n【Interviewer】%s \r\n【Documents to bring along】\r\n 1. Identification Card / Passport \r\n 2. Testimonials (if any) \r\n"
    . "%s\r\n \r\n".
        "Kindly contact the contact person once you have reached our office; do note that you will be required to register yourself prior to the interview session. \r\n \r\n"
    . "Should you require further clarification, please feel free to contact us.\r\n \r\nThank you and we are looking forward to meeting you soon.",

    'send_interview_th_msg_content' => "แจ้งนัดสัมภาษณ์งาน \r\n บริษัท แฟลช เอ็กเพรส \r\n คุณ : %s \r\n กรุณาตรวจสอบรายละเอียดตามลิงก์  %s",
    'send_interview_en_online_msg_content' => "Dear %s, Pleased to inform that you have been shortlisted for an interview with Flash. Please find the interview details from %s . Good luck!",
    'send_interview_en_msg_content' => "Dear %s, Pleased to inform that you have been shortlisted for an interview with Flash. Please find the interview details from %s . Good luck!",
    //面试官
    'th_interview_email' => "แจ้งนัดสัมภาษณ์งาน \r\n บริษัท แฟลช เอ็กเพรส \r\n คุณ : %s \r\n  โทร : %s \r\n HCID: %s \r\n ตำแหน่ง : %s \r\n แผนก : %s \r\n ประจำสาขา : %s \r\n วันที่นัดสัมภาษณ์ : %s \r\n"
    . "สถานที่นัดสัมภาษณ์ : %s \r\n ติดต่อผู้รับผิดชอบสาขา : %s \r\n",
    
    'en_interview_online_email' => "Dear [%s] \r\n\r\nGood day. Please be reminded that you have an upcoming interview session. \r\nPlease find the appended information below for your perusal: - \r\n\r\n"
    . "Candidate：%s  \r\n"
    . "HCID：%s \r\n"
    . "Position：%s \r\n"
    . "Date / Time：%s \r\n"
    . "Interview Platform： \r\n"
    . "Interview Link： \r\n"
    . "Contact Person：%s \r\n"
    . "\r\nAfter the interview, kindly record the interview feedback accordingly in Backyard."
    . "\r\n\r\nThank you.",
    
    'en_interview_email' => "Dear [%s] \r\n \r\nGood day. Please be reminded that you have an upcoming interview session. \r\nPlease find the appended information below for your perusal: - \r\n\r\n"
    . "Candidate: %s \r\n"
    . "HCID: %s \r\n"
    . "Position: %s \r\n"
    . "Date / Time: %s \r\n"
    . "Interview Room: \r\n"
    . "Contact Person: %s \r\n\r\n"
    . "After the interview, kindly record the interview feedback accordingly in Backyard."
    . "\r\n\r\nThank you.",
    
);