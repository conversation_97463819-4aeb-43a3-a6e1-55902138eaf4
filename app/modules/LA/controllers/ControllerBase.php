<?php

namespace FlashExpress\bi\App\Modules\LA\Controllers;

use FlashExpress\bi\App\Controllers\ControllerBase AS GlobalControllerBase;

class ControllerBase extends GlobalControllerBase {

    /**
     * 初始化
     */
    public function initialize() {
        parent::initialize();
    }
    /**
     * 验证省市乡地区是否匹配正则格式
     * @param $paramIn
     * @param $address_format
     */
    public function preg_match_city_content($paramIn){

//        if (empty($paramIn["residence_house_num"]) && empty($paramIn["residence_village_num"]) && empty($paramIn["residence_village"]) && empty($paramIn["residence_alley"]) && empty($paramIn["residence_street"])) {
//            $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('4175')));
//        }

        $address_format = "/^[A-Za-z0-9 \/\ \-()*~.,]*/";
        $country_code = getCountryValue();//调用此函数前确认该函数已添加过对应国家code代码逻辑
        $country_name = $this->getTranslation()->_('nationality_'.$country_code);//菲律宾
        $resume_address_error = str_replace('|s|',$country_name,$this->getTranslation()->_('resume_address_error'));
        //户口所在地验证
        if($country_code !=$paramIn['register_country']){

            if($paramIn['register_government'] && !preg_match($address_format,$paramIn['register_government'])){
                $this->jsonReturn($this->checkReturn(-3, $resume_address_error));
            }
            if($paramIn['register_city'] && !preg_match($address_format,$paramIn['register_city'])){
                $this->jsonReturn($this->checkReturn(-3, $resume_address_error));
            }

            if($paramIn['register_town'] && !preg_match($address_format,$paramIn['register_town'])){
                $this->jsonReturn($this->checkReturn(-3, $resume_address_error));
            }
            if($paramIn['register_postcodes'] && !preg_match($address_format,$paramIn['register_postcodes'])){
                $this->jsonReturn($this->checkReturn(-3, $resume_address_error));
            }

            if($paramIn['register_house_num'] && !preg_match($address_format,$paramIn['register_house_num'])){
                $this->jsonReturn($this->checkReturn(-3, $resume_address_error));
            }

            if($paramIn['register_village_num'] && !preg_match($address_format,$paramIn['register_village_num'])){
                $this->jsonReturn($this->checkReturn(-3, $resume_address_error));
            }

            if($paramIn['register_village'] && !preg_match($address_format,$paramIn['register_village'])){
                $this->jsonReturn($this->checkReturn(-3, $resume_address_error));
            }

            if($paramIn['register_alley'] && !preg_match($address_format,$paramIn['register_alley'])){
                $this->jsonReturn($this->checkReturn(-3, $resume_address_error));
            }

            if($paramIn['register_street'] && !preg_match($address_format,$paramIn['register_street'])){
                $this->jsonReturn($this->checkReturn(-3, $resume_address_error));
            }
        }

        //居住所在地信息验证
        if($country_code !=$paramIn['residence_country']){
            if($paramIn['residence_government'] && !preg_match($address_format,$paramIn['residence_government'])){
                $this->jsonReturn($this->checkReturn(-3, $resume_address_error));
            }
            if($paramIn['residence_city'] && !preg_match($address_format,$paramIn['residence_city'])){
                $this->jsonReturn($this->checkReturn(-3, $resume_address_error));
            }
            if($paramIn['residence_town'] && !preg_match($address_format,$paramIn['residence_town'])){
                $this->jsonReturn($this->checkReturn(-3, $resume_address_error));
            }
            if($paramIn['residence_postcodes'] && !preg_match($address_format,$paramIn['residence_postcodes'])){
                $this->jsonReturn($this->checkReturn(-3, $resume_address_error));
            }
            if($paramIn['residence_house_num'] && !preg_match($address_format,$paramIn['residence_house_num'])){
                $this->jsonReturn($this->checkReturn(-3, $resume_address_error));
            }
            if($paramIn['residence_village_num'] && !preg_match($address_format,$paramIn['residence_village_num'])){
                $this->jsonReturn($this->checkReturn(-3, $resume_address_error));
            }
            if($paramIn['residence_village'] && !preg_match($address_format,$paramIn['residence_village'])){
                $this->jsonReturn($this->checkReturn(-3, $resume_address_error));
            }
            if($paramIn['residence_alley'] && !preg_match($address_format,$paramIn['residence_alley'])){
                $this->jsonReturn($this->checkReturn(-3, $resume_address_error));
            }
            if($paramIn['residence_street'] && !preg_match($address_format,$paramIn['residence_street'])){
                $this->jsonReturn($this->checkReturn(-3, $resume_address_error));
            }
        }



    }

}
