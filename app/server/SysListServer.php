<?php

namespace FlashExpress\bi\App\Server;

use Exception;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\enumsTh;
use FlashExpress\bi\App\Models\backyard\BankListModel;
use FlashExpress\bi\App\Models\backyard\HireTypeImportListModel;
use FlashExpress\bi\App\Models\backyard\HrCustomerResumeReasonModel;
use FlashExpress\bi\App\Models\backyard\HrDeliveryModel;
use FlashExpress\bi\App\Models\backyard\HrEntryModel;
use FlashExpress\bi\App\Models\backyard\HrhcModel;
use FlashExpress\bi\App\Models\backyard\HrInterestedDepartmentModel;
use FlashExpress\bi\App\Models\backyard\HrInterviewOfferModel;
use FlashExpress\bi\App\Models\backyard\HrInterviewPassTypeModel;
use FlashExpress\bi\App\Models\backyard\HrJobDepartmentRelationModel;
use FlashExpress\bi\App\Models\backyard\HrJobGroupModel;
use FlashExpress\bi\App\Models\backyard\HrJobTitleByModel;
use FlashExpress\bi\App\Models\backyard\HrRecruitChannelsModel;
use FlashExpress\bi\App\Models\backyard\HrResumeAiScoreModel;
use FlashExpress\bi\App\Models\backyard\HrResumeBackupModel;
use FlashExpress\bi\App\Models\backyard\HrResumeFilterModel;
use FlashExpress\bi\App\Models\backyard\HrResumeModel;
use FlashExpress\bi\App\Models\backyard\HrSchoolModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoPositionModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffRenewContractApplyModel;
use FlashExpress\bi\App\Models\backyard\SysCityModel;
use FlashExpress\bi\App\Models\backyard\SysDepartmentModel;
use FlashExpress\bi\App\Models\backyard\SysDistrictModel;
use FlashExpress\bi\App\Models\backyard\SysProvinceModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Modules\MY\library\enumsContract;
use FlashExpress\bi\App\Repository\DepartmentRepository;
use FlashExpress\bi\App\Repository\HrJobGroupRepository;
use FlashExpress\bi\App\Repository\HrJobTitleRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Repository\SysDepartmentRepository;
use  FlashExpress\bi\App\Repository\SysListRepository;
use FlashExpress\bi\App\Server\InterviewServer;

class SysListServer extends BaseServer
{
    protected $sysList;

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 获取动态资源列表
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function sysList($paramIn = [])
    {
        $typeCode    = $paramIn['type_code'];
        $addressType = isset($paramIn['address_type']) ? $paramIn['address_type'] : 0;
        if (!$typeCode) {

            $data              = [
                'getStoreList',
                'getDepartmentList',
                'getPositionList',
                'getCityList',
                'getJobList',
                'getManagePieceList',
                'getManageRegionList',
                'getRegionList',
                'getNwStoreList',
                'getHcList',
                'getStoreJobList',
                'getTrainPlaceList',
                'getMyStoreList',
                'getRepeatHcList',
                'getHcReferences',
                'getDepartmentJobTitleList',
                'getDepartmentListV2',
                'getJobGroup',
                'getHcManager',
                'getCustomerResumeReason',
                'getStaffListForTalentDepart',
                'getWorkingDayRestTypeList',
                'getSchoolList',
                'getInterestedDepartmentList',
                'getNationalityRegion',
                'getAddressCountryRegion',
                'getWorkingCountry',
                'getThHeadOfficeNoLikeJobList',
                'getImportResult',
                'getEntryState',
                'getHireTypeImportSource',
                'checkPriorityUrgent',
            ];
            $returnArr['data'] = $data;
            return $this->checkReturn($returnArr);
        }

        $typeCodeArr = explode(',', $typeCode);
        foreach ($typeCodeArr as $k => $v) {
            try {
                $state = method_exists($this, $v);
                if ($state) {
                    if ($v == 'getCityList') {
                        $data = $this->$v([
                            "address_type" => $addressType,
                        ]);
                    } else {
                        if (in_array($v, [
                            'getHcList',
                            'getRepeatHcList',
                            'getHcReferences',
                            'getDepartmentJobTitleList',
                            'getDepartmentListV2',
                            'getCustomerResumeReason',
                            'getWorkingDayRestTypeList',
                        ])) {
                            $data = $this->$v($paramIn);
                        } else {
                            $data = $this->$v();
                        }
                    }
                    $returnArr['data'][$v] = $data;
                }
            } catch (\Exception $e) {
                $this->getDI()->get('logger')->write_log("sysList-方法{$v}异常信息:".$e->getMessage().$e->getTraceAsString(),
                    'error');
            }
        }
        $phone_area_code_list                      = \FlashExpress\bi\App\library\enums::$phone_area_code_enums;
        $returnArr['data']['getPhoneAreaCodeList'] = array_values($phone_area_code_list);
        return $this->checkReturn($returnArr);
    }

    //客服简历 候选人需要延期提供信息 具体信息
    public function getCustomerResumeReason($paramIn)
    {
        $tmpData = [];
        if (!empty($paramIn['name'])) {
            $where = [
                'conditions' => "option like :option: or english like :english: ",
                'bind'       => [
                    'manage_piece_ids' => ['english' => $paramIn['name'], 'option' => $paramIn['name']],
                ],
                'columns'    => 'id as code,option,english',
            ];
        } else {
            $where = [
                'columns' => 'id as code,option,english',
            ];
        }
        $rst = HrCustomerResumeReasonModel::find($where)->toArray();

        foreach ($rst as $k => $v) {
            if (in_array($this->lang, ['zh', 'zh-CN'])) {
                $tmpData[] = [
                    'code' => $v['code'],
                    'name' => $v['option'],
                ];
            } else {
                $tmpData[] = [
                    'code' => $v['code'],
                    'name' => $v['english'],
                ];
            }
        }
        return $data['dataList'] = $tmpData;
    }

    /**
     * 获取静态资源列表
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function staticSysList()
    {
        //面试管理-简历库 hc状态下拉
        $data['interviewResumeListHcStateList'] = $this->interviewResumeListHcStateList();

        //hc列表状态下拉
        $data['hcStateList'] = $this->getHcStateList();

        //hc审批状态列表下拉
        $data['hcApprovalStateList'] = $this->getHcApprovalStateList();

        //hc列表审批阶段下拉
        $data['hcApprovalStageList'] = $this->getHcApprovalStageList();

        //hc列表优先级下拉
        $data['hcPriorityList'] = $this->getHcPriorityList();

        //JD列表岗位类型下拉
        $data['jdTypeList'] = $this->getJdTypeList();

        //面试hc状态下拉
        $data['interviewHcStateList'] = $this->getInterviewHcStateList();

        //黑名单列表信息来源类型
        $data['blacklistType'] = $this->getBlacklistType();

        //简历分数枚举
        $data['resumeAiScoreTypeList'] = $this->getResumeAiScoreTypeList();

        //黑名单列表状态列表
        $data['blacklistStatus'] = $this->getBlacklistStatus();

        //面试取消选项列表
        $data['cancelInterviewList'] = $this->getCancelInterviewList();

        //面试淘汰选项列表
        $data['outInterviewList'] = $this->getOutInterviewList();

        //offer下拉员工属性
        $data['offerStaffJobType'] = $this->getOfferStaffJobType();

        //简历下拉员工属性
        $data['resumeJobType'] = $this->getResumeJobType();

        // 期望雇佣类型
        $data['hireType'] = $this->getHiretype();

        $data['projectNumList'] = $this->getProjectNumList();
        
        //offer-设备费列表
        $data['equipmentCostList'] = $this->getEquipmentCostList();

        //投递状态
        $data['deliveryStatusList'] = $this->deliveryStatusList();

        //返聘状态
        $data['resumeBackupStatusList'] = $this->resumeBackupStatusList();

        //简历来源
        $data['resumeSourceList'] = $this->resumeSourceList();

        //招聘渠道列表
        $data['recruitChannelList'] = $this->getRecruitChannelList();

        //train-培训管理状态
        $data['trainStateList'] = $this->getTrainStateList();

        // hc列表-用人原因
        $data['reasonType'] = $this->getReasonType();

        //人才库-简历来源
        $data['talentpoll_resume_source'] = $this->getTalentPollResumeSource();
        //人才库-excel导出指定选项字段
        $data['talentpoll_excel_label_list'] = $this->getTalentPollExcelLableList();

        //电子合同-劳动合同类型下拉项
        $data['labor_contract_type'] = $this->getlaborContractTypeLableList();
        //获取行政区
        $data['sys_province_list'] = $this->getSysProvinceListFromCache();

        $data['carTypeList'] = $this->getCarTypeList();

        // 面试管理-沟通状态下拉
        $data['interview_communication_status'] = $this->interviewCommunicationStatusList();

        // 面试管理-沟通失败原因下拉
        $data['interview_communication_failure_reason']= $this->interviewCommunicationFailureReasonList();

        // 面试管理-沟通成功原因下拉
        $data['interview_communication_success_reason']= $this->interviewCommunicationSuccessReasonList();

        // 币种
        $data['currency']= $this->getCurrencyList();

        // 简历类型下拉
        $data['reserveTypeList'] = $this->reserveTypeList($this->lang);

        //面试状态列表
        $data['interview_state_list'] = (new InterviewServer($this->lang))->getStateList()['data']['dataList'] ?? [];

        //员工轮休时给定的默认的休息列表
        $data['default_rest_day_list'] = $this->getDefaultRestDay();

        //获取年级
        $data['getGradeList'] = $this->getGradeList();
        $data['getRecruitTypeList'] = $this->getRecruitTypeList();
        //语言能力
        $data['language_ability_list'] = $this->language_ability_list();

        //获取员工在职状态
        $data['getStaffStateList'] = $this->getStaffStateList();

        //获取员工在职状态
        $data['getStaffContractApproveStatus'] = $this->getStaffContractApproveStatus();

        //合同状态
        $data['getStaffContractStatus'] = $this->getStaffContractStatus();
        //最新操作人筛选类型
        $data['last_operator_query_type_list'] = $this->getLastOperatorQueryType();

        //简历筛选取消原因
        $data['resumeFilterCancelTypeList'] = $this->getResumeFilterCancelTypeList();

        //获取招聘总部配置职位
        $data['getJointlyRecruitedIdList'] = $this->getJointlyRecruitedIdList();

        //获取招聘总部配置岗位
        $data['getJointlyRecruitedJdIdList'] = $this->getJointlyRecruitedJdIdList();

        //获取可更换hc的职位
        $data['getUpdateResumeHcJobList'] = $this->getUpdateResumeHcJobList();
        //身份证确认状态
        $data['identityStatusList'] = $this->identityStatusList();

        //验证是否可以修改优先级
        $data['checkPriorityUrgent'] = $this->checkPriorityUrgent();

        return $data;
    }

    /**
     * 语言能力 枚举  产品不要翻译
     * @return string[]
     */
    public function language_ability_list(): array
    {
        $result = [];
        $list   = enums::$languageAbility;
        foreach ($list as $key => $item) {
            $result[] = ['key' => strval($key), 'name' => $item];
        }
        return $result;
    }

    /**
     * 面试管理-沟通失败原因下拉
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function interviewCommunicationFailureReasonList($paramIn = [])
    {
        // 面试管理-沟通失败原因下拉code,1=联系不上、2=要求岗位或网点没有hc、3=不符合岗位要求、4=候选人已拒绝、5=其他
        $dataList = [
            [
                'key'  => 1,
                'name' => self::$t->_('communication_failure_reason_1'),
            ],
            [
                'key'  => 2,
                'name' => self::$t->_('communication_failure_reason_2'),
            ],
            [
                'key'  => 3,
                'name' => self::$t->_('communication_failure_reason_3'),
            ],
            [
                'key'  => 4,
                'name' => self::$t->_('communication_failure_reason_4'),
            ],
        ];

        if (isCountry('PH')) {
            $dataList[] = [
                'key'  => 6,
                'name' => self::$t->_('communication_failure_reason_6'),
            ];
        }

        $dataList[] = [
            'key'  => 5,
            'name' => self::$t->_('communication_failure_reason_5'),
        ];

        return $dataList;
    }

    /**
     * 面试管理-沟通成功原因下拉
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function interviewCommunicationSuccessReasonList($paramIn = [])
    {
        // 面试管理-沟通失败原因下拉code,1=联系不上、2=要求岗位或网点没有hc、3=不符合岗位要求、4=候选人已拒绝、5=其他
        $dataList = [
            [
                'key'  => 1,
                'name' => self::$t->_('communication_success_reason_1'),
            ],
            [
                'key'  => 2,
                'name' => self::$t->_('communication_success_reason_2'),
            ],
            [
                'key'  => 3,
                'name' => self::$t->_('communication_success_reason_3'),
            ],
            [
                'key'  => 4,
                'name' => self::$t->_('communication_success_reason_4'),
            ],
            [
                'key'  => 99,
                'name' => self::$t->_('communication_success_reason_99'),
            ],
        ];

        return $dataList;
    }

    /**
     * 面试管理-沟通状态下拉
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function interviewCommunicationStatusList($paramIn = [])
    {
        //面试管理-沟通状态下拉code,1=沟通成功、2=沟通失败、3=未沟通
        $dataList = [
            [
                'key'  => 1,
                'name' => self::$t->_('communicate_state_1'),
            ],
            [
                'key'  => 2,
                'name' => self::$t->_('communicate_state_2'),
            ],
            [
                'key'  => 3,
                'name' => self::$t->_('communicate_state_3'),
            ],
            [
                'key'  => 4,
                'name' => self::$t->_('communicate_state_4'),
            ],
        ];
        return $dataList;
    }

    /**
     *
     * 期望岗位与车类型映射
     *
     * @return array
     *
     */
    public function getCarTypeList()
    {
        $carTypes = SettingEnvServer::getSetVal('car_types_group');
        $result   = [];
        if ($carTypes) {
            $carTypes = json_decode($carTypes, true);
            foreach ($carTypes as $jdId => $carIds) {
                $tmp = ['job_id' => $jdId, 'car_list' => []];
                foreach ($carIds as $carId) {
                    $tmp['car_list'][] = [
                        'key'  => $carId,
                        'name' => enums::CAR_TYPE_DESC[$carId],
                    ];
                }
                $result[] = $tmp;
            }
        }

        return $result;
    }

    /**
     * 城市列表
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getCityList($paramIn = [])
    {
        $this->sysList = new SysListRepository();
        $addressType   = $paramIn['address_type'];
        //获取城市
        $data = $this->sysList->getAddressList(['address_type' => $addressType]);
        return $data;
    }

    /**
     * 网点列表
     * @Access  public
     * @Param   request
     * @Return  array
     */

    public function getStoreList($paramIn = [])
    {
        $this->sysList = new SysListRepository();

        $id = $paramIn['id'];
        if ($id) {
            $data = $this->sysList->getStoreList(['id' => $id]);
        } else {
            $store_ids = $this->getMySetStoreIds();
            if (!empty($store_ids)) {
                $data = $this->sysList->getStoreList(['select_ids' => getIdsStr($store_ids)]);
            } else {
                $data = $this->sysList->getStoreList();
            }
        }
        return $data;
    }

    /**
     * 网点模糊搜索
     * @param $params
     * @return array
     */
    public function searchStoreList($params): array
    {
        $sys_list_repository = new SysListRepository();
        $list                = $sys_list_repository->searchStoreList($params);

        if (empty($params['search_name']) || (!empty($params['search_name']) && strstr(strtolower(enums::HEAD_OFFICE), strtolower($params['search_name'])))) {
            $head_office = ['id' => enums::HEAD_OFFICE_ID, 'name' => enums::HEAD_OFFICE];
            array_unshift($list, $head_office);
        }

        return $this->checkReturn(['data' => $list]);
    }

    /**
     * 获取片区下所有网点
     * @param array $manage_piece
     * @return array
     */
    public function getStoreListByPiece(array $manage_piece)
    {
        if (empty($manage_piece)) {
            return [];
        }
        $store_ids = SysStoreModel::find([
            'conditions' => "state = 1 and name!='SP_3PL' and category not in (6,3) and manage_piece in ({manage_piece_ids:array})",
            'bind'       => [
                'manage_piece_ids' => $manage_piece,
            ],
            'columns'    => 'id',
        ])->toArray();

        return array_column($store_ids, 'id');
    }

    /**
     * 获取指定大区下所有网点
     * @param array $manage_piece
     * @return array
     */
    public function getStoreListByRegion(array $manage_region)
    {
        if (empty($manage_region)) {
            return [];
        }

        $store_ids = SysStoreModel::find([
            'conditions' => "state = 1 and name!='SP_3PL' and category not in (6,3) and manage_region in ({manage_region_ids:array})",
            'bind'       => [
                'manage_region_ids' => $manage_region,
            ],
            'columns'    => 'id',
        ])->toArray();

        return array_column($store_ids, 'id');
    }

    /**
     * 获取片区下所有网点
     * @param array $manage_piece
     * @return array
     */
    public function getStoreListByPieceNew(array $manage_piece)
    {
        if (empty($manage_piece)) {
            return [];
        }
        $store_ids = SysStoreModel::find([
            'conditions' => "state = 1 and manage_piece in ({manage_piece_ids:array})",
            'bind'       => [
                'manage_piece_ids' => $manage_piece,
            ],
            'columns'    => 'id',
        ])->toArray();

        return array_column($store_ids, 'id');
    }

    /**
     * 获取指定大区下所有网点
     * @param array $manage_piece
     * @return array
     */
    public function getStoreListByRegionNew(array $manage_region)
    {
        if (empty($manage_region)) {
            return [];
        }

        $store_ids = SysStoreModel::find([
            'conditions' => "state = 1 and manage_region in ({manage_region_ids:array})",
            'bind'       => [
                'manage_region_ids' => $manage_region,
            ],
            'columns'    => 'id',
        ])->toArray();

        return array_column($store_ids, 'id');
    }


    /**
     * HC列表
     * @Access  public
     * @Param   request
     * @Return  array
     */

    public function getHcList($paramIn = [])
    {
        //HC里面有判断，所以不用判断是否是HRISSALES
        $paramIn['reason_type'] = '1,3';

        //过滤hc对应的job_title 为空 1-过滤 0不过滤
        //!如果不过滤会导致发送offer的时候无法选择职位而无法入职
        $paramIn['filter_job_title_is_null'] = 1;

        $data = (new InterviewServer($this->lang))->interviewHcList($paramIn);
        return $data["data"]["dataList"];
    }

    /**
     * 获取部门列表
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getDepartmentList($paramIn = [])
    {
        $this->sysList = new SysListRepository();

        $id = $paramIn['id'];
        if ($id) {
            $data = $this->sysList->getDepartmentList(['id' => $id]);
        } else {
            $temp              = [];
            $temp['hierarchy'] = 1;
            $temp['ids']       = "";
            if ($this->ifHrisSales) {
                $ids         = (new DepartmentRepository())->getDepartmentIdsByRole();
                $temp["ids"] = implode(",", $ids);
            }
            $data = $this->sysList->getDepartmentList($temp);
        }
        return $data;
    }

    /**
     * 获取职位列表
     * @Access  public
     * @param array $paramIn
     * @return array|mixed
     */
    public function getPositionList($paramIn = [])
    {
        $this->sysList = new SysListRepository();

        $id           = $paramIn['id'] ?? '';
        $name         = $paramIn['name'] ?? '';
        $departmentId = $paramIn['department_id'] ?? '';
        if ($id || $name || $departmentId) {
            $data = $this->sysList->getPositionList($paramIn);
        } else {
            $data = $this->sysList->getPositionList();
        }
        return $data;
    }

    /**
     * 获取JD列表
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getJobList($paramIn = [])
    {
        $this->sysList = new SysListRepository();

        $department_id = [];
        if ($this->ifHrisSales) {
            $department_id = (new DepartmentRepository())->getDepartmentIdsByRole();
        }
        $data = $this->sysList->getJobList(["department_id" => $department_id]);
        return $data;
    }

    /**
     * 获取职组列表
     */
    public function getJobGroup()
    {
        $job_group = HrJobGroupModel::find([
            'columns' => 'id,name',
            'pid=:pid: and is_del=:is_del:',
            'bind'    => ['pid' => 0, 'is_del' => 0],
        ])->toArray();

        return $job_group ? $job_group : [];
    }

    /**
     * @return array[]
     */
    public function interviewResumeListHcStateList(): array
    {
        return  [
            [
                'key'  => 2,
                'name' => self::$t->_('4002'),
            ],
            [
                'key'  => 3,
                'name' => self::$t->_('4003'),
            ],
            [
                'key'  => 4,
                'name' => self::$t->_('4004'),
            ],
            [
                'key'  => 9,
                'name' => self::$t->_('4023'),
            ],
        ];
    }

    /**
     * 获取hc列表状态下拉
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getHcStateList($paramIn = [])
    {
        //状态表code,1=未生效，2=招聘中，3=已完成，4=已作废，5=已拒绝，6=已同意，7=待审批，8=申请，9=已过期
        $hcStateList = [
            [
                'key'  => 1,
                'name' => self::$t->_('4001'),
            ],
            [
                'key'  => 2,
                'name' => self::$t->_('4002'),
            ],
            [
                'key'  => 3,
                'name' => self::$t->_('4003'),
            ],
            [
                'key'  => 4,
                'name' => self::$t->_('4004'),
            ],
            [
                'key'  => 9,
                'name' => self::$t->_('4023'),
            ],
        ];
        return $hcStateList;
    }

    /**
     * 获取hc列表审批状态下拉
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getHcApprovalStateList($paramIn = [])
    {
        //状态表code,1=未生效，2=招聘中，3=已完成，4=已作废，5=已拒绝，6=已同意，7=待审批，8=申请，9=已过期
        $hcApprovalStateList = [
            [
                'key'  => 7,
                'name' => self::$t->_('4015'),
            ],
            [
                'key'  => 6,
                'name' => self::$t->_('4017'),
            ],
            [
                'key'  => 5,
                'name' => self::$t->_('4005'),
            ],
            [
                'key'  => 4,
                'name' => self::$t->_('4031'),
            ],
        ];
        return $hcApprovalStateList;
    }

    /**
     * 获取hc列表审批阶段下拉
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getHcApprovalStageList($paramIn = [])
    {
        //hc列表审批阶段,1=1级待审，2=2级待审，3=已完成审批
        $hcApprovalStageList = [
            [
                'key'  => 1,
                'name' => self::$t->_('4024'),
            ],
            [
                'key'  => 2,
                'name' => self::$t->_('4025'),
            ],
            [
                'key'  => 3,
                'name' => self::$t->_('4038'),
            ],
            [
                'key'  => 4,
                'name' => self::$t->_('4026'),
            ],
        ];
        return $hcApprovalStageList;
    }

    /**
     * 获取hc列表优先级下拉
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getHcPriorityList($paramIn = [])
    {
        $hcPriorityList = [
            [
                'key'   => 1,
                'name'  => self::$t->_('4027'),
                'name1' => 'P1',
            ],
            [
                'key'   => 2,
                'name'  => self::$t->_('4028'),
                'name1' => 'P2',
            ],
            [
                'key'   => 3,
                'name'  => self::$t->_('4029'),
                'name1' => 'P3',
            ],
            [
                'key'   => 4,
                'name'  => self::$t->_('4030'),
                'name1' => 'P4',
            ],
        ];

        //马来新增P0优先级
        if (isCountry('MY')) {
            array_unshift($hcPriorityList,[
                'key'   => 0,
                'name'  => self::$t->_('hc_priority_urgent'),
                'name1' => 'P0',
            ]);
        }

        return $hcPriorityList;
    }

    public function getInterviewHcStateList()
    {
        $interviewHcStateList = [
            [
                'key'  => 1,
                'name' => self::$t->_('4201'),
            ],
            [
                'key'  => 2,
                'name' => self::$t->_('4202'),
            ],
            [
                'key'  => 3,
                'name' => self::$t->_('4004'),
            ],
            [
                'key'  => 4,
                'name' => self::$t->_('4204'),
            ],
        ];
        return $interviewHcStateList;
    }

    /**
     * 获取面试列表取消状态下拉
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getInterviewCancelStateList()
    {
        //取消状态
        $cancelInterviewData = [
            [
                'key'  => 1,
                'name' => $this->getTranslation()->_('4301'),
            ],
            [
                'key'  => 2,
                'name' => $this->getTranslation()->_('4302'),
            ],
            [
                'key'  => 3,
                'name' => $this->getTranslation()->_('4303'),
            ],
            [
                'key'  => 4,
                'name' => $this->getTranslation()->_('4304'),
            ],
            [
                'key'  => 5,
                'name' => $this->getTranslation()->_('4305'),
            ],
            [
                'key'  => 6,
                'name' => $this->getTranslation()->_('4306'),
            ],
        ];

        return $cancelInterviewData;
    }

    /**
     * 获取JD类型
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getJdTypeList()
    {
        //取消状态
        $jdTypeList = [
            [
                'key'  => 1,
                'name' => self::$t->_('3014'),
            ],
            [
                'key'  => 2,
                'name' => self::$t->_('3015'),
            ],
            [
                'key'  => 3,
                'name' => self::$t->_('3016'),
            ],
        ];

        return $jdTypeList;
    }

    /**
     * 获取黑名单列表信息来源类型
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getBlacklistType()
    {
        //黑名单列表信息来源类型
        return  [
            [
                'key'  => 1,
                'name' => self::$t->_('7523'),
            ],
            [
                'key'  => 2,
                'name' => self::$t->_('7524'),
            ],
            [
                'key'  => 3,
                'name' => self::$t->_('7525'),
            ],
            [
                'key'  => 4,
                'name' => self::$t->_('7526'),
            ],
            [
                'key'  => 5,
                'name' => self::$t->_('7527'),
            ],
            [
                'key'  => 6,
                'name' => self::$t->_('7528'),
            ],
            [
                'key'  => 7,
                'name' => self::$t->_('7533'),
            ],
            [
                'key'  => 8,
                'name' => self::$t->_('7534'),
            ],
            [
                'key'  => 9,
                'name' => self::$t->_('7535'),
            ],
            [
                'key'  => 10,
                'name' => self::$t->_('7536'),
            ],
            [
                'key'  => 11,
                'name' => self::$t->_('7537'),
            ],
        ];
    }

    /**
     * 获取黑名单列表状态列表
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getBlacklistStatus()
    {
        //黑名单列表信息来源类型
        $blacklistStatus = [
            [
                'key'  => 1,
                'name' => self::$t->_('7515'),
            ],
            [
                'key'  => 2,
                'name' => self::$t->_('7516'),
            ],
        ];

        return $blacklistStatus;
    }

    /**
     * 获取面试取消选项列表
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getCancelInterviewList()
    {
        $cancelInterviewData = [
            [
                'key'  => 1,
                'name' => self::$t->_('4301'),
            ],
            [
                'key'  => 2,
                'name' => self::$t->_('4302'),
            ],
            [
                'key'  => 3,
                'name' => self::$t->_('4303'),
            ],
            [
                'key'  => 4,
                'name' => self::$t->_('4304'),
            ],
            [
                'key'  => 5,
                'name' => self::$t->_('4305'),
            ],
            [
                'key'  => 7,
                'name' => self::$t->_('7530'),
            ],
            [
                'key'  => 8,
                'name' => self::$t->_('8115'),
            ],
//            [
//                'key' => 9,
//                'name' => $this->getTranslation()->_('8115'),
//            ],
            [
                'key'  => 6,
                'name' => self::$t->_('4306'),
            ],
        ];
        return $cancelInterviewData;
    }

    /**
     * 获取面试淘汰选项列表
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getOutInterviewList()
    {
        $outInterviewData = [
            [
                'key'  => 1,
                'name' => self::$t->_('7529'),
            ],
            [
                'key'  => 2,
                'name' => self::$t->_('7530'),
            ],
            [
                'key'  => 3,
                'name' => self::$t->_('7531'),
            ],
            [
                'key'  => 5,
                'name' => self::$t->_('8117'),
            ],
            [
                'key'  => 6,
                'name' => self::$t->_('8118'),
            ],
            [
                'key'  => 7,
                'name' => self::$t->_('8119'),
            ],
            [
                'key'  => 8,
                'name' => self::$t->_('8120'),
            ],
            [
                'key'  => 4,
                'name' => self::$t->_('7532'),
            ],
        ];
        return $outInterviewData;
    }

    /**
     * 获取offer下拉员工属性
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getOfferStaffJobType()
    {
        $offerStaffJobType = [
            [
                'key'  => 1,
                'name' => self::$t->_('7600'),
            ],
            [
                'key'  => 4,
                'name' => self::$t->_('7603'),
            ],
            [
                'key'  => 2,
                'name' => self::$t->_('7601'),
            ],
            [
                'key'  => 3,
                'name' => self::$t->_('7602'),
            ],

        ];
        return $offerStaffJobType;
    }

    /**
     * 获取简历下拉员工属性
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getResumeJobType()
    {
        $offerStaffJobType = [
            [
                'key'  => 1,
                'name' => self::$t->_('7600'),
            ],
            [
                'key'  => 2,
                'name' => self::$t->_('7601'),
            ],
            [
                'key'  => 3,
                'name' => self::$t->_('7602'),
            ],
            [
                'key'  => 4,
                'name' => self::$t->_('7603'),
            ],
        ];
        return $offerStaffJobType;
    }

    public function getHireType()
    {
        $hire_type_enum = SettingEnvServer::getSetVal('hire_type_enum');
        $hire_type_enum = explode(',', ($hire_type_enum ? $hire_type_enum : "1,2,3,4,5"));
        $return_data = [];
        foreach ($hire_type_enum as $k=>$v){
            $return_data[$k]["key"] = intval($v);
            $return_data[$k]["name"] = self::$t->_('hire_type_'.$v);
        }
        return array_values($return_data);
    }

    /**
     * 获取项目期数
     * @return array
     */
    public function getProjectNumList(): array
    {
        $projectNumList = [];
        foreach (HrInterviewOfferModel::$project_num_list as $k => $v) {
            $projectNumList[] = [
                'key' => (string) $k,
                'name' =>$this->getTranslation()->_($v),
            ];
        }

        $vanCourierProjectNumList = [];

        foreach (HrInterviewOfferModel::$van_courier_project_num_list as $k => $v) {
            $vanCourierProjectNumList[] = [
                'key'  => (string)$k,
                'name' => $this->getTranslation()->_($v),
            ];
        }

        $data =  [
            [
                'job_title_id' => (String) enumsTh::JOB_TITLE_EV_COURIER,
                'list' => $projectNumList
            ],
            [
                'job_title_id' => (String) enumsTh::JOB_TITLE_VAN_COURIER_PROJECT,
                'list' => $vanCourierProjectNumList
            ],
        ];

        return $data;
    }

    /**
     * 获取offer-设备费列表
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getEquipmentCostList()
    {
        $equipmentCostList    = env("equipmentCostPositionIds", "{}");
        $equipmentCostList    = json_decode($equipmentCostList, true);
        $equipmentCostListArr = [];
        foreach ($equipmentCostList as $k => $v) {
            $equipmentCostListArr[$k]['key']  = $k;
            $equipmentCostListArr[$k]['name'] = $v;
        }
        shuffle($equipmentCostListArr);
        return $equipmentCostListArr;
    }

    /**
     * 获取招聘渠道列表
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getRecruitChannelList($channel_id = 0)
    {
        $cache_key  = "resume_recruit_channe";
        $redis      = $this->getDI()->get('redisLib');
        $cache_data = $redis->get($cache_key);
        $this->getDI()->get('logger')->write_log("getRecruitChannelList缓存中内容:".json_encode($cache_data,
                JSON_UNESCAPED_UNICODE), 'info');
        //获取渠道列表数据
        if (empty($cache_data) || env('runtime') != 'pro') {
            //获取招聘渠道内容
            $recruit_channels = HrRecruitChannelsModel::find([
                'columns' => 'id as key, name',
                'order'   => 'id asc',
            ])->toArray();

            if (!empty($recruit_channels)) {
                $redis->set($cache_key, json_encode($recruit_channels, JSON_UNESCAPED_UNICODE), 3600); //缓存内容
            }
        } else {
            $recruit_channels = json_decode($cache_data, true);
        }
        $this->getDI()->get('logger')->write_log("getRecruitChannelList最终返回数据内容:".json_encode($recruit_channels,
                JSON_UNESCAPED_UNICODE), 'info');
        //兼容获取指定id的渠道名称功能
        if ($recruit_channels && $channel_id) {
            $recruit_channels_column = array_column($recruit_channels, 'name', 'key');
            return $recruit_channels_column[$channel_id] ?? '';
        }

        return $recruit_channels;
    }


    /**
     * 获取大区列表
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getRegionList($paramIn = [])
    {
        $this->sysList = new SysListRepository();

        $data = $this->sysList->getRegionList();
        return $data;
    }

    /**
     * 获取dc sp网点
     * @param array $paramIn
     * @return array
     */
    public function getNwStoreList($paramIn = [])
    {
        $this->sysList = new SysListRepository();

        return $this->sysList->getNwStoreList();
    }

    /**
     * 获取大区数据
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getManageRegionList($paramIn = [])
    {
        $this->sysList = new SysListRepository();
        $data = $this->sysList->getManageRegionListFromCache();
        return $data;
    }

    /**
     * 获取片区数据
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getManagePieceList($paramIn = [])
    {
        $this->sysList = new SysListRepository();
        $data = $this->sysList->getManagePieceListFromCache($paramIn);
        return $data;
    }

    /**
     * 获取网点职位列表
     * @param array $paramIn
     * @return array
     */
    public function getStoreJobList($paramIn = [])
    {
        $this->sysList = new SysListRepository();

        $storeJobList = env('store_hc_job', '7, 21, 10003, 10008, 10197');

        $data = $this->sysList->getJobList($storeJobList);
        return $data;
    }

    /**
     * 获取培训状态
     * @param array $paramIn
     * @return array
     */
    public function getTrainStateList()
    {
        //状态表code：1待确认 2待培训 3培训中 4培训通过 5未通过
        $trainStateList = [
            [
                'key'  => 1,
                'name' => self::$t->_('8110'),
            ],
            [
                'key'  => 2,
                'name' => self::$t->_('8111'),
            ],
            [
                'key'  => 3,
                'name' => self::$t->_('8112'),
            ],
            [
                'key'  => 4,
                'name' => self::$t->_('8113'),
            ],
            [
                'key'  => 5,
                'name' => self::$t->_('8114'),
            ],
        ];
        return $trainStateList;
    }

    public function getReasonType()
    {
        return [
            [
                "key"  => 1,
                "name" => str_replace(["招聘", "Recruitment"], ["新增", "Add"],
                    self::$t->_("hc_reason_type_1")),
            ],
            [
                "key"  => 2,
                "name" => self::$t->_("hc_reason_type_2"),
            ],
            [
                "key"  => 3,
                "name" => self::$t->_("hc_reason_type_3"),
            ],
        ];
    }

    /**
     * 人才库-简历来源选项
     * @return array
     */
    public function getTalentPollResumeSource()
    {
        return [
            [
                "key"  => 1,
                "name" => 'Buddy',
            ],
            [
                "key"  => 2,
                "name" => 'Facebook',
            ],
            [
                "key"  => 3,
                "name" => 'DC',
            ],
            [
                "key"  => 4,
                "name" => 'Referral',
            ],
            [
                "key"  => 5,
                "name" => 'Walk-in',
            ],
            [
                "key"  => 6,
                "name" => 'Friend',
            ],
            [
                "key"  => 7,
                "name" => 'JobBKK',
            ],
            [
                "key"  => 8,
                "name" => 'JobsDB',
            ],
            [
                "key"  => 9,
                "name" => 'JobThai',
            ],
            [
                "key"  => 10,
                "name" => 'JobTopGun',
            ],
            [
                "key"  => 11,
                "name" => 'Linkedin',
            ],
            [
                "key"  => 12,
                "name" => 'Others',
            ],


        ];
    }

    /**
     * 人才库excel 下载可选字段列表
     * @return array
     */
    public function getTalentPollExcelLableList()
    {
        return [
            [
                "key"           => 'id',
                "name"          => self::$t->_("id"),
                'must_selected' => 1,
            ],
            [
                "key"           => 'name_zh',
                "name"          => self::$t->_("name_zh"),
                'must_selected' => 1,
            ],
            [
                "key"           => 'name_en',
                "name"          => self::$t->_("name_en"),
                'must_selected' => 0,
            ],
            [
                "key"           => 'call_name',
                "name"          => self::$t->_("call_name"),
                'must_selected' => 0,
            ],
            [
                "key"           => 'email',
                "name"          => self::$t->_("email"),
                'must_selected' => 0,
            ],
            [
                "key"           => 'mobile',
                "name"          => self::$t->_("mobile"),
                'must_selected' => 0,
            ],
            [
                "key"           => 'department',
                "name"          => self::$t->_("department"),
                'must_selected' => 0,
            ],
            [
                "key"           => 'current_position',
                "name"          => self::$t->_("current_position"),
                'must_selected' => 0,
            ],
            [
                "key"           => 'expected_position',
                "name"          => self::$t->_("expected_position"),
                'must_selected' => 0,
            ],
            [
                "key"           => 'resume_source_text',
                "name"          => self::$t->_("resume_source_text"),
                'must_selected' => 0,
            ],
            [
                "key"           => 'current_salary',
                "name"          => self::$t->_("current_salary"),
                'must_selected' => 0,
            ],
            [
                "key"           => 'expected_salary',
                "name"          => self::$t->_("expected_salary"),
                'must_selected' => 0,
            ],
            [
                "key"           => 'idcard',
                "name"          => self::$t->_("idcard"),
                'must_selected' => 0,
            ],
            [
                "key"           => 'is_can_speak_chinese_text',
                "name"          => self::$t->_("is_can_speak_chinese_text"),
                'must_selected' => 0,
            ],
            [
                "key"           => 'is_have_work_permit_text',
                "name"          => self::$t->_("is_have_work_permit_text"),
                'must_selected' => 0,
            ],
            [
                "key"           => 'communication_status_text',
                "name"          => self::$t->_("communication_status_text"),
                'must_selected' => 0,
            ],
            [
                "key"           => 'candidate_status_text',
                "name"          => self::$t->_("candidate_status_text"),
                'must_selected' => 0,
            ],
            [
                "key"           => 'communication_hr_name',
                "name"          => self::$t->_("communication_hr_name"),
                'must_selected' => 0,
            ],
            [
                "key"           => 'updated_at',
                "name"          => self::$t->_("communication_time"),
                'must_selected' => 0,
            ],
            [
                "key"           => 'adder_hr_name',
                "name"          => self::$t->_("adder_hr_name"),
                'must_selected' => 0,
            ],

        ];
    }


    /**
     * 获取培训地点
     * @param array $paramIn
     * @return array
     */
    public function getTrainPlaceList($paramIn = [])
    {
        $this->sysList = new SysListRepository();

        return $this->sysList->getTrainPlaceList();
    }

    /**
     * 获取当前用户的网点列表
     */
    public function getMyStoreList()
    {
        $this->sysList = new SysListRepository();

        return $this->sysList->getMyStoreList($this->userInfo['id']);
    }

    /**
     * *Hc管理-HC网点，获得大区-片区列表
     * @return array
     */
    public function getManageRegionAndPieceList($is_html = 1)
    {
        $this->sysList = new SysListRepository();

        return $this->sysList->getManageRegionAndPieceList($is_html);
    }

    /**
     * 获取重复的hc列表
     * 在操作者提交用人申请时，校验HC所属部门、JD ID、所属网点、用人原因title（新增/转岗/离职）
     * 若该HC满足以上四项校验条件与HC列表中状态为1-审批中，2-招聘中，7-待审批的HC信息存在完全相同的情况
     * @param array $paramIn
     * @return array|mixed
     */
    public function getRepeatHcList($paramIn = [])
    {
        return (new HcServer())->checkRepeatHcRequestList($paramIn);
    }

    /**
     * 获取HC参考数据
     * @param array $paramIn
     * @return bool|mixed|null
     */
    public function getHcReferences($paramIn = [])
    {
        //[1]获取参数
        $departmentId = $this->processingDefault($paramIn, 'department_id');
        $storeId      = $this->processingDefault($paramIn, 'store_id');
        $jobTitleId   = $this->processingDefault($paramIn, 'job_title_id');

        //[2]数据验证
        $validations = [
            "department_id" => "Required|Int",
            "store_id"      => "Required|Str",
            "job_title_id"  => "Required|Int",
        ];
        $this->validateCheck($paramIn, $validations);

        $apiClient = new ApiClient('by_rpc', '', 'get_hc_references', $this->lang);
        $apiClient->setParams([
            'budget_month'  => date('Y-m'),
            'department_id' => $departmentId,
            'store_id'      => $storeId,
            'job_title_id'  => $jobTitleId,
        ]);
        $result = $apiClient->execute();

        return $result['result']['data'] ?? [];
    }

    /**
     * 获取部门关联的职位
     * @param array $paramIn
     * @return bool|mixed|null
     */
    public function getDepartmentJobTitleList($paramIn = [])
    {
        $this->sysList = new SysListRepository();

        $departmentId = $this->processingDefault($paramIn, 'department_id');
        //[2]数据验证
        $validations = [
            "department_id" => "Required|Int",
        ];

        $this->validateCheck($paramIn, $validations);
        $departmentData = $this->sysList->getDepartmentJobTitleList($departmentId);
        $server = new InterviewServer();

        foreach ($departmentData as $key => &$value) {
            $working_day_rest_type_arr_text = [];
            $working_day_rest_type_arr      = [];
            if (!empty($value['working_day_rest_type'])) {
                $working_day_rest_type_arr = $working_day_rest_type_arr_text = explode(',',
                    $value['working_day_rest_type']);
                array_walk($working_day_rest_type_arr_text, function (&$val) {
                    $val = $this->getTranslation()->_('working_day_rest_type_'.$val);
                });
            }
            $value['is_show_language_ability'] = false;
            //泰国 非一线 语言能力
            if (isCountry('TH') && !$server->isFirstLineJob($departmentId, $value['job_title_id'])) {
                $value['is_show_language_ability'] = true;
            }

            $departmentData[$key]['working_day_rest_type_arr']      = $working_day_rest_type_arr;
            $departmentData[$key]['working_day_rest_type_arr_text'] = $working_day_rest_type_arr_text;
        }

        return $departmentData;
    }

    /**
     * 获取申请人所在公司下的所有子部门
     * @param array $paramIn
     * @return array
     */
    public function getDepartmentListV2($paramIn = [])
    {
        $this->sysList = new SysListRepository();

        //[1]获取当前登录人ID
        $staff_id = $this->processingDefault($paramIn, 'staff_id');

        return $this->sysList->getDepartmentListV2($staff_id);
    }

    /**
     * 获取劳动合同类型选项
     * @return array
     */
    public function getlaborContractTypeLableList()
    {
        $labor_contract_type = enums::$contract_child_type_data[COUNTRY_CODE][enums::CONTRACT_LDHT] ?? [];
        if (empty($labor_contract_type)) {
            return [];
        }

        //泰国新增个人代理合同
        if (isCountry()) {
            $labor_contract_type[enums::CONTRACT_LDHT_AGENT]            = 'contract_child_type_' . enums::CONTRACT_LDHT_AGENT;
        }

        //泰国新增个人代理合同
        if (isCountry('PH')) {
            $labor_contract_type[enums::CONTRACT_LDHT_PH_AGENT] = 'contract_child_type_' . enums::CONTRACT_LDHT_PH_AGENT;
        }

        foreach ($labor_contract_type as $id => $value) {
            $result[] = ['id' => $id, 'name' => self::$t->_($value)];
        }


        return $result;
    }

    /**
     * 获取hc负责人下拉内容数据
     */
    public function getHcManager($param)
    {
        //获取设置的hc招聘负责人部门ID内容
        $department_ids = SettingEnvServer::getSetVal('get_hc_manager_by_dept_ids');

        $return_data = [];
        if ($department_ids) {
            //获取所有设置的部门以及子部门ID
            $department_id_list = explode(',', $department_ids);
            if (empty($department_id_list)) {
                return [];
            }
            foreach ($department_id_list as $department_id) {
                //获取下级所有部门
                $dept_child_datas = self::getDepartChildData($department_id);
                $dept_ids[]       = array_column($dept_child_datas, 'id');
            }
            $all_dept_ids = [];
            foreach ($dept_ids as $ids) {
                $all_dept_ids = array_merge($all_dept_ids, $ids);
            }
            $all_dept_ids = array_values(array_unique($all_dept_ids));
            if (empty($all_dept_ids)) {
                return [];
            }
            //获取所有部门数据的部门ID和部门名称的对应关系数据
            $dept_data = (new SysListRepository())->getDepartmentList(['ids' => implode(',', $all_dept_ids)]);
            $this->getDI()->get('logger')->write_log("getHcManager dept_ids :".json_encode($all_dept_ids), 'info');
            $dept_id_name_map = array_column($dept_data, 'name', 'id');
            //获取设置的部门下符合条件的所有员工表数据

            //获取列表
            $builder = $this->modelsManager->createBuilder();
            $builder->columns([
                    "
                h.staff_info_id,
                h.name,
                h.nick_name,
                h.job_title,
                h.node_department_id
               ",
                ]
            );
            $builder->from(['h' => HrStaffInfoModel::class]);

            if (isCountry('TH')) {
                $builder->andWhere('h.node_department_id IN ({node_department_id:array}) and h.formal IN (1,4) and h.state=1 and h.is_sub_staff=0 ', ['node_department_id' => $all_dept_ids]);
            } else {
                $builder->andWhere('h.node_department_id IN ({node_department_id:array}) and h.formal=1 and h.state=1 and h.is_sub_staff=0 ',
                    ['node_department_id' => $all_dept_ids]);
            }

            //查看角色是否配置
            $roles = SettingEnvServer::getSetVal('get_hc_manager_by_roles');
            if ($roles) {
                $roles = explode(',', $roles);
                $builder->leftJoin(HrStaffInfoPositionModel::class, 'p.staff_info_id = h.staff_info_id', 'p');
                $builder->andWhere('p.position_category IN ({position_category:array}) ',
                    ['position_category' => $roles]);
                $builder->groupBy('h.staff_info_id');
            }

            if(!empty($param['search_staff'])){
                $builder->andWhere('(h.staff_info_id like :search_staff: or h.name like :search_staff:)', ['search_staff' => "%{$param['search_staff']}%"]);
            }

            $builder->limit(20);

            $staff_list = $builder->getQuery()->execute()->toArray();

            if (empty($staff_list)) {
                return $return_data;
            }

            //获取职位数据
            $job_list = HrJobTitleByModel::find()->toArray();
            if (!empty($job_list)) {
                $job_id_name_map = array_column($job_list, 'job_name', 'id');
            }
            //todo 返回页面显示数据
            foreach ($staff_list as $staff) {
                $return_data[] = [
                    'staff_id'        => $staff['staff_info_id'],
                    'staff_name'      => $staff['nick_name'] ? $staff['nick_name'] : $staff['name'],
                    'department_name' => $dept_id_name_map[$staff['node_department_id']] ?? '',
                    'job_title_name'  => $job_id_name_map[$staff['job_title']] ?? '',
                ];
            }
        }
        return $return_data;
    }

    /**
     * 获取指定部门ID信息以及所有下级部门 ID 数据
     */
    public static function getDepartChildData($department_id)
    {
        //获取该部门下所有子部门
        $dept = SysDepartmentModel::findFirst([
            'conditions' => ' id = :deptid: ',
            'bind'       => [
                'deptid' => $department_id,
            ],
        ]);
        $data = [];
        if (isset($dept->id) && $dept->id) {
            $departmentChain = $dept->ancestry_v3;
            $data            = SysDepartmentModel::find([
                'conditions' => 'ancestry_v3 like :chain: or id = :id:',
                'bind'       => [
                    'chain' => "{$departmentChain}/%",
                    'id'    => $dept->id,
                ],
            ])->toArray();
        }
        return $data;
    }


    /**
     * @description:获取行政区列表
     *
     * @param null
     *
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2021/11/30 23:27
     */

    public function getSysProvinceList()
    {
        $SysProvinceList = SysProvinceModel::find([
            'columns'    => 'code as key,name',
            'conditions' => 'deleted = 0 ',//未删除
        ])->toArray();
        return $SysProvinceList;
    }

    /**
     * 验证是否需要更新
     * @return array
     */
    public function getAddressVersion(): array
    {
        $data['version'] = 1;

        if ($version = SettingEnvServer::getSetVal('winhr_address_version')) {
            $data['version'] = intval($version);
        }
        return $data;
    }

    /**
     * 获取简历招聘负责人列表数据
     */
    public function getStaffListForTalentDepart()
    {
        //获取配置的简历招聘负责人取值返回的所有部门ID
        $resume_recruiter_depts = $this->getResumeRecruiterDeptsData();
        $this->getDI()->get('logger')->write_log(['resume_recruiter_depts'=>$resume_recruiter_depts], 'info');
        //获取指定部门下的所有人作为简历招聘负责人
        $staffList = (new StaffRepository())->getStaffListByDeparementIds($resume_recruiter_depts);;
        $res = [];
        foreach ($staffList as $v) {
            $res[] = ['code' => $v['staff_info_id'], 'name' => $v['staff_info_id']." ".$v['nick_name']." ".$v['name']];
        }
        return $res;
    }


    public function getStaticSalaryApprove($paramIn = [])
    {

        $setVal = (new SettingEnvServer())->getMultiEnvByCode(['salary_approve_company_list','lnt_recruitment']);

        $companies = [];
        if (!empty($setVal['salary_approve_company_list'])) {
            $setVals = explode("|", $setVal['salary_approve_company_list']);
            foreach ($setVals as $val) {
                $val         = explode('@', $val)[0];
                $companies[] = [
                    'code' => $val,
                    'name' => $val,
                ];
            }
        }
        //回显用
        if (isCountry('MY') ) {
            $lnt = [
                'code' => enumsContract::$salaryCompanyMap[enumsContract::LNT_COMPANY_ID],
                'name' => enumsContract::$salaryCompanyMap[enumsContract::LNT_COMPANY_ID],
            ];
            $express = [
                'code' => enumsContract::$salaryCompanyMap[enumsContract::FLASH_EXPRESS_COMPANY_ID],
                'name' => enumsContract::$salaryCompanyMap[enumsContract::FLASH_EXPRESS_COMPANY_ID],
            ];
            if(empty($paramIn['hc_id'])){
                return array_merge($companies, [$lnt]);
            }
            $hcInfo = HrhcModel::FindFirst([
                'conditions' => 'hc_id=:hc_id:',
                'bind'       => ['hc_id' => $paramIn['hc_id']],
            ]);

            $departmentInfo = (new SysDepartmentRepository())->getDepartmentInfoById($hcInfo->department_id,
                ['ancestry_v3']);
            if (empty($departmentInfo)) {
                throw new Exception('部门不存在！！id=>'.$hcInfo->department_id);
            }
            $dept_flash_express_id       = (new SettingEnvServer())->getSetVal('dept_flash_express_id');
            $deptList = explode('/', $departmentInfo['ancestry_v3']);

            //开关打卡，只招lnt
            if (!empty($setVal['lnt_recruitment'])) {
                //快递部门只找快递和lnt的
                if (in_array($dept_flash_express_id, $deptList)) {
                    if (in_array($hcInfo->hire_type, [HrStaffInfoModel::HIRE_TYPE_AGENT,HrStaffInfoModel::HIRE_TYPE_PART_TIME_AGENT,HrStaffInfoModel::HIRE_TYPE_OTHER])) {
                        $companies = [$express];
                    } else {
                        $companies = [$lnt];
                    }
                    return $companies;
                }

                foreach ($companies as $k => $company) {
                    if ($company['code'] == enumsContract::$salaryCompanyMap[enumsContract::LNT_COMPANY_ID]) {
                        unset($companies[$k]);
                    }
                }
                $companies = array_values($companies);
            }
        }

        return $companies;
    }


    /**
     * 获取配置的简历招聘负责人取值返回的所有部门ID
     * @return array
     */
    public function getResumeRecruiterDeptsData()
    {
        $departIds = SettingEnvServer::getSetVal('talent_acquisition_department_id');
        if (empty($departIds)) {
            return [];
        }
        //
        $setting_dept_id_arr = array_values(array_unique(explode(',', $departIds)));

        //获取所有设置部门（有效的未删除部门）
        $setting_depts = \FlashExpress\bi\App\Models\backyard\SysDepartmentModel::find([
            'conditions' => 'id in ({dept_id:array}) and deleted = 0 ',
            'bind'       => [
                'dept_id' => $setting_dept_id_arr,
            ],
            'columns'    => ['id', 'ancestry_v3', 'name'],
        ])->toArray();

        if (empty($setting_depts)) {
            return [];
        }

        //获取所有有效部门数据
        $all_department_list = \FlashExpress\bi\App\Models\backyard\SysDepartmentModel::find([
            'conditions' => 'deleted = 0',
            'columns'    => ['id', 'ancestry_v3', 'name'],
        ])->toArray();

        $all_dept_ids = $setting_dept_id_arr;//先把当前设置的部门ID记录寄来

        //循环所有部门数据中属于前设置部门的子部门的ID
        foreach ($all_department_list as $all_item) {
            foreach ($setting_depts as $setting_dept) {
                if (strpos($all_item['ancestry_v3'], $setting_dept['ancestry_v3'].'/') !== false) {
                    $all_dept_ids[] = $all_item['id'];
                }
            }
        }

        if (empty($all_dept_ids)) {
            return [];
        }

        return $all_dept_ids;
    }

    /**
     * 获取面试不通过类型-原因
     * @return array
     */
    function getInterviewPassType()
    {
        $data      = [];
        $data_type = HrInterviewPassTypeModel::find([
            'columns'    => 'pass_type, pass_reason',
            'conditions' => ' deleted=0 ',
        ])->toArray();
        foreach ($data_type as $key => $val) {
            $return_data[$val['pass_type']]['id']        = $val['pass_type'].'.0';
            $return_data[$val['pass_type']]['key']       = (int)$val['pass_type'];
            $return_data[$val['pass_type']]['name']      = $this->getTranslation()->_(enums::INTERVIEW_PASS_TYPE_PREFIX.$val['pass_type']);
            $return_data[$val['pass_type']]['is_input']  = in_array($val['pass_type'], [5]) ? true : false;
            $return_data[$val['pass_type']]['is_parent'] = true;
            if ($val['pass_reason']) {
                $return_data[$val['pass_type']]['child'][] = [
                    'id'        => $val['pass_type'].'.'.$val['pass_reason'],
                    'key'       => (int)$val['pass_reason'],
                    'name'      => $this->getTranslation()->_(enums::INTERVIEW_PASS_TYPE_REASON_PREFIX.$val['pass_type'].'_'.$val['pass_reason']),
                    'is_input'  => ($val['pass_type'] == 4 && $val['pass_reason'] == 6) ? true : false,
                    'is_parent' => false,
                ];
            }
        }
        $data['dataList'] = array_values($return_data);
        return $data;
    }

    /**
     * 获取员工列表
     * @param $params
     * @return array
     */
    public function getUserList($params)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(HrStaffInfoModel::class);
        $builder->columns('staff_info_id,name');
        $builder->andWhere('state IN ({state:array})', ['state' => [1, 3]]);
        $builder->andWhere('is_sub_staff = :is_sub_staff:', ['is_sub_staff' => 0]);

        if (!empty($params['keyword'])) {
            $builder->andWhere('staff_info_id like :keyword:', ['keyword' => '%'.$params['keyword'].'%']);
        }

        $builder->orderBy('id');
        $builder->limit(100);
        $list = $builder->getQuery()->execute()->toArray();

        return $this->checkReturn(['data' => $list]);
    }

    public function getThHeadOfficeNoLikeJobList(){
        $winhr_th_head_office_no_like_job     = SettingEnvServer::getSetVal('winhr_th_head_office_no_like_job') ?? '';
        $winhr_th_head_office_no_like_job_arr = empty($winhr_th_head_office_no_like_job) ? [] : explode(',', $winhr_th_head_office_no_like_job);
        $h5_th_head_office_no_like_job     = SettingEnvServer::getSetVal('h5_th_head_office_no_like_job') ?? '';
        $h5_th_head_office_no_like_job_arr = empty($h5_th_head_office_no_like_job) ? [] : explode(',', $h5_th_head_office_no_like_job);
        return [
            'winhr_th_head_office_no_like_job'=>$winhr_th_head_office_no_like_job_arr,
            'h5_th_head_office_no_like_job'=>$h5_th_head_office_no_like_job_arr,
        ];
    }

    /**
     * @return array[]
     */
    public function getImportResult(): array
    {
        return [
            [
                'key'  => HireTypeImportListModel::IMPORT_STATE_SUCCESS,
                'name' => self::$t->_('contract_import_success'),
            ],[
                'key'  => HireTypeImportListModel::IMPORT_STATE_FAIL,
                'name' => self::$t->_('contract_import_fail'),
            ],
        ];
    }

    /**
     * @return array[]
     */
    public function getEntryState(): array
    {
        return [
            [
                'key'  => HireTypeImportListModel::STATUS_EMPLOYED,
                'name' => $this->getTranslation()->_('4809'),
            ],[
                'key'  => HireTypeImportListModel::STATUS_TO_BE_EMPLOYED,
                'name' => $this->getTranslation()->_('4904'),
            ],[
                'key'  => HireTypeImportListModel::STATUS_NOT_EMPLOYED,
                'name' => $this->getTranslation()->_('4905'),
            ],
        ];
    }

    /**
     * @return array[]
     */
    public function getHireTypeImportSource() : array
    {
        return [
            [
                'key'  => HireTypeImportListModel::DATA_SOURCE_WIN_HR,
                'name' => 'WHR',
            ],[
                'key'  => HireTypeImportListModel::DATA_SOURCE_BY,
                'name' => 'BY',
            ],
        ];
    }

    /**
     * 工作天数&轮休规则
     * @param array $paramIn
     * @return array
     */
    public function getWorkingDayRestTypeList($paramIn = [])
    {
        $this->sysList              = new SysListRepository();
        $working_day_rest_type_list = [];
        //[1]获取当前登录人ID
        $department_id = $this->processingDefault($paramIn, 'department_id', 2);
        $job_title_id  = $this->processingDefault($paramIn, 'job_title_id', 2);

        $list = $this->sysList->getWorkingDayRestType($department_id, $job_title_id);
        if (!empty($list)) {
            foreach ($list as $key => $value) {
                $working_day_rest_type_list[] = [
                    'key'   => $value,
                    'value' => $this->getTranslation()->_('working_day_rest_type_'.$value),
                ];
            }
        }
        return $working_day_rest_type_list;
    }

    /**
     * 员工轮休时给定的默认的休息列表
     */
    public function getDefaultRestDay ($lang = 'en'): array
    {
        $data = [];
        foreach (range(1,7) as $item) {
            $data[] =  ['name' => $this->getTranslation($lang)->_('default_rest_day_'.$item),'value' =>(string)$item];
        }
       return $data;
    }

    /**
     * 获取货币枚举
     * @return array
     */
    public function getCurrencyList()
    {
        $currencyList =  HrInterviewOfferModel::$currency_list;

        $returnData = [];

        foreach ($currencyList as $k => $v) {
            $returnData[] = [
                'key' => $k,
                'value' => self::$t->_($v.'_info'),
                'name' => self::$t->_($v),
            ];
        }

        return $returnData;
    }

    /**
     * 获取年级
     */
    public function getGradeList()
    {
        $gradeList =  enums::$gradeList;

        $returnData = [];

        foreach ($gradeList as $k => $v) {
            $returnData[] = [
                'key' => (string) $k,
                'value' => self::$t->_($v),
            ];
        }

        return $returnData;
    }

    /**
     * 获取学校列表
     * @return array
     */
    public function getSchoolList()
    {
        return HrSchoolModel::find([
            'columns'    => 'id,name',
            'conditions' => 'is_deleted = :is_deleted: ',
            'order'      => 'name ASC',
            'bind'       => [
                'is_deleted' => enums::IS_DELETED_NO,
            ],
        ])->toArray();
    }

    /**
     * 获取感兴趣的部门
     */
    public function getInterestedDepartmentList()
    {
        $data = HrInterestedDepartmentModel::find([
            'columns'    => 'id,name',
            'conditions' => 'is_deleted = :is_deleted: ',
            'bind'       => [
                'is_deleted' => enums::IS_DELETED_NO,
            ],
        ])->toArray();

        array_push($data, ['id' => HrInterestedDepartmentModel::DEPARTMENT_OTHER, 'name' => self::$t->_('interested_department_other')]);

        return $data;
    }

    /**
     * 获取h5参数列表
     */
    public function getH5SysList($typeCodeArr)
    {
        $returnData = ['data' => []];

        foreach ($typeCodeArr as $k => $v) {
            $data = [];
            if (method_exists($this, $v)) {

                $data = $this->$v();
            }

            $returnData['data'][$v] = $data;
        }

        return $this->checkReturn($returnData);
    }

    /**
     * @return array
     */
    public function getBankTypeList()
    {
        $bankIdEnvCode = 'by_staff_bank_id_list';
        //[1]获取银行枚举选项数据
        $bank_ids = (new SettingEnvServer())->getSetVal($bankIdEnvCode);
        $bank_list = [];
        if($bank_ids){
            $bank_list = BankListModel::find([
                'conditions' => 'bank_id in ({bank_ids:array})',
                'bind'=> ['bank_ids'=>explode(',',$bank_ids)],
                'columns' => "bank_id as value ,bank_name as label,max_length,min_length",
                'order'      => 'sort_num desc,bank_id desc',
            ])->toArray();
        }
        return $bank_list;
    }

    /**
     * @return array
     */
    public function getIndependentBankTypeList()
    {
        $bankIdEnvCode = 'by_staff_bank_id_list_independent';
        //[1]获取银行枚举选项数据
        $bank_ids = (new SettingEnvServer())->getSetVal($bankIdEnvCode);
        $bank_list = [];
        if($bank_ids){
            $bank_list = BankListModel::find([
                'conditions' => 'bank_id in ({bank_ids:array})',
                'bind'=> ['bank_ids'=>explode(',',$bank_ids)],
                'columns' => "bank_id as value ,bank_name as label,max_length,min_length",
                'order'      => 'sort_num desc,bank_id desc',
            ])->toArray();
        }
        return $bank_list;
    }

    /**
     * 获取招聘类型列表
     * @return array
     */
    public function getRecruitTypeList()
    {
        $recruitTypeList =  HrResumeModel::$recruitTypeList;

        $returnData = [];

        foreach ($recruitTypeList as $k => $v) {
            $returnData[] = [
                'key' => (string) $k,
                'value' => self::$t->_($v),
            ];
        }

        return $returnData;
    }

    /**
     * 获取员工状态
     */
    public function getStaffStateList()
    {
        $onJobStatus =  HrStaffInfoModel::$on_job_status;

        $returnData = [];

        foreach ($onJobStatus as $k => $v) {
            $returnData[] = [
                'key' => (string) $k,
                'name' => self::$t->_($v),
            ];
        }

        return $returnData;
    }

    /**
     * 获取员工状态
     */
    public function getStaffContractApproveStatus()
    {
        $staffContractApproveStatusList =  HrStaffRenewContractApplyModel::$status_list;

        $returnData = [];

        foreach ($staffContractApproveStatusList as $k => $v) {
            $returnData[] = [
                'key' => (string) $k,
                'name' => self::$t->_($v),
            ];
        }

        return $returnData;
    }

    /**
     * 合同状态
     */
    public function getStaffContractStatus()
    {
        //待添加、待发送、等待员工签字、员工已拒绝、合同生效中、待续约
        $staffContractStatusList = [
            enums::CONTRACT_STATUS_ADD,
            enums::CONTRACT_STATUS_SEND,
            enums::CONTRACT_STATUS_SIGNATURE,
            enums::CONTRACT_STATUS_REFUSE,   //员工已拒绝
            enums::CONTRACT_STATUS_UNSIGNED, //员工未签字
            enums::CONTRACT_STATUS_TAKE,     //合同生效中
            enums::CONTRACT_STATUS_RDNEWAL,
            enums::CONTRACT_STATUS_EXPIRES,
        ];

        $returnData = [];

        foreach ($staffContractStatusList as $v) {
            $returnData[] = [
                'key' => (string) $v,
                'name' => self::$t->_('contract_status_'.$v),
            ];
        }

        return $returnData;
    }

    /**
     * @description 获取国籍/地区
     * @return array|mixed
     * @throws \Exception
     */
    public function getNationalityRegion()
    {
        return $this->getRegionByDictCode(['dict_code' => 'nationality_region']);
    }

    /**
     * @description 获取住址/地区
     * @return array|mixed
     * @throws \Exception
     */
    public function getAddressCountryRegion()
    {
        return $this->getRegionByDictCode(['dict_code' => 'address_country_region']);
    }

    /**
     * @description 获取工作国家
     * @return array|mixed
     * @throws \Exception
     */
    public function getWorkingCountry()
    {
        return $this->getRegionByDictCode(['dict_code' => 'working_country']);
    }

    /**
     * @description 获取外协黑名单原因
     * @return array|mixed
     * @throws \Exception
     */
    public function getOsBlackReason()
    {
        return $this->getRegionByDictCode(['dict_code' => 'os_black_reason']);
    }

    /**
     * @description 发送请求
     * @param $params
     * @return array|mixed
     * @throws \Exception
     */
    private function getRegionByDictCode($params)
    {
        $apiClient = new ApiClient('hcm_rpc_endpoint', '', 'getDictionaryByDictCode', $this->lang);
        $apiClient->setParams($params);
        $result = $apiClient->execute();

        $this->getDI()->get("logger")->write_log("getRegionByDictCode result: " . json_encode($result, JSON_UNESCAPED_UNICODE), "info");
        if ($result['result'] && $result['result']['code'] != 1) {
            throw new \Exception($result['result']['msg'],100);
        }

        $data = [];
        foreach ($result['result']['data'] as $item) {
            $data[] = [
                'key'   => $item['value'],
                'name'  => $item['label'],
            ];
        }

        return $data ?? [];
    }

    /**
     * @description 获取全部字典值
     * @param $params
     * @return array|mixed
     * @throws \Exception
     */
    public function getTotalDictionaryRegionByDictCode($params,$lang='')
    {
        $apiClient = new ApiClient('hcm_rpc_endpoint', '', 'getTotalDictionaryItemsByDictCode', $lang?:$this->lang);
        $apiClient->setParams($params);
        $result = $apiClient->execute();

        $this->getDI()->get("logger")->write_log("getTotalDictionaryItemsByDictCode result: " . json_encode($result, JSON_UNESCAPED_UNICODE), "info");
        if ($result['result'] && $result['result']['code'] != 1) {
            throw new \Exception($result['result']['msg'],100);
        }

        return $result['result']['data'] ?? [];
    }

    /**
     * 简历最新操作人筛选枚举
     * @return array[]
     */
    public function getLastOperatorQueryType(): array
    {
        //简历管理、面试管理 last_operator_query_type
        return [
            [
                'key'  => '0',
                'name' => 'All',
            ],
            [
                'key'  => '1',
                'name' => self::$t->_('last_operator_query_type_1'),
            ],
            [
                'key'  => '2',
                'name' => self::$t->_('last_operator_query_type_2'),
            ],
        ];
    }

    /**
     * 简历筛选取消原因
     */
    public function getResumeFilterCancelTypeList()
    {
        $returnData = [];

        foreach (HrResumeFilterModel::$cancel_type_list as $k => $v) {
            $returnData[] = [
                'key' => (string) $k,
                'name' => self::$t->_($v),
            ];
        }

        return $returnData;
    }

    /**
     * 简历类型下拉
     * @return array
     */
    public function reserveTypeList($lang = '')
    {
        return [
            [
                'key'  => '0',
                'name' => 'All',
            ],
            [
                'key'  => '1',
                'name' => $this->getTranslationByLang($lang)->_('reserve_type_1'),
            ],
            [
                'key'  => '2',
                'name' => $this->getTranslationByLang($lang)->_('reserve_type_2'),
            ],
        ];
    }

    /**
     * 获取招聘总部职位
     * @return mixed
     */
    public function getJointlyRecruitedIdList()
    {
        return (new SettingEnvServer())->setExpire(30)->getSetValToArrayFromCache('jointly_recruited_ids');
    }

    /**
     * 获取招聘总部岗位
     * @return mixed
     */
    public function getJointlyRecruitedJdIdList()
    {
        return (new SettingEnvServer())->setExpire(30)->getSetValToArrayFromCache('jointly_recruited_jdids');
    }

    /**
     * 获取可以更换hc的职位和名称
     * @return array
     */
    public function getUpdateResumeHcJobList()
    {
        $jobIds = (new SettingEnvServer())->setExpire(30)->getSetValToArrayFromCache('replace_hc_jobids');

        $returnData = [];

        if (empty($jobIds)) {
            return $returnData;
        }

        $positionList = (new HrJobTitleRepository())->getJobTitleList(['id', 'job_name'], ['id IN ({id:array})'], ['id' => $jobIds]);

        foreach ($positionList as $value) {
            $returnData[] = [
                'job_title_id'   => $value['id'],
                'job_title_name' => $value['job_name'],
            ];
        }

        return $returnData;
    }

    /**
     * 获取省市区信息
     * @return array
     */
    public function getProvinceCityDistrict(): array
    {
        $cityList = SysCityModel::find([
            'columns'    => "code as city_code,name,province_code",
            'conditions' => "deleted = 0",
            'order'      => "CONVERT(name USING gbk) asc",
        ])->toArray();


        if (isCountry('MY')) {
            $cityList = array_column($cityList, null, 'city_code');

            $districtList = SysDistrictModel::find([
                'columns'    => "code as district_code,name,city_code",
                'conditions' => "deleted = 0",
                'order'      => "CONVERT(name USING gbk) asc",
            ])->toArray();

            foreach ($districtList as $v) {
                if (!empty($cityList[$v['city_code']])) {
                    $cityList[$v['city_code']]['district_list'][] = $v;
                }
            }

            $cityList = array_values($cityList);
        }

        $provinceList = SysProvinceModel::find([
            'columns'    => "code as province_code,name",
            'conditions' => "deleted = 0",
            'order'      => "CONVERT(name USING gbk) asc",
        ])->toArray();

        $provinceList = array_column($provinceList, null, 'province_code');

        foreach ($cityList as $vv) {
            if (!empty($provinceList[$vv['province_code']])) {
                $provinceList[$vv['province_code']]['city_list'][] = $vv;
            }
        }

        return array_values($provinceList);
    }

    /**
     * 投递状态
     * @return array
     */
    public function deliveryStatusList(): array
    {
        $returnData = [];

        foreach (HrDeliveryModel::$status_list as $k => $v) {
            $returnData[] = [
                'key' => (int) $k,
                'name' => $this->getTranslation()->_($v),
            ];
        }

        return $returnData;
    }

    /**
     * 备份简历状态
     * @return array
     */
    public function resumeBackupStatusList(): array
    {
        $returnData = [];

        foreach (HrResumeBackupModel::$status_list as $k => $v) {
            $returnData[] = [
                'key' => (int) $k,
                'name' => $this->getTranslation()->_($v),
            ];
        }

        return $returnData;
    }

    /**
     * 简历来源状态
     * @return array
     */
    public function getResumeAiScoreTypeList(): array
    {
        $returnData = [];

        foreach (HrResumeAiScoreModel::$scoreInterval as $k => $v) {
            $returnData[] = [
                'key' => (string) $k,
                'name' =>$this->getTranslation()->_($v['name']),
            ];
        }

        return $returnData;
    }

    /**
     * 简历来源状态
     * @return array
     */
    public function resumeSourceList(): array
    {
        $returnData = [];

        foreach (HrResumeModel::$source_enums as $k => $v) {
            if ($k == HrResumeModel::SOURCE_BUDDY) {
                continue;
            }

            $returnData[] = [
                'key' => (int) $k,
                'name' =>$this->getTranslation()->_('resume_src_'.$k),
            ];
        }

        return $returnData;
    }

    /**
     * 简历来源状态
     * @return bool
     */
    public function checkPriorityUrgent(): bool
    {
        if (!isCountry('MY')) {
            return false;
        }

        //获取编辑hc优先级员工
        $editHcPriorityStaff = (new SettingEnvServer())->getSetValToArray('edit_hc_priority_staff');

        //非编辑hc优先级员工，且员工存在，返回true
        if ($this->userInfo['id'] && in_array($this->userInfo['id'],$editHcPriorityStaff)) {
            return true;
        }

        return false;
    }
    /**
     * 身份证确认状态
     * @return array
    */
    public function identityStatusList(): array
    {
        $returnData = [];

        if(isCountry('PH')) {
            unset(HrResumeModel::$identity_validate_status_text[HrResumeModel::IDENTITY_VALIDATE_STATUS_UNTREATED]);
        }

        foreach (HrResumeModel::$identity_validate_status_text as $k => $v) {
            $returnData[] = [
                'key' => (int) $k,
                'name' => self::$t->_($v),
            ];
        }

        return $returnData;
    }
}