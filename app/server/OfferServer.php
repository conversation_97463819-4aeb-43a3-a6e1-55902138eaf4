<?php

namespace FlashExpress\bi\App\Server;

use App\Library\Enums\GlobalEnums;
use App\Modules\LearningPlan\Models\LearningPlanModel;
use App\Modules\Organization\Models\DepartmentModel;
use FlashExpress\bi\App\Helper\OfferHelper;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\enumsTh;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\library\Tools;
use FlashExpress\bi\App\Models\backyard\HireTypeImportListModel;
use FlashExpress\bi\App\Models\backyard\HrEntryModel;
use FlashExpress\bi\App\Models\backyard\HrInterviewOfferModel;
use FlashExpress\bi\App\Models\backyard\HrOfferSalaryOperatorLogModel;
use FlashExpress\bi\App\Models\backyard\HrResumeModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\HcModel;
use FlashExpress\bi\App\Modules\TH\Server\StaticSysServer;
use FlashExpress\bi\App\Repository\HcRepository;
use FlashExpress\bi\App\Repository\ResumeRepository;
use FlashExpress\bi\App\Repository\SysListRepository;
use Phalcon\DiInterface;

/**
 * offer服务
 */
class OfferServer extends BaseServer
{
    public function __construct($lang = 'en')
    {
        parent::__construct($lang);
    }

    /**
     * Offer列表
     */
    public function list($params, $isExport = false)
    {
        //定义分页熟悉
        $pageSize  = empty($params['page_size']) ? 20 : $params['page_size'];
        $pageNum   = empty($params['page_num']) ? 1 : $params['page_num'];
        $pageffset = $pageSize * ($pageNum - 1);

        //不是管理员 并且开启了菜单权限
        $is_menu_permission = SettingEnvServer::getMenuPermission('whr_offer_menu_list');
        $authoritySql       = '';
        if ($this->userInfo['is_admin'] == enums::$is_admin['off'] && $is_menu_permission) {
            $authoritySql = (new HcServer())->assembleAuthoritySql('', $this->userInfo);
        }

        //生成数据库model对象
        $builder = $this->modelsManager->createBuilder();

        //组装参数
        $builder->from(['offer' => HrInterviewOfferModel::class]);
        $builder->columns('count(1) as total');
        $builder->innerJoin(HrEntryModel::class, 'offer.id=entry.interview_offer_id ', 'entry');
        $builder->leftJoin(HcModel::class, 'offer.hc_id=hc.hc_id ', 'hc');
        $builder->leftJoin(HrResumeModel::class, 'offer.resume_id=resume.id ', 'resume');

        //1发送 2取消发送 ，3-offer签字撤销，4-薪资审批撤销，5-offer签字驳回
//        $builder->where('offer.status = :status:', ['status' => 1]);

        //添加where条件
        $builder = $this->formatWhere($builder, $params);

        //数据权限
        if ($authoritySql) {
            $builder->andWhere($authoritySql);
        }

        //设置排序
        $builder->orderBy('offer.id desc');

        $totalInfo = $builder->getQuery()->getSingleResult();
        $count     = $totalInfo->total;

        $columns = [
            'offer.resume_id as resume_id',
            'offer.hc_id as hc_id',
            'resume.name as name',
            'resume.phone as phone',
            'resume.resume_last_operator',
            'hc.hire_type',
            'hc.job_title as job_title_id',
            'hc.department_id as department_id',
            'hc.worknode_id as store_id',
            'offer.submitter_id as submitter_id',
            'offer.created_at as created_at',
            'offer.currency as currency',
            'offer.subsidy_type as subsidy_type',
            'offer.work_time as work_time', //预计入职日期
            'entry.entry_date as entry_date', //到岗日期
            'entry.status as entry_status', //入职状态
            'entry.staff_id as staff_info_id', //工号
        ];

        if ($isExport) {
            $columns = array_merge($columns, [
                'money',    //试用期工资
                'trial_salary',    //通过试用期工资
                'position',    //岗位津贴
                'exp',    //经验津贴
                'rental',    //租车津贴
                'food',    //餐补
                'dangerously',//危险区域津贴
            ]);

            $builder->columns($columns);
            $builder->limit(10000);
            $items = $builder->getQuery()->execute()->toArray();
            return $this->formatInfo($items);
        }

        $builder->limit($pageSize, $pageffset);
        $builder->columns($columns);

        //获取结果
        $items = $builder->getQuery()->execute()->toArray();

        //格式化输出
        $items = $this->formatInfo($items);

        $data['data'] = [
            'dataList'   => $items,
            'pagination' => [
                'count'     => (string)$count,
                'pageNum'   => $pageNum, //每页多少条
                'pageSize'  => $pageSize,  //当前页
                'pageCount' => (string)ceil($count / $pageSize),  //总页数
            ],
        ];

        return $this->checkReturn($data);
    }

    /**
     * 格式化offer返回数据
     * @param $data
     * @return mixed
     */
    public function formatInfo($data)
    {
        $sysListServer     = (new SysListServer());
        $sysListRepository = (new  SysListRepository());
        $resumeServer      = Tools::reBuildCountryInstance(new ResumeServer());
        $t                 = $this->getTranslation();
        $entry_status_text = [
            1 => $t->_('4903'),
            2 => $t->_('4904'),
            3 => $t->_('4905'),
        ];

        //查询用户operator_id
        $submitterIds                   = array_column($data, 'submitter_id');
        $resume_last_operator_staff_ids = array_column($data, 'resume_last_operator');

        $staffIds  = array_merge($submitterIds, $resume_last_operator_staff_ids);
        $staffIds  = array_values(array_unique($staffIds));
        $staffList = (new StaffServer())->getStaffListByIds($staffIds, [
            'staff_info_id',
            'name',
            'nick_name',
            'job_title',
        ]);

        $staffListKv = array_column($staffList, null, 'staff_info_id');


        //获取职位列表
        $jobTitleData = $sysListServer->getPositionList();
        $jobTitleListKv = array_column($jobTitleData, 'job_name', 'id');

        //查询部门表数据
        $departmentData   = $sysListRepository->getDepartmentList();
        $departmentListKv   = array_column($departmentData, 'name', 'id');

        //查询网点数据
        $storeData = [];
        $storeIds = array_values(array_unique(array_column($data, 'store_id')));
        if ($storeIds) {
            $storeIds    = getIdsStr($storeIds);
            $storeData = $sysListRepository->getStoreList(['ids' => $storeIds]);
            $storeData = array_column($storeData, null, 'id');
        }

        foreach ($data as & $v) {
            $v['created_at']        = show_time_zone($v['created_at']);
            $v['work_time']         = OfferHelper::formatWorkTime($v['work_time']);
            $v['entry_date']        = OfferHelper::formatWorkTime($v['entry_date']);
            $v['entry_status_name'] = $entry_status_text[$v['entry_status']] ?? '';
            $v['staff_info_id']     = $v['staff_info_id'] ?? '';

            //职位名称
            $v['job_title_name']   = $jobTitleListKv[$v['job_title_id']] ?? '';

            //部门名称
            $v['department_name']   = $departmentListKv[$v['department_id']] ?? '';

            //雇佣类型
            $v['hire_type_text'] = $v['hire_type'] ? $t->_('hire_type_' . $v['hire_type']) : '';

            //网点信息
            $storeInfo = $storeData[$v['store_id']] ?? [];

            //网点名称
            $v['store_name']   = $storeInfo['name'] ?? '';

            //所属区域
            $v['sorting_no'] = $resumeServer->getSortingNoByStoreInfo($storeInfo);

            //最新操作人职位
            $resumeLastOperatorInfo                   = $staffListKv[$v['resume_last_operator']] ?? [];
            $v['resume_last_operator_name']           = StaffServer::getStaffNameView($resumeLastOperatorInfo, 2);
            $v['resume_last_operator_job_title_name'] = $jobTitleListKv[$resumeLastOperatorInfo['job_title']] ?? '';

            //offer创建人职位
            $submitterInfo                      = $staffListKv[$v['submitter_id']] ?? [];
            $v['submitter_name']                = StaffServer::getStaffNameView($submitterInfo, 2);
            $v['submitter_name_job_title_name'] = $jobTitleListKv[$submitterInfo['job_title']] ?? '';
        }

        return $data;
    }

    public function formatWhere($builder, $params)
    {
        //应聘者姓名/手机号
        if (!empty($params['keyword'])) {
            $builder->andWhere("resume.name LIKE :keyword: OR resume.phone LIKE :keyword:",
                ['keyword' => "%{$params['keyword']}%"]);
        }

        //简历最新操作人查询
        switch (intval($params['last_operator_query_type'])) {
            case 0://ALL
                if (!empty($params['resume_last_operator']) && $params['resume_last_operator'] > 0) {
                    $builder->andWhere("resume.resume_last_operator = :resume_last_operator_staff_id:",
                        ["resume_last_operator_staff_id" => intval($params['resume_last_operator'])]);
                }
                break;
            case 1://为空
                $builder->andWhere("resume.resume_last_operator = :resume_last_operator:",
                    ["resume_last_operator" => 0]);
                break;
            case 2://不为空
                $builder->andWhere("resume.resume_last_operator > :resume_last_operator:",
                    ["resume_last_operator" => 0]);

                if (!empty($params['resume_last_operator']) && $params['resume_last_operator'] > 0) {
                    $builder->andWhere("resume.resume_last_operator = :resume_last_operator_staff_id:",
                        ["resume_last_operator_staff_id" => intval($params['resume_last_operator'])]);
                }
                break;
        }

        //HCID
        if (!empty($params['hc_id'])) {
            $builder->andWhere("offer.hc_id = :hc_id:", ['hc_id' => $params['hc_id']]);
        }

        //CVID
        if (!empty($params['resume_id'])) {
            $builder->andWhere("offer.resume_id = :resume_id:", ['resume_id' => $params['resume_id']]);
        }

        if (!empty($params['staff_info_id'])) {
            $builder->andWhere("entry.staff_id = :staff_info_id:", ['staff_info_id' => $params['staff_info_id']]);
        }

//        if (!empty($params['department_id'])) {
//            $builder->andWhere("hc.department_id = :department_id:", ['department_id' => $params['department_id']]);
//        }
        if (!empty($params['department_id'])) {
            $deptIds = SysServer::getDepartmentConditionByParams($params);
            if (!empty($deptIds)) {
                $builder->inWhere('hc.department_id', $deptIds);
            }
        }

        //职位搜索
        if (!empty($params['job_title'])) {
            if (is_array($params['job_title'])) {
                $builder->andWhere("hc.job_title IN ({job_title:array}) ", ['job_title' => array_values($params['job_title'])]);
            } else {
                $builder->andWhere("hc.job_title = :job_title:", ['job_title' => $params['job_title']]);
            }
        }

        if (!empty($params['worknode_id'])) {
            $builder->andWhere("hc.worknode_id = :worknode_id:", ['worknode_id' => $params['worknode_id']]);
        }

        //submitter_id
        if (!empty($params['submitter_id'])) {
            $builder->leftJoin(HrStaffInfoModel::class, 'offer.submitter_id=staff.staff_info_id ', 'staff');
            $builder->andWhere("staff.staff_info_id LIKE :submitter_id: OR staff.name LIKE :submitter_id:",
                ['submitter_id' => "%{$params['submitter_id']}%"]);
        }

        //offer创建时间-开始
        if (!empty($params['start_created_at'])) {
            $builder->andWhere("offer.created_at >= :start_created_at:",
                ['start_created_at' => zero_time_zone($params['start_created_at'].' 00:00:00')]);
        }

        //offer创建时间-结束
        if (!empty($params['end_created_at'])) {
            $builder->andWhere("offer.created_at <= :end_created_at:",
                ['end_created_at' => zero_time_zone($params['end_created_at'].' 23:59:59')]);
        }

        //预计入职时间
        if (!empty($params['start_work_time'])) {
            $builder->andWhere("offer.work_time >= :start_work_time:",
                ['start_work_time' => $params['start_work_time'].' 00:00:00']);
        }

        //预计入职时间
        if (!empty($params['end_work_time'])) {
            $builder->andWhere("offer.work_time <= :end_work_time:",
                ['end_work_time' => $params['end_work_time'].' 23:59:59']);
        }

        //到岗时间
        if (!empty($params['start_entry_date'])) {
            $builder->andWhere("entry.entry_date >= :start_entry_date:",
                ['start_entry_date' => $params['start_entry_date'].' 00:00:00']);
        }

        //到岗时间
        if (!empty($params['end_entry_date'])) {
            $builder->andWhere("entry.entry_date <= :end_entry_date:",
                ['end_entry_date' => $params['end_entry_date'].' 23:59:59']);
        }

        //入职状态
        if (!empty($params['entry_status'])) {
            $builder->andWhere("entry.status = :entry_status:", ['entry_status' => $params['entry_status']]);
        }

        if (!empty($params['hire_type'])) {
            $builder->andWhere("hc.hire_type IN ({hire_type:array})", ['hire_type' => array_values($params['hire_type'])]);
        }

        return $builder;
    }

    /**
     * 保存logService
     */
    public function addOfferSalaryOperatorLog($params)
    {
        //进行验证
        $params['money'] = OfferHelper::checkOfferSalary($params['money']);

        //进行验证
        $params['trial_salary'] = OfferHelper::checkOfferSalary($params['trial_salary']);


        //进行保存
        if (isset($params['is_default']) && $params['is_default']) {
            if (empty($params['resume_id'])) {
                return false;
            }

            //查询或者更新
            $logInfo = HrOfferSalaryOperatorLogModel::findFirst([
                'conditions' => "resume_id = :resume_id: AND is_default=:is_default:",
                'bind'       => [
                    'resume_id'  => $params['resume_id'],
                    'is_default' => 1,
                ],
            ]);
        } else {
            if (empty($params['interview_offer_id'])) {
                return false;
            }
        }

        if (empty($logInfo)) {
            $logInfo = new HrOfferSalaryOperatorLogModel();
        }

        $logInfo->interview_offer_id = $params['interview_offer_id'] ?? 0;
        $logInfo->resume_id          = $params['resume_id'] ?? 0;

        //money
        $logInfo->money        = !empty($params['money']) ? json_encode($params['money']) : '';
        $logInfo->trial_salary = !empty($params['trial_salary']) ? json_encode($params['trial_salary']) : '';


        if (!empty($params['is_default'])) {
            $logInfo->is_default  = 1;
            $logInfo->operator_id = 0;
        } else {
            $logInfo->is_default  = 0;
            $logInfo->operator_id = !empty($params['operator_id']) ? $params['operator_id'] : $this->userInfo['id'];
        }

        $logInfo->save();

        return $this->checkReturn(1);
    }

    /**
     * 获取操作日志
     * @param $offerId
     */
    public function getOfferSalaryOperatorLogList($interviewOfferId, $resumeId , $paramIn = [])
    {
        $offerlist = HrOfferSalaryOperatorLogModel::find([
            'conditions' => "interview_offer_id = :interview_offer_id:",
            'bind'       => ['interview_offer_id' => $interviewOfferId],
            'columns'    => 'interview_offer_id,money,trial_salary,operator_id,created_at',
            'order'      => 'id DESC',
        ])->toArray();

        //查询初始默认值
        $defaultlist = HrOfferSalaryOperatorLogModel::find([
            'conditions' => "resume_id = :resume_id: AND is_default = :is_default:",
            'bind'       => [
                'resume_id'  => $resumeId,
                'is_default' => 1,
            ],
            'columns'    => 'interview_offer_id,resume_id,money,trial_salary,operator_id,updated_at as created_at',
        ])->toArray();

        $list = array_merge($offerlist, $defaultlist);

        $returnData = [];

        // 获取各项薪资单位
        $staticServer = new StaticSysServer();
        $wageSubsidyList       = $staticServer->getWageSubsidyList(['resume_id'=>$resumeId]);
        $resumeHcData = (new ResumeServer())->getResumeHcInterviewData($resumeId);
        $offer_salary_unit_arr = [];
        foreach ($wageSubsidyList as $k => $v) {
            $offer_salary_unit_arr = array_merge($offer_salary_unit_arr, $v["data"]);
        }
        $offer_salary_unit_arr = array_column($offer_salary_unit_arr,"unit","key");

        //查看用户
        $staffIds  = array_values(array_filter(array_column($list, 'operator_id')));
        $staffList = (new StaffServer())->getStaffListByIds($staffIds);
        $staffList = array_column($staffList, null, 'staff_info_id');

        foreach ($list as & $v) {
            $info  = [];
            $money = json_decode($v['money'], true);

            $trialSalary = [];
            if ($v['trial_salary']) {
                $trialSalary = json_decode($v['trial_salary'], true);
            }

            if ($money) {

                //泰国单独处理
                if (isCountry('th')) {
                    if (isset($money['currency']) && $money['currency'] == HrInterviewOfferModel::CURRENCY_SGD) {
                        $compnay = 'SGD';
                        $wageSubsidyList       = $staticServer->getWageSubsidyList(['resume_id'=>$resumeId], $compnay);
                        $offer_salary_unit_arr = [];
                        foreach ($wageSubsidyList as $ks => $vs) {
                            $offer_salary_unit_arr = array_merge($offer_salary_unit_arr, $vs["data"]);
                        }
                        $offer_salary_unit_arr = array_column($offer_salary_unit_arr, "unit", "key");

                        if (!empty($paramIn['is_th_fulfillment_internship'])) {
                            $offer_salary_unit_arr['renting'] = 'SGD/Day';
                        }
                    } else {
                        $compnay = 'THB';

                        $wageSubsidyList       = $staticServer->getWageSubsidyList(['resume_id'=>$resumeId], $compnay);
                        $offer_salary_unit_arr = [];
                        foreach ($wageSubsidyList as $ks => $vs) {
                            $offer_salary_unit_arr = array_merge($offer_salary_unit_arr, $vs["data"]);
                        }
                        $offer_salary_unit_arr = array_column($offer_salary_unit_arr,"unit","key");
;
                        if (!empty($paramIn['is_th_fulfillment_internship'])) {
                            $offer_salary_unit_arr['renting'] = 'THB/Day';
                        }
                    }
                }
                foreach ($money as $key => $value) {
                    if (isset($trialSalary[$key])) {
                        $info[$key]["value"] = $value.'/'.$trialSalary[$key];
                    } else {
                        $info[$key]["value"] = $value;
                    }
                    if (isCountry('TH') && $resumeHcData['hire_type'] == HrStaffInfoModel::HIRE_TYPE_DAY){
                        $_unit = "{$compnay}/Day";
                    }else{
                        $_unit = "{$compnay}/Month";
                    }
                    $info[$key]["unit"]  = $offer_salary_unit_arr[$key] ?? $_unit;
                }
            }

            if (empty($v['operator_id'])) {
                $info['operator_id']   = (string) env('system_operator', 10000);
                $info['operator_name'] = $this->getTranslation()->_('contract_sys');
            } else {
                $staffinfo             = $staffList[$v['operator_id']] ?? [];
                $info['operator_name'] = StaffServer::getStaffNameView($staffinfo, 2);
                $info['operator_id']   = $v['operator_id'];
            }

            $info['created_at']  = show_time_zone($v['created_at']);

            $returnData[] = $info;
        }

        return $returnData;
    }

    /**
     * 获取offer默认配置项
     * @param $params
     * @return array
     * @throws ValidationException
     */
    public function getDefaultSetting($params)
    {
        $resumeId = $params['resume_id'] ?? 0;

        //查询简历信息
        $resumeInfo = (new ResumeRepository())->getResumeInfo(['id' => $resumeId]);

        if (empty($resumeInfo)) {
            throw new ValidationException(self::$t->_('operation_data_not_exist'));
        }

        $returnData = [];

        $returnData['company'] = $this->getDefaultCompany($resumeInfo['hc_id'] ?? 0);

        return $this->checkReturn(['data' => $returnData]);
    }

    /**
     * @param $hcId
     */
    public function getDefaultCompany($hcId)
    {
        return '';
    }

    /**
     * @description 获取发送offer短信模版
     * @param $short_url
     * @return string
     */
    public function getSendOfferSmsTemplate($short_url): string
    {
        if (isCountry('TH')) {
            $template = <<<EOF
SMS ยืนยันการรับจ้างขนส่งพัสดุรายชิ้น\r\n
ยินดีต้อนรับท่านเข้าสู่ครอบครัวแฟลช โฮม โอเปอร์เรชั่น จำกัด\r\n
กรุณาตรวจสอบรายละเอียดตามลิงก์ %s\r\n
EOF;
        } else if (isCountry('PH')) {
            $template = <<<EOF
Confirmation Message\r\n
Please find the IC details from %s. Thank you.\r\n
EOF;
        } else {
            $template = <<<EOF
Thank you for applying to become an independent contractor with Flash Express. Please find the confirmation details from %s\r\n
Thank you and we are looking forward to meeting you soon.\r\n
EOF;
        }

        return sprintf($template, $short_url);
    }

    /**
     * @description 获取发送offer详细内容
     * @param array $params
     * @return string
     */
    public function getSendOfferMessage($params = [])
    {
        if (isCountry('TH')) {
            return $this->getThSendOfferMessage($params);
        } else if (isCountry('PH')) {
            return $this->getPHSendOfferMessage($params);
        } else {
            return $this->getMySendOfferMessage($params);
        }
    }

    private function getThSendOfferMessage($params)
    {
        $template = <<<EOF
SMS บริษัท แฟลช โฮม โอเปอร์เรชั่น จำกัด  ยืนยันการรับจ้างขนส่งพัสดุรายชิ้น/r/n
บริษัทฯ ขอนัดหมายคุณ : %s/r/n
เข้ารับพัสดุที่สาขา : %s/r/n
ในวันที่ : %s เวลา : 8:00 น. /r/n
ประเภท : ผู้รับจ้างขนส่งพัสดุรายชิ้น(%s)/r/n
ติดต่อผู้รับผิดชอบสาขา : %s/r/n
เบอร์โทรผู้ประสานงาน : %s/r/n
EOF;

        //短信内容 eg
        //SMS บริษัท แฟลช โฮม โอเปอร์เรชั่น จำกัด  ยืนยันการรับจ้างขนส่งพัสดุรายชิ้น
        //บริษัทฯ ขอนัดหมายคุณ : พิพัฒน์ งามนัก员工姓名
        //เข้ารับพัสดุที่สาขา :3MGO_CDC-เมืองเก่า HC工作地点
        //ในวันที่ : 2024/01/03 เวลา : 8:00 น. 导入excel的预计到岗日期
        //ประเภท : ผู้รับจ้างขนส่งพัสดุรายชิ้น(Bike Courier) HC职位名称
        //ติดต่อผู้รับผิดชอบสาขา :นาย ขจรเกียรติ ทองแพง(0859282484) 工作地点的网点负责人姓名(电话)
        //เบอร์โทรผู้ประสานงาน :นางสาว นัยนา กองฉลาด(0954810869) offer发送人姓名(电话)导入excel操作人姓名（电话）

        return sprintf($template, $params['staff_name'],
            $params['store_name'],
            $params['expected_entry_date'],
            $params['job_title_name'],
            $params['store_manager_info'],
            $params['operator_name']
        );
    }

    private function getMySendOfferMessage($params)
    {
        if ($params['data_type'] == HireTypeImportListModel::DATA_TYPE_LNT) {
            $template = <<<EOF
We are pleased to offer you the position of %s at LNT Express. You will be assigned to provide service to the following client:/r/n/r/n
Client:Flash Malaysia Express Sdn Bhd/r/n
Location:%s/r/n
Start Date:%s/r/n
Client contact:%s/r/nr/n
Thank you .
EOF;
            return sprintf($template, $params['job_title_name'],
                $params['store_name'],
                $params['expected_entry_date'],
                $params['store_manager_mobile']
            );
        }

        $template = <<<EOF
Hi %s/r/n
/r/n
Thank you for applying to become an independent contractor with Flash Express./r/n
/r/n
We would like to invite you to come to our service point to sign the independent contractor service contract to start service :/r/n
Location: %s/r/n
Date: %s/r/n
Contact Person: %s/r/n
Contact No.:%s/r/n
/r/n
We look forward to seeing you there. Otherwise, we'll assume you're no longer interested to proceed further./r/n
/r/n
Have a nice day!/r/n
EOF;
        return sprintf($template, $params['staff_name'],
            $params['store_name'],
            $params['expected_entry_date'],
            $params['store_manager_name'],
            $params['store_manager_mobile']
        );
    }

    private function getPHSendOfferMessage($params)
    {
        $template = <<<EOF
  Welcome to Flash Express!/r/n
  Recommended Revision: Hi %s! We are happy to welcome you as a contractor of Flash Express!/r/n
  The details of your assignment are as follows: /r/n
  Delivery Date: %s at 9:00 AM/r/n
  Role: Independent Contractor/r/n
  Location: %s/r/n
  Contact Persons: %s (Contact No.: %s)/r/n
  /r/n
  Good luck!/r/n
EOF;
        return sprintf($template, $params['staff_name'],
            $params['expected_entry_date'],
            $params['store_name'],
            $params['store_manager_name'],
            $params['store_manager_mobile']
        );
    }

    /**
     * 获取公共业务配置
     * @param $params
     * @return array
     */
    public function getCommonBusiness($params): array
    {
        $data = [
            'is_project_num' => $this->isProjectNum($params['resume_id'] ?? 0,$params['job_title_id'] ?? 0)
        ];

        return $this->checkReturn(['data' => $data]);
    }

    /**
     * 获取是否展示电车期数
     * @param $resumeId
     * @param $jobTitleId
     * @return int
     */
    public function isProjectNum($resumeId, $jobTitleId): int
    {
        if (!isCountry('TH') || !$resumeId || !$jobTitleId) {
            return 0;
        }

        //电车和小黄车职位有项目期数
        if (!in_array($jobTitleId,[enumsTh::JOB_TITLE_EV_COURIER,enumsTh::JOB_TITLE_VAN_COURIER_PROJECT])) {
            return 0;
        }

        //获取车辆信息
        $ebilityInfo = (new ResumeRepository())->ebilityInfo($resumeId);

        if (empty($ebilityInfo)) {
            return 0;
        }

        if ($ebilityInfo['car_owner'] == enums::$car_own_type['company_car']) {
            return 1;
        }

        return 0;
    }
}