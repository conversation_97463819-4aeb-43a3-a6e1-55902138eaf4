<?php
namespace FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\library\Excel;
use FlashExpress\bi\App\library\Tools;
use FlashExpress\bi\App\Server\AddEntryServer;
use FlashExpress\bi\App\Server\EntryServer;
use FlashExpress\bi\App\Server\HcServer;
use Exception;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\Server\ResumeServer;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;

class EntryController extends Controllers\ControllerBase
{
    protected $server;

    public function initialize()
    {
        parent::initialize();
        $this->server        = ['entry' => new EntryServer(),
                                'hc'    => new HcServer()];
        $this->department_id = 7;
        $this->CEO           = $this->config->hr->ceo;
        $this->COO           = $this->config->hr->coo;
        $this->HRM           = $this->config->hr->hrm;
        $this->biUrl         = $this->config->hr->bi_url;
        $this->paramIn       = $this->request->getPost();
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
    }

    public function onConstruct()
    {
        parent::onConstruct();
    }

    /**
     * 入职列表+搜索
     * @Access  public
     * @Param   request
     * @Return  jsonData
     * @Permission(action='Entry.entryList')
     */
    public function entryListAction()
    {
        $paramIn             = $this->paramIn;
        $paramIn['ceo']      = $this->CEO;
        $paramIn['coo']      = $this->COO;
        $paramIn['hrm']      = $this->HRM;
        $paramIn['userinfo'] = $this->userinfo;
    
        /* 检查自己权限 */
        $rolePermissions = $this->server['hc']->checkRolePermissions($this->userinfo);
        if ($rolePermissions['hcBase'] != 1) {
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('4009')));
        }
        //获取数据权限
        $is_admin = $this->userinfo['is_admin'];   //1是超管
        $authority_stores_ids=$this->userinfo['permission_stores_ids'];   //网点权限
        $authority_department_ids = $this->userinfo['permission_department_ids'];   //部门权限
    
    
        $paramIn['is_admin'] = $is_admin;   //1是超管
        $paramIn['authority_stores_ids']=$authority_stores_ids;
        $paramIn['authority_department_ids'] = $authority_department_ids;

        // 入参校验
        $validations = [];
        if(!empty($paramIn['resume_last_operator'])){
            $validations['resume_last_operator'] = 'IntLe:99999999|>>>:'. $this->getTranslation()->_('input_staff_id_error');
        }
        $this->validateCheck($paramIn, $validations);

        $returnArr = (new EntryServer())->listEntry($paramIn);
        return $this->jsonReturn($returnArr);
    }

    /**
     * 入职列表导出
     * @Access  public
     * @Param   request
     * @Return  jsonData
     * @Permission(action='Entry.entryListExport')
     */
    public function entryListExportAction()
    {
        $this->getDI()->get('logger')->write_log("entryListExport paramIn:" . json_encode($this->paramIn,JSON_UNESCAPED_UNICODE), 'info');

        $paramIn             = $this->paramIn;
        $paramIn['ceo']      = $this->CEO;
        $paramIn['coo']      = $this->COO;
        $paramIn['hrm']      = $this->HRM;
        $paramIn['userinfo'] = $this->userinfo;
        $superAdministratorIds = env('superAdministratorIds',"");
        $superAdministratorIdsArr = explode(',', $superAdministratorIds);
        //超管账号，不添加数据权限过滤
        $is_superman = 0;
        if (in_array($this->userinfo['id'],$superAdministratorIdsArr)){
           $is_superman = 1;
        }
        /* 检查自己权限 */
        $rolePermissions = $this->server['hc']->checkRolePermissions($this->userinfo);
        if ($rolePermissions['hcBase'] != 1) {
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('4009')));
        }
        // 入参校验
        $validations = [];
        if(!empty($paramIn['resume_last_operator'])){
            $validations['resume_last_operator'] = 'IntLe:99999999|>>>:'. $this->getTranslation()->_('input_staff_id_error');
        }
        $this->validateCheck($paramIn, $validations);

        //是否有符合条件的待导出数据
        $is_have_data = 1;
        $returnArr   = (new EntryServer())->listEntryExport($paramIn,$is_have_data,$is_superman);
        //如果没有数据直接返回，提示无数据
        if(!$is_have_data){
            $returnData = [
                'code' => 1,
                'msg'  => '',
                'data' => [
                    'is_have_data'=>0,
                    'is_superman'=>$is_superman,
                    'file_url' => '',
                ],
            ];
            return $this->jsonReturn($returnData);
        }
        $fileName    = 'employeeList.' . time() . rand(1000, 9999);
        $field[] = 'CVID';//简历id
        $field[] = 'HC ID';//HC id
        $field[] = $this->getTranslation()->_('7021');//工号
        $field[] = $this->getTranslation()->_('8303');//称呼
        $field[] = $this->getTranslation()->_('8304');//名
        $field[] = $this->getTranslation()->_('8305');//姓
        $field[] = $this->getTranslation()->_('8306');//全名
        $field[] = $this->getTranslation()->_('7024');//英文名(员工信息)
        $field[] = $this->getTranslation()->_('7025');//手机号
        $field[] = $this->getTranslation()->_('7027');//个人邮箱
        $field[] = $this->getTranslation()->_('7044');//职位
        $field[] = $this->getTranslation()->_('hire_type');//雇佣类型
        $field[] = $this->getTranslation()->_('7043');//所属网点
        $field[] = $this->getTranslation()->_('8054');//出生日期
        $field[] = $this->getTranslation()->_('7045');//年龄
        $field[] = $this->getTranslation()->_('7052');//国籍
        $field[] = $this->getTranslation()->_('7053');//工作国家
        $field[] = $this->getTranslation()->_('7046');//工作城市
        $field[] = $this->getTranslation()->_('8801');//是否残疾
        $field[] = $this->getTranslation()->_('8802');//残疾证编号
        $field[] = $this->getTranslation()->_('7047');//入库时间
        $field[] = $this->getTranslation()->_('7034');//预计入职时间
        $field[] = $this->getTranslation()->_('7048');//入职时间
        $field[] = $this->getTranslation()->_('7049');//办理时间
//            $this->getTranslation()->_('7699'),//设备费
        if (isCountry('TH') || isCountry('PH') || isCountry('MY')) {
            $field[] = $this->getTranslation()->_('identity_confirmation_status'); //身份证确认状态
        }
        $field[] = $this->getTranslation()->_('7033');//状态

        //【员工信息】
        $field[] = $this->getTranslation()->_('7026');//身份证/护照(员工信息)
        $field[] = $this->getTranslation()->_('8307');//户口所在地
        $field[] = $this->getTranslation()->_('7029');//部门(员工信息)
        $field[] = $this->getTranslation()->_('department.group_ceo');
        $field[] = $this->getTranslation()->_('department.c_level');
        $field[] = $this->getTranslation()->_('department.bu');
        $field[] = $this->getTranslation()->_('department.level_1');
        $field[] = $this->getTranslation()->_('department.level_2');
        $field[] = $this->getTranslation()->_('department.level_3');
        $field[] = $this->getTranslation()->_('department.level_4');
        $field[] = $this->getTranslation()->_('7030');//职位(员工信息)
            //$this->getTranslation()->_('8200'),//职等
        $field[] = $this->getTranslation()->_('8201');//职级
        $field[] =  $this->getTranslation()->_('7031');//所属区域
        $field[] = $this->getTranslation()->_('7038');//所属网点(员工信息)
        $field[] = $this->getTranslation()->_('8202');//工作天数
        $field[] = $this->getTranslation()->_('7037');//银行卡号(员工信息)
        $field[] = $this->getTranslation()->_('7804');//基本薪资
        $field[] = $this->getTranslation()->_('7803');//通过试用期薪资
        $field[] = $this->getTranslation()->_('7702');//职位补贴
        $field[] = $this->getTranslation()->_('7703');//工作经验补贴
        $field[] = $this->getTranslation()->_('7708');//租车津贴
        $field[] = $this->getTranslation()->_('7704');//餐补
        $field[] = $this->getTranslation()->_('7705');//危险区域津贴
        $field[] = $this->getTranslation()->_('8300');//车辆类型
        $field[] = $this->getTranslation()->_('8301');//车牌号
        $field[] = $this->getTranslation()->_('8302');//上牌地点
        $field[] = $this->getTranslation()->_('car_engine_number');//发动机号

        if (isCountry('TH') || isCountry('PH') || isCountry('MY')) {
            $field[] = $this->getTranslation()->_('principal_staff_name'); //最新操作人
        }

        $header      = $field;
        $data        = $returnArr;
        $file_path = Excel::exportExcel($header, $data, 'entrylist' . time());
        $fileUrl = $file_path ? $file_path['object_url'] : '';
        $returnData = [
            'code' => 1,
            'msg'  => '',
            'data' => [
                'is_superman'=>$is_superman,
                'is_have_data'=>1,
                'file_url' => $fileUrl,
            ],
        ];
        return $this->jsonReturn($returnData);
    }


    /**
     * 办理入职
     * @Access  public
     * @Permission(action='Entry.entryAdd')
     */
    public function entryAddAction()
    {

        $paramIn             = $this->paramIn;
        $paramIn['ceo']      = $this->CEO;
        $paramIn['coo']      = $this->COO;
        $paramIn['hrm']      = $this->HRM;
        $paramIn['staff_id'] = $this->userinfo['id'];
        $paramIn['bi_url']   = $this->biUrl;

        $rolePermissions = $this->server['hc']->checkRolePermissions($this->userinfo);
        if ($rolePermissions['hcBase'] != 1) {
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('4009')));
        }

        //操作
        $returnArr = $this->atomicLock(function () use ($paramIn) {
            if ($paramIn['status'] == 1) {
                $returnArr = (new AddEntryServer())->addEntry($paramIn);
            } else {
                $returnArr = [
                    'code' => intval((new EntryServer())->cancelEntry($paramIn)),
                    'msg'  => '',
                    'data' => [],
                ];
            }
            return $returnArr;
        }, 'entryAddAction:' . $this->userinfo['staff_id'], 50);

        if ($returnArr === false) { //没有获取到锁
            $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('5202')));
        }

        return $this->jsonReturn($returnArr);
    }

    /**
     * 入职信息
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function entryInfoAction()
    {
        $paramIn             = $this->paramIn;
        $paramIn['ceo']      = $this->CEO;
        $paramIn['coo']      = $this->COO;
        $paramIn['hrm']      = $this->HRM;
        $paramIn['userinfo'] = $this->userinfo;
        $paramIn['bi_url']   = $this->biUrl;

        /* 检查自己是否有权限 人事=7 */
        $department      = [
            'department_id' => $this->department_id,
        ];
        $rolePermissions = $this->server['hc']->checkRolePermissions($this->userinfo);
        if ($rolePermissions['hcBase'] != 1) {
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('4009')));
        }
        $returnArr = (new EntryServer())->infoEntry($paramIn);
        return $this->jsonReturn($returnArr);
    }

    /**
     * 已发offer人员列表
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function offerStaffListAction()
    {
        $paramIn             = $this->paramIn;
        $paramIn['ceo']      = $this->CEO;
        $paramIn['coo']      = $this->COO;
        $paramIn['hrm']      = $this->HRM;
        $paramIn['userinfo'] = $this->userinfo;

        /* 检查自己权限 */
        $department      = [
            'department_id' => $this->department_id,
        ];
        $rolePermissions = $this->server['hc']->checkRolePermissions($this->userinfo);
        if ($rolePermissions['hcBase'] != 1) {
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('4009')));
        }

        $returnArr = $this->server['entry']->offerStaffList($paramIn);
        return $this->jsonReturn($returnArr);
    }

    /**
     * HIRS 部门&职位列表
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function sysinfoAction()
    {
        $paramIn             = $this->paramIn;
        $paramIn['userinfo'] = $this->userinfo;
        $paramIn['bi_url']   = $this->biUrl;
        $returnArr           = (new EntryServer())->sysinfo($paramIn);
        $data = \FlashExpress\bi\App\library\enums::$phone_area_code_enums;
        $returnArr['data']['phone_area_code_list'] = array_values($data);
        return $this->jsonReturn($returnArr);
    }

    /**
     * 根据部门、职位 获取 职级可选范围
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function level_gradeAction()
    {
        $validations = [
            'department_id' => 'Required|IntGt:0|>>>:' . $this->getTranslation()->_('miss_args'),
            'job_title_id'  => 'Required|IntGt:0|>>>:' . $this->getTranslation()->_('miss_args'),
        ];
        $this->validateCheck($this->paramIn, $validations);
        $returnArr           = (new EntryServer())->level_grade($this->paramIn);
        return $this->jsonReturn($returnArr);
    }

    /**
     * HIRS 部门&职位联动列表
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function managerAction()
    {
        $paramIn             = $this->paramIn;
        $paramIn['userinfo'] = $this->userinfo;
        $paramIn['bi_url']   = $this->biUrl;
        $returnArr           = $this->server['entry']->manager($paramIn);
        return $this->jsonReturn($returnArr);
    }

    /**
     * 填写入职信息
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function entryRecEditAction()
    {
        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['id'];
        $paramIn['userinfo'] = $this->userinfo;
        if($paramIn['mobile_area_code'] == 66 && strlen($paramIn['mobile']) !== 10){
            return $this->jsonReturn($this->checkReturn(-3, $this->getTranslation()->_('4117')));
        }
        /* 检查自己是否有权限 人事=7 */
        $department      = [
            'department_id' => $this->department_id,
        ];
        $rolePermissions = $this->server['hc']->checkRolePermissions($this->userinfo);
        if ($rolePermissions['hcBase'] != 1) {
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('4009')));
        }
        // 由于其他系统电话号是 10 位的 所以 将电话号补全 10 位
//            $mobileLen = strlen($paramIn['mobile']);
//            $defLen = 10;
//            if($mobileLen > $defLen){
//                $paramIn['mobile'] = substr($paramIn['mobile'],0,10);
//            } elseif($mobileLen < $defLen){
//                $paramIn['mobile'] = $paramIn['mobile'] . str_repeat('0',$defLen-$mobileLen);
//            }
        //残疾人字段信息验证
        $disability_info_error = (new EntryServer())->validate_disability_error($paramIn);
        if($disability_info_error){
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('miss_args')));
        }

        $returnArr = (new EntryServer())->entryRecEdit($paramIn);
        return $this->jsonReturn($returnArr);
    }

    /**
     * 获取入职信息
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function entryRecDetailAction()
    {
        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['id'];

        /* 检查自己是否有权限 人事=7 */
        $department      = [
            'department_id' => $this->department_id,
        ];
        $rolePermissions = $this->server['hc']->checkRolePermissions($this->userinfo);
        if ($rolePermissions['hcBase'] != 1) {
            return $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('4009')));
        }

        $returnArr = (new EntryServer())->entryRecDetail($paramIn);
        if(!isset($returnArr['mobile_area_code'])){
            $returnArr['mobile_area_code'] = '';
        }
        
        return $this->jsonReturn($returnArr);

    }
    /**
     * 获取办理入职配置信息
     * @return null
     * @throws ValidationException
     */
    public function entryConfigAction()
    {
        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['id'];
        $validations = [
            'entry_id' => 'Required|IntGt:0|>>>:' . $this->getTranslation()->_('miss_args'),
        ];
        $this->validateCheck($this->paramIn, $validations);
        $returnArr = (new EntryServer())->getEntryConfig($paramIn);
        return $this->jsonReturn($this->checkReturn(['data'=>$returnArr]));
    }

    public function checkCompanyEmailAction()
    {
        $paramIn             = $this->paramIn;
        $paramIn['staff_id'] = $this->userinfo['id'];
        $validations         = [
            'entry_id'        => 'Required|StrLenGe:1|>>>:entry_id param error',
            'department_id'   => 'Required|StrLenGe:1|>>>:department_id param error',
            'sys_store_id'    => 'Required|StrLenGe:1|>>>:sys_store_id param error',
            'job_title'       => 'Required|StrLenGe:1|>>>:job_title param error',
            'job_title_grade' => 'StrLenGe:0|>>>:job_title_grade param error',
            'role_id'         => 'ArrLenGe:0|>>>:role_id param error',
        ];
        $this->validateCheck($this->paramIn, $validations);
        $entryServer = Tools::reBuildCountryInstance(new EntryServer());
        $returnArr = $entryServer->checkCompanyEmail($paramIn);
        return $this->jsonReturn($this->checkReturn(['data' => $returnArr]));
    }
}
