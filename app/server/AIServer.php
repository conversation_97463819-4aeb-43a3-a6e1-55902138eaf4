<?php

namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\Models\backyard\HrJobTitleByModel;
use FlashExpress\bi\App\Models\backyard\SettingEnvModel;
use FlashExpress\bi\App\Models\backyard\SysDepartmentModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;

/**
 * 该类专用对接AI
 */
class AIServer extends BaseServer
{
    protected $params = [];


    public function __construct($lang = 'en')
    {
        parent::__construct($lang);
    }

    /**
 * 调用接口识别身份证
 * @param $id_card_url
 * @param $code
 */
    public function ai_id_card_ocr_post($id_card_url, $code = [
        'ai_id_card_ocr_domain',
        'ai_id_card_ocr_url',
        'ai_id_card_ocr_secret_id',
        'ai_id_card_ocr_secret_key',
    ])
    {
        if(empty($code)) {
            return false;
        }
        $this->getDI()->get('logger')->write_log('ai_id_card_ocr_post  参数：'.$id_card_url, 'info');
        $setting_env_info = SettingEnvModel::find([
            'conditions' => 'code in ({code:array})',
            'bind'       => [
                'code' => $code,
            ],
        ])->toArray();
        if (empty($setting_env_info)) {
            $this->getDI()->get('logger')->write_log('ai_id_card_ocr_post---未配置setting env');
            return false;
        }

        $setting_env_info = array_column($setting_env_info, 'set_val', 'code');

        $url        = $setting_env_info['ai_id_card_ocr_domain'].$setting_env_info['ai_id_card_ocr_url'];
        $secret_id  = $setting_env_info['ai_id_card_ocr_secret_id'];
        $secret_key = $setting_env_info['ai_id_card_ocr_secret_key'];

        $tmp       = intval(microtime(true) * 1000);
        $str_token = "POST"."\n".$setting_env_info['ai_id_card_ocr_url']."\n".$tmp."\n".$secret_id;
        $t         = hash_hmac("sha1", $str_token, $secret_key, true);
        $token     = "{$tmp}_{$secret_id}_".base64_encode($t);

        $headers = [
            'X-FLE-Token: '.$token,
            'X-FC-TIMESTAMP: '.time(),
            'Content-type: application/x-www-form-urlencoded',
        ];

        $post_date = 'url='.$id_card_url.'&return_idcard_type=true';
        $ai_result = httpPostFun($url, $post_date, $headers);
        $this->getDI()->get('logger')->write_log('ai_id_card_ocr_post url: '.$id_card_url.'结果:'.json_encode($ai_result), 'info');
        $res = json_decode($ai_result, true);
        $log = [
            'url'    => $url,
            'param'  => $post_date,
            'env'    => $setting_env_info,
            'result' => $res,
        ];
        $this->getDI()->get('logger')->write_log('ai_id_card_ocr_post 结果:'.json_encode($log), 'info');
        return $res;
    }

    /**
     * 调用接口识别身份证
     * @param $id_card_url
     * @param $code
     */
    public function ai_id_card_mosaic_post($data, $code = [
        'ai_id_card_ocr_domain',
        'ai_id_card_mosaic_url',
        'ai_id_card_ocr_secret_id',
        'ai_id_card_ocr_secret_key',
    ])
    {
        if(empty($code)) {
            return false;
        }
        //测试环境 端口 不一致
        if(RUNTIME == 'dev'){
            $code[] = 'ai_id_card_mosaic_domain';
        }

        $this->getDI()->get('logger')->write_log(['ai_id_card_mosaic_post' => $data['points']], 'info');
        $setting_env_info = SettingEnvModel::find([
            'conditions' => 'code in ({code:array})',
            'bind'       => [
                'code' => $code,
            ],
        ])->toArray();
        if (empty($setting_env_info)) {
            $this->getDI()->get('logger')->write_log('ai_id_card_mosaic_post---未配置setting env');
            return false;
        }

        $setting_env_info = array_column($setting_env_info, 'set_val', 'code');

        //测试环境 端口 不一致
        if(RUNTIME == 'dev'){
            $setting_env_info['ai_id_card_ocr_domain'] = $setting_env_info['ai_id_card_mosaic_domain'];
        }

        $url        = $setting_env_info['ai_id_card_ocr_domain'].$setting_env_info['ai_id_card_mosaic_url'];
        $secret_id  = $setting_env_info['ai_id_card_ocr_secret_id'];
        $secret_key = $setting_env_info['ai_id_card_ocr_secret_key'];

        $tmp       = intval(microtime(true) * 1000);
        $str_token = "POST"."\n".$setting_env_info['ai_id_card_mosaic_url']."\n".$tmp."\n".$secret_id;
        $t         = hash_hmac("sha1", $str_token, $secret_key, true);
        $token     = "{$tmp}_{$secret_id}_".base64_encode($t);

        $headers = [
            'X-FLE-Token: '.$token,
            'X-FC-TIMESTAMP: '.time(),
            'Content-type: application/json',
        ];


        $post_date = json_encode($data, JSON_UNESCAPED_UNICODE);
        $ai_result = httpPostFun($url, $post_date, $headers);
        $this->getDI()->get('logger')->write_log('ai_id_card_mosaic_url url: '.$post_date.'结果:'.json_encode($ai_result), 'info');
        $res = json_decode($ai_result, true);
        $log = [
            'url'    => $url,
            'param'  => $post_date,
            'env'    => $setting_env_info,
            'result' => $res,
        ];
        $this->getDI()->get('logger')->write_log('ai_id_card_mosaic_post 结果:'.json_encode($log), 'info');
        return $res;
    }



    /**
     *
     * 过滤字符串中特殊字符
     *
     * 该方法过滤的特殊字符都是会导致php报错的特殊字符，严格慎用过滤其他字符！！！
     *
     * 替换的字符要和要过滤 字符 数组的索引值对应
     * 用于AI 识别出的字符串，进行过滤
     */
    public static function stringSpecialCharsReplace($name)
    {
        if(empty($name)){
            return  $name;
        }
        //去除“-”，“—”，“/”，“·”，“.”逗号，空格，换行
        $search_arr = ['-', '—', '/', '·', '.', ',', '，', ' ', '  ', '   ', '\n', '\r', '\r\n'];
        $replace_arr = [''];//特殊字符对应的替代字符
        return str_replace($search_arr,$replace_arr,$name);
    }
    
    public function by_ai_bank_card($param)
    {
        if (!isCountry('TH')){
            return $this->checkReturn(-3);
        }
        $apiClient = (new ApiClient('by_rpc', '', 'ai_bank_card', $this->lang));
        $apiClient->setParams($param);
        $result = $apiClient->execute();
        if (!isset($result['result']['code']) || $result['result']['code'] != 1) {
            $this->logger->write_log("rpc by_ai_bank_card异常 " . json_encode($result));
            return $this->checkReturn(-3);
        }
        return $result['result']['data'] ?? [];
    }


}
