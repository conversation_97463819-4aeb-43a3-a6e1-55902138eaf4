<?php

namespace FlashExpress\bi\App\Modules\MY\Controllers;

use FlashExpress\bi\App\Controllers\ContractController as ControllerBase;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Excel;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\WinhrExcelTaskModel;
use FlashExpress\bi\App\Modules\MY\library\ContractFilter;
use FlashExpress\bi\App\Modules\MY\Server\ContractServer;
use FlashExpress\bi\App\Server\DownLoadTaskService;

class ContractController extends ControllerBase
{
    public $paramIn;

    public function initialize()
    {
        parent::initialize();
        $this->paramIn = $this->request->getPost();
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
    }

    public function onConstruct()
    {
        parent::onConstruct();
    }

    /**
     * 合同列表
     * @Permission(action='contract.getList')
     */
    public function getListAction()
    {
        $this->paramIn = array_filter($this->paramIn);
        $validations   = (new ContractFilter())->getListValidations();
        $this->validateCheck($this->paramIn, $validations);
        //获取候选人列表
        $returnArr = ContractServer::getInstance()->getList($this->paramIn);
        return $this->jsonReturn($returnArr);
    }

    /**
     * 查看页面接口
     * 获取员工合同&合同分类配置项
     * @Permission(action='contract.getList')
     */
    public function getStaffContractAction()
    {
        //获取员工合同列表
        $paramIn     = array_filter($this->paramIn);
        $validations = ContractServer::$staff_id_validations;
        $this->validateCheck($paramIn, $validations);
        $returnArr = ContractServer::getInstance()->getStaffContract($paramIn['staff_id']);
        return $this->jsonReturn($returnArr);
    }

    /**
     * 保存并添加接口(更新操作）
     * @Permission(action='contract.getList')
     */
    public function saveAction()
    {
        // 验参
        $paramIn     = $this->paramIn;
        $validations = (new ContractFilter())->getSaveValidations();
        $this->validateCheck($paramIn, $validations);

        $returnArr = $this->atomicLock(function () use ($paramIn) {
            return (new ContractServer())->saveContract($this->paramIn, $this->userInfo);
        }, ':winhr:saveAction:ct_id:' . $this->paramIn['ct_id'], 30);

        return $this->jsonReturn($returnArr);
    }

    /**
     * 合同操作接口
     * 1：删除，2：解除 5：下载
     * @Permission(action='contract.getList')
     */
    public function editAction()
    {
        // 验参
        $paramIn     = $this->paramIn;
        $userInfo    = $this->userInfo;
        $validations = ContractServer::$edit_status_validations;
        $this->validateCheck($this->paramIn, $validations);

        //修改状态
        $returnArr = $this->atomicLock(function () use ($paramIn, $userInfo) {
            return (new ContractServer())->changeStatus($paramIn['ct_id'], $paramIn['action_type'], $userInfo);
        }, ':winhr:editAction:ct_id:' . $paramIn['ct_id'] . ':action_type:' . $paramIn['action_type'], 10);

        return $this->jsonReturn($returnArr);
    }

    /**
     * 发送邮箱
     * @Permission(action='contract.getList')
     */
    public function sendEmailContractAction()
    {
        // 验参
        $paramIn     = $this->paramIn;
        $validations = (new ContractFilter())->getSendEmailValidations();
        $this->validateCheck($paramIn, $validations);

        //修改状态
        $returnArr = $this->atomicLock(function () use ($paramIn) {
            return ContractServer::getInstance()->sendEmailContract($paramIn);
        }, ':winhr:sendEmailContractAction:ct_id:' . $paramIn['ct_id'], 3);

        return $this->jsonReturn($returnArr);
    }

    /**
     * 获取合同时间
     * @Permission(action='contract.getList')
     */
    public function getDateIsLongAction()
    {
        $params      = array_filter($this->paramIn);
        $validations = (new ContractFilter())->getDateIsLongValidations();
        $this->validateCheck($params, $validations);
        //获取候选人列表
        $returnArr = ContractServer::getInstance()->getDateIsLong($params);
        return $this->jsonReturn($this->checkReturn(['data' => $returnArr]));
    }

    /**
     * 导出员工列表
     * @Permission(action='contract.getList')
     */
    public function exportListAction()
    {
        //参数验证
        $paramIn = array_filter($this->paramIn);
        $validations   = (new ContractFilter())->getListValidations();
        $this->validateCheck($paramIn, $validations);

        $paramIn['userInfo'] = $this->userinfo;
        $paramIn['lang']     = $this->lang;    //语言

        //获取总数
        $total = (new ContractServer())->getList(array_merge($paramIn,['is_total' => 1]));

        if ($total > enums::EXPORT_MAX_NUM_LIMIT) {
            throw new ValidationException($this->getTranslation()->_('export_down_num_error'));
        }

        //获取文件名
        $fileName   = 'contractList' . '_' . $this->lang . '_' . date('Ymd-His') . '.xlsx';
        $file_name   = str_replace(' ', '_', $fileName);
        $action_name = 'contract' . WinhrExcelTaskModel::ACTION_NAME_SPACE_SEPARATOR . 'exportList';

        //入task表走,队列导出
        $data = (new DownLoadTaskService())->insertTask($this->userinfo['id'], $action_name, $paramIn, $file_name);

        return $this->jsonReturn($data);
    }
}
