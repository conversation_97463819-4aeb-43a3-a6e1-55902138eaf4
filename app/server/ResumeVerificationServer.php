<?php

namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\Helper\ResumeVerificationHelper;
use FlashExpress\bi\App\Repository\ResumeRepository;

class ResumeVerificationServer extends BaseServer
{
    /**
     * 验证图片集合
     * @param $data
     * @return bool
     */
    public function checkImgList($data)
    {
        $fileType = (new ResumeServer())->getAnnexFileType();

        foreach ($data['file_type'] as $k => $v) {
            if ($v) {
                ResumeVerificationHelper::inseartErrorInfoAnnex($k, self::$t->_('upload_msg_prefix',['text'=> $fileType[$k] ?? '']));
            }
        }

        return true;
    }

    /**
     * 返回组装好的验证数据
     * @return int[]
     */
    public function returnInfo()
    {
        $returnInfo = [
            'is_error' => 0,
            'count'    => 0
        ];

        $errorData = ResumeVerificationHelper::getErrorData();
;
        if (empty($errorData)) {
            return $returnInfo;
        }

        $returnInfo['is_error'] = 1;

        $countAll = 0;

        foreach ($errorData as $k => $v) {
            $count =  count($v);

            $returnInfo[$k]['detail'] = $v;
            $returnInfo[$k]['count'] = $count;
            $countAll = $countAll + $count;
        }

        $returnInfo['count'] = $countAll;

        return  $returnInfo;
    }

    /**
     * 验证是否残疾人简历
     * @param $resumeId
     * @return bool
     */
    public function checkDeformityByCvid($resumeId)
    {
        $surveyList = (new ResumeRepository())->getSurvey($resumeId);

        //answer_key == 14  question_value==1

        foreach ($surveyList as $value) {
            if ($value['answer_key'] == 14 && $value['question_value'] == 1 && $value['type'] == 3 && !empty($value['question_text'])) {
                return true;
            }
        }

        return false;
    }

    /**
     * 过滤数据
     * @param 验证
     * @param 数据
     * @param 是否初始化
     */
    public function filterateFirstOnlineData(&$validations, &$paramIn = [],$isDefault = 0)
    {
        $fields = $this->getNetworkOnlineFields();

        foreach ($fields as $k => $v) {
            if ($v['is_delete'] && isset($validations[$k])) {
                unset($validations[$k]);
            }

            if (!empty($v['check'])) {
                $validations[$k] = $v['check'];
            }
        }

        //重置数据
        if ($isDefault) {
            foreach ($fields as $k => $v) {
                if ($v['is_delete'] && isset($paramIn[$k])) {
                    $paramIn[$k] = $v['default'];
                }
            }
        }
    }

    /**
     * 添加验证方法
     * @param $validations
     */
    public function addFirstOnlineData(&$validations)
    {
        $fields = $this->addNetworkOnlineFields();

        foreach ($fields as $k => $v) {
            if (!empty($v['check'])) {
                $validations[$k] = $v['check'];
            }
        }
    }
}