<?php

namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\Helper\InterviewHelper;
use FlashExpress\bi\App\library\enumsTh;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\BgCheckAnnexLogModel;
use FlashExpress\bi\App\Models\backyard\BgCheckAnnexModel;
use FlashExpress\bi\App\Models\backyard\HrEconomyAbilityModel;
use FlashExpress\bi\App\Models\backyard\HrhcModel;
use FlashExpress\bi\App\Models\backyard\HrInterviewRecordModel;
use FlashExpress\bi\App\Models\backyard\HrJdModel;
use FlashExpress\bi\App\Models\backyard\HrJobTitleByModel;
use FlashExpress\bi\App\Models\backyard\OfferSendSalaryModel;
use FlashExpress\bi\App\Models\backyard\SalaryApproveModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Models\HcModel;
use FlashExpress\bi\App\Repository\H5appointRepository;
use FlashExpress\bi\App\Repository\SysManagePieceRepository;
use FlashExpress\bi\App\Repository\SysManageRegionRepository;
use FlashExpress\bi\App\Server\SettingEnvServer;
use Exception;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\Models\backyard\HrAnnexModel;
use FlashExpress\bi\App\Models\backyard\HrEntryModel;
use FlashExpress\bi\App\Models\backyard\HrInterviewInfoModel;
use FlashExpress\bi\App\Models\backyard\HrInterviewModel;
use FlashExpress\bi\App\Models\backyard\HrInterviewOfferModel;
use FlashExpress\bi\App\Models\backyard\HrLogModel;
use FlashExpress\bi\App\Models\backyard\HrResumeModel;
use FlashExpress\bi\App\Models\backyard\ResumeOutLogModel;
use FlashExpress\bi\App\Models\bi\HrOperatorLogModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\HrInterviewSubscribeModel;
use FlashExpress\bi\App\Models\fle\StaffInfoModel;
use FlashExpress\bi\App\Models\backyard\SysDepartmentModel;
use FlashExpress\bi\App\Server\OfferSignServer;
use FlashExpress\bi\App\Repository\BlacklistRepository;
use FlashExpress\bi\App\Repository\CustomerresumeRepository;
use FlashExpress\bi\App\Repository\EntryRepository;
use  FlashExpress\bi\App\Repository\InterviewRepository;
use  FlashExpress\bi\App\Repository\DepartmentRepository;
use FlashExpress\bi\App\Repository\PermissionsRepository;
use FlashExpress\bi\App\Repository\PublicRepository;
use  FlashExpress\bi\App\Repository\StaffRepository;
use  FlashExpress\bi\App\Repository\FlashJdRepository;
use  FlashExpress\bi\App\Repository\HcRepository;
use  FlashExpress\bi\App\Repository\ResumeRepository;
use  FlashExpress\bi\App\Repository\SysListRepository;
use  FlashExpress\bi\App\Repository\OfferRepository;
use FlashExpress\bi\App\Repository\TrainRepository;
use http\Exception\UnexpectedValueException;
use Phalcon\Db;
use FlashExpress\bi\App\Models\backyard\HrInterviewerOperationModel;

class InterviewServer extends BaseServer
{
    protected $interview;
    protected $staff;
    protected $jd;
    protected $interview_state;
    protected $public;
    protected $entry;
    protected $train;
    public $state_array;
    public $wage_array;
    protected $hc;
    protected $offer;
    protected $blacklist;
    protected $sysList;
    protected $resume;
    protected $store_server;
    protected $department;
    protected $log;

    //未沟通
    const NO_COMMUNICATION = 1;
    //待面试
    const WAITING_INTERVIEW = 5;
    //未反馈归类
    const NO_FEEDBACK = [
        self::NO_COMMUNICATION,
        self::WAITING_INTERVIEW,
    ];

    public $departmentIds = [];
    public $ancestries    = [];
    public static $first_line_jobs = [];//一线岗位
    /**
     * 薪资单位
     */
    const SALARY_UNIT = [
        'THB' => 1,
        'PHP' => 2,
        'VND' => 3,
        'MYR' => 4,
        'LAK' => 5,
        'IN'  => 6,
        'SGD' => 7,
    ];

    //offer附件、背调附件权限标识
    protected $annex_ofer_permission = 0;//offer附件权限
    protected $annex_bg_permission = 0; //背调附件权限


    // 薪资查看权限工号    这里的工号会被替换掉! 期望薪资
//    public $salary_view_permission = [21358, 33287, 31856, 17758, 28228, 56780, 23357, 21899, 30555, 24715, 56207, 55578];

    public function __construct($lang = 'en')
    {
        parent::__construct($lang);
        $this->wage_array = UC('positionMoney');
    }


    public function getStateArray(): array
    {
        $t = $this->getTranslation();

        return [
            '1'  => $t->_('4801'),               //未沟通
            '2'  => $t->_('4810'),               //待筛选
            '3'  => $t->_('re_hc_wait_feedback'),//再次关联hc待反馈
            '5'  => $t->_('4802'),               //待面试
            '10' => $t->_('4803'),               //面试中
            '20' => $t->_('4804'),               //待发OFFER
            '25' => $t->_('4805'),               //已发OFFER
            '30' => $t->_('4806'),               //已拒绝
            '31' => $t->_('4807'),               //已取消
            '32' => $t->_('4808'),               //已删除
            '40' => $t->_('4809'),               //已入职
            '51' => $t->_('filter_state_51'),    //筛选待反馈
            '52' => $t->_('filter_state_52'),    //筛选通过
            '53' => $t->_('filter_state_53'),    //筛选不通过
            '54' => $t->_('filter_state_54'),    //筛选取消
        ];
    }

    /**
     * 面试管理-简历列表
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getInterviewResume($paramIn = [])
    {
        $state_code = $paramIn["state_code"] ?? 1;
        $deleted    = 0;
        $sql        = "--
                SELECT
                    * 
                FROM
                    hr_resume 
                WHERE
                    state_code = {$state_code} 
                    AND deleted = {$deleted}";
        $obj        = $this->db->query($sql);
        $data       = $obj->fetchAll(\Phalcon\Db::FETCH_ASSOC);
    }

    /**
     * 面试HC列表
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function interviewHcList($paramIn = [])
    {
        $this->sysList = new SysListRepository();

        //[1]参数定义
        $returnData         = ['data' => ['dataList' => []]];
        $staffId            = $this->processingDefault($paramIn, 'staff_id', 2);
        $noPage             = $this->processingDefault($paramIn, 'no_page', 4, false);
        $pageSize           = $this->processingDefault($paramIn, 'page_size', 2, 10);
        $pageNum            = $this->processingDefault($paramIn, 'page_num', 2, 1);
        $departmentId       = $this->processingDefault($paramIn, 'department_id', 2);
        $jobId              = $this->processingDefault($paramIn, 'job_id', 2);
        $hcId               = $this->processingDefault($paramIn, 'hc_id', 2);
        $cityCode           = $this->processingDefault($paramIn, 'city_code');
        $stateCode          = $this->processingDefault($paramIn, 'state_code');
        $orderField         = $this->processingDefault($paramIn, 'order_field', 1, 'expirationdate');
        $orderSort          = $this->processingDefault($paramIn, 'sort', 2, 3);
        $store_id           = $this->processingDefault($paramIn, 'store_id');//网店ID
        $export             = $this->processingDefault($paramIn, 'export', 2, 0);
        $priority_id        = $this->processingDefault($paramIn, 'priority_id', 2);
        $reason_type        = '1,3'; // 只列出招聘和离职人数
        $job_title_id       = $this->processingDefault($paramIn, 'job_title_id', 2);
        $filter_job_title   = $this->processingDefault($paramIn, 'filter_job_title_is_null', 2, 0);
        $hire_type          = $this->processingDefault($paramIn, 'hire_type', 3, 0);
        $search_priority_id = $paramIn['search_priority_id'] ?? '';

        //[2]查询面试HC列表
        $param = [
            'staff_id'         => $staffId,
            'no_page'          => $noPage,
            'page_size'        => $pageSize,
            'page_num'         => $pageNum,
            'department_id'    => $departmentId,
            'job_id'           => $jobId,
            'city_code'        => $cityCode,
            'state_code'       => $stateCode,
            'order_field'      => $orderField,
            'sort'             => $orderSort,
            'store_id'         => $store_id,
            'export'           => $export,
            'priority_id'      => $priority_id,
            'hc_id'            => $hcId,
            'reason_type'      => $reason_type,
            'deleted'          => 1,
            'job_title'        => $job_title_id,
            'filter_job_title' => $filter_job_title,
            'hire_type'        => $hire_type,
            'is_sub_department'=> $paramIn['is_sub_department'] ?? 2,
            'search_priority_id'=> $search_priority_id,
        ];

        if (empty($param['department_id']) && $this->ifHrisSales) {
            $param['department_id'] = (new DepartmentRepository())->getDepartmentIdsByRole();
        }

        $interviewHcData = (new InterviewRepository())->getInterviewHcList($param);
        if (!empty($interviewHcData['dataList'])) {
            //获取工作网点信息 匹配用的
            $worknodeIdsArr = array_unique(array_column($interviewHcData['dataList'], "worknode_id"));
            $worknodeIds    = getIdsStr($worknodeIdsArr);
            $worknode       = $this->sysList->getStoreList([
                "ids" => $worknodeIds,
            ]);
            $worknodeData   = array_column($worknode, null, 'id');

            //获取全部职位
            $jobTitleIdsArr = array_filter(array_unique(array_column($interviewHcData['dataList'], "job_title")));
            $jobTitleIds    = getIdsWithoutQuotes($jobTitleIdsArr);
            $jobTitle       = $this->sysList->getPositionList([
                "ids" => $jobTitleIds,
            ]);
            $jobTitleData   = array_column($jobTitle, 'job_name', 'id');
            //获取大区片区信息
            $regionAndPiece = $this->sysList->getManageRegionAndPieceList(0);
            //补充网店信息数据
            foreach ($interviewHcData['dataList'] as $key => $item) {
                $manage_region_id                                        = $worknodeData[$item['worknode_id']]['manage_region'] ?? 0; //大区id
                $manage_piece_id                                         = $worknodeData[$item['worknode_id']]['manage_piece'] ?? 0; //片区id
                $interviewHcData['dataList'][$key]['worknode_name']      = $worknodeData[$item['worknode_id']]['name'] ?? 'head office';
                $interviewHcData['dataList'][$key]['sorting_no']         = $worknodeData[$item['worknode_id']]['sorting_no'] ?? '';
                $interviewHcData['dataList'][$key]['job_title_name']     = $jobTitleData[$item['job_title_id']] ?? '';
                $interviewHcData['dataList'][$key]['job_title_id']       = $item['job_title_id'];
                $interviewHcData['dataList'][$key]['job_id']             = $item['job_id'];
                $interviewHcData['dataList'][$key]['manage_region_name'] = $regionAndPiece['region'][$manage_region_id] ?? "";
                $interviewHcData['dataList'][$key]['manage_piece_name']  = $regionAndPiece['piece'][$manage_piece_id] ?? "";
                if ($export == 1) {
                    //已招满时间
                    $interviewHcData['dataList'][$key]['hc_full_time'] = "";
                    if ($item['state_code'] == 2) {
                        $interviewHcData['dataList'][$key]['hc_full_time'] = $item['updated_at'];
                    }
                }

                $working_day_rest_type_str = '';
                if (!empty($item['working_day_rest_type'])) {
                    $working_day_rest_type = explode(',', $item['working_day_rest_type']);
                    array_walk($working_day_rest_type, function (&$val) {
                        $val = $this->getTranslation()->_('working_day_rest_type_'.$val);
                    });
                    $working_day_rest_type_str = implode(',', $working_day_rest_type);
                }
                $interviewHcData['dataList'][$key]['working_day_rest_type_text'] = $working_day_rest_type_str;//工作天数&轮休规则
            }
        }

        $returnData['data'] = $interviewHcData;
        return $this->checkReturn($returnData);
    }

    /**
     * 获取列表
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getAddressList($paramIn = [])
    {
        $this->sysList = new SysListRepository();

        $addressType                    = $this->processingDefault($paramIn, 'address_type', 2, 2);// 1国家 2省 3市 4区
        $cityList                       = $this->sysList->getAddressList(['address_type' => $addressType]);
        $returnData['data']['dataList'] = $cityList;
        return $this->checkReturn($returnData);
    }

    /**
     * 获取工资列表
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getWageList($paramIn = [])
    {
        //工资列表
        $wage_list = $this->wage_array;

        $returnData['data']['dataList'] = $wage_list;

        return $this->checkReturn($returnData);
    }


    /**
     * 获取状态列表
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function getStateList()
    {
        //状态列表
        $state_array = $this->getStateArray();

        $resume_filter_state_arr = array_values(enums::$resume_filter_state);
        $resume_filter_states    = implode(',', $resume_filter_state_arr);

        $state_sql = "select count(hi.state) as num,hi.state from hr_resume hr 
        left join hr_interview hi on hi.resume_id = hr.id and hi.hc_id = hr.hc_id where hi.state != '' and hi.state not in({$resume_filter_states}) group by hi.state";


        $dataObj = $this->getDI()->get('db_rby')->query($state_sql);
        $data    = $dataObj->fetchAll(\Phalcon\Db::FETCH_ASSOC);

        $noCommunication = [
            [
                'num'        => '0',
                'state'      => '1',
                'state_name' => $this->getTranslation()->_('4801'),
            ],
            [
                'num'        => '0',
                'state'      => '3',
                'state_name' => $this->getTranslation()->_('re_hc_wait_feedback'),
            ],
        ];
        foreach ($data as $key => $value) {
            $data[$key]['state_name'] = $state_array[$value['state']] ?? '';
        }

        $allCommunication = [
            [
                'num'        => '0',
                'state'      => '0',
                'state_name' => 'All',
            ],
        ];

        //增加简历筛选状态
        $resume_filter_state = [
            [
                'num'        => '0',
                'state'      => enums::$resume_filter_state['waiting_feedback'],
                'state_name' => $this->getTranslation()->_('filter_state_51'),
            ],
            [
                'num'        => '0',
                'state'      => enums::$resume_filter_state['pass'],
                'state_name' => $this->getTranslation()->_('filter_state_52'),
            ],
            [
                'num'        => '0',
                'state'      => enums::$resume_filter_state['not_pass'],
                'state_name' => $this->getTranslation()->_('filter_state_53'),
            ],
            [
                'num'        => '0',
                'state'      => enums::$resume_filter_state['cancel'],
                'state_name' => $this->getTranslation()->_('filter_state_54'),
            ],
        ];

        $data                           = array_merge($noCommunication, $resume_filter_state, $data, $allCommunication);
        $data                           = $this->filterStateData($data);
        $returnData['data']['dataList'] = $data;

        return $this->checkReturn($returnData);
    }

    /**
     *
     * 薪资审批 添加审批
     *
     * @param $userinfo
     * @param $params
     */
    public function approveSalary($userinfo, $params)
    {
        if (isCountry("TH")){
            // 验证黑灰名单
            if (isset($params['resume_id']) && !empty($params['resume_id'])){
                $black_grey_list = (new BlackgreylistServer())->check_black_grey_list([
                    "resume_id" => $params['resume_id'],
                ]);
                if ($black_grey_list["is_black_list"]){
                    return $this->checkReturn(-3, $this->getTranslation()->_('identity_black_list_existed'));
                }
                $resume_data = HrResumeModel::findFirst([
                    'conditions' => 'id = :resume_id: and deleted=0',
                    'bind'       => [
                        'resume_id' => $params['resume_id'],
                    ],
                ]);
                if (empty($resume_data)){
                    return $this->checkReturn(-3, self::$t->_('resume_err_1'));
                }
                $resume_data = $resume_data->toArray();
                (new OutsourcingBlackListServer())->check($resume_data['credentials_num'],'winhr',true,$this->lang);
            }
        }


        /* 校验hc */
        $hcData = (new HcRepository())->checkHc(['hc_id' => $params['hc_id']]);
        if ($hcData['state_code'] == 3 || $hcData['surplusnumber'] == 0) {
            return $this->checkReturn(-3, $this->getTranslation()->_('11027'));
        }
        if ($hcData['state_code'] == 4) {

            return $this->checkReturn(-3, $this->getTranslation()->_('11028'));
        }
        if ($hcData['state_code'] == 9) {
            return $this->checkReturn(-3, $this->getTranslation()->_('11031'));
        }
        unset($params['hc_id']);
        (new ResumeServer())->setLastOperator($params['resume_id']);



        $apiClient = (new ApiClient('by_rpc', '', 'salary_add_approve', $this->lang));
        $apiClient->setParams(array_merge($params, ["userinfo" => $userinfo]));
        $result = $apiClient->execute();
        return $result['result'];
    }

    /**
     *
     * 薪资审批 添加审批
     *
     * @param $userinfo
     * @param $params
     */
    public function approveSalaryV2($userinfo, $params)
    {
        if (isCountry('ID') && !empty($params['resume_id'])){
            $resumeModel = HrResumeModel::findFirst(
                [
                    'conditions' => "id = :resume_id:",
                    'bind' => ['resume_id' => $params['resume_id']],
                ]
            );
            if (empty($resumeModel) || empty($resumeModel->nationality)){
                throw new BusinessException($this->getTranslation()->_('offer_need_nationality'));
            }
        }
        
        /* 校验hc */
        $hcData = (new HcRepository())->checkHc(['hc_id' => $params['hc_id']]);
        if ($hcData['state_code'] == 3 || $hcData['surplusnumber'] == 0) {
            return $this->checkReturn(-3, $this->getTranslation()->_('11027'));
        }
        if ($hcData['state_code'] == 4) {
            return $this->checkReturn(-3, $this->getTranslation()->_('11028'));
        }
        if ($hcData['state_code'] == 9) {
            return $this->checkReturn(-3, $this->getTranslation()->_('11031'));
        }
        unset($params['hc_id']);

        $apiClient = (new ApiClient('by_rpc', '', 'salary_add_approve_v2', $this->lang));
        $apiClient->setParams(array_merge($params, ["userinfo" => $userinfo]));
        $result = $apiClient->execute();
        return $result['result'];
    }


    /**
     * @param $resumeId
     * @return mixed
     */
    public function approveSalaryItems($resumeId)
    {
        $apiClient = (new ApiClient('by_rpc', '', 'approve_salary_items', $this->lang));
        $apiClient->setParams(['resume_id' => $resumeId]);
        $result = $apiClient->execute();
        return $result['result'];
    }


    /**
     * 撤销薪资审批
     *
     * @param $userinfo
     * @param $resumeId
     * @param $revokeRemark
     *
     */
    public function revokeSalary($userinfo, $resumeId, $revokeRemark)
    {
        $apiClient = (new ApiClient('by_rpc', '', 'salary_revoke_approve', $this->lang));
        $apiClient->setParams(array_merge(["resume_id" => $resumeId, "revoke_remark" => $revokeRemark],
            ["userinfo" => $userinfo]));
        $result = $apiClient->execute();
        return $result['result'];
    }

    /**
     *
     * 获取审批详情
     *
     * @param $userinfo
     * @param $resumeId
     *
     */
    public function approvalDetail($userinfo, $resumeId)
    {
        $apiClient = (new ApiClient('by_rpc', '', 'salary_approve_detail', $this->lang));
        $apiClient->setParams(array_merge(["resume_id" => $resumeId], ['userinfo' => $userinfo]));
        $result = $apiClient->execute();
        //如果次权限字段存在重新赋值
        if (isset($result['result']['data']['head']['salary_view_permission'])) {
            $result['result']['data']['head']['salary_view_permission'] = (isset($result['result']['data']['head']['staff_id']) && $result['result']['data']['head']['staff_id'] == $userinfo['id']) || $userinfo['salary_structure_permission'] == enums::$salary_structure_permission['on'] ? 1 : 0;
        }

        if (!empty($result['result']['data']['salary_items'])) {
            $salaryItems                              = $this->approveSalaryItems($resumeId);
            $result['result']['data']['salary_items'] = $salaryItems['data'];
        } else {
            $result['result']['data']['salary_items'] = null;
        }

        if (empty($result['result']['data']['head'])) {
            $result['result']['data']['head'] = (object)[];
        }
        if (empty($result['result']['data']['detail'])) {
            $result['result']['data']['detail'] = [];
        }
        if (empty($result['result']['data']['stream'])) {
            $result['result']['data']['stream'] = [];
        }


        return $this->checkReturn($result['result']);
    }

    /**
     * 构建 rpc data
     *
     * @param $method
     * @param $params
     * @return mixed
     *
     */
    private function buildJsonRpcData($method, $params)
    {
        $rpc['jsonrpc']  = "2.0";
        $rpc['method']   = $method;
        $rpc['id']       = time();
        $rpc['params'][] = ["locale" => $this->lang];
        $rpc['params'][] = $params;

        return $rpc;
    }

    /**
     * 获取简历列表
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function interviewList($paramIn = [], $userinfo)
    {
        $this->sysList = new SysListRepository();
        $this->entry   = new EntryRepository();

        //参数
        $phone                             = $this->processingDefault($paramIn, 'phone', 1);
        $job_name                          = $this->processingDefault($paramIn, 'job_name', 1);
        $hc_job_name                       = $this->processingDefault($paramIn, 'hc_job_name', 1);
        $department_id                     = $this->processingDefault($paramIn, 'department_id', 2);
        $hc_department_id                  = $this->processingDefault($paramIn, 'hc_department_id', 2);
        $province_name                     = $this->processingDefault($paramIn, 'province_id', 1);
        $work_city_id                      = $this->processingDefault($paramIn, 'work_city_id', 1);
        $state                             = $this->processingDefault($paramIn, 'state', 2);
        $updated_start                     = $this->processingDefault($paramIn, 'updated_start', 2);
        $updated_end                       = $this->processingDefault($paramIn, 'updated_end', 2);
        $delivery_time_start               = $this->processingDefault($paramIn, 'delivery_time_start', 2);
        $delivery_time_end                 = $this->processingDefault($paramIn, 'delivery_time_end', 2);
        $ai_score_type                     = $this->processingDefault($paramIn, 'ai_score_type', 3);
        $created_start                     = $this->processingDefault($paramIn, 'created_start', 2);
        $created_end                       = $this->processingDefault($paramIn, 'created_end', 2);
        $interview_id                      = $this->processingDefault($paramIn, 'interview_id', '');
        $store_id                          = $this->processingDefault($paramIn, 'store_id');
        $resume_store_id                   = $this->processingDefault($paramIn, 'resume_store_id');
        $hc_id                             = $this->processingDefault($paramIn, 'hc_id', 2);
        $cvId                              = $this->processingDefault($paramIn, 'cv_id', 2);
        $is_weed_out                       = $this->processingDefault($paramIn, 'is_weed_out', 2);
        $alternative_job_id                = $this->processingDefault($paramIn, 'alternative_job_id', 2);
        $approvalStatus                    = $this->processingDefault($paramIn, 'approval_status',
            2); // 审批状态 1 待审批 2 已同意 3 已驳回 4 已撤销
        $send_time_start                   = $this->processingDefault($paramIn, 'send_time_start');
        $send_time_end                     = $this->processingDefault($paramIn, 'send_time_end');
        $noPage                            = $this->processingDefault($paramIn, 'no_page', 4, false);
        $pageNum                           = $this->processingDefault($paramIn, 'page_num', 2, 1);
        $pageSize                          = $this->processingDefault($paramIn, 'page_size', 2, 10);
        $expected_arrivaltime_left         = $this->processingDefault($paramIn, 'expected_arrivaltime_left');
        $expected_arrivaltime_right        = $this->processingDefault($paramIn, 'expected_arrivaltime_right');
        $jobTitle                          = $this->processingDefault($paramIn, 'job_title_id', 2);
        $resume_source                     = $this->processingDefault($paramIn, 'source', 2); //简历来源 1=h5，2=whr，4-by推荐
        $resume_approve_state              = $this->processingDefault($paramIn, 'resume_approve_state',
            2); //by简历审核状态 1-待审核，2-审核通过，3-审核驳回
        $recommender_staff_id              = $this->processingDefault($paramIn, 'recommender_staff_id', 2); //by简历推荐人工号
        $is_feedback                       = $this->processingDefault($paramIn, 'is_feedback',
            2); //面试结果 1:未取消 1：已取消 3：面试完成
        $entry_status                      = $this->processingDefault($paramIn, 'entry_status', 2); //入职状态筛选
        $entry_staff_id                    = $this->processingDefault($paramIn, 'entry_staff_id', 2);//入职工号筛选
        $interview_communication_status    = $this->processingDefault($paramIn, 'interview_communication_status',
            2); //面试管理-沟通状态下拉code
        $communication_failure_reason_type = $this->processingDefault($paramIn, 'communication_failure_reason_type',
            2); //面试管理-沟通失败原因下拉code,1=联系不上、2=要求岗位或网点没有hc、3=不符合岗位要求、4=候选人已拒绝、5=其他
        $last_operator_query_type          = $this->processingDefault($paramIn, 'last_operator_query_type');  // 简历最新操作人筛选类型
        $resume_last_operator              = $this->processingDefault($paramIn, 'resume_last_operator');  // 简历最新操作人
        $is_relation_hc                    = $this->processingDefault($paramIn, 'is_relation_hc');      // 是否关联hc

        $priority_ids                      = $this->processingDefault($paramIn, 'priority_ids', 3);   // hc优先级
        $credentials_num                   = $this->processingDefault($paramIn, 'credentials_num');         // 身份证号
        $manage_region                     = $this->processingDefault($paramIn, 'manage_region',3);   //大区
        $manage_piece                      = $this->processingDefault($paramIn, 'manage_piece',3);    //片区
        $recruit_channel                   = $this->processingDefault($paramIn, 'recruit_channel',2);    //招聘渠道
        $pageSize                          = !$noPage ? $pageSize : 50000;
        $pageNum                           = !$noPage ? $pageNum : 1;

        /// 这里是新的权限方法
        // $state  5 待面试  10 面试中  20 代发 offer 25 已发 offer  40 是已入职
        //判断是否为超管  不是超管使用权限  是超管无限制
        $is_admin                 = $userinfo['is_admin'];   //1是超管
        $authority_stores_ids     = $userinfo['permission_stores_ids'];   //网点权限
        $authority_department_ids = $userinfo['permission_department_ids'];   //部门权限
        $is_menu_permission       = SettingEnvServer::getMenuPermission('whr_interview_menu_list');

        $resumeIds    = [];
        $resumeServer = (new ResumeServer());
        //todo 获取业务数据
        $resumeData = (new InterviewRepository($userinfo))->getInterviewResume([
            "phone"                             => $phone,
            "job_name"                          => $job_name,
            "hc_job_name"                       => $hc_job_name,
            "province_name"                     => $province_name,
            "work_city_id"                      => $work_city_id,
            "state"                             => $state,
            "updated_start"                     => $updated_start,
            "updated_end"                       => $updated_end,
            "delivery_time_start"               => $delivery_time_start,
            "delivery_time_end"                 => $delivery_time_end,
            "ai_score_type"                     => $ai_score_type,
            "created_start"                     => $created_start,
            "created_end"                       => $created_end,
            "department_id"                     => $department_id,
            "hc_department_id"                  => $hc_department_id,
            "interview_id"                      => $interview_id,
            "store_id"                          => $store_id,
            "resume_store_id"                   => $resume_store_id,
            "hc_id"                             => intval($hc_id),
            "cv_id"                             => intval($cvId),
            "approval_status"                   => $approvalStatus,
            "send_time_start"                   => $send_time_start,
            "send_time_end"                     => $send_time_end,
            "noPage"                            => $noPage,
            "page_num"                          => $pageNum,
            "page_size"                         => $pageSize,
            'ifHrisSales'                       => $this->ifHrisSales,
            'resumeIds'                         => $resumeIds,
            'alternative_job_id'                => $alternative_job_id,
            'is_weed_out'                       => $is_weed_out,
            'expected_arrivaltime_left'         => $expected_arrivaltime_left,
            'expected_arrivaltime_right'        => $expected_arrivaltime_right,
            'job_title'                         => $jobTitle,
            'authority_department_ids'          => $authority_department_ids,
            'authority_stores_ids'              => $authority_stores_ids,
            'is_admin'                          => $is_admin,
            'is_feedback'                       => $is_feedback,
            'resume_source'                     => $resume_source,
            'resume_approve_state'              => $resume_approve_state,
            'recommender_staff_id'              => $recommender_staff_id,
            'entry_status'                      => $entry_status, //入职状态筛选
            'entry_staff_id'                    => $entry_staff_id, //入职工号筛选
            'interview_communication_status'    => $interview_communication_status, //沟通状态
            'communication_failure_reason_type' => $communication_failure_reason_type, //沟通失败原因
            'is_menu_permission'                => $is_menu_permission, //入职工号筛选$is_menu_permission
            'last_operator_query_type'          => $last_operator_query_type,
            'resume_last_operator'              => $resume_last_operator,
            'is_relation_hc'                    => $is_relation_hc,
            'priority_ids'                      => $priority_ids,
            'credentials_num'                   => $credentials_num,
            'manage_region'                     => $manage_region,
            'manage_piece'                      => $manage_piece,
            'recruit_channel'                   => $recruit_channel,
            'is_sub_department'                 => $paramIn['is_sub_department'] ?? 2,
            'hc_state_code'                     => $paramIn['hc_state_code'] ?? [],
        ]);
        if (isset($paramIn['is_count']) && $paramIn['is_count'] == 1) {
            return $resumeData['pagination']['count'] ?? 0;
        }

        $permissionRep = new PermissionsRepository();

        $resumeData['permissions'] = $permissionRep->checkRoleResumeListPermissions($this->userInfo);

        if (!$resumeData["dataList"]) {
            $returnData['data'] = $resumeData;
            return $this->checkReturn($returnData);
        }
        //状态列表
        $state_array = $this->getStateArray();

        //关联查询 hr_interview_subscribe 表 条件 interview_id
        $interviewIdsArr        = array_filter(array_unique(array_column($resumeData["dataList"], "interview_id")));
        $submitterIds           = array_filter(array_unique(array_column($resumeData['dataList'], 'submitter_id')));
        $submitterInfos         = $this->sysList->getStaffListByIds($submitterIds);
        $interviewIds           = getIdsStr($interviewIdsArr);
        $interviewSubscribeData = (new InterviewRepository())->getInterviewSubscribe([
            "interview_ids" => $interviewIds,
        ]);
        $interviewSubscribeData = array_column($interviewSubscribeData, null, 'interview_id');
        //关联查询 面试状态和操作相关联
        $result = [];
        if ($interviewIds) {
            $interview_sql = "--
                        SELECT
                            hi.interview_id,
                            ifnull( hif.interview_info_id, '' ) AS interview_info_id,
                            hif.hc_id 
                        FROM
                            hr_interview hi
                            LEFT JOIN hr_interview_info hif ON hi.interview_id = hif.interview_id 
                        WHERE
                            hi.interview_id IN ( $interviewIds ) and hif.state != 0";
            $dataObj       = $this->getDI()->get('db_rby')->query($interview_sql);
            $data          = $dataObj->fetchAll(\Phalcon\Db::FETCH_ASSOC);


            foreach ($data as $k => $v) {
                $result[$v['interview_id']][$v['hc_id']][] = $v['interview_info_id'];
            }
        }

        //取最新的hc
        if ($result) {
            foreach ($result as $k => $v) {
                $current = '';
                $hc_id   = '';
                foreach ($v as $k_2 => $v2) {
                    $max = max($v2);
                    if ($max > $current) {
                        unset($result[$k][$hc_id]);
                        $current = $max;
                        $hc_id   = $k_2;
                    } else {
                        unset($result[$k][$k_2]);
                    }
                }
                unset($current);
                unset($hc_id);
                $result[$k] = (current($result[$k]));
            }
        }

        //关联查询 hr_interview_offer 表 条件 interview_id
        $interviewOfferData = (new InterviewRepository())->getInterviewOffer($interviewIdsArr);
        $interviewOfferData = array_column($interviewOfferData, null, 'interview_id');

        // 关联查询 hr_interview_offer_sign_approve 表（offer签字审批表）， 获取当前offer最新签字状态
        $offer_sign_lasted_data = (new InterviewRepository())->getOfferSignApproveData($interviewIdsArr);


        //查询部门表数据
        $departmentData = $this->sysList->getDepartmentList();
        $departmentData = array_column($departmentData, 'name', 'id');

        //查询职位表数据
        $job_title_map = $this->sysList->getJobTitleList();

        //查询城市
//        $cityData = $this->sysList->getProvinceList();
//        $cityData = array_column($cityData, null, 'code');

        $cityData = $this->sysList->getProvinceList();
        $cityData = array_column($cityData, null, 'code');

        //获取市列表，上面那个其实是省列表
        $cityList = (new H5appointRepository())->getCityList();
        $cityListKy = array_column($cityList, 'name', 'code');

        /* 获取工作网点列表 */
        $worknodeIdsArr = array_unique(array_merge(
            array_column($resumeData["dataList"], "worknode_id"),
            array_column($resumeData["dataList"], "store_id"),
            array_column($resumeData["dataList"], "recommend_store_id")
        ));
        $worknodeIds    = getIdsStr($worknodeIdsArr);
        $worknode       = $this->sysList->getStoreList([
            "ids" => $worknodeIds,
        ]);
        $worknodeData   = array_column($worknode, null, 'id');

        //获取JD名称 匹配用的 ids
        $alternative_job_ids_str = array_column($resumeData['dataList'], 'alternative_job_ids');
        $alternative_job_ids     = [];
        foreach ($alternative_job_ids_str as $idsStr) {
            $alternative_job_ids = array_merge($alternative_job_ids, explode(',', $idsStr));
            $alternative_job_ids = array_unique($alternative_job_ids);
        }

        $jobIdsArr   = array_unique(
            array_merge(array_column($resumeData["dataList"], "hc_job_id"),
                array_column($resumeData["dataList"], "job_id"),
                $alternative_job_ids
            )
        );
        $jobIds      = getIdsStr($jobIdsArr);
        $jobList     = $this->sysList->getJobList([
            "ids" => $jobIds,
        ]);
        $jobListData = array_column($jobList, 'job_name', 'job_id');

        //获取黑名单 匹配用的
        $blacklistIdsArr = array_unique(array_column($resumeData["dataList"], "credentials_num"));
        $blacklistIds    = getIdsStr($blacklistIdsArr);
        $blackList       = $this->sysList->getBlacklist([
            "ids" => $blacklistIds,
        ]);
        $blackListData   = array_column($blackList, null, 'identity');

        /* 获取员工id对应名字数组 */
        $interviewer_ids       = array_filter(array_column($interviewSubscribeData, "interviewer_id"));
        $resume_provider_ids   = array_filter(array_column($interviewSubscribeData, "resume_provider_id"));
        $recommender_staff_ids = array_filter(array_column($resumeData["dataList"], "recommender_staff_id")); //by推荐人
        $filter_staff_ids      = array_filter(array_column($resumeData["dataList"], "filter_staff_id")); //简历筛选推荐人
        $staffIdsArr           = array_unique(array_merge($interviewer_ids, $resume_provider_ids,
            $recommender_staff_ids, $filter_staff_ids));
        /* 获取员工信息列表 */
        $staffList = $this->sysList->getStaffListForBi($staffIdsArr);
        $staffData = array_column($staffList, null, 'staff_info_id');


        //招聘渠道
        $recruitChannel = (new SysListServer())->getRecruitChannelList();
        $recruitChannel = array_column($recruitChannel, 'name', 'key');

        /* 获取入职员工的工号 */
        $resumeIds       = array_unique(array_column($resumeData["dataList"], "resume_id"));
        $resumeIds       = getIdsStr($resumeIds);
        $resumeStaffList = $this->entry->getEntryStaffInfo([
            'resume_ids' => $resumeIds,
        ]);
        $resumeStaffList = array_column($resumeStaffList, null, 'resume_id');

        $resumeIds  = array_values(array_filter(array_unique(array_column($resumeData["dataList"], "resume_id"))));
        $historyHcs = $this->getHistoryHc($resumeIds);

        /**
         *  获取 简历 薪资审批状态*/
        $approvalList = $this->getLastSalaryApproval(array_unique(array_column($resumeData["dataList"], "resume_id")));

        $approvalList = array_column($approvalList, null, 'resume_id');
        $approveJobs  = [];
        if ($approvalList) {
            $jobDatas    = $this->getDI()->get("db_rby")->fetchAll("select * from hr_job_title where id in ( " . implode(",",
                    array_column($approvalList, 'job_id')) . ")");
            $approveJobs = array_column($jobDatas, null, 'id');
        }

        //查询职位数据
        $positionData = $this->sysList->getPositionList();
        $positionData = array_column($positionData, 'job_name', 'id');

        $shift_list = (new SysListRepository())->getAllShiftList();
        $shift_list = array_column($shift_list, null, 'id');

        $child_department_list = (new SysListRepository())->getChildDepartmentListById(25);
        $child_department_ids  = array_column($child_department_list, 'id');

        //offer附件权限
        $this->annex_ofer_permission = $this->isHaveOfferAnnexPermission($paramIn['staff_id']);

        //背调附件权限
        $this->annex_bg_permission = $this->isHaveBgAnnexPermission($paramIn['staff_id']);


        $config = $this->getDI()->getConfig();
        //获取sub_id
        $operation_info_record = $this->getSubID($resumeData["dataList"]);

        $new_operations = array_column($this->getNewOperationState(array_values($operation_info_record)), 'state',
            'interview_sub_id');

        $offerIds = array_column($interviewOfferData, '********');
        $offerIds = $this->hasBgCheckAnnexOffer($offerIds);

        $resume_last_operator_staff_ids = array_values(array_unique(array_column($resumeData["dataList"],'resume_last_operator')));
        $resume_last_operator_staff_res = (new StaffServer())->getStaffListByIds($resume_last_operator_staff_ids,['staff_info_id','name','nick_name']);
        $resume_last_operator_staff_list = array_column($resume_last_operator_staff_res, null, 'staff_info_id');

        //获取大区数据
        $regionList = (new SysManageRegionRepository())->getListFromCache(['id', 'name']);
        $regionList = array_column($regionList, 'name', 'id');

        //获取片区数据
        $pieceList = (new SysManagePieceRepository())->getListFromCache(['id', 'name']);
        $pieceList = array_column($pieceList, 'name', 'id');

        //获取多个配置文件
        $updateHcSetting = (new SettingEnvServer())->getMultiEnvByCode([
            'replace_hc_jobids',
            'replace_hc_hire_type',
            'replace_hc_staff_ids',
        ]);

        $updateHcSettingParams = [
            'setting_job_title'     => $updateHcSetting['replace_hc_jobids'] ? explode(',', $updateHcSetting['replace_hc_jobids']) : [],
            'setting_hire_type'     => $updateHcSetting['replace_hc_hire_type'] ? explode(',', $updateHcSetting['replace_hc_hire_type']) : [],
            'setting_staff_info_id' => $updateHcSetting['replace_hc_staff_ids'] ? explode(',', $updateHcSetting['replace_hc_staff_ids']) : [],
        ];

        //todo 业务处理
        foreach ($resumeData["dataList"] as $k => $v) {
            $resumeData["dataList"][$k]["resume_last_operator_name"] = StaffServer::getStaffNameView($resume_last_operator_staff_list[$v['resume_last_operator']] ?? [],2);

            //赋值ope_id
            if (!empty($v['sub_id']) && $operation_info_record) {
                $resumeData["dataList"][$k]["ope_id"] = $operation_info_record[$v['sub_id']];
            } else {
                $resumeData["dataList"][$k]["ope_id"] = '';
            }
            $phone_area_code                     = !empty($v['phone_area_code']) ? '+' . $v['phone_area_code'] : '+66';
            $resumeData["dataList"][$k]["phone"] = $phone_area_code . ' ' . $v['phone'];
            //发送offer时间
            if ($v['state'] != 25 && $v['state'] != 40) {
                $resumeData["dataList"][$k]["send_time"] = "";
            }
            //offer提交人
            $submitter_id = $interviewOfferData[$v["interview_id"]]["submitter_id"] ?? "";

            //offer附件权限
            if ((!empty($submitter_id) && $submitter_id == $paramIn['staff_id']) || $this->annex_ofer_permission) {
                $resumeData["dataList"][$k]['permissions_attach_offer'] = 1;
            } else {
                $resumeData["dataList"][$k]['permissions_attach_offer'] = 0;
            }

            //背调附件权限
            if ((!empty($submitter_id) && $submitter_id == $paramIn['staff_id']) || $this->annex_bg_permission) {
                $resumeData["dataList"][$k]['permissions_attach_bg'] = 1;
            } else {
                $resumeData["dataList"][$k]['permissions_attach_bg'] = 0;
            }


            //面试-详细地址
            $resumeData["dataList"][$k]["detail_address"] = $interviewSubscribeData[$v["interview_id"]]["detail_address"] ?? "";
            //面试-subscribeId
            $resumeData["dataList"][$k]["subscribe_id"] = $interviewSubscribeData[$v["interview_id"]]["id"] ?? "";
            //面试-subscribeId-简历提供人
            $resumeData["dataList"][$k]["resume_provider_id"] = $interviewSubscribeData[$v["interview_id"]]["resume_provider_id"] ?? "";
            //面试-subscribeId-简历提供人名字
            if ($interviewSubscribeData[$v["interview_id"]]['resume_provider_id'] == "-1") {
                $resumeData["dataList"][$k]["resume_provider_name"] = "DC";
            } elseif ($interviewSubscribeData[$v["interview_id"]]['resume_provider_id'] == "-2") {
                $resumeData["dataList"][$k]["resume_provider_name"] = "null";
            } else {
                $resume_provider_id = $interviewSubscribeData[$v["interview_id"]]["resume_provider_id"] ?? 0;
                $resumeData["dataList"][$k]["resume_provider_name"] = StaffServer::getStaffNameView($staffData[$resume_provider_id] ?? [],2,$resume_provider_id);
            }

            //面试-subscribeId-interview_time
            $add_hour  = $this->getDI()['config']['application']['add_hour'];
            $_now_time = gmdate('Y-m-d H:i:s', time() + $add_hour * 3600);

            $resumeData["dataList"][$k]["interview_time"] = $interviewSubscribeData[$v["interview_id"]]["interview_time"] ?? "";
            //面试反馈等待时长
            $resumeData["dataList"][$k]["wating_feedback"] = '';
            if ($resumeData["dataList"][$k]['interview_time']) {
                $_interview_time                               = $resumeData["dataList"][$k]['interview_time'];
                $resumeData["dataList"][$k]["wating_feedback"] = $this->getWaitFeedbackTime($_interview_time,
                    $_now_time);
            }
            $resumeData["dataList"][$k]["is_salary_approve"] = $permissionRep->isHasSalaryPermission($this->userInfo) ? 1 : 0;

            $resumeData["dataList"][$k]["is_offer_submitter"] = ($submitter_id == $userinfo['id']) ? 1 : 0;
            $resumeData["dataList"][$k]["********"]           = $interviewOfferData[$v["interview_id"]]["********"] ?? "";

            //off附件上传时间
            $resumeData["dataList"][$k]["offer_annex_upload_time"] = $interviewOfferData[$v["interview_id"]]["offer_annex_upload_time"] ?? "";
            //offer-最后更新时间
            $resumeData["dataList"][$k]["offer_updated_at"] = $interviewOfferData[$v["interview_id"]]["offer_updated_at"] ?? "";
            //offer-预计到岗时间
            $resumeData["dataList"][$k]["offer_work_time"] = isset($interviewOfferData[$v["interview_id"]]) ? ($interviewOfferData[$v["interview_id"]]["offer_work_time"] ?: '') : strval($v['expected_arrivaltime']);
            //offer-培训日期
            $resumeData["dataList"][$k]["offer_train_time"] = $interviewOfferData[$v["interview_id"]]["offer_train_time"] ?? "";

            //offer-基本工资
            $resumeData["dataList"][$k]["offer_money"] = $interviewOfferData[$v["interview_id"]]["money"] ?? "";
            //offer-职位津贴
            $resumeData["dataList"][$k]["offer_position"] = $interviewOfferData[$v["interview_id"]]["position"] ?? "";
            //offer-经验津贴
            $resumeData["dataList"][$k]["offer_exp"] = $interviewOfferData[$v["interview_id"]]["exp"] ?? "";
            //offer-餐补
            $resumeData["dataList"][$k]["offer_food"] = $interviewOfferData[$v["interview_id"]]["food"] ?? "";
            //offer-危险区域津贴
            $resumeData["dataList"][$k]["offer_dangerously"] = $interviewOfferData[$v["interview_id"]]["dangerously"] ?? "";
            //offer-电脑补贴
            $resumeData["dataList"][$k]["offer_computer"] = $interviewOfferData[$v["interview_id"]]["computer"] ?? "";
            //offer-租房津贴
            $resumeData["dataList"][$k]["offer_renting"] = $interviewOfferData[$v["interview_id"]]["renting"] ?? "";
            //offer-租车津贴
            $resumeData["dataList"][$k]["offer_rental"] = $interviewOfferData[$v["interview_id"]]["rental"] ?? "";
            //offer-设备费
//            $resumeData["dataList"][$k]["offer_equipment"] = $interviewOfferData[$v["interview_id"]]["equipment_cost"] ?? "";
            // 备注
            $resumeData["dataList"][$k]["other"] = $interviewOfferData[$v["interview_id"]]["other"] ?? "";
            //offer附件
            $resumeData["dataList"][$k]['file_offer_url'] = $interviewOfferData[$v["interview_id"]]["offer_annex_url"] ?? "";
            //背调附件
            $resumeData["dataList"][$k]['file_backgroud_check_url'] = $interviewOfferData[$v["interview_id"]]["bg_check_annex_url"] ?? "";
            // 是否有背调附件
            $resumeData["dataList"][$k]['is_has_file_backgroud_check'] = isset($interviewOfferData[$v["interview_id"]]) && in_array($interviewOfferData[$v['interview_id']]['********'], $offerIds) ? 1 : 0;

            //员工雇佣类型
            $resumeData["dataList"][$k]['hire_type_text'] = $v['hire_type'] ? $this->getTranslation()->_('hire_type_'.$v['hire_type']) : '';

            //面试官 staff_id
            $resumeData["dataList"][$k]["interviewer_id"] = $interviewSubscribeData[$v["interview_id"]]["interviewer_id"] ?? "";
            //面试官 姓名
            $interviewer_id = $interviewSubscribeData[$v["interview_id"]]["interviewer_id"] ?? 0;
            $resumeData["dataList"][$k]["interviewer_name"] = StaffServer::getStaffNameView($staffData[$interviewer_id] ?? [],2,$interviewer_id);

            //hc-岗位名称
            if ($v['hc_job_id']) {
                $resumeData["dataList"][$k]['hc_job_name'] = $jobListData[$v['hc_job_id']];
            } else {
                $resumeData["dataList"][$k]['hc_job_name'] = '';
            }

            // 备选岗位
            if ($v['alternative_job_ids']) {

                $resumeData["dataList"][$k]['alternative_job_names'] = $this->formatAlternativeJobs($v['alternative_job_ids'],
                    $jobListData);
            } else {
                $resumeData["dataList"][$k]['alternative_job_names'] = '';
            }

            // 历史hc
            $historyHcsStr = '';
            if (isset($historyHcs[$v['resume_id']])) {
                $historyHcsStr = implode(", ", array_diff($historyHcs[$v['resume_id']], [$v['hc_id']]));
            }
            $resumeData['dataList'][$k]['history_hcs'] = $historyHcsStr;

            //hc-部门名称
            if ($v['hc_department_id']) {
                $resumeData["dataList"][$k]['hc_department_name'] = $departmentData[$v['hc_department_id']] ?? '';
                $resumeData["dataList"][$k]['is_express_line']    = $this->isExpressLine($v['hc_department_id']) ? 1 : 0;
            } else {
                $resumeData["dataList"][$k]['hc_department_name'] = '';
                $resumeData["dataList"][$k]['is_express_line']    = 0;
            }
            //没有反馈信息的，不纳入PC操作下的查看面试反馈项
            if (isset($result[$v['interview_id']])) {
                $resumeData["dataList"][$k]['interview_msg'] = $result[$v['interview_id']];
            } else {
                $resumeData["dataList"][$k]['interview_msg'] = [];
            }
            //反馈信息
            $resumeData["dataList"][$k]["is_feedback"] = intval($new_operations[$v['sub_id']] ?? 0);

            //jd-部门名称
            $resumeData["dataList"][$k]['department_name'] = $departmentData[$v['department_id']] ?? '';
            //简历-状态
            $resumeData["dataList"][$k]['state_name'] = $state_array[$v['state']] ?? '';

            //追加显示简历offer签字状态字段
            if ($offer_sign_lasted_data) {
                $pdf_domain_prefix                                    = $this->getDI()->getConfig()->application['img_prefix'];
                $offer_sign_submitter                                 = $offer_sign_lasted_data[$v["interview_id"]]['submitter_id'] ?? 0;//offer签字发起人
                $offer_sign_state                                     = $offer_sign_lasted_data[$v["interview_id"]]['approve_state'] ?? 0;
                $offer_sign_state_name                                = $offer_sign_state ? $this->getTranslation()->_('offer_sign_state_' . $offer_sign_state) : '';
                $offer_sign_pdf_path                                  = $offer_sign_lasted_data[$v["interview_id"]]['pdf_path'] ?? '';
                $resumeData['dataList'][$k]['offer_sign_status']      = $offer_sign_state;
                $resumeData['dataList'][$k]['offer_sign_status_text'] = $offer_sign_state_name;
                $resumeData['dataList'][$k]['offer_sign_pdf_path']    = $offer_sign_pdf_path ? $pdf_domain_prefix . $offer_sign_pdf_path : '';
                $resumeData['dataList'][$k]['is_offersign_submitter'] = ($offer_sign_submitter == $userinfo['id']) ? 1 : 0;//是否是offer签字发起人，1-是，0-否，该字段与is_can_revoke结合使用，都是1是可撤销offer签字
            }

            //来源
            $resumeData['dataList'][$k]['source'] = $v['source'] == 2
            && isset($submitterInfos[$v['submitter_id']])
            && $submitterInfos[$v['submitter_id']]['department_id'] != 7 ? "3" : $v['source'];
            // 员工id
            $resumeData['dataList'][$k]['submitter_id'] = $v['submitter_id'];

            //追加工作城市 work_city
            $provinceName = $cityData[$v['work_code']]['name'] ?? '';
            //期望工作城市-市，MY显示输入内容不进行转义，省还用上变address_id
            $cityName = $cityListKy[$v['work_city_id']] ?? '';
            $resumeData["dataList"][$k]['work_city'] = $provinceName.' '.$cityName;

            //hc-网点一级行政区
            $resumeData["dataList"][$k]['work_name'] = isset($cityData[$v['province_code']]['name']) ? $cityData[$v['province_code']]['name'] : '';
            //hc-网点名称
            $resumeData["dataList"][$k]['worknode_name'] = $worknodeData[$v['worknode_id']]["name"];
            //hc-网点所属区域
            $resumeData["dataList"][$k]['sorting_no'] = $worknodeData[$v['worknode_id']]["sorting_no"];
            //hc-网点地址
            $resumeData["dataList"][$k]['hc_detail_address'] = $worknodeData[$v['worknode_id']]["detail_address"];
            //简历-姓名
            if (isCountry('Ph')) {
                $resumeData["dataList"][$k]['name'] = $v['name'];
            } else {
                $resumeData["dataList"][$k]['name'] = $v['first_name']." ".$v['last_name'];
            }
            //简历-姓名
            $resumeData["dataList"][$k]['name_en'] = $v['first_name_en']." ".$v['last_name_en'];
            //简历-email
            $resumeData["dataList"][$k]['email'] = $v['email'] ?? '';
            //简历-招聘渠道
            $resumeData["dataList"][$k]['recruit_channel_name'] = $recruitChannel[$v['recruit_channel']];
            //检测是否在黑名单
            $resumeData["dataList"][$k]['is_blacklist'] = $blackListData[$v["credentials_num"]] ? 1 : 0;
            //检测性别
            //$resumeData["dataList"][$k]['sex'] = $server->getResumeSex($v['sex'], $v['call_name']);
            //简历-所属网点
            $resumeData["dataList"][$k]['store_name'] = $worknodeData[$v["store_id"]]["name"] ?? "";
            $resumeData["dataList"][$k]['sex_title']  = enums::$sex_title[$v['sex']] ?? '';
            //简历-期望岗位
            $resumeData["dataList"][$k]['expect_job_name'] = $jobListData[$v['expect_job_id']] ?? '';
            //入职-员工工号
            $resumeData["dataList"][$k]['staff_id'] = $resumeStaffList[$v['resume_id']]['staff_id'] ?? '';
            //简历入库时间
            $resumeData["dataList"][$k]['resume_created_at'] = $v['resume_created_at'] ?? '';
            //入职-办理时间
            $resumeData["dataList"][$k]['operate_date'] = $resumeStaffList[$v['resume_id']]['operate_date'] ?? '';
            // 薪资审批状态
            $resumeData["dataList"][$k]['approval_status']      = isset($approvalList[$v['resume_id']]) ? $approvalList[$v['resume_id']]['status'] : '';
            $resumeData["dataList"][$k]['approval_status_text'] = isset($approvalList[$v['resume_id']]) ? $this->translationTransfer($approvalList[$v['resume_id']]['status']) : '';
            // 基本薪资  通过试用期后薪资
            $resumeData["dataList"][$k]['basic_salary']      = isset($approvalList[$v['resume_id']]) ? $approvalList[$v['resume_id']]['basic_salary'] : '';
            $resumeData["dataList"][$k]['trial_salary']      = isset($approvalList[$v['resume_id']]) ? $approvalList[$v['resume_id']]['trial_salary'] : '';
            $resumeData["dataList"][$k]['renting']           = isset($approvalList[$v['resume_id']]) ? $approvalList[$v['resume_id']]['renting'] : '';
            $resumeData["dataList"][$k]['company']           = isset($approvalList[$v['resume_id']]) ? $approvalList[$v['resume_id']]['company'] : '';
            $resumeData["dataList"][$k]['job_title_grade']   = isset($approvalList[$v['resume_id']]) ? $approvalList[$v['resume_id']]['job_title_grade'] : '';
            $resumeData["dataList"][$k]['position']          = isset($approvalList[$v['resume_id']]) ? $approvalList[$v['resume_id']]['position_allowance'] : '';
            $resumeData["dataList"][$k]['xinghuo_allowance'] = isset($approvalList[$v['resume_id']]) ? $approvalList[$v['resume_id']]['xinghuo_allowance'] : '';
            $resumeData["dataList"][$k]['approve_job_id']    = $v['job_title_id'] ?? '';
            $resumeData["dataList"][$k]['approve_job_name']  = $approveJobs[$v['job_title_id']]['job_name'] ?? '';

            // 1. 薪资审批表单、offer表单，谁发起的谁可查看
            $resumeData['dataList'][$k]['salary_view_permission'] =
                (
                    (isset($approvalList[$v['resume_id']]) && $approvalList[$v['resume_id']]['submitter_id'] == $userinfo['id']) ||
                    ($userinfo['salary_structure_permission'] == enums::$salary_structure_permission['on'])
                ) ? 1 : 0;

            $resumeData['dataList'][$k]['is_can_revoke'] = $approvalList[$v['resume_id']]['submitter_id'] == $userinfo['id'] ? 1 : 0;

            //简历年龄
            if (isset($v['date_birth']) && $v['date_birth']) {
                if ((int)date("Y") < (int)date('Y', strtotime($v['date_birth']))) {
                    $age = (int)(date("Y-m-d") - ($v['date_birth'] - 543));
                } else {
                    $age = (int)(date("Y-m-d") - $v['date_birth']);
                }
            } else {
                $age = '';
            }
            $resumeData["dataList"][$k]['age'] = $age;
            //简历银行卡号
            $resumeData["dataList"][$k]['bank_no'] = $staffData[$resumeData["dataList"][$k]['staff_id']]['bank_no'] ?? '';
            $hc_job_title_name = $positionData[$v['job_title_id']] ?? '';
            $resumeData["dataList"][$k]['hc_job_title_name'] =$hc_job_title_name;

            //职位名称
            $resumeData["dataList"][$k]['job_title_name'] = !empty($v['job_title_id']) ? sprintf("(%s) %s", $v['job_title_id'], $hc_job_title_name) : '';

            //根据职位判断是否是一线员工职位标识（offer签字模块使用该逻辑）
            $resumeData["dataList"][$k]["is_front_line_job"] = $this->isFrontLineJob($v['job_title_id']);
            //根据部门+职位 判断是否是一线员工职位标识（简历筛选、薪资审批使用该逻辑）
            // 面试回退也使用该逻辑
            $resumeData["dataList"][$k]["is_first_line_job"] = empty($v['hc_id']) ? null : $this->isFirstLineJob($v['hc_department_id'],
                $v['job_title_id']);

            //验证是否可以编辑简历hc
            $updateHcSettingParams['staff_info_id']            = $this->userInfo['id'] ?? 0;
            $updateHcSettingParams['job_title']                = $v['job_title_id'] ?? 0;
            $updateHcSettingParams['hire_type']                = $v['hire_type'] ?? 0;
            $updateHcSettingParams['is_first_line_job']        = $resumeData["dataList"][$k]["is_first_line_job"];
            $resumeData["dataList"][$k]["is_update_resume_hc"] = InterviewHelper::isUpdateResumeHc($updateHcSettingParams);

            //HC不是HCM-设置中心-系统设置-WinHR-non_recommend_resume_ids配置职位：操作列显示“推荐简历”
            //https://flashexpress.feishu.cn/wiki/JQ7TwB5POicjAqk6bHXcxg6gnTf
            $resumeData["dataList"][$k]["is_recommended"] = $this->isRecommendedResume([
                'hc_id'         => $v['hc_id'],
                'department_id' => $v['hc_department_id'],
                'job_title'     => $v['job_title_id'],
            ]);

            //offer-班次 - 默认值
            $shift_id = 0;
            if ($v['worknode_id'] != -1) {
                if (!in_array($resumeData["dataList"][$k]['state'], [1, 5, 10, 20])) {
                    $shift_id = $interviewOfferData[$v["interview_id"]]["shift_id"];
                }
                if (in_array($v['hc_department_id'], $child_department_ids)) {
                    $shift_id = 7;
                } else {
                    switch ($v['hc_department_id']) {
                        case 65:
                        case 51:
                            $shift_id = 7;
                            break;
                        case 32:
                            $shift_id = 5;
                            break;
                        default:
                            $shift_id = 7;
                            break;
                    }
                }
                if (isset($shift_list[$shift_id])) {
                    $resumeData["dataList"][$k]["shift_id"] = $shift_id;
                    //班次类型
                    $shift_type                                    = $shift_list[$shift_id]['type'] ?? '';
                    $resumeData['dataList'][$k]['shift_type']      = $shift_type;
                    $resumeData['dataList'][$k]['shift_type_text'] = empty($shift_type) ? '' : $this->getTranslation()->_('shift_' . $shift_type);
                    $resumeData['dataList'][$k]['shift_text']      = ($shift_list[$shift_id]['start'] ?? '') . '-' . ($shift_list[$shift_id]['end'] ?? '');
                } else {
                    $resumeData["dataList"][$k]["shift_id"]        = $shift_id;
                    $resumeData['dataList'][$k]['shift_type']      = '';
                    $resumeData['dataList'][$k]['shift_type_text'] = '';
                    $resumeData['dataList'][$k]['shift_text']      = '';
                }
            } else {
                $resumeData["dataList"][$k]["shift_id"]        = $shift_id;
                $resumeData['dataList'][$k]['shift_type']      = '';
                $resumeData['dataList'][$k]['shift_type_text'] = '';
                $resumeData['dataList'][$k]['shift_text']      = '';
            }

            //by 推荐人字段信息

            //简历来源
            $resumeData["dataList"][$k]['source_text'] = empty($v['source']) ? '' :$this->getTranslation()->_('resume_src_'.$v['source']) ;

            //by推荐网点
            if ($v["recommend_store_id"]) {
                $resumeData["dataList"][$k]['recommend_store_name'] = $worknodeData[$v['recommend_store_id']]["name"] ?? '';
            }
            //by推荐人
            if ($v["recommender_staff_id"]) {
                $current_recommender                                       = $staffData[$v["recommender_staff_id"]] ?? [];
                $resumeData["dataList"][$k]["recommender_name"]            = ($current_recommender['name'] ?? '') . "({$v["recommender_staff_id"]})";
                $resumeData["dataList"][$k]["recommender_department_name"] = $departmentData[$current_recommender['node_department_id']] ?? '';
                $resumeData["dataList"][$k]["recommender_job_name"]        = $job_title_map[$current_recommender['job_title']] ?? '';
            }
            //by推荐简历审核状态
            if ($v['approve_state']) {
                $resumeData["dataList"][$k]["approve_state_text"] = $this->getTranslation()->_('resume_approve_state_' . $v['approve_state']);
            }

            //简历薪资信息权限控制
            $is_have_salary_permission = $resumeServer->isHaveSalaryOrAnnexPermission(
                $v['resume_id'],
                $v['filter_state'],
                $v['state'],
                $v['recruiter_id'],
                $this->userInfo['id'],
                $this->userInfo['expected_salary_permission']
            );
            if (!$is_have_salary_permission) {
                $resumeData['dataList'][$k]['entry_salary']   = '*****';
                $resumeData['dataList'][$k]['current_salary'] = '*****';
            }

            //by 推荐人字段信息

            //简历来源
            $resume_source                             = array_flip(enums::$resume_source);
            $resumeData["dataList"][$k]['source_text'] = $resume_source[$v['source']] ?? null;
            //by推荐网点
            if ($v["recommend_store_id"]) {
                $resumeData["dataList"][$k]['recommend_store_name'] = $worknodeData[$v['recommend_store_id']]["name"] ?? '';
            }

            //by推荐简历审核状态
            if ($v['resume_approve_state']) {
                $resumeData["dataList"][$k]["resume_approve_state_text"] = $this->getTranslation()->_('resume_approve_state_' . $v['resume_approve_state']);
            }

            //简历筛选-筛选人姓名
            $resumeData["dataList"][$k]["filter_staff_name"] = $staffData[$v["filter_staff_id"]]['name'] ?? '';

            //获取简历筛选反馈等待时长
            $resumeData["dataList"][$k]["filter_wating_feedback"] = $this->getWaitFeedbackTime($v['recommend_time'],
                $v['feedback_time']);

            //推荐时间转换成对应时区日期显示
            $resumeData["dataList"][$k]["recommend_time"] = date('Y-m-d H:i:s',
                (strtotime($v['recommend_time']) + $add_hour * 3600));

            $resumeData["dataList"][$k]["filter_not_pass_check_hc_is_change"] = $state == enums::$resume_filter_state['not_pass'] && $v['hc_id_is_change'] ? 1 : 0;
            $resumeData["dataList"][$k]["delivery_time"]                      = show_time_zone($v['delivery_time']);
            $resumeData["dataList"][$k]["ai_score"]                           = is_null($v['ai_score']) ? '' : $v['ai_score'].$this->getTranslation()->_('unit_fen');

            //大区片区
            $manageRegionId                                   = $worknodeData[$v['worknode_id']]['manage_region'] ?? '';
            $resumeData["dataList"][$k]['manage_region_name'] = $regionList[$manageRegionId] ?? '';
            $managePieceId                                    = $worknodeData[$v['worknode_id']]['manage_piece'] ?? '';
            $resumeData["dataList"][$k]['manage_piece_name']  = $pieceList[$managePieceId] ?? '';

            //优先级
            $resumeData["dataList"][$k]["priority_name"] = $this->getTranslation()->_(HrhcModel::$priority_id_list[$v['priority_id']] ?? '');
        }

        $this->getDI()->get('logger')->write_log("interviewResumeListAction resumeData:" . json_encode($resumeData['pagination'],
                JSON_UNESCAPED_UNICODE), 'info');
        $returnData['data'] = $resumeData;

        return $this->checkReturn($returnData);
    }

    /**
     * 计算两个时间之间的时长显示（xx天xx小时xx分）
     * @param $left_time
     * @param $right_time
     * @return string
     */
    public function getWaitFeedbackTime($left_time, $right_time)
    {

        if (empty($left_time) || empty($right_time)) {
            return '';
        }
        $left_time  = strtotime($left_time);
        $right_time = strtotime($right_time);

        $is_writing = $right_time - $left_time;
        if ($is_writing > 0) {
            $timeInfo = getTimeDifference($right_time, $left_time);
            $str      = '';
            if ($timeInfo['day'] > 0) {
                $str .= sprintf($this->getTranslation()->_('wait_time_feedback_interview_day'), $timeInfo['day']);
            }
            if ($timeInfo['hour'] > 0) {
                $str .= sprintf($this->getTranslation()->_('wait_time_feedback_interview_hour'), $timeInfo['hour']);
            }
            if ($timeInfo['min'] > 0) {
                $str .= sprintf($this->getTranslation()->_('wait_time_feedback_interview_minutes'), $timeInfo['min']);
            }

            return $str;
        } else {
            return '-';
        }
    }

    /**
     * 是否是快递业务线
     *
     * @param $departmentId
     * @return bool
     *
     */
    public function isExpressLine($departmentId)
    {
        if (!$this->departmentIds) {

            $departmentIds       = (new SettingEnvServer())->getSetVal('express_line_departments');
            $this->departmentIds = explode(',', $departmentIds);
        }

        if ($this->departmentIds) {
            if (!$this->ancestries) {

                $sysDepartments   = SysDepartmentModel::find([
                    'columns'    => 'id, ancestry_v3',
                    'conditions' => ' id in ({ids:array}) ',
                    'bind'       => ['ids' => $this->departmentIds],
                ])->toArray();
                $this->ancestries = array_column($sysDepartments, 'ancestry_v3');
            }

            foreach ($this->ancestries as $ancestry) {
                if (SysDepartmentModel::findFirst([
                    'conditions' => ' id = :id: and ancestry_v3 like :ancestry_v3: ',
                    'bind'       => [
                        'id'          => $departmentId,
                        'ancestry_v3' => $ancestry . '%',
                    ],
                ])) {

                    return true;
                }
            }
        }

        return false;
    }


    public function formatAlternativeJobs($alternativeJobIds, $jobListData)
    {
        $alternativeJobIds = explode(',', $alternativeJobIds);
        $jobNames          = [];
        foreach ($alternativeJobIds as $alternativeJobId) {
            if (isset($jobListData[$alternativeJobId])) {
                $jobNames[] = $jobListData[$alternativeJobId];
            }
        }

        return implode(',', $jobNames);
    }

    public function getHistoryHc($resumeIds)
    {
        $historyHcs = [];
        if ($resumeIds) {
            $interviews = ResumeOutLogModel::find([
                'conditions' => ' resume_id in ({resume_ids:array}) ',
                'bind'       => [
                    'resume_ids' => $resumeIds,
                ],
            ])->toArray();

            foreach ($interviews as $interview) {
                $historyHcs[$interview['resume_id']][] = $interview['hc_id'];
            }
        }
        return $historyHcs;
    }


    /**
     *
     * 获取简历薪资审批最终状态
     *
     *
     * @param $resumeIds
     */
    public function getLastSalaryApproval($resumeIds)
    {
        $list = [];
        if ($resumeIds && is_array($resumeIds)) {
            $sql  = "--
                select
                        *
                from salary_approve
                where id in (
                    select max(`id`) from salary_approve where resume_id in (".implode(",", $resumeIds).") group by resume_id
               ) and status != 9";
            $list = $this->getDI()->get("db")->fetchAll($sql, Db::FETCH_ASSOC);
            $list = array_column($list, null, 'resume_id');
        }

        return $list;
    }

    /**
     * 薪资审批 状态 翻译转换
     *
     * @param $approvalStatus
     */
    public function translationTransfer($approvalStatus)
    {
        $translationMaps = [
            1 => $this->getTranslation()->_('4015'),
            2 => $this->getTranslation()->_('4017'),
            3 => $this->getTranslation()->_('4005'),
            4 => $this->getTranslation()->_('4031'),
        ];

        return isset($translationMaps[$approvalStatus]) ? $translationMaps[$approvalStatus] : "";
    }

    /**
     * @deprecated  废弃
     * 通过网点获取网点负责人
     * HUB    [15] Branch Manager
     *    [272] HUB Supervisor
     *
     * SHOP    [101] Shop Supervisor
     *        [296] Store Supervisor
     *
     * DC/SP [16]   Branch Supervisor
     *         [451] Assistant Branch Supervisor
     * @desc 按照职位ID以及工号找，顺序 都取最小的
     */
    public function storeIdGetLeader($store_id)
    {
        $job_title = '(15,16,101,272,296,451)';
        $sql       = "select id,job_title,name,mobile,organization_id from staff_info where 
			organization_id = '{$store_id}'
			and state = 1 -- 取在职员工
			and job_title in {$job_title}
			order by
			job_title asc,
			id asc 
			limit 1
			";
        $ret       = $this->getDI()->get("db_fle")->query($sql)->fetch(\PDO::FETCH_ASSOC);
        return $ret;
    }

    /**
     *
     * 文件短信内容发送
     *
     * @param $mobile
     * @param $content
     *
     *
     */
    public function sendSms($mobile, $content)
    {

        $config  = $this->getDI()->get('config');
       // $content = addcslashes($content, "\r\n");

        $result = curlJsonRpc($config['api']['api_send_sms'], curlJsonRpcStr($mobile, $content, "win_hr_interview", 3));

        return $result;
    }

    /**
     * 获取发送offer短信模板
     * @param $resumeId
     * @param $********
     * @return array
     */
    public function getSendOfferSmsTpl($resumeId, $******** = 0)
    {
        $resumeInfo = (new ResumeRepository())->getOfferInfoByResumeId($resumeId);
        $result     = [];
        if (empty($resumeInfo)) {
            return $result;
        }
        $result['credentials_category'] = $resumeInfo['credentials_category'];
        $result['phone_area_code']      = !empty($resumeInfo['phone_area_code']) ? $resumeInfo['phone_area_code'] : '66';
        $result['email']                = $resumeInfo['email'];
        $result['phone']                = $resumeInfo['phone'];
        if (isCountry('Ph')) {
            $result['name'] = $resumeInfo['name'];
        } else {
            $result['name'] = $resumeInfo['first_name']." ".$resumeInfo['last_name'];
        }
        $result['content'] = $this->sendOfferSmsTpl($resumeId, $********);
        return $result;
    }

    /**
     * 发送offer 短信模板
     *
     * @param $resumeId
     * @param $countryCode
     *
     */
    public function sendOfferSmsTpl($resumeId, $******** = 0)
    {
        $countryCode       = strtolower(env('country_code'));
        $resumeInfo = $this->resumeData($resumeId);
        $tmp        = "";
        if (strtolower($countryCode) == 'my') {

            $sql =  "select * from hr_interview_offer where id = :********";
            //必须查主库
            $offerInfo = $this->getDI()->get("db")->fetchOne($sql, Db::FETCH_ASSOC,[
                '********' => $********
            ]);

            //月薪制
            if ($offerInfo && $offerInfo['company_id'] == HrStaffInfoModel::CONTRACT_COMPANY_ID_LNT) {
                $param['store_name']           = $resumeInfo['hr_hc']['store_name'] ?? '';
                $param['expected_entry_date']  = !empty($resumeInfo['hr_interview_offer']['work_time']) ? date("Y/m/d", strtotime($resumeInfo['hr_interview_offer']['work_time'])) : '';
                $param['store_manager_mobile'] = $resumeInfo['hr_hc']['store_manager_mobile'] ?: '';
                $tmp = "We are pleased to offer you the position of {$resumeInfo['hr_interview_offer']['hr_job_title']['job_name']}  at LNT Express. You will be assigned to provide service to the following client:\r\n\r\n";
                $tmp     .= "Client:  Flash Malaysia Express Sdn Bhd\r\n";
                $tmp     .= "Location: {$param['store_name']}\r\n";
                $tmp     .= "Start Date: {$param['expected_entry_date']}\r\n";
                $tmp     .= "Client contact: {$param['store_manager_mobile']}\r\n";
                $tmp     .= "LNT contact: {$resumeInfo['hr_interview_offer']['submitter']['mobile_company']}\r\n\r\n";
                $tmp     .= "Thank you.";
                return $tmp;
            }
            //个人代理
            if (isset($resumeInfo['hr_hc']['hire_type']) && in_array($resumeInfo['hr_hc']['hire_type'],HrStaffInfoModel::$agentTypeTogether)) {
                $param['store_name']           = isset($resumeInfo['hr_hc']['store_name']) ? $resumeInfo['hr_hc']['store_name'] : '';
                $param['expected_entry_date']  = !empty($resumeInfo['hr_interview_offer']['work_time']) ? date("Y/m/d", strtotime($resumeInfo['hr_interview_offer']['work_time'])) : '';
                $param['store_manager_name']   = $resumeInfo['hr_hc']['store_manager_name'] ?: '';
                $param['store_manager_mobile'] = $resumeInfo['hr_hc']['store_manager_mobile'] ?: '';
                $tmp .= "Hi {$resumeInfo['first_name']}\r\n\r\n";
                $tmp .= "Thank you for applying to become an independent contractor with Flash Express.\r\n\r\n";
                $tmp .= "We would like to invite you to come to our service point to sign the independent contractor service contract to start service :\r\n";
                $tmp     .= "Location: {$param['store_name']}\r\n";
                $tmp     .= "Date: {$param['expected_entry_date']}\r\n";
                $tmp     .= "Contact Person: {$param['store_manager_name']}\r\n";
                $tmp     .= "Contact No.:{$param['store_manager_mobile']}\r\n\r\n";
                $tmp     .= "We look forward to seeing you there. Otherwise, we'll assume you're no longer interested to proceed further.\r\n\r\n";
                $tmp     .= "Have a nice day!\r\n";
                return $tmp;
            }
            // 马来
            $tmp .= "Employment Confirmation Message\r\n\r\n";
            $tmp .= "Welcome to join Flash Malaysia Express family!\r\n";
            $tmp .= "Flash sincerely invites : ".$resumeInfo['first_name']."\r\n";

            $tmp     .= "Working Location : " . (isset($resumeInfo['hr_hc']['store_name']) ? $resumeInfo['hr_hc']['store_name'] : '') . "\r\n";
            $tmp     .= "Temporary Location（For branches that not yet open）: - \r\n";
            $tmp     .= "Onboard date : " . (isset($resumeInfo['hr_interview_offer']['work_time']) ? date("Y/m/d",
                    strtotime($resumeInfo['hr_interview_offer']['work_time'])) : '') . "\r\n";
            $tmp     .= "Position : " . (isset($resumeInfo['hr_interview_offer']['hr_job_title']['job_name']) ? $resumeInfo['hr_interview_offer']['hr_job_title']['job_name'] : '') . "\r\n";
            $manager = isset($resumeInfo['hr_hc']['store_manager']) ? $resumeInfo['hr_hc']['store_manager'] : '';

            $tmp     .= "Branch Contact Person : " . $manager . "\r\n";
            $staff   = isset($resumeInfo['hr_interview_offer']['submitter']) && $resumeInfo['hr_interview_offer']['submitter'] ? (string)$resumeInfo['hr_interview_offer']['submitter']['name'] . "(" . (string)$resumeInfo['hr_interview_offer']['submitter']['mobile_company'] . ")" : "";
            $tmp     .= "Contact Person : " . $staff . "\r\n\r\n";
            $tmp     .= "Have a nice day!\r\n";

        } else {
            if (strtolower($countryCode) == 'ph') {
                //  菲律宾
                if (isset($resumeInfo['hr_hc']['hire_type']) && $resumeInfo['hr_hc']['hire_type'] == HrStaffInfoModel::HIRE_TYPE_AGENT){
                    // 个人代理
                    $call_en = enums::$call_en;
                    $call_name = $resumeInfo['call_name'] ? $call_en[$resumeInfo['call_name']] : '';
                    $name = $call_name.$resumeInfo['name'];
                    $tmp .= "Welcome to Flash Express!\r\n";
                    $tmp .= "Recommended Revision: Hi ".$name."! We are happy to welcome you as a contractor of Flash Express!\r\n";
                    $tmp .= "The details of your assignment are as follows: \r\n";
                    $tmp .= "Delivery Date: ".(isset($resumeInfo['hr_interview_offer']['work_time']) ? date("Y/m/d",
                            strtotime($resumeInfo['hr_interview_offer']['work_time'])) : '')." at 9:00 AM\r\n";
                    $tmp .= "Role: Independent Contractor\r\n";
                    $tmp .= "Location: ".(isset($resumeInfo['hr_hc']['store_name']) ? $resumeInfo['hr_hc']['store_name'] : '')."\r\n";
                    $store_manager_name = isset($resumeInfo['hr_hc']['store_manager_name']) ? $resumeInfo['hr_hc']['store_manager_name'] : '';
                    $store_manager_mobile = isset($resumeInfo['hr_hc']['store_manager_mobile']) ? $resumeInfo['hr_hc']['store_manager_mobile'] : '';
                    $tmp .= "Contact Persons: ".$store_manager_name." (Contact No.: ".$store_manager_mobile.")\r\n";
                    $tmp .= "\r\n";
                    $tmp .= "Good luck!";

                }else{
                    $tmp .= "Job Confirmation\r\n";
                    $tmp .= "Welcome to the Flash Express family!\r\n";
                    $tmp .= "We would like to invite : " . $resumeInfo['name'] . "\r\n";
                    $tmp     .= "Work Place :".(isset($resumeInfo['hr_hc']['store_name']) ? $resumeInfo['hr_hc']['store_name'] : '')."\r\n";
                    $tmp     .= "Training Branch (for Branch haven't opened) : - \r\n";
                    $tmp     .= "Start Date : ".(isset($resumeInfo['hr_interview_offer']['work_time']) ? date("Y/m/d",
                            strtotime($resumeInfo['hr_interview_offer']['work_time'])) : '')."\r\n";
                    $tmp     .= "Position : ".(isset($resumeInfo['hr_interview_offer']['hr_job_title']['job_name']) ? $resumeInfo['hr_interview_offer']['hr_job_title']['job_name'] : '')."\r\n";
                    $manager = isset($resumeInfo['hr_hc']['store_manager']) ? $resumeInfo['hr_hc']['store_manager'] : '';
                    $tmp     .= "Branch Supervisor contact :".$manager."\r\n";
                    $staff   = isset($resumeInfo['hr_interview_offer']['submitter']) && $resumeInfo['hr_interview_offer']['submitter'] ? (string)$resumeInfo['hr_interview_offer']['submitter']['name']."(".(string)$resumeInfo['hr_interview_offer']['submitter']['mobile_company'].")" : "";
                    $tmp     .= "HR contact :".$staff."\r\n";
                    $tmp     .= "Wishing you good luck with your new job!\r\n";
                }
            } else {
                if (strtolower($countryCode) == 'vn') {
                    //  越南
                    $tmp .= "SMS Thư mời nhận việc\r\n";
                    $tmp .= "Chào mừng bạn gia nhập Flash Express.\r\n";
                    $tmp .= "Hoan nghênh bạn : ".$resumeInfo['first_name']." ".$resumeInfo['last_name']."\r\n";

                    $tmp     .= "Bắt đầu công việc tại chi nhánh :".(isset($resumeInfo['hr_hc']['store_name']) ? $resumeInfo['hr_hc']['store_name'] : '')."\r\n";
                    $tmp     .= "Thực tập tại chi nhánh (chỉ dành cho các chi nhánh chưa mở) : - \r\n";
                    $tmp     .= "Ngày bắt đầu : ".(isset($resumeInfo['hr_interview_offer']['work_time']) ? date("Y/m/d",
                            strtotime($resumeInfo['hr_interview_offer']['work_time'])) : '')."\r\n";
                    $tmp     .= "Chức vụ : ".(isset($resumeInfo['hr_interview_offer']['hr_job_title']['job_name']) ? $resumeInfo['hr_interview_offer']['hr_job_title']['job_name'] : '')."\r\n";
                    $manager = isset($resumeInfo['hr_hc']['store_manager']) ? $resumeInfo['hr_hc']['store_manager'] : '';
                    $tmp     .= "Người phụ trách chi nhánh :".$manager."\r\n";
                    $staff   = isset($resumeInfo['hr_interview_offer']['submitter']) && $resumeInfo['hr_interview_offer']['submitter'] ? (string)$resumeInfo['hr_interview_offer']['submitter']['name']."(".(string)$resumeInfo['hr_interview_offer']['submitter']['mobile_company'].")" : "";
                    $tmp     .= "Số điện thoại của HR :".$staff."\r\n";
                    $tmp     .= "Trân trọng thông báo và chúc bạn may mắn.\r\n";
                } else {
                    if (strtolower($countryCode) == 'la') {
                        //  老挝
                        $tmp .= "SMS ຢືນຢັນການຈ້າງວຽກ\r\n";
                        $tmp .= "ຍິນດີຕ້ອນຮັບທ່ານເຂົ້າສູ່ຄອບຄົວ ແຟັຣດ ເອັກເພສ໌\r\n";
                        $tmp .= "ທາງບໍລິສັດ ຂໍນັດຫມາຍທ່ານ : ".$resumeInfo['first_name']." ".$resumeInfo['last_name']."\r\n";

                        $tmp     .= "ເລີ່ມວຽກທີ່ສາຂາ :".(isset($resumeInfo['hr_hc']['store_name']) ? $resumeInfo['hr_hc']['store_name'] : '')."\r\n";
                        $tmp     .= "ຝຶກງານທີ່ສາຂາ : - \r\n";
                        $tmp     .= "ວັນທີ່ເລີ່ມງານ : ".(isset($resumeInfo['hr_interview_offer']['work_time']) ? date("Y/m/d",
                                strtotime($resumeInfo['hr_interview_offer']['work_time'])) : '')."\r\n";
                        $tmp     .= "ຕໍາແຫນ່ງ : ".(isset($resumeInfo['hr_interview_offer']['hr_job_title']['job_name']) ? $resumeInfo['hr_interview_offer']['hr_job_title']['job_name'] : '')."\r\n";
                        $manager = isset($resumeInfo['hr_hc']['store_manager']) ? $resumeInfo['hr_hc']['store_manager'] : '';
                        $tmp     .= "ຕິດຕໍ່ຜູ້ຮັບຜິດຊອບສາຂາ :".$manager."\r\n";
                        $staff   = isset($resumeInfo['hr_interview_offer']['submitter']) && $resumeInfo['hr_interview_offer']['submitter'] ? (string)$resumeInfo['hr_interview_offer']['submitter']['name']."(".(string)$resumeInfo['hr_interview_offer']['submitter']['mobile_company'].")" : "";
                        $tmp     .= "ເບີໂທຜູ້ຮັບສະຫມັກ :".$staff."\r\n";
                        $tmp     .= "ຂໍໃຫ້ມີຄວາມສຸກໃນການເຮັດວຽກຂອງທ່ານ\r\n";
                    } else {
                        if (strtolower($countryCode) == 'id') {
                            //  印尼
                            $tmp .= "SMS konfirmasi pekerjaan\r\n";
                            $tmp .= "Selamat datang di keluarga Flash Express.\r\n";
                            $tmp .= "Perusahaan ingin membuat janji dengan Anda : ".$resumeInfo['first_name']." ".$resumeInfo['last_name']."\r\n";

                            $tmp     .= "Mulai bekerja di cabang :".(isset($resumeInfo['hr_hc']['store_name']) ? $resumeInfo['hr_hc']['store_name'] : '')."\r\n";
                            $tmp     .= "Magang cabang (untuk cabang yang belum buka saja) : - \r\n";
                            $tmp     .= "Tanggal mulai : ".(isset($resumeInfo['hr_interview_offer']['work_time']) ? date("Y/m/d",
                                    strtotime($resumeInfo['hr_interview_offer']['work_time'])) : '')."\r\n";
                            $tmp     .= "Posisi : ".(isset($resumeInfo['hr_interview_offer']['hr_job_title']['job_name']) ? $resumeInfo['hr_interview_offer']['hr_job_title']['job_name'] : '')."\r\n";
                            $manager = isset($resumeInfo['hr_hc']['store_manager']) ? $resumeInfo['hr_hc']['store_manager'] : '';
                            $tmp     .= "Kontak penanggung jawab cabang :".$manager."\r\n";
                            $staff   = isset($resumeInfo['hr_interview_offer']['submitter']) && $resumeInfo['hr_interview_offer']['submitter'] ? (string)$resumeInfo['hr_interview_offer']['submitter']['name']."(".(string)$resumeInfo['hr_interview_offer']['submitter']['mobile_company'].")" : "";
                            $tmp     .= "Nomor telepon perekrut :".$staff."\r\n";
                            $tmp     .= "Semoga berhasil dengan pekerjaan Anda.\r\n";
                        } else {
                            if (strtolower($countryCode) == 'th' && isset($resumeInfo['hr_hc']['hire_type']) && $resumeInfo['hr_hc']['hire_type'] == 5) {
                                // 泰国实习生短信模版
                                $tmp .= "SMS ยืนยันการจ้างงาน\r\n";
                                $tmp .= "ยินดีต้อนรับท่านเข้าสู่ครอบครัว แฟลช เอ็กซ์เพรส\r\n";
                                $tmp .= "ทางบริษัทฯ ขอนัดหมายคุณ : ".$resumeInfo['first_name']." ".$resumeInfo['last_name']."\r\n";


                                $tmp .= "เริ่มงานที่ : ".(isset($resumeInfo['hr_hc']['store_name']) ? $resumeInfo['hr_hc']['store_name'] : '')."\r\n";
                                $tmp .= "ตำแหน่งที่ฝึกงาน : ".(isset($resumeInfo['hr_interview_offer']['hr_job_title']['job_name']) ? $resumeInfo['hr_interview_offer']['hr_job_title']['job_name'] : '')."\r\n";
                                $tmp .= "ค่าฝึกงาน: ".(isset($resumeInfo['hr_interview_offer']['internship_salary']) ? number_format($resumeInfo['hr_interview_offer']['internship_salary'] / 100) : '')." บาท/วัน \r\n";

                                $tmp   .= "วันที่เริ่มงาน : ".(isset($resumeInfo['hr_interview_offer']['work_time']) ? date("Y/m/d",
                                        strtotime($resumeInfo['hr_interview_offer']['work_time'])) : '')."\r\n";
                                $staff = isset($resumeInfo['hr_interview_offer']['submitter']) && $resumeInfo['hr_interview_offer']['submitter'] ? (string)$resumeInfo['hr_interview_offer']['submitter']['name']."(".(string)$resumeInfo['hr_interview_offer']['submitter']['mobile_company'].")" : "";
                                $tmp   .= "ติดต่อผู้รับผิดชอบ :".$staff."\r\n";
                            }elseif (strtolower($countryCode) == 'th' && isset($resumeInfo['hr_hc']['hire_type']) && $resumeInfo['hr_hc']['hire_type'] == HrStaffInfoModel::HIRE_TYPE_AGENT){
                                // 泰国个人代理短信模版
                                $tmp .= "SMS บริษัท แฟลช โฮม โอเปอร์เรชั่น จำกัด  ยืนยันการรับจ้างขนส่งพัสดุรายชิ้น\r\n";
                                $tmp .= "บริษัทฯ ขอนัดหมายคุณ :".$resumeInfo['first_name']." ".$resumeInfo['last_name']."\r\n";
                                $tmp .= "เข้ารับพัสดุที่สาขา :".(isset($resumeInfo['hr_hc']['store_name']) ? $resumeInfo['hr_hc']['store_name'] : '')."\r\n";
                                $tmp   .= "ในวันที่ :".(isset($resumeInfo['hr_interview_offer']['work_time']) ? date("Y/m/d",
                                        strtotime($resumeInfo['hr_interview_offer']['work_time'])) : '')." เวลา: 8:00 น.\r\n";
                                $tmp .= "ประเภท : ผู้รับจ้างขนส่งพัสดุรายชิ้น(".(isset($resumeInfo['hr_interview_offer']['hr_job_title']['job_name']) ? $resumeInfo['hr_interview_offer']['hr_job_title']['job_name'] : '').")\r\n";
                                $manager = isset($resumeInfo['hr_hc']['store_manager']) ? $resumeInfo['hr_hc']['store_manager'] : '';
                                $tmp   .= "ติดต่อผู้รับผิดชอบสาขา :".$manager."\r\n";
                                $staff = isset($resumeInfo['hr_interview_offer']['submitter']) && $resumeInfo['hr_interview_offer']['submitter'] ? (string)$resumeInfo['hr_interview_offer']['submitter']['name']."(".(string)$resumeInfo['hr_interview_offer']['submitter']['mobile_company'].")" : "";
                                $tmp   .= "เบอร์โทรผู้ประสานงาน :".$staff."\r\n";

                            } else {
                                $tmp .= "SMS ยืนยันการจ้างงาน\r\n";
                                $tmp .= "ยินดีต้อนรับท่านเข้าสู่ครอบครัว แฟลช เอ็กซ์เพรส\r\n";
                                $tmp .= "ทางบริษัทฯ ขอนัดหมายคุณ : ".$resumeInfo['first_name']." ".$resumeInfo['last_name']."\r\n";

                                $tmp     .= "เริ่มงานที่สาขา :".(isset($resumeInfo['hr_hc']['store_name']) ? $resumeInfo['hr_hc']['store_name'] : '')."\r\n";
                                $tmp     .= "ฝึกงานสาขา (สำหรับสาขาที่ยังไม่เปิดเท่านั้น) : - \r\n";
                                $tmp     .= "วันที่เริ่มงาน : ".(isset($resumeInfo['hr_interview_offer']['work_time']) ? date("Y/m/d",
                                        strtotime($resumeInfo['hr_interview_offer']['work_time'])) : '')."\r\n";
                                $tmp     .= "ตำแหน่ง : ".(isset($resumeInfo['hr_interview_offer']['hr_job_title']['job_name']) ? $resumeInfo['hr_interview_offer']['hr_job_title']['job_name'] : '')."\r\n";
                                $manager = isset($resumeInfo['hr_hc']['store_manager']) ? $resumeInfo['hr_hc']['store_manager'] : '';
                                $tmp     .= "ติดต่อผู้รับผิดชอบสาขา :".$manager."\r\n";
                                $staff   = isset($resumeInfo['hr_interview_offer']['submitter']) && $resumeInfo['hr_interview_offer']['submitter'] ? (string)$resumeInfo['hr_interview_offer']['submitter']['name']."(".(string)$resumeInfo['hr_interview_offer']['submitter']['mobile_company'].")" : "";
                                $tmp     .= "เบอร์โทรผู้สรรหา :".$staff."\r\n";
                                $tmp     .= "ขอให้มีความสุขกับการทำงานค่ะ\r\n";
                            }
                        }
                    }
                }
            }
        }

        //泰国模板处理
        $templateServer = new TemplateServer();

        if ($templateServer->checkThFulfillmentInternship([
            'hire_type'     => $resumeInfo['hr_hc']['hire_type'] ?? 0,
            'department_id' => $resumeInfo['hr_hc']['department_id'] ?? 0,
            'job_title_id'  => $resumeInfo['hr_hc']['job_title'] ?? 0,
        ])) {
            return $templateServer->getFulfillmentInternshipOfferTpl($resumeInfo);
        }

        return $tmp;
    }


    /**
     * 获取预约面试简历模板
     *
     * @param $resumeId
     *
     */
    public function getReserveSmsTpl($resumeId)
    {
        $this->sysList    = new SysListRepository();
        $this->resume     = new ResumeRepository();
        $this->department = new DepartmentRepository();

        //简历详情
        $resumeInfo = $this->resume->resumeInfoV2($resumeId);
        $result = [];
        $tmp    = "";
        if ($resumeInfo) {
            $result['credentials_category'] = $resumeInfo['credentials_category'];
            $result['phone_area_code']      = $resumeInfo['phone_area_code'];
            $result['email']                = $resumeInfo['email'];
            $result['phone']                = $resumeInfo['phone'];
            $result['name']                 = $resumeInfo['first_name'] . " " . $resumeInfo['middle_name'] . " " . $resumeInfo['last_name'] . " " . $resumeInfo['suffix_name'];
            $result['interviewer']          = $resumeInfo['interviewer'];


            //hc所属部门不能为空
            if (empty($resumeInfo['hr_hc_department_id'])) {
                throw new ValidationException($this->getTranslation()->_('3003'));
            }

            //判断称谓
            $hr_hc_department = $this->department->departmentDetail(['department_id' => $resumeInfo['hr_hc_department_id']]);
            //网点信息
            $storeInfo    = (new SysStoreModel())->getSysStoreInfo($resumeInfo['worknode_id']);
            $storeManager = (new StaffServer())->getInfoByStaffId($storeInfo['manager_id']);
            $resumeInfo['departmentName'] = $hr_hc_department['name'] ?? '';
            $resumeInfo['manager']        = $storeManager ? $storeManager['name'] . "(" . $storeManager['mobile'] . ")" : "";
            $resumeInfo['storeName']      = $storeInfo['name'] ?? '';

            //hr信息
            $tpl_common = require BASE_PATH . '/app/common/tpl_common.php';
            $hire_type = $resumeInfo['hire_type'] ?? '';

            if (isCountry('MY') && $hire_type == HrStaffInfoModel::HIRE_TYPE_MONTH) {
                $tpl = require BASE_PATH.'/app/common/tpl_my_lnt.php';
                $tpl = array_merge($tpl_common, $tpl);
            } elseif (isCountry('MY') && in_array($hire_type, HrStaffInfoModel::$agentTypeTogether)) {
                //新建个模板
                $tpl = require BASE_PATH.'/app/common/tpl_my_agent.php';
                $tpl = array_merge($tpl, $tpl_common);
            } elseif (isCountry('TH') && $resumeInfo['hire_type'] == HrStaffInfoModel::HIRE_TYPE_AGENT) {
                $tpl = require BASE_PATH.'/app/common/tpl_th_agent.php';
            } else {
                if (isCountry('TH') && $resumeInfo['hire_type'] == HrStaffInfoModel::HIRE_TYPE_AGENT) {
                    $tpl = require BASE_PATH.'/app/common/tpl_th_agent.php';
                } elseif (in_array(env('country_code'), ['TH', 'ID'])) {
                    $tpl = require BASE_PATH.'/app/common/tpl.php';
                } elseif (isCountry('PH') && $resumeInfo['hire_type'] == HrStaffInfoModel::HIRE_TYPE_AGENT) {
                    $tpl = require BASE_PATH.'/app/common/tpl_ph_agent.php';
                } else {
                    $tpl = require BASE_PATH.'/app/common/tpl_2.php';
                }

                $tpl = array_merge($tpl, $tpl_common);
            }

            $tpl = (new TemplateServer())->replaceInterviewTpl($resumeInfo, $tpl);

            //求职者-泰文 开始
            $countryCode           = strtoupper(env('country_code'));
            if (isCountry('PH') && $hire_type == HrStaffInfoModel::HIRE_TYPE_AGENT){
                $countryApplicantEmail = $this->reserveSmsOrEmailTpl($resumeInfo, $countryCode, 1, $tpl,$hire_type);
            }else{
                $countryApplicantEmail = $this->reserveSmsOrEmailTpl($resumeInfo, $countryCode, 1, $tpl,$hire_type);
            }
            $lanKey = 'language_common_' . getCountryValue();

            //邮件
            $data['type']        = $countryCode;
            $data['target_type'] = 1;
            $data['content']     = $countryApplicantEmail;
            $data['title']       = $this->getTranslation()->_($lanKey);
            $result['list'][]    = $data;

            //手机
            $data['target_type'] = 2;
            $result['list'][]    = $data;

            //求职者-泰文
            $enOnLineApplicantEmail = $this->reserveSmsOrEmailTpl($resumeInfo, 'EN_ONLINE', 1, $tpl,$hire_type);
            $data['type']           = 'EN_ONLINE';
            $data['target_type']    = 1;
            if (isCountry(['TH','PH']) && $resumeInfo['hire_type'] == HrStaffInfoModel::HIRE_TYPE_AGENT) {
                $data['content']        = $countryApplicantEmail;
            }else{
                $data['content']        = $enOnLineApplicantEmail;
            }
            $data['title']          = $this->getTranslation()->_('send_msg_type_3');
            $result['list'][]       = $data;

            //邮件
            $data['target_type'] = 2;
            $data['title']       = $this->getTranslation()->_('send_msg_type_3');
            $result['list'][]    = $data;

            //求职者-英文
            $enApplicantEmail = $this->reserveSmsOrEmailTpl($resumeInfo, 'EN', 1, $tpl,$hire_type);

            $data['type']        = 'EN';
            $data['target_type'] = 1;
            if (isCountry(['TH','PH']) && $resumeInfo['hire_type'] == HrStaffInfoModel::HIRE_TYPE_AGENT) {
                $data['content']     = $countryApplicantEmail;
            }else{
                $data['content']     = $enApplicantEmail;
            }

            $data['title']       = $this->getTranslation()->_('send_msg_type_2');
            $result['list'][]    = $data;

            $data['target_type'] = 2;
            $result['list'][]    = $data;

            unset($data);
            //求职者-泰文 结束

            //面试官-泰文 开始
            //面试官-泰文
            $result['interviewer_info']['name']           = $result['interviewer'];
            $result['interviewer_info']['interviewer_id'] = $resumeInfo['interviewer_id'];

            $conuryInterviewEmail = $this->reserveSmsOrEmailTpl($resumeInfo, $countryCode, 2, $tpl,$hire_type);

            $data['type']                         = $countryCode;
            $data['content']                      = $conuryInterviewEmail;
            $data['title']                        = $this->getTranslation()->_($lanKey);
            $result['interviewer_info']['list'][] = $data;

            //面试官-英文线上
            $enOnlineInterviewEmail = $this->reserveSmsOrEmailTpl($resumeInfo, 'EN_ONLINE', 2, $tpl,$hire_type);

            $data['type']                         = 'EN_ONLINE';
            if (isCountry('TH') && $resumeInfo['hire_type'] == HrStaffInfoModel::HIRE_TYPE_AGENT) {
                $data['content']                      = $conuryInterviewEmail;
            }else{
                $data['content']                      = $enOnlineInterviewEmail;
            }
            $data['title']                        = $this->getTranslation()->_('send_msg_type_3');
            $result['interviewer_info']['list'][] = $data;

            //面试官-英文
            $enInterviewEmail = $this->reserveSmsOrEmailTpl($resumeInfo, 'EN', 2, $tpl,$hire_type);

            $data['type']                         = 'EN';
            if (isCountry('TH') && $resumeInfo['hire_type'] == HrStaffInfoModel::HIRE_TYPE_AGENT) {
                $data['content']                      = $conuryInterviewEmail;
            }else{
                $data['content']                      = $enInterviewEmail;
            }
            $data['title']                        = $this->getTranslation()->_('send_msg_type_2');
            $result['subscribe_id']               = $resumeInfo['subscribe_id'];
            $result['interviewer_info']['list'][] = $data;
            //面试官-泰文 结束
        }

        return $result;
    }

    /**
     *
     * @param $resumeId
     * @param $countryCode
     * @param $id
     * @return string
     *
     */
    public function offerSmsContent($resumeId, $id)
    {
        $countryCode = strtolower(env('country_code'));
        if ($countryCode == 'ph') {
            $countryCode = 'th';
        }
        $tmp = '';
        $resumeInfo = (new ResumeRepository())->getResumeInfo(['id' => $resumeId]);
        $hcInfo = (new HcRepository())->hcInfo($resumeInfo['hc_id'] ?? 0);

        if (isCountry('MY')) {
            $url = $this->getShortUrl(env("h5_url").$countryCode."/offer/tpl?resume_id=".$resumeId."&country_code=".env('country_code')."&id=".$id);
            //个人代理的 短链内容
            if (in_array($hcInfo['hire_type'], HrStaffInfoModel::$agentTypeTogether)) {
                $offerServer = new OfferServer($this->lang);
                return $offerServer->getSendOfferSmsTemplate($url);
            }
            $tmp .= "Employment Confirmation Message \r\n";
            $tmp .= "Welcome to join Flash Express family! Please find the offer details from ". $url ." . Thank you and we are looking forward to meeting you soon. \r\n";
        }elseif (isCountry('PH') && !empty($hcInfo['hire_type']) && ($hcInfo['hire_type'] == HrStaffInfoModel::HIRE_TYPE_AGENT)){
            $tmp .= "Confirmation Message \r\n";
            $tmp .= "Please find the IC details from ".$this->getShortUrl(env("h5_url").$countryCode."/offer/tpl?resume_id=".$resumeId."&country_code=".env('country_code')."&id=".$id)." . Thank you. \r\n";
        }elseif (isCountry('PH') && (empty($hcInfo['hire_type']) || ($hcInfo['hire_type'] != HrStaffInfoModel::HIRE_TYPE_AGENT))){
            $tmp .= "Employment Confirmation Message \r\n";
            $tmp .= "Welcome to join Flash Express family! Please find the offer details from ".$this->getShortUrl(env("h5_url").$countryCode."/offer/tpl?resume_id=".$resumeId."&country_code=".env('country_code')."&id=".$id)." . Thank you and we are looking forward to meeting you soon. \r\n";
        } else {
            $tmp .= "SMS ยืนยันการจ้างงาน \r\n";
            $tmp .= "ยินดีต้อนรับท่านเข้าสู่ครอบครัว แฟลช เอ็กซ์เพรส \r\n";
            $tmp .= "กรุณาตรวจสอบรายละเอียดตามลิงก์ " . $this->getShortUrl(env("h5_url") . $countryCode . "/offer/tpl?resume_id=" . $resumeId . "&country_code=" . env("country_code") . "&id=" . $id) . " \r\n";
            $tmp .= "ขอให้มีความสุขกับการทำงานค่ะ \r\n";
        }

        if (isCountry('TH')) {
            if (!empty($hcInfo['hire_type']) && ($hcInfo['hire_type'] == HrStaffInfoModel::HIRE_TYPE_AGENT)) {
                $tmp = '';
                $tmp .= "SMS ยืนยันการรับจ้างขนส่งพัสดุรายชิ้น \r\n";
                $tmp .= "ยินดีต้อนรับท่านเข้าสู่ครอบครัวแฟลช โฮม โอเปอร์เรชั่น จำกัด \r\n";
                $tmp .= "กรุณาตรวจสอบรายละเอียดตามลิงก์ " . $this->getShortUrl(env("h5_url") . $countryCode . "/offer/tpl?resume_id=" . $resumeId . "&country_code=" . env("country_code") . "&id=" . $id) . " \r\n";
            }

            if ((new TemplateServer())->checkThFulfillmentInternship($hcInfo)) {
                $tmp = '';
                $tmp .= "SMS ยืนยันการจ้างงาน \r\n";
                $tmp .= "ยินดีต้อนรับท่านเข้าสู่ครอบครัว แฟลช ฟูลฟิลล์เม้นท์ \r\n";
                $tmp .= "กรุณาตรวจสอบรายละเอียดตามลิงก์ " . $this->getShortUrl(env("h5_url") . $countryCode . "/offer/tpl?resume_id=" . $resumeId . "&country_code=" . env("country_code") . "&id=" . $id) . " \r\n";
                $tmp .= "ขอให้มีความสุขกับการทำงานค่ะ \r\n";
            }
        }
        
        return $tmp;
    }

    /**
     * @param $resumeId
     * @param $type
     * @param $id
     * @return string
     *
     */
    public function smsContent($resumeId, $type, $id)
    {
        $this->resume = new ResumeRepository();
        //简历详情
        $resumeInfo = $this->resume->resumeInfoV2($resumeId);

        if (isCountry('TH') && $resumeInfo['hire_type'] == HrStaffInfoModel::HIRE_TYPE_AGENT) {
            $tpl = require BASE_PATH . '/app/common/tpl_th_agent.php';
        }elseif (isCountry('PH') && $resumeInfo['hire_type'] == HrStaffInfoModel::HIRE_TYPE_AGENT){
            $tpl = require BASE_PATH . '/app/common/tpl_ph_agent.php';
        }elseif (in_array(env('country_code'), ['TH', 'ID'])) {
            $tpl = require BASE_PATH . '/app/common/tpl.php';
        } else {
            $tpl = require BASE_PATH . '/app/common/tpl_2.php';
        }

        $countryCode = strtolower(env('country_code'));

        if ($countryCode == 'ph') {
            $countryCode = 'th';
        }
        $content    = '';

        //替换短信中的flash
        if (isCountry('PH')) {
            if (!empty($tpl['send_interview_en_online_msg_content'])) {
                $tpl['send_interview_en_online_msg_content'] = str_replace('interview with Flash','interview',$tpl['send_interview_en_online_msg_content']);
            }

            if (!empty($tpl['send_interview_en_msg_content'])) {
                $tpl['send_interview_en_msg_content'] = str_replace('interview with Flash','interview',$tpl['send_interview_en_msg_content']);
            }
        }

        $tpl = (new TemplateServer())->replaceInterviewTpl($resumeInfo, $tpl);

        if ($type == 'TH') {
            if (isCountry('TH') && $resumeInfo['hire_type'] == HrStaffInfoModel::HIRE_TYPE_AGENT){
                $content = sprintf(
                    $tpl['send_interview_th_agent_msg_content'],
                    $resumeInfo['first_name'].' '.$resumeInfo['last_name'],
                    $this->getShortUrl(env('h5_url').$countryCode.'/interview/tpl?country_code='.env('country_code').'&resume_id='.$resumeId.'&type=TH&id='.$id)
                );
            }else{
                $content = sprintf(
                    $tpl['send_interview_th_msg_content'],
                    $resumeInfo['first_name'].' '.$resumeInfo['last_name'],
                    $this->getShortUrl(env('h5_url').$countryCode.'/interview/tpl?country_code='.env('country_code').'&resume_id='.$resumeId.'&type=TH&id='.$id)
                );
            }
        } else {
            if (isCountry('PH') && $resumeInfo['hire_type'] == HrStaffInfoModel::HIRE_TYPE_AGENT){
                $content = sprintf(
                    $tpl['send_interview_ph_agent_msg_content'],
                    $resumeInfo['first_name'].' '.$resumeInfo['last_name'],
                    $this->getShortUrl(env('h5_url').$countryCode.'/interview/tpl?country_code='.env('country_code').'&resume_id='.$resumeId.'&type=TH&id='.$id)
                );
            }else{
                if ($type == 'EN_ONLINE') {
                    $content = sprintf(
                        $tpl['send_interview_en_online_msg_content'],
                        $resumeInfo['first_name'] . ' ' . $resumeInfo['last_name'],
                        $this->getShortUrl(env('h5_url') . $countryCode . '/interview/tpl?country_code=' . env('country_code') . '&resume_id=' . $resumeId . "&type=EN_ONLINE&id=" . $id)
                    );
                } else {
                    if ($type == 'EN') {
                        $content = sprintf(
                            $tpl['send_interview_en_msg_content'],
                            $resumeInfo['first_name'] . ' ' . $resumeInfo['last_name'],
                            $this->getShortUrl(env('h5_url') . $countryCode . '/interview/tpl?country_code=' . env('country_code') . '&resume_id=' . $resumeId . "&type=EN&id=" . $id)
                        );
                    }
                }
            }
        }

        $this->getDI()->get('logger')->write_log('smsContent ' . json_encode(func_num_args(),
                JSON_UNESCAPED_UNICODE) . " content:  " . $content, 'info');
        return $content;
    }

    /**
     * 预约面试短信模板
     * @param $resumeId
     * @param $countryCode
     * @param $type -- 1-候选人 2-面试官
     *
     */
    public function reserveSmsOrEmailTpl($resumeInfo, $countryCode, $type = 1, $tpl = '', $hire_type = '')
    {
        $resumeServer = new ResumeServer();


        //企业官网
        $flash_express_co_ltd = SettingEnvServer::getSetVal('flash_express_co_ltd');

        if ((new TemplateServer())->checkThFulfillmentInternship([
            'hire_type'     => $resumeInfo['hire_type'] ?? 0,
            'department_id' => $resumeInfo['hr_hc_department_id'] ?? 0,
            'job_title_id'  => $resumeInfo['hc_job_title'] ?? 0,
        ])) {
            $flash_express_co_ltd = 'https://www.flashfulfillment.co.th';
        }
        //登记link
        $registration_link = SettingEnvServer::getSetVal('registration_link');

        $jobName = isset($resumeInfo['job_name']) ? $resumeInfo['job_name'] : '';
        if (isset($resumeInfo['hc_job_name'])) {
            $jobName = $resumeInfo['hc_job_name'];
        }

        $staff = '';
        if (!empty($resumeInfo['interviewer'])) {
            $staff .= $resumeInfo['interviewer'];
        }

        if (!empty($resumeInfo['mobile_company'])) {
            $staff .= "(".(string)$resumeInfo['mobile_company'].")";
        }

        // 1 候选人模版
        if ($type == 1) {
            if (in_array($countryCode, ['TH', 'PH', 'LA', 'MY', 'VN', 'ID'])) {
                $tplTemplate = $tpl[strtolower($countryCode . '_applicant_email')] ?? $tpl['th_applicant_email'];
                if (isCountry('MY') && $hire_type == HrStaffInfoModel::HIRE_TYPE_MONTH){
                    $tmp = sprintf($tplTemplate,
                        $resumeInfo['first_name'],
                        $jobName,
                        $resumeInfo['interview_time'] ?? ''
                    );
                } elseif (isCountry('TH') && $hire_type == HrStaffInfoModel::HIRE_TYPE_AGENT){
                    $tmp = sprintf($tplTemplate,
                        $resumeInfo['first_name']." ".$resumeInfo['middle_name'],
                        $resumeInfo['last_name']." ".$resumeInfo['suffix_name'],
                        $resumeInfo['phone'],
                        $resumeInfo['storeName'] ?? '',
                        $resumeInfo['interview_time'] ?? '',
                        $resumeInfo['detail_address'] ?? ''
                    );
                } elseif (isCountry('PH') && $hire_type == HrStaffInfoModel::HIRE_TYPE_AGENT){
                    // ph 个人代理
                    $sex         = $resumeServer->getResumeSex($resumeInfo['sex'], $resumeInfo['call_name']);
                    $call_name = $sex == 1 ? 'Mr' : 'Ms';
                    $tmp = sprintf($tpl['ph_applicant_email_agent'],
                        $call_name,
                        $resumeInfo['first_name'] . " " . $resumeInfo['middle_name'],
                        $resumeInfo['last_name'] . " " . $resumeInfo['suffix_name'],
                        $resumeInfo['storeName'] ?? '',
                        $resumeInfo['interview_time'] ?? '',
                        $resumeInfo['detail_address'] ?? '',
                        $resumeInfo['recruiter_staff_name'] ?? '',
                        $resumeInfo['recruiter_staff_mobile'] ?? '',
                        $staff
                    );
                } else{
                    $tmp = sprintf($tplTemplate,
                        $resumeInfo['first_name']." ".$resumeInfo['middle_name'],
                        $resumeInfo['last_name']." ".$resumeInfo['suffix_name'],
                        $resumeInfo['phone'],
                        $jobName,
                        $resumeInfo['departmentName'] ?? '',
                        $resumeInfo['storeName'] ?? '',
                        $resumeInfo['interview_time'] ?? '',
                        $resumeInfo['detail_address'] ?? '',
                        $resumeInfo['manager'] ?? ''
                    );
                }

            } elseif ($countryCode == 'EN_ONLINE') {
                $sex         = $resumeServer->getResumeSex($resumeInfo['sex'], $resumeInfo['call_name']);
                $appellation = $sex == 1 ? 'Mr' : 'Ms';

                if (isCountry('MY') && $hire_type == HrStaffInfoModel::HIRE_TYPE_MONTH){
                    return  sprintf($tpl['en_applicant_online_email'],
                        $appellation,
                        $resumeInfo['first_name'],
                        $jobName,
                        $resumeInfo['interview_time'] ?? ''
                    );
                }

                $tmp = sprintf($tpl['en_applicant_online_email'],
                    $appellation,
                    $resumeInfo['first_name'] . " " . $resumeInfo['middle_name'],
                    $resumeInfo['last_name'] . " " . $resumeInfo['suffix_name'],
                    $jobName,
                    $resumeInfo['departmentName'] ?? '',
                    $flash_express_co_ltd,
                    $resumeInfo['interview_time'] ?? '',
                    $resumeInfo['hr'] ?? '',
                    $resumeInfo['hr_mobile'] ?? '',
                    $staff,
                    $registration_link
                );
            } elseif ($countryCode == 'EN'){
                $sex         = $resumeServer->getResumeSex($resumeInfo['sex'], $resumeInfo['call_name']);
                $appellation = $sex == 1 ? 'Mr' : 'Ms';

                if (isCountry('MY') && $hire_type == HrStaffInfoModel::HIRE_TYPE_MONTH) {
                    return sprintf($tpl['en_applicant_email'],
                        $appellation,
                        $resumeInfo['first_name'],
                        $jobName,
                        $resumeInfo['interview_time'] ?? ''
                    );
                }

                $tmp = sprintf($tpl['en_applicant_email'],
                    $appellation,
                    $resumeInfo['first_name'] . " " . $resumeInfo['middle_name'],
                    $resumeInfo['last_name'] . " " . $resumeInfo['suffix_name'],
                    $jobName,
                    $resumeInfo['departmentName'] ?? '',
                    $flash_express_co_ltd,
                    $resumeInfo['detail_address'] ?? '',
                    $resumeInfo['interview_time'] ?? '',
                    $resumeInfo['hr'] ?? '',
                    $resumeInfo['hr_mobile'] ?? '',
                    $staff,
                    $registration_link
                );
            }
            return $tmp;
        }

        // 2 面试官 模版
        if (in_array($countryCode, ['TH', 'PH', 'LA', 'MY', 'VN', 'ID'])) {
            $tplTemplate = $tpl[strtolower($countryCode.'_interview_email')] ?? $tpl['th_interview_email'];
            if (isCountry('TH') && $hire_type == HrStaffInfoModel::HIRE_TYPE_AGENT) {
                // 个人代理单独的模版
                $tmp = sprintf(
                    $tplTemplate,
                    $resumeInfo['name'],
                    $resumeInfo['phone'] ?? '',
                    $resumeInfo['hc_id'] ?? '',
                    $resumeInfo['storeName'] ?? '',
                    $resumeInfo['interview_time'] ?? '',
                    $resumeInfo['detail_address'] ?? '',
                    $resumeInfo['manager'] ?? ''
                );
            } else {
                $tmp = sprintf(
                    $tplTemplate,
                    $resumeInfo['name'],
                    $resumeInfo['phone'],
                    $resumeInfo['hc_id'],
                    $jobName,
                    $resumeInfo['departmentName'] ?? '',
                    $resumeInfo['storeName'] ?? '',
                    $resumeInfo['interview_time'],
                    $resumeInfo['detail_address'],
                    $resumeInfo['manager'] ?? ''
                );
            }
        } elseif ($countryCode == 'EN_ONLINE') {
            $tmp = sprintf(
                $tpl['en_interview_online_email'],
                $resumeInfo['interviewer'],
                $resumeInfo['first_name']." ".$resumeInfo['middle_name']." ".$resumeInfo['last_name']." ".$resumeInfo['suffix_name'],
                $resumeInfo['hc_id'],
                $jobName,
                $resumeInfo['interview_time'],
                $resumeInfo['hr'].' '.$resumeInfo['hr_mobile']
            );
        } elseif ($countryCode == 'EN') {
            $tmp = sprintf(
                $tpl['en_interview_email'],
                $resumeInfo['interviewer'],
                $resumeInfo['first_name']." ".$resumeInfo['middle_name']." ".$resumeInfo['last_name']." ".$resumeInfo['suffix_name'],
                $resumeInfo['hc_id'],
                $jobName,
                $resumeInfo['interview_time'],
                $resumeInfo['hr'].' '.$resumeInfo['hr_mobile']
            );
        }

        return $tmp;
    }

    /**
     * 预约面试简历模板
     *
     *
     * @param $resumeId
     * @param $countryCode
     * @param $type
     *
     */
    public function reserveSmsTpl($resumeId, $countryCode, $type)
    {
        $resumeInfo = $this->resumeData($resumeId);
        if (in_array(strtoupper($countryCode), ['TH', 'ID'])) {
            $tpl = require BASE_PATH . '/app/common/tpl.php';
        } else {
            $tpl = require BASE_PATH . '/app/common/tpl_2.php';
        }

        $resumeServer = new ResumeServer();

        //企业官网
        $flash_express_co_ltd = SettingEnvServer::getSetVal('flash_express_co_ltd');
        //登记link
        $registration_link = SettingEnvServer::getSetVal('registration_link');

        $jobName = isset($resumeInfo['hr_jd']['job_name']) ? $resumeInfo['hr_jd']['job_name'] : '';
        if (isset($resumeInfo['hr_hc']['job_name'])) {
            $jobName = $resumeInfo['hr_hc']['job_name'];
        }

        if ($type == 'TH') {
            $tmp = sprintf($tpl['th_applicant_email'],
                $resumeInfo['first_name']." ".$resumeInfo['middle_name'],
                $resumeInfo['last_name']." ".$resumeInfo['suffix_name'],
                $resumeInfo['phone'],
                $jobName,
                isset($resumeInfo['hr_hc']['department_name']) ? $resumeInfo['hr_hc']['department_name'] : '',
                isset($resumeInfo['hr_hc']['store_name']) ? $resumeInfo['hr_hc']['store_name'] : '',
                isset($resumeInfo['hr_interview_subscribe']['interview_time']) ? $resumeInfo['hr_interview_subscribe']['interview_time'] : '',
                isset($resumeInfo['hr_interview_subscribe']['detail_address']) ? $resumeInfo['hr_interview_subscribe']['detail_address'] : '',
                isset($resumeInfo['hr_hc']['store_manager']) ? $resumeInfo['hr_hc']['store_manager'] : ''
            );
        } else {
            if ($type == 'EN_ONLINE') {
                $sex         = $resumeServer->getResumeSex($resumeInfo['sex'], $resumeInfo['call_name']);
                $appellation = $sex == 1 ? 'Mr' : 'Ms';
                $staff       = isset($resumeInfo['hr_interview_subscribe']['interviewer']) && $resumeInfo['hr_interview_subscribe']['interviewer'] ? (string)$resumeInfo['hr_interview_subscribe']['interviewer']['name'] . "(" . (string)$resumeInfo['hr_interview_subscribe']['interviewer']['mobile_company'] . ")" : "";

                $tmp = sprintf($tpl['en_applicant_online_email'],
                    $appellation,
                    $resumeInfo['first_name'] . " " . $resumeInfo['middle_name'],
                    $resumeInfo['last_name'] . " " . $resumeInfo['suffix_name'],
                    $jobName,
                    isset($resumeInfo['hr_hc']['department_name']) ? $resumeInfo['hr_hc']['department_name'] : '',
                    $flash_express_co_ltd,
                    isset($resumeInfo['hr_interview_subscribe']['interview_time']) ? $resumeInfo['hr_interview_subscribe']['interview_time'] : '',
                    isset($resumeInfo['hr_interview_subscribe']['hr']['name']) ? $resumeInfo['hr_interview_subscribe']['hr']['name'] : '',
                    isset($resumeInfo['hr_interview_subscribe']['hr']['mobile']) ? $resumeInfo['hr_interview_subscribe']['hr']['mobile'] : '',
                    $staff,
                    $registration_link
                );
            } else {
                $sex         = $resumeServer->getResumeSex($resumeInfo['sex'], $resumeInfo['call_name']);
                $appellation = $sex == 1 ? 'Mr' : 'Ms';
                $staff       = isset($resumeInfo['hr_interview_subscribe']['interviewer']) && $resumeInfo['hr_interview_subscribe']['interviewer'] ? (string)$resumeInfo['hr_interview_subscribe']['interviewer']['name'] . "(" . (string)$resumeInfo['hr_interview_subscribe']['interviewer']['mobile_company'] . ")" : "";

                $tmp = sprintf($tpl['en_applicant_email'],
                    $appellation,
                    $resumeInfo['first_name'] . " " . $resumeInfo['middle_name'],
                    $resumeInfo['last_name'] . " " . $resumeInfo['suffix_name'],
                    $jobName,
                    isset($resumeInfo['hr_hc']['department_name']) ? $resumeInfo['hr_hc']['department_name'] : '',
                    $flash_express_co_ltd,
                    isset($resumeInfo['hr_interview_subscribe']['detail_address']) ? $resumeInfo['hr_interview_subscribe']['detail_address'] : '',
                    isset($resumeInfo['hr_interview_subscribe']['interview_time']) ? $resumeInfo['hr_interview_subscribe']['interview_time'] : '',
                    isset($resumeInfo['hr_interview_subscribe']['hr']['name']) ? $resumeInfo['hr_interview_subscribe']['hr']['name'] : '',
                    isset($resumeInfo['hr_interview_subscribe']['hr']['mobile']) ? $resumeInfo['hr_interview_subscribe']['hr']['mobile'] : '',
                    $staff,
                    $registration_link
                );

            }
        }


        return $tmp;
    }


    /**
     * 聚合相关的简历数据
     *
     * @param $resumeId
     * @return array
     */
    public function resumeData($resumeId)
    {
        $resumeInfo = HrResumeModel::findFirst([
            'conditions' => ' id = :id: ',
            'bind'       => [
                'id' => $resumeId,
            ],
        ]);
        $resumeInfo = $resumeInfo ? $resumeInfo->toArray() : [];
        if ($resumeInfo) {
            $hrJdInfo            = HrJdModel::findFirst([
                'conditions' => ' job_id = :job_id: ',
                'bind'       => [
                    'job_id' => $resumeInfo['job_id'],
                ],
            ]);
            $hrJdInfo            = $hrJdInfo ? $hrJdInfo->toArray() : [];
            $resumeInfo['hr_jd'] = $hrJdInfo;

            $interviewInfo              = HrInterviewModel::findFirst([
                'conditions' => ' resume_id = :resume_id: and hc_id = :hc_id: ',
                'bind'       => [
                    'resume_id' => $resumeInfo['id'],
                    'hc_id'     => $resumeInfo['hc_id'],
                ],
            ]);
            $interviewInfo              = $interviewInfo ? $interviewInfo->toArray() : [];
            $resumeInfo['hr_interview'] = $interviewInfo;

            if ($interviewInfo) {
                $hcInfo = HrHcModel::findFirst([
                    'conditions' => ' hc_id = :hc_id: ',
                    'bind'       => [
                        'hc_id' => $interviewInfo['hc_id'],
                    ],
                ]);
                $hcInfo = $hcInfo ? $hcInfo->toArray() : [];

                if ($hcInfo) {
                    $department = SysDepartmentModel::findFirst([
                        'conditions' => ' id = :id: ',
                        'bind'       => [
                            'id' => $hcInfo['department_id'],
                        ],
                    ]);
                    if ($department) {
                        $department = $department->toArray();

                        $hcInfo['department_name'] = $department['name'];
                    }

                    $hcInfo['store_manager']        = '';
                    $hcInfo['store_manager_name']   = '';
                    $hcInfo['store_manager_mobile'] = '';

                    if ($hcInfo['worknode_id'] == '-1') {
                        $hcInfo['store_name']           = enums::HEAD_OFFICE;

                    } else {
                        $sysStore = SysStoreModel::findFirst([
                            'conditions' => ' id =  :id: ',
                            'bind'       => [
                                'id' => $hcInfo['worknode_id'],
                            ],
                        ]);
                        if ($sysStore) {
                            $sysStore             = $sysStore->toArray();
                            $hcInfo['store_name'] = $sysStore['name'];
                            $storeManager         = (new StaffServer())->getInfoByStaffId($sysStore['manager_id']);
                            //网点管理人
                            $manager                        = $storeManager ? $storeManager['name']."(".$storeManager['mobile'].")" : "";
                            $hcInfo['store_manager']        = $manager;
                            $hcInfo['store_manager_name']   = $storeManager['name'] ?? '';
                            $hcInfo['store_manager_mobile'] = $storeManager['mobile'] ?? '';
                        }
                    }

                    if (!empty($hcInfo['job_title'])) {
                        $hrJobTitle = HrJobTitleByModel::findFirst([
                            'conditions' => ' id =  :id: ',
                            'bind'       => [
                                'id' => $hcInfo['job_title'],
                            ],
                        ]);
                        if ($hrJobTitle) {
                            $hrJobTitle         = $hrJobTitle->toArray();
                            $hcInfo['job_name'] = $hrJobTitle['job_name'];
                        }
                    }



                    $interviewSubscribeInfo = HrInterviewSubscribeModel::findFirst([
                        'conditions' => ' hc_id = :hc_id: and interview_id = :interview_id: ',
                        'bind'       => [
                            'hc_id'        => $hcInfo['hc_id'],
                            'interview_id' => $interviewInfo['interview_id'],
                        ],
                    ]);
                    $interviewSubscribeInfo = $interviewSubscribeInfo ? $interviewSubscribeInfo->toArray() : [];
                    if ($interviewSubscribeInfo) {
                        $add_hour                                 = $this->getDI()['config']['application']['add_hour'];
                        $interviewSubscribeInfo['interview_time'] = date("Y-m-d H:i:s",
                            strtotime($interviewSubscribeInfo['interview_time'] . " + " . $add_hour . " hours"));
                        $staffInfo                                = HrStaffInfoModel::findFirst([
                            'conditions' => ' staff_info_id = :staff_id: ',
                            'bind'       => [
                                'staff_id' => $interviewSubscribeInfo['interviewer_id'],
                            ],
                        ]);
                        if ($staffInfo) {
                            $staffInfo                             = $staffInfo->toArray();
                            $interviewSubscribeInfo['interviewer'] = $staffInfo;
                        }

                        $hr = StaffInfoModel::findFirst([
                            'conditions' => ' id = :id: ',
                            'bind'       => [
                                'id' => $interviewSubscribeInfo['staff_id'],
                            ],
                        ]);

                        if ($hr) {
                            $hr                           = $hr->toArray();
                            $interviewSubscribeInfo['hr'] = $hr;
//                            $interviewSubscribeInfo['hr_mobile'] = $hr['hr_mobile'];
                        }

                        $resumeInfo['hr_interview_subscribe'] = $interviewSubscribeInfo;
                    }

                    $hrInterviewOffer = HrInterviewOfferModel::findFirst([
                        'conditions' => 'hc_id = :hc_id: and interview_id = :interview_id: and resume_id = :resume_id: ',
                        'bind'       => [
                            'hc_id'        => $hcInfo['hc_id'],
                            'interview_id' => $interviewInfo['interview_id'],
                            'resume_id'    => $resumeInfo['id'],
                        ],
                        'order'      => 'id desc',
                    ]);

                    $hrInterviewOffer = $hrInterviewOffer ? $hrInterviewOffer->toArray() : [];
                    if ($hrInterviewOffer) {
                        $hrJobTitleModel                  = HrJobTitleByModel::findFirst([
                            'conditions' => ' id = :id: ',
                            'bind'       => [
                                'id' => $hrInterviewOffer['position_id'],
                            ],
                        ]);
                        $hrJobTitle                       = $hrJobTitleModel ? $hrJobTitleModel->toArray() : [];
                        $hrInterviewOffer['hr_job_title'] = $hrJobTitle;
                        $staffInfo                        = HrStaffInfoModel::findFirst([
                            'conditions' => ' staff_info_id = :staff_id: ',
                            'bind'       => [
                                'staff_id' => $hrInterviewOffer['submitter_id'],
                            ],
                        ]);
                        if ($staffInfo) {
                            $staffInfo                     = $staffInfo->toArray();
                            $hrInterviewOffer['submitter'] = $staffInfo;
                        }
                    }
                    $resumeInfo['hr_interview_offer'] = $hrInterviewOffer;
                }

                $resumeInfo['hr_hc'] = $hcInfo;
            }
        }

        return $resumeInfo;
    }


    /**
     * 面试管理搜索导航
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function interviewSearchBar($paramIn = [])
    {
        $this->sysList = new SysListRepository();

        $returnArr   = [
            'departmentData'      => [],
            'jobData'             => [],
            'cityData'            => [],
            'hcStatusData'        => [],
            'cancelInterviewData' => [],
        ];
        $type        = $this->processingDefault($paramIn, 'type', 1);
        $addressType = $this->processingDefault($paramIn, 'address_type', 2, 2);// 1国家 2省 3市 4区
        //获取城市
        $cityList              = $this->sysList->getAddressList(['address_type' => $addressType]);
        $returnArr['cityData'] = $cityList;

        //获取部门
        $departmentData              = $this->sysList->getDepartmentList();
        $returnArr['departmentData'] = $departmentData;

        //获取岗位
        $jobData              = $this->sysList->getJobList(['type' => $type]);
        $returnArr['jobData'] = $jobData;

        //获取hc优先级
        $priorityArr = UC('hc')['priorityArr'];
        foreach ($priorityArr as $k => $v) {
            $_temp         = [];
            $_temp['id']   = $k;
            $_temp['text'] = $this->getTranslation()->_($v);
            $priority[]    = $_temp;
        }
        $returnArr['priority'] = $priority;

        //获取状态
        $hcStatusData              = [
            [
                'key'  => 1,
                'name' => $this->getTranslation()->_('4201'),
            ],
            [
                'key'  => 2,
                'name' => $this->getTranslation()->_('4202'),
            ],
            [
                'key'  => 3,
                'name' => $this->getTranslation()->_('4004'),
            ],
            [
                'key'  => 4,
                'name' => $this->getTranslation()->_('4204'),
            ],
        ];
        $returnArr['hcStatusData'] = $hcStatusData;

        //取消状态
        $cancelInterviewData              = [
            [
                'key'  => 1,
                'name' => $this->getTranslation()->_('4301'),
            ],
            [
                'key'  => 2,
                'name' => $this->getTranslation()->_('4302'),
            ],
            [
                'key'  => 3,
                'name' => $this->getTranslation()->_('4303'),
            ],
            [
                'key'  => 4,
                'name' => $this->getTranslation()->_('4304'),
            ],
            [
                'key'  => 5,
                'name' => $this->getTranslation()->_('4305'),
            ],
            [
                'key'  => 6,
                'name' => $this->getTranslation()->_('4306'),
            ],
        ];
        $returnArr['cancelInterviewData'] = $cancelInterviewData;
        $returnData['data']               = $returnArr;
        return $this->checkReturn($returnData);
    }

    /**
     * 招聘职位添加或修改=预约面试修改
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function addOrUpdateInterviewAppointment($paramIn = [])
    {


        $this->interview = new InterviewRepository();
        $this->staff     = new StaffRepository();
        $this->hc        = new HcRepository();
        $this->resume    = new ResumeRepository();
        $this->log       = new LogServer();

        //[1]参数定义
        $staffId            = $this->processingDefault($paramIn, 'staff_id', 2);
        $hcId               = $this->processingDefault($paramIn, 'hc_id', 2);
        $interviewerId      = $this->processingDefault($paramIn, 'interviewer_id', 2);
        $interviewTime      = $this->processingDefault($paramIn, 'interview_time');
        $resume_provider_id = $this->processingDefault($paramIn, 'resume_provider_id');
        $countryCode        = $this->processingDefault($paramIn, 'country_code', 1, 'TH');
        $countryName        = $this->processingDefault($paramIn, 'country_name', 1, 'TH');
        $provinceCode       = $this->processingDefault($paramIn, 'province_code');
        $provinceName       = $this->processingDefault($paramIn, 'province_name');
        $cityCode           = $this->processingDefault($paramIn, 'city_code');
        $cityName           = $this->processingDefault($paramIn, 'city_name');
        $districtCode       = $this->processingDefault($paramIn, 'district_code');
        $districtName       = $this->processingDefault($paramIn, 'district_name');
        $detailAddress      = $this->processingDefault($paramIn, 'detail_address');
        $resumeId           = $this->processingDefault($paramIn, 'resume_id', 2);
        $subscribeId        = $this->processingDefault($paramIn, 'subscribe_id', 2);
        $shopId             = $this->processingDefault($paramIn, 'shop_id');
        $postalCode         = $this->processingDefault($paramIn, 'postal_code');
        $hr_remark          = $this->processingDefault($paramIn, 'hr_remark');
        $ope_id             = $this->processingDefault($paramIn, 'ope_id');
        $is_edit            = $this->processingDefault($paramIn, 'is_edit',2);//修改预约面试操作会传递is_edit参数

        // 判断是否走修改预约面试逻辑 回退的面试需要走修改预约面试
        $interview_back = HrInterviewSubscribeModel::INTERVIEW_BACK_NO;
        if ($subscribeId){
            $subscribe_info = HrInterviewSubscribeModel::findFirst([
                'conditions' => "id = :subscribe_id:",
                'bind'       => ['subscribe_id' => $subscribeId],
            ])->toArray();
            if (empty($subscribe_info)) {
                return $this->checkReturn(-3, $this->getTranslation()->_('4415'));
            }
            $interview_back = in_array($subscribe_info['interview_back'],[HrInterviewSubscribeModel::INTERVIEW_BACK_YES,HrInterviewSubscribeModel::INTERVIEW_BACK_NO]) ? $subscribe_info['interview_back'] : HrInterviewSubscribeModel::INTERVIEW_BACK_NO;
            if ($interview_back == HrInterviewSubscribeModel::INTERVIEW_BACK_YES){
                $is_edit = 1;
            }
        }

        if (!empty($resumeId)){
            $resume_data = HrResumeModel::findFirst([
                'conditions' => 'id = :resume_id: and deleted=0',
                'bind'       => [
                    'resume_id' => $resumeId,
                ],
            ]);
            if (empty($resume_data)){
                return $this->checkReturn(-3, self::$t->_('resume_err_1'));
            }
            $resume_data = $resume_data->toArray();
        }else{
            $resume_data = [];
        }
        

        //校验黑名单
        if (isCountry('TH')){
            $black_grey_list = (new BlackgreylistServer())->check_black_grey_list([
                "resume_id" => $resumeId,
            ]);
            if ($black_grey_list["is_black_list"]){
                return $this->checkReturn(-3, $this->getTranslation()->_('7500'));
            }
        }else{
            //校验黑名单
            $isBalcklist = (new BlacklistRepository())->blacklistCheck([
                "resume_id" => $resumeId,
            ]);
            if ($isBalcklist > 0) {
                return $this->checkReturn(-3, $this->getTranslation()->_('7500'));
            }
        }

        (new OutsourcingBlackListServer())->check($resume_data['credentials_num'] ?? '','winhr',true,$this->lang);


        //[2] 校验提交人
        $staffData = $this->staff->checkoutStaff($staffId);
        if (empty($staffData)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('1001'));
        }

        //校验简历id
        $resumeInfo = $this->resume->getResumeInfo([
            "id" => $resumeId,
        ]);
        if (!$resumeInfo) {
            return $this->checkReturn(-3, $this->getTranslation()->_('miss_args'));
        }

        if ($resumeInfo['hc_id'] != $hcId) {
            throw new ValidationException($this->getTranslation()->_('resume_hc_id_an_update_occurred'));
        }

        //校验hc
        $hcData    = $this->hc->hcInfo($hcId);
        $stateCode = $hcData["state_code"] ?? "";
        if ($hcData['state_code'] == 3 || $hcData['surplusnumber'] == 0) {
            return $this->checkReturn(-3, $this->getTranslation()->_('11027'));
        }
        if ($hcData['state_code'] == 4) {
            return $this->checkReturn(-3, $this->getTranslation()->_('11028'));
        }
        if ($hcData['state_code'] == 9) {
            return $this->checkReturn(-3, $this->getTranslation()->_('11031'));
        }

        //[3]判断面试主表是否存在
        $param       = [
            'hc_id'     => $hcId,
            'resume_id' => $resumeId,
        ];
        $interviewId = (new InterviewRepository())->interviewAddOrUpdate($param);
        if (!$interviewId) {
            return $this->checkReturn(-3, $this->getTranslation()->_('4414'));
        }


        //[4] 判断是否有面试中的数据
        $subscribeData = (new InterviewRepository())->interviewSubscribeCheck(['interview_id' => $interviewId],
            $subscribeId);
        if (!empty($subscribeData) && ($subscribeData['state'] == 5 || $subscribeData['state'] == 10 || $subscribeData['state'] == 20 || $subscribeData['state'] == 25 || $subscribeData['state'] == 40)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('4417'));
        }

        //[5] 主业务处理
        $interviewTime       = getZeroTimeZone($interviewTime, $this->timezone);
        $insertOrUpdateParam = [
            'hc_id'              => $hcId,
            'interviewer_id'     => $interviewerId,
            'interview_time'     => $interviewTime,
            'resume_provider_id' => $resume_provider_id,
            'country_code'       => $countryCode,
            'country_name'       => $countryName,
            'province_code'      => $provinceCode,
            'province_name'      => $provinceName,
            'city_code'          => $cityCode,
            'city_name'          => $cityName,
            'district_code'      => $districtCode,
            'district_name'      => $districtName,
            'detail_address'     => $detailAddress,
            'interview_id'       => $interviewId,
            'staff_id'           => $staffId,
            'shop_id'            => $shopId,
            'postal_code'        => $postalCode,
            'hr_remark'          => $hr_remark,
        ];
        if (!isset($paramIn["resume_provider_id"])) {
            unset($insertOrUpdateParam["resume_provider_id"]);
        }

        if ($subscribeId) {
            $have_subscribe_record = true;//更新预约面试表,是 非首次预约面试（下一轮操作）的标识
            $insertOrUpdateParam['subscribe_id'] = $subscribeId;
            $subscribeId                         = (new InterviewRepository())->interviewUpdate($insertOrUpdateParam);
        } else {
            $have_subscribe_record = false; //新增预约面试，是首次预约面试的标识
            //查询层级
            $level = (new InterviewRepository())->selectLevel($interviewId);
            $insertOrUpdateParam['level'] = $level + 1;

            //查询是否存在
            $sql     = "select * from hr_interview_subscribe where interview_id=".$interviewId;
            $dataObj = $this->getDI()->get('db')->query($sql);
            $data    = $dataObj->fetch(\Phalcon\Db::FETCH_ASSOC);
            if ($data) {
                $insertOrUpdateParam['subscribe_id'] = $data['id'];
                $subscribeId                         = (new InterviewRepository())->interviewUpdate($insertOrUpdateParam);
            } else {
                $subscribeId = (new InterviewRepository())->interviewAdd($insertOrUpdateParam, $resumeId);
            }
        }

        if (!$subscribeId) {
            return $this->checkReturn(-3, $this->getTranslation()->_('4416'));
        }

        // 添加面试日志
        // 如果回退状态=1 则走创建
        if (!$is_edit || $interview_back == HrInterviewSubscribeModel::INTERVIEW_BACK_YES){
            $log_action = enums::$log_option['create'];
        } else{
            $log_action = enums::$log_option['modify'];
        }

        $this->log->addLog([
            'module_id'   => $interviewId,
            'module_type' => enums::$log_module_type['interview'],
            'action'      => $log_action,
            'data_after'  => $insertOrUpdateParam,
        ]);

        // 添加面试记录
        // 如果回退状态=1 则走创建
        if (!$is_edit && $interview_back == HrInterviewSubscribeModel::INTERVIEW_BACK_YES){
            $record_state = HrInterviewRecordModel::STATE_DEFAULT;
        }else{
            $record_state = HrInterviewRecordModel::STATE_EDIT;
        }
        $this->addHrInterviewRecord(
            $interviewId,
            $record_state,
            [
                'resume_id'         => $resumeId ?? '',
                'interview_address' => $detailAddress ?? '',
                'interview_time'    => $interviewTime ?? '',
                'interviewer_id'    => $interviewerId ?? '',
            ]
        );



        //预约面试重置简历表的淘汰和沟通装填
        $resume_up_data = [
            'is_out'       => enums::$resume_is_out['not_out'],//未淘汰
            'state_code'   => enums::$resume_state_code['communicated'],//已沟通
            'filter_state' => enums::$resume_filter_state['add_interview'],//已预约面试

        ];
        //简历最新操作人
        if (isCountry(['TH','PH','MY']) && !empty($this->userInfo['id'])) {
            $resume_up_data['resume_last_operator']       = $this->userInfo['id'];
            $resume_up_data['resume_last_operation_time'] = gmdate('Y-m-d H:i:s');
        }

        $this->getDI()->get('db')->updateAsDict('hr_resume', $resume_up_data,
            'id = '.$resumeId
        );

        //面试状态重置为待面试
        $interviewInfo = $this->resume->getInterviewByresume($interviewId);
        if (isset($interviewInfo['state']) && in_array($interviewInfo['state'], [1, 30, 31, 52])) {
            $this->getDI()->get('db')->updateAsDict(
                'hr_interview',
                [
                    'state'         => 5,
                    'cancel_type'   => null,
                    'cancel_reason' => null,
                ],
                'interview_id = '.$interviewId
            );
        }


        if ($have_subscribe_record == true && $is_edit == 1) {
            /**修改面试信息（修改的是有过预约面试记录的面试信息）逻辑处理**/
            $operation_info = HrInterviewerOperationModel::findFirst([
                'conditions' => 'id = :id:',
                'bind'       => ['id' => $ope_id],
            ]);
            if ($operation_info) {
                $operation_info->interviewer_id = $interviewerId;
                if ($interview_back == HrInterviewSubscribeModel::INTERVIEW_BACK_YES){
                    $operation_info->state = HrInterviewerOperationModel::STATE_NOT_OPERATION;
                }
                $operation_info->interview_time = $interviewTime;
                $operation_info->detail_address = $detailAddress;
                $operation_info->shop_id        = $shopId;//网点编号
                $operation_info->update_id      = $staffId;//修改操作hr编号
                //$operation_info->state          = HrInterviewerOperationModel::STATE_NOT_OPERATION;//重新面试
                $res                            = $operation_info->save();
                $log_level                      = $res ? 'info' : 'error';
                $this->getDI()->get('logger')->write_log('addOrUpdateInterviewAppointment：hr在winhr后台修改预约面试记录-写入表 ' . json_encode($paramIn,
                        JSON_UNESCAPED_UNICODE), $log_level);
            }
        } else {
            /**进入下一步（非首次预约面试）或 首次预约面试 处理逻辑 **/

            $opeartion['interview_sub_id'] = $subscribeId; //面试ID;
            $opeartion['interviewer_id']   = $interviewerId;//面试官ID;
            $opeartion['interview_time']   = $interviewTime;//面试时间;
            $opeartion['detail_address']   = $detailAddress;//面试地址;
            $opeartion['shop_id']          = $shopId;//网点编号
            $opeartion['create_id']        = $staffId;//面试操作hr信息
            $interviewer_opeartion         = (new HrInterviewerOperationModel())->addInfo($opeartion);
            $ope_id                        = $interviewer_opeartion;
            //日志
            $log_level = $interviewer_opeartion ? 'info' : 'error';
            $this->getDI()->get('logger')->write_log('addOrUpdateInterviewAppointment: hr在winhr后台新建预约面试记录-写入表' . json_encode($paramIn,
                    JSON_UNESCAPED_UNICODE), $log_level);
        }
        //记录招聘负责人
        if (!$is_edit) {
            $interview_state = $interviewInfo['state'] ?? 0;
            (new ResumeServer())->updateResumeRecruiter($resumeId, $this->userInfo['id'],
                $this->userInfo['node_department_id'], $resumeInfo['filter_state'], $interview_state, 4);
        }


        //数据返回
        $returnData['data'] = ['subscribe_id' => $subscribeId, 'ope_id' => $ope_id];
        return $this->checkReturn($returnData);
    }

    /**
     * 预约详情
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function interviewAppointmentDetail($paramIn = [])
    {
        $this->sysList      = new SysListRepository();
        $this->store_server = new SysStoreServer();
        $this->log          = new LogServer();

        //[1]参数定义
        $subscribeId = $this->processingDefault($paramIn, 'subscribe_id');

        //[2]校验是否存在
        $param    = [
            'subscribe_id' => $subscribeId,
        ];
        $jdDetail = (new InterviewRepository())->subscribeDetail($param, 1);
        if ($jdDetail) {
            $paramLog                  = [
                'module_id'   => $jdDetail['interview_id'],
                'module_type' => '1,2,3,4',
            ];
            $store_server              = $this->store_server->getSysStoreList();
            $jdDetail['worknode_name'] = $store_server[$jdDetail['worknode_id']]['store_name'] ?? 'head office';
            //简历提供人
            if ($jdDetail["resume_provider_id"] == "-1") {
                $jdDetail['resume_provider_name'] = "DC";
            } elseif ($jdDetail["resume_provider_id"] == "-2") {
                $jdDetail['resume_provider_name'] = "null";
            } else {
                if ($jdDetail['resume_provider_id']) {
                    //$jdDetail['resume_provider_id']
                    $staffInfo                        = (new HrStaffInfoModel)->getHrStaffInfo($jdDetail['resume_provider_id']);
                    $jdDetail['resume_provider_name'] = StaffServer::getStaffNameView($staffInfo,3);
                } else {
                    $jdDetail['resume_provider_name'] = "";
                }
            }
            //日志信息
            $historyData                      = $this->log->infoLog($paramLog);
            $jdDetail['infoInterviewHistory'] = $historyData;
        }
        return $this->checkReturn(['data' => $jdDetail]);
    }

    /**
     * 操作历史
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function operatingHistory($paramIn = [])
    {
        $this->log = new LogServer();

        //[1]参数定义
        $interviewId = $this->processingDefault($paramIn, 'interview_id', 2);
        $module_type = $this->processingDefault($paramIn, 'module_type', 2, 1);
        //[2]校验是否存在
        $param    = [
            'module_id'   => $interviewId,
            'module_type' => '1,2,3,4,13',
        ];
        $dataList = $this->log->infoLog($param);

        return $this->checkReturn(['data' => ['dataList' => $dataList]]);
    }


    /**
     * 取消预约
     * @param array $paramIn
     * @return array
     */
    public function interviewAppointmentStatus($paramIn = [])
    {
        $this->blacklist = new BlacklistRepository();
        $this->log       = new LogServer();

        //[1]参数定义
        $subscribeId    = $this->processingDefault($paramIn, 'subscribe_id');
        $status         = $this->processingDefault($paramIn, 'status', 2, 2);
        $cancelType     = $this->processingDefault($paramIn, 'cancel_type', 2);
        $cancelReason   = $this->processingDefault($paramIn, 'cancel_reason');
        $is_blacklist   = $this->processingDefault($paramIn, 'is_blacklist', 2, 0);
        $ope_id         = $this->processingDefault($paramIn, 'ope_id', 2, 0);
        $interview_back = $this->processingDefault($paramIn, 'interview_back', 2, 0);

        //[2]校验是否存在
        $param           = [
            'subscribe_id' => $subscribeId,
        ];
        $subscribeDetail = (new InterviewRepository())->subscribeDetail($param);
        if (empty($subscribeDetail)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('4415'));
        }

        //加入黑名单
        if ($is_blacklist == 1) {
            //加入黑名单
            $sql  = "select resume_id from hr_interview where interview_id = ".$subscribeDetail['interview_id'];
            $obj  = $this->getDI()->get('db')->query($sql);
            $data = $obj->fetch(\Phalcon\Db::FETCH_ASSOC);
            if ($data) {
                $id = $this->blacklist->blacklistAdd([
                    "resume_id" => $data['resume_id'],
                    "type"      => 6,
                    "remark"    => $cancelReason,
                ]);
                if ($id == -1) {
                    return $this->checkReturn(-3, $this->getTranslation()->_('7522'));
                }
            } else {
                return $this->checkReturn(-3, $this->getTranslation()->_('4008'));
            }
        }

        //查询层级
        $level = (new InterviewRepository())->selectLevel($subscribeDetail['interview_id']);

        //修改面试状态
        if ($ope_id) {
            $operationInfo = HrInterviewerOperationModel::findFirst([
                'conditions' => 'id=:id:',
                'bind'       => ['id' => $ope_id],
            ]);
            if ($operationInfo) {
                $operationInfo->state     = 3;
                $operationInfo->cancel_at = gmdate('Y-m-d H:i:s');
                if ($interview_back == HrInterviewSubscribeModel::INTERVIEW_BACK_YES){
                    // 回退逻辑需重新刷创建时间
                    $operationInfo->created_at = gmdate('Y-m-d H:i:s');
                }
                if (!$operationInfo->save()) {
                    $this->getDI()->get('logger')->write_log('interview -interviewAppointmentCancelAction: 修改:HrInterviewerOperationModel fail' . json_encode($ope_id,
                            JSON_UNESCAPED_UNICODE), 'notice');
                }
            }
        } else {//数据异常
            $this->getDI()->get('logger')->write_log('interview -interviewAppointmentCancelAction: 请求参数没有:ope_id' . json_encode($paramIn,
                    JSON_UNESCAPED_UNICODE), 'info');
        }

        if ($interview_back == HrInterviewSubscribeModel::INTERVIEW_BACK_YES){
            // 走面试回退逻辑

            // 校验hc 只有招聘中的hc 面试才可以回退
            if ($subscribeDetail['hc_state_code'] != HrhcModel::STATE_RECRUITING){
                return $this->checkReturn(-3, $this->getTranslation()->_('interview_cancel_and_related_hc_1'));
            }
            $result = $this->getDI()->get('db')->updateAsDict(
                'hr_interview_subscribe',
                [
                    'interview_back' => $interview_back,
                    'created_at' => gmdate('Y-m-d H:i:s'),
                    'interview_back_cancel_type'   => $cancelType,
                    'interview_back_cancel_reason' => $cancelReason,
                ],
                'id = ' . $subscribeId
            );
            if ($result){
                //添加日志
                $this->log->addLog([
                    'module_id'    => $subscribeDetail['interview_id'],
                    'module_type'  => 1,
                    'module_level' => $level + 1,
                    'action'       => 29,
                    'data_after'   => $subscribeDetail,
                ]);
            }else{
                return $this->checkReturn(-3, $this->getTranslation()->_('4419'));
            }
        }else{
            // 走面试终止逻辑
            if ($status == 2) {
                //回调修改取消原因
                $param = [
                    'interview_id'  => $subscribeDetail['interview_id'],
                    'cancel_type'   => $cancelType,
                    'cancel_reason' => $cancelReason,
                ];
                (new InterviewRepository())->interviewCancel($param);
            }
            //添加日志
            $this->log->addLog([
                'module_id'    => $subscribeDetail['interview_id'],
                'module_type'  => 1,
                'module_level' => $level,
                'action'       => 4,
                'data_after'   => $param,
            ]);
            // 添加面试记录
            $this->addHrInterviewRecord($subscribeDetail['interview_id'],HrInterviewRecordModel::STATE_CANCEL);
            //修改预约面试状态
            $param = [
                'subscribe_id' => $subscribeId,
                'status'       => $status,
            ];
            $flag  = (new InterviewRepository())->interviewAppointmentStatus($param);
            if (!$flag) {
                return $this->checkReturn(-3, $this->getTranslation()->_('4419'));
            }
        }
        //获取HR相关信息
        $hr_info = (new StaffRepository())->getStaffInfoById($subscribeDetail['staff_id']);
        //求职者信息
        $resume_info = (new InterviewRepository())->getResumeByInterviewId($subscribeDetail['interview_id']);
        $resume_info = $resume_info ? $resume_info[0] : [];

        //预约面试详情
        $subscribe_and_title = (new \FlashExpress\bi\App\Repository\HrInterviewSubscribeRepository())->getSubscribeDetail($subscribeId);

        //发送BY消息
        $params['staff_info_id'] = $subscribeDetail['interviewer_id'];
        $params['message_title'] = $this->getTranslation()->_('7528');
        //消息内容

        $hr_info['name']           = $hr_info['nick_name'] ?: $hr_info['name_en'];
        $name                      = $resume_info['name'] ? $resume_info['name'] : $resume_info['first_name_en'] . ' ' . $resume_info['last_name_en'];
        $params['message_content'] = str_replace(
            ["{nick_name}", "{staff_info_id}", "{updated_at}", "{name}", "{cvid}", "{job_name}"],
            [
                $hr_info['name'],
                $hr_info['staff_info_id'],
                date('Y-m-d', strtotime($subscribe_and_title['updated_at'])),
                $name,
                $resume_info['cvid'],
                $subscribe_and_title['job_name'],
            ],
            $this->getTranslation()->_('cancel_interview_content')
        );

        //push 屏幕通知
        $params['message_params'] = 'message_list'; //通知跳转地址
        $push_res                 = (new PublicServer())->pushMessageToScreen($params);
        // $push_info = $push_res ? 'info' : 'notice';
        $this->getDI()->get('logger')->write_log('发送面试取消通知至BY-pushMessageToScreen: ' . json_encode($params,
                JSON_UNESCAPED_UNICODE) . ',res:' . json_encode($push_res), 'info');

        //push 站内行
        $params['message_content'] .= "|||{$subscribeDetail['interview_id']},{$subscribeDetail['id']},{$ope_id}";
        $params['category']        = 41; //面试通知
        $params['category_code']   = 1; //通知类型 前端显示按钮文案
        $send_msg_to_interviewer   = (new PublicRepository())->pushMessageToInterviewer($params);

        // log 级别
        // $info_lev = $send_msg_to_interviewer ? 'info' : 'notice';
        $this->getDI()->get('logger')->write_log('发送面试取消通知至BY-pushMessageToInterviewer:' . json_encode($params,
                JSON_UNESCAPED_UNICODE) . ',res:' . json_encode($send_msg_to_interviewer), 'info');

        return $this->checkReturn(1);
    }

    /**
     * 发送offer
     * 第二版， 迁移sendoffer 部分 ， 薪资项改动比较大
     * @param array $paramIn
     * @return array
     * @throws BusinessException
     */
    public function sendOfferV2($paramIn = [])
    {
        $this->offer = new OfferRepository();
        $this->hc    = new HcRepository();
        $this->log   = new LogServer();

        $resume_id       = $this->processingDefault($paramIn, 'resume_id', 2);
        $currencyId      = $this->processingDefault($paramIn, 'currency', 2, 1);
        $other           = $this->processingDefault($paramIn, 'other', 1); // 备注
        $staff_job_type  = $this->processingDefault($paramIn, 'staff_job_type', 2);
        $hire_type       = $this->processingDefault($paramIn, 'hire_type', 2);
        $position_id     = $this->processingDefault($paramIn, 'position_id', 2);
        $in_salary_range = $this->processingDefault($paramIn, 'in_salary_range', 2);
        $work_time       = $this->processingDefault($paramIn, 'work_time');
        $submitterId     = $this->processingDefault($paramIn, 'staff_id', 2);
        $job_title_grade = $paramIn['job_title_grade'];//职级字段
        $shift_id        = $this->processingDefault($paramIn, 'shift_id', 2);
        $work_days       = $this->processingDefault($paramIn, 'work_days', 2);
        // 薪资项
        $money        = $this->processingDefault($paramIn, 'money', 3);
        $trial_salary = $this->processingDefault($paramIn, 'trial_salary', 3);

        if (date("Y-m-d", strtotime($paramIn['work_time'])) < date("Y-m-d", time()) || empty($paramIn['work_time'])) {
            return $this->checkReturn(-3, $this->getTranslation()->_('4428'));
        }
        $interview_id   = $paramIn['interview_id'];
        $interview_data = $this->getInterviewInfo($interview_id);
        if (empty($interview_data)) {
            throw new BusinessException("面试信息获取失败，面试ID：{$interview_id}");
        }
        //只有待发offer状态可以发送offer签字操作
        if ($interview_data['state'] != 20) {
            throw new BusinessException("待发offer状态才可进行此操作，面试ID：$interview_id,当前面试状态：{$interview_data['state']}");
        }

        //校验简历是否完善
        $interviewSql  = "select hr_resume.is_perfect,hr_resume.hc_id,hr_resume.credentials_num,hr_resume.date_birth from hr_interview LEFT JOIN hr_resume on hr_interview.resume_id = hr_resume.id and hr_interview.hc_id = hr_resume.hc_id where hr_interview.interview_id=".$paramIn['interview_id'];
        $data          = $this->getDI()->get('db')->query($interviewSql);
        $interviewData = $data->fetch(\Phalcon\Db::FETCH_ASSOC);
        if (!$interviewData || $interviewData['is_perfect'] == 2) {
            return $this->checkReturn(-3, $this->getTranslation()->_('6119'));
        }
        
        //增加校验
        if (
            !empty($interviewData['hc_id'])
            &&
            !empty($paramIn['hc_id'])
            &&
            $interviewData['hc_id'] != $paramIn['hc_id'])
        {
            throw new ValidationException($this->getTranslation()->_('no_server'));
        }


        //校验生日
        if ((strtotime("-18 years", time()) < strtotime($interviewData['date_birth']))) {
            return $this->checkReturn(-3, $this->getTranslation()->_('8810'));
        }

        //校验黑名单
        if (isCountry('TH')){
            $black_grey_list = (new BlackgreylistServer())->check_black_grey_list([
                "identity" => $interviewData['credentials_num'],
            ]);
            if ($black_grey_list["is_black_list"]){
                return $this->checkReturn(-3, $this->getTranslation()->_('7501'));
            }
        }else{
            $isBalcklist = (new BlacklistRepository())->blacklistCheck([
                "identity" => $interviewData['credentials_num'],
            ]);
            if ($isBalcklist > 0) {
                return $this->checkReturn(-3, $this->getTranslation()->_('7501'));
            }
        }

        (new OutsourcingBlackListServer())->check($interviewData['credentials_num'] ?? '','winhr',true,$this->lang);


        /* 校验hc */
        $hcData = $this->hc->checkHc(['hc_id' => $paramIn['hc_id']]);
        if ($hcData['state_code'] == 3 || $hcData['surplusnumber'] == 0) {
            return $this->checkReturn(-3, $this->getTranslation()->_('11027'));
        }
        if ($hcData['state_code'] == 4) {
            return $this->checkReturn(-3, $this->getTranslation()->_('11028'));
        }
        if ($hcData['state_code'] == 9) {
            return $this->checkReturn(-3, $this->getTranslation()->_('11031'));
        }

        /* 校验此offer是否存在 */
        $interviewOfferData = $this->offer->checkOffer([
            'interview_id' => $paramIn['interview_id'],
            'status'       => 1,
        ]);
        if ($interviewOfferData) {
            return $this->checkReturn(-3, $this->getTranslation()->_('4424'));
        }

        $dbcon = $this->getDI()->get('db');
        $dbcon->begin();
        try {
            $resume                 = HrResumeModel::findFirst([
                'conditions' => 'id = :id:',
                'bind'       => [
                    'id' => $paramIn['resume_id'],
                ],
            ]);
            $resume                 = $resume ? $resume->toArray() : [];
            $is_need_salary_approve = SettingEnvServer::getSetVal('is_need_salary_approve');//是否需要薪资审批，1：需要，0或未设置：不需要
            $codes                  = SettingEnvServer::getSetVal('send_offer_white_credentials');
            $codes                  = $codes ? explode(',', $codes) : [];
            if ($is_need_salary_approve && $resume && isset($resume['credentials_num']) && !in_array($resume['credentials_num'],
                    $codes)) {
                $params = [
                    'userinfo'        => $this->userInfo,
                    'resume_id'       => $resume_id,
                    'job_id'          => $position_id,
                    'in_salary_range' => $paramIn['in_salary_range'] ?? 0,
                    'money'           => [
                        'basic_salary'         => $money['basic_salary'] / 100,
                        'house_rental'         => $money['house_rental'] / 100,
                        'xinghuo_allowance'    => $money['xinghuo_allowance'] / 100,
                        'money'                => $money['money'] / 100,
                        'welfare_allowance'    => $money['welfare_allowance'] / 100,
                        'food_allowance'       => $money['food_allowance'] / 100,
                        'variable_allowance'   => $money['variable_allowance'] / 100,
                        'attendance_allowance' => $money['attendance_allowance'] / 100,
                        'transport_allowance'  => $money['transport_allowance'] / 100,
                        'language_allowance'   => $money['language_allowance'] / 100,
                        'is_computer_allowance'=> $money['is_computer_allowance'],
                        'is_camera_allowance'  => $money['is_camera_allowance'],
                    ],
                    'trial_salary'    => [
                        'basic_salary'         => $trial_salary['basic_salary'] / 100,
                        'house_rental'         => $trial_salary['house_rental'] / 100,
                        'xinghuo_allowance'    => $trial_salary['xinghuo_allowance'] / 100,
                        'money'                => $trial_salary['money'] / 100,
                        'welfare_allowance'    => $trial_salary['welfare_allowance'] / 100,
                        'food_allowance'       => $trial_salary['food_allowance'] / 100,
                        'variable_allowance'   => $trial_salary['variable_allowance'] / 100,
                        'attendance_allowance' => $trial_salary['attendance_allowance'] / 100,
                        'transport_allowance'  => $trial_salary['transport_allowance'] / 100,
                        'language_allowance'   => $trial_salary['language_allowance'] / 100,
                        'is_computer_allowance'=> $trial_salary['is_computer_allowance'],
                        'is_camera_allowance'  => $trial_salary['is_camera_allowance'],
                    ],
                ];
                $this->getDI()->get("logger")->write_log("sendOffer check " . json_encode($params,
                        JSON_UNESCAPED_UNICODE), "info");
                $apiClient = (new ApiClient('by_rpc', '', 'salary_can_approve_v2', $this->lang));
                $apiClient->setParams($params);
                $result = $apiClient->execute();
                $this->getDI()->get("logger")->write_log("sendOffer check " . json_encode($result,
                        JSON_UNESCAPED_UNICODE), "info");
                if ($result['result'] && $result['result']['code'] != 1) {
                    return $this->checkReturn(-3, $result['result']['msg']);
                }
            }
            $params = [
                'interview_id'         => $interview_id,
                'resume_id'            => $resume_id,
                'hc_id'                => $paramIn['hc_id'],
                'currency'             => $currencyId,
                'in_salary_range'      => $in_salary_range,
                'position_id'          => $position_id,
                'job_title_grade'      => $job_title_grade, //新增职级字段
                'work_time'            => $work_time,
                'staff_job_type'       => $staff_job_type,
                'hire_type'            => $hire_type,
                'other'                => $other,
                'status'               => 1,
                'shift_id'             => $shift_id,
                'work_days'            => $work_days,
                'send_time'            => gmdate('Y-m-d H:i:s'),//发送offer时间
                'money'                => $money['money'],
                'trial_salary'         => $trial_salary['money'],
                'basic_salary'         => $money['basic_salary'],
                'welfare_allowance'    => $money['welfare_allowance'],
                'variable_allowance'   => $money['variable_allowance'],
                'food'                 => $money['food_allowance'],
                'attendance_allowance' => $money['attendance_allowance'],
                'transport_allowance'  => $money['transport_allowance'],
                'renting'              => $money['house_rental'],
                'submitter_id'         => $submitterId,
                'xinghuo_allowance'    => $money['xinghuo_allowance'],
                'language_allowance'   => $money['language_allowance'],
                'is_computer_allowance'=> $money['is_computer_allowance'],
                'is_camera_allowance'  => $money['is_camera_allowance'],
            ];

            $success = $dbcon->insertAsDict('hr_interview_offer', $params);
            if (!$success) {
                return $this->checkReturn(-3, $this->getTranslation()->_('4701'));
            }
            $interview_******** = $dbcon->lastInsertId();

            $dbcon->insertAsDict('offer_send_salary', [
                '********'             => $interview_********,
                'money'                => $trial_salary['money'],
                'basic_salary'         => $trial_salary['basic_salary'],
                'welfare_allowance'    => $trial_salary['welfare_allowance'],
                'variable_allowance'   => $trial_salary['variable_allowance'],
                'food_allowance'       => $trial_salary['food_allowance'],
                'attendance_allowance' => $trial_salary['attendance_allowance'],
                'transport_allowance'  => $trial_salary['transport_allowance'],
                'house_rental'         => $trial_salary['house_rental'],
                'xinghuo_allowance'    => $trial_salary['xinghuo_allowance'],
                'language_allowance'   => $trial_salary['language_allowance'],
                'is_computer_allowance'   => $trial_salary['is_computer_allowance'],
                'is_camera_allowance'   => $trial_salary['is_camera_allowance'],
            ]);

            //记录薪资修改记录
            (new OfferServer())->addOfferSalaryOperatorLog([
                'interview_********' => $interview_********,
                'money'              => $money,
                'trial_salary'       => $trial_salary,
            ]);
            $send_time = gmdate('Y-m-d H:i:s', time());
            $flag      = self::updateInterviewStatus([
                'id'        => $paramIn['interview_id'],
                'state'     => 25,
                'send_time' => $send_time,
            ]);
            if (!$flag) {
                $dbcon->rollback();
                return $this->checkReturn(-3, $this->getTranslation()->_('4701'));
            }


            //todo 添加入职表数据
            $this->AddEntryData($paramIn['interview_id'], $interview_********, $paramIn['resume_id'],
                $paramIn['hc_id'],$hcData['hire_type']);

            /* 修改hc剩余数-1 */
            if ($interview_********) {
                //进行hc人数操作是在重新获取一遍hc，防止hc剩余人数已发生变更
                $current_hc = $this->hc->checkHc(['hc_id' => $paramIn['hc_id'],'for_update'=>true]);
                $this->getDI()->get("logger")->write_log(['current_hc'=>$current_hc,'paramIn'=>$paramIn], "info");
                if ($current_hc['state_code'] == 3 || $current_hc['surplusnumber'] == 0) {
                    $dbcon->rollback();
                    return $this->checkReturn(-3, $this->getTranslation()->_('4425'));
                }
                if ($current_hc['surplusnumber'] == 1) {
                    /* 修改hc表状态 改为已完成 */
                    $dbcon->updateAsDict('hr_hc', ['surplusnumber' => 0, 'state_code' => 3],
                        'hc_id = ' . $paramIn['hc_id']);
                } else {
                    $dbcon->updateAsDict('hr_hc', ['surplusnumber' => $current_hc['surplusnumber'] - 1],
                        'hc_id = ' . $paramIn['hc_id']);
                }
            }

            //添加日志
            $this->log->addLog([
                'module_id'     => $paramIn['interview_id'],
                'module_type'   => enums::$log_module_type['offer'],
                'action'        => enums::$log_option['send'],
                'module_status' => enums::$log_status['to_be_confirmed'],
                'data_after'    => $params,
            ]);
            // 创建保存点
            $dbcon->commit();
            //添加hc记录
            (new HcServer())->sync_hc($paramIn['hc_id']);
            //hr操作淘汰 若客服处理进度等于待处理、延期处理、待审核
            //      点击淘汰简历、将客服处理进度改成=处理终止，并将处理终止原因标记为“招聘HR已处理”
            if (env('country_code') == 'PH') {
                (new CustomerresumeRepository())->getCustomerResumeById($paramIn['resume_id']);
            }
        } catch (\Exception $exception) {
            $dbcon->rollback();
            $this->getDI()->get('logger')->write_log('发送offer失败:' . $exception->getMessage() . $exception->getTraceAsString(),
                'notice');
            return $this->checkReturn(-3, $this->getTranslation()->_('4008'));
        }
        return $this->checkReturn(1);
    }

    /**
     * 发送offer
     * @param array $paramIn
     * @return array
     */
    public function sendOffer($paramIn = [])
    {
        $this->offer = new OfferRepository();
        $this->hc    = new HcRepository();
        $this->log   = new LogServer();

        //[1]参数定义
        $currencyId = $this->processingDefault($paramIn, 'currency', 2, 1);
        $other      = $this->processingDefault($paramIn, 'other', 1); // 备注

        $staff_job_type = $this->processingDefault($paramIn, 'staff_job_type', 2);
        $hire_type      = $this->processingDefault($paramIn, 'hire_type', 2);
        $exp            = $this->processingDefault($paramIn, 'exp', 2);
        $food           = $this->processingDefault($paramIn, 'food', 2);
        $position       = $this->processingDefault($paramIn, 'position', 2);
        $rental         = $this->processingDefault($paramIn, 'rental', 2);
        $fuel           = $this->processingDefault($paramIn, 'fuel', 2);
        $computer       = $this->processingDefault($paramIn, 'computer', 2);
        $renting        = $this->processingDefault($paramIn, 'renting', 2);
        $submitterId    = $this->processingDefault($paramIn, 'staff_id', 2);
        $dangerously    = $this->processingDefault($paramIn, 'dangerously', 2);
        $is_blacklist   = $this->processingDefault($paramIn, 'is_balcklist', 2);
        $position_id    = $this->processingDefault($paramIn, 'position_id', 2);

        $in_salary_range = $this->processingDefault($paramIn, 'in_salary_range', 2);

        $work_time                   = $paramIn['work_time'];
        $train_time                  = $paramIn['train_time'] ?? '';
        $train_place                 = $paramIn['train_place'] ?? '';
        $gasoline_allowance          = $this->processingDefault($paramIn, 'gasoline_allowance', 2);
        $island_allowance            = $this->processingDefault($paramIn, 'island_allowance', 2);
        $attendance_allowance        = $this->processingDefault($paramIn, 'attendance_allowance', 2);
        $basic_salary                = $this->processingDefault($paramIn, 'basic_salary', 2);
        $transport_allowance         = $this->processingDefault($paramIn, 'transport_allowance', 2);
        $performance_allowance       = $this->processingDefault($paramIn, 'performance_allowance', 2);
        $xinghuo_allowance           = $this->processingDefault($paramIn, 'xinghuo_allowance', 2);
        $deminimis_benefits          = $this->processingDefault($paramIn, 'deminimis_benefits', 2);
        $other_non_taxable_allowance = $this->processingDefault($paramIn, 'other_non_taxable_allowance', 2);

        $other_taxable_allowance     = $this->processingDefault($paramIn, 'other_taxable_allowance', 2);
        $recommended                 = $this->processingDefault($paramIn, 'recommended', 2);
        $phone_subsidy               = $this->processingDefault($paramIn, 'phone_subsidy', 2);
        $shift_id                    = $this->processingDefault($paramIn, 'shift_id', 2);
        $internship_salary           = $this->processingDefault($paramIn, 'internship_salary', 2);
        $subsidyType                 = $this->processingDefault($paramIn, 'subsidy_type', 2, 0);

        if (isset($paramIn['job_title_grade']) && $paramIn['job_title_grade'] != null) {
            $job_title_grade = $paramIn['job_title_grade'];//职级字段
        }


        if (date("Y-m-d", strtotime($paramIn['work_time'])) < date("Y-m-d", time()) || empty($paramIn['work_time'])) {
            return $this->checkReturn(-3, $this->getTranslation()->_('4428'));
        }

        $interview_id   = $paramIn['interview_id'];

        $interview_data = $this->getInterviewInfo($interview_id);
        if (empty($interview_data)) {
            throw new BusinessException("面试信息获取失败，面试ID：{$interview_id}");
        }
        //只有待发offer状态可以发送offer签字操作
        if ($interview_data['state'] != 20) {
            throw new BusinessException("待发offer状态才可进行此操作，面试ID：$interview_id,当前面试状态：{$interview_data['state']}");
        }

        //校验简历是否完善
        $interviewSql  = "select hr_resume.is_perfect,hr_resume.hc_id,hr_resume.credentials_num,hr_resume.date_birth from hr_interview LEFT JOIN hr_resume on hr_interview.resume_id = hr_resume.id and hr_interview.hc_id = hr_resume.hc_id where hr_interview.interview_id=".$paramIn['interview_id'];
        $data          = $this->getDI()->get('db')->query($interviewSql);
        $interviewData = $data->fetch(\Phalcon\Db::FETCH_ASSOC);
        if (!$interviewData || $interviewData['is_perfect'] == 2) {
            return $this->checkReturn(-3, $this->getTranslation()->_('6119'));
        }

        //增加校验
        if (
            !empty($interviewData['hc_id'])
            &&
            !empty($paramIn['hc_id'])
            &&
            $interviewData['hc_id'] != $paramIn['hc_id'])
        {
            throw new ValidationException($this->getTranslation()->_('no_server'));
        }

        $dataYear = 18;
        if (isCountry('TH')) {
            $hcInfo = HcModel::findFirst([
                'columns'    => 'department_id,job_title as job_title_id,hire_type,worknode_id',
                'conditions' => 'hc_id = :hc_id:',
                'bind'       => [
                    'hc_id' => $interviewData['hc_id'] ?? 0,
                ],
            ]);

            $hcInfo = $hcInfo ? $hcInfo->toArray() : [];

            if ((new TemplateServer())->checkThFulfillmentInternship($hcInfo)) {
                $dataYear = 15;
            }

            //泰国验证项目期数
            if ((new OfferServer())->isProjectNum($paramIn['resume_id'],$hcInfo['job_title_id'])) {
                if (empty($paramIn['project_num'])) {
                    throw new ValidationException($this->getTranslation()->_('project_num_not_empry'));
                }
            }

            // 校验网点营业状态
            if (!empty($hcInfo['worknode_id']) && $hcInfo['worknode_id'] != enums::HEAD_OFFICE_ID){
                $storeObj = SysStoreModel::findFirst([
                    'conditions' => 'id = :storeId:',
                    'bind'       => ['storeId' => $hcInfo['worknode_id']],
                ]);
                $storeData = $storeObj ? $storeObj->toArray() : [];
                if (empty($storeData) || $storeData['use_state'] != SysStoreModel::USE_STATE_YES){
                    throw new ValidationException($this->getTranslation()->_('store_use_state_err'));
                }
            }

            //EV Courier[1930]，且简历中【车主】≠公司车辆，toast提示“简历中车主不是公司车辆，无法发送offer”
            if (!empty($hcInfo['job_title_id']) && $hcInfo['job_title_id'] == enumsTh::JOB_TITLE_EV_COURIER) {
                $ecomomyAbilityInfo = HrEconomyAbilityModel::findFirst([
                    'conditions' => 'resume_id = :resume_id:',
                    'bind'       => [
                        'resume_id' => (int)$paramIn['resume_id'],
                    ],
                ]);

                if (!$ecomomyAbilityInfo || $ecomomyAbilityInfo->car_owner != enums::$car_own_type['company_car']) {
                    throw new ValidationException($this->getTranslation()->_('resume_car_owner_not_company'));
                }
            }
        }

        //校验生日
        if ((strtotime("-{$dataYear} years", time()) < strtotime($interviewData['date_birth']))) {
            return $this->checkReturn(-3, $this->getTranslation()->_('year_unqualified',['year' => $dataYear]));
        }

        //校验黑名单
        if (isCountry('TH')){
            $black_grey_list = (new BlackgreylistServer())->check_black_grey_list([
                "identity" => $interviewData['credentials_num'],
            ]);
            if ($black_grey_list["is_black_list"]){
                return $this->checkReturn(-3, $this->getTranslation()->_('7501'));
            }
        }else{
            $isBalcklist = (new BlacklistRepository())->blacklistCheck([
                "identity" => $interviewData['credentials_num'],
            ]);
            if ($isBalcklist > 0) {
                return $this->checkReturn(-3, $this->getTranslation()->_('7501'));
            }
        }

        (new OutsourcingBlackListServer())->check($interviewData['credentials_num'] ?? '','winhr',true,$this->lang);

        //获取offer指定职位ids有设备费 需求：当入职职位为“Bike Courier”或“Van Courier”时，才展示需扣除设备费字段，其余职位不体现
//        $equipmentCostPositionIds    = env("equipmentCostPositionIds", "{}");
//        $equipmentCostPositionIdsArr = json_decode($equipmentCostPositionIds, true);
//        if (!$position_id || !in_array($position_id, array_keys($equipmentCostPositionIdsArr))) {
//            $equipment_cost = 0;
//        } else {
//            $equipment_cost = $equipmentCostPositionIdsArr[$position_id];
//
//            //新币去汇率
//            if (isCountry('Th') && $currencyId == HrInterviewOfferModel::CURRENCY_SGD) {
//                $equipment_cost = intval($equipment_cost/25/100) * 100;
//            }
//        }
//        if (isCountry(['TH','PH']) && $hire_type == HrStaffInfoModel::HIRE_TYPE_AGENT){
//            // 个人代理强转0
//            $equipment_cost = 0;
//        }

        //校验班次id是否正确
        /*
        $shift_list = (new SysListRepository())->getAllShiftList();
        $shift_list = array_column($shift_list, null, 'id');
        if(!isset($shift_list[$shift_id])) {
            return $this->checkReturn(-3, 'shift ID error');
        }
        */

        //[2]校验是否存在
        $param = [
            'interview_id'                => $paramIn['interview_id'],
            'resume_id'                   => $paramIn['resume_id'],
            'hc_id'                       => $paramIn['hc_id'],
            'currency'                    => $currencyId,
            'subsidy_type'                => $subsidyType,
            'money'                       => $paramIn['money'],
            'trial_salary'                => isset($paramIn['trial_salary']) ? $paramIn['trial_salary'] : 0,
            'in_salary_range'             => $in_salary_range,
            'position_id'                 => $position_id,
            'job_title_grade'             => $job_title_grade, //新增职级字段
            'other'                       => $other,
            'work_time'                   => $work_time,
            'staff_job_type'              => $staff_job_type,
            'hire_type'                   => $hire_type,
            'exp'                         => $exp,
            'food'                        => $food,
            'position'                    => $position,
            'rental'                      => $rental,
            'fuel'                        => $fuel,
            'computer'                    => $computer,
            'renting'                     => $renting,
            'dangerously'                 => $dangerously,
//            'equipment_cost'              => $equipment_cost,
            'submitter_id'                => $submitterId,
            'status'                      => 1,
            'shift_id'                    => $shift_id,
            'gasoline_allowance'          => $gasoline_allowance,
            'island_allowance'            => $island_allowance,
            'attendance_allowance'        => $attendance_allowance,
            'basic_salary'                => $basic_salary,
            'transport_allowance'         => $transport_allowance,
            'performance_allowance'       => $performance_allowance,
            'xinghuo_allowance'           => $xinghuo_allowance,
            'deminimis_benefits'          => $deminimis_benefits,
            'other_non_taxable_allowance' => $other_non_taxable_allowance,

            'other_taxable_allowance'     => $other_taxable_allowance,
            'recommended'                 => $recommended,
            'phone_subsidy'               => $phone_subsidy,
            'send_time'                   => gmdate('Y-m-d H:i:s'),//发送offer时间
            'internship_salary'           => $internship_salary,//实习期工资，金额/Day
            'offer_template_key'          => $paramIn['offer_template_key'] ?? '',
        ];

        //gdl补贴
        if (isset($paramIn['gdl_allowance'])) {
            $param['gdl_allowance'] = intval($paramIn['gdl_allowance']);
        }
        //工作天数（泰国 一线职位才有该字段目前2021.10.29）
        if (isset($paramIn['work_days']) && $paramIn['work_days']) {
            $param['work_days'] = $paramIn['work_days'];
        }

        //项目期数
        if (isCountry()) {
            $param['project_num'] = !empty($paramIn['project_num']) ? $paramIn['project_num'] : null;
        }

        /* 校验此offer是否存在 */
        $interviewOfferData = $this->offer->checkOffer([
            'interview_id' => $paramIn['interview_id'],
            'status'       => 1,
        ]);
        if ($interviewOfferData) {
            return $this->checkReturn(-3, $this->getTranslation()->_('4424'));
        }
        /* 校验hc */
        $hcData = $this->hc->checkHc(['hc_id' => $paramIn['hc_id']]);
        if ($hcData['state_code'] == 3 || $hcData['surplusnumber'] == 0) {
            return $this->checkReturn(-3, $this->getTranslation()->_('11027'));
        }
        if ($hcData['state_code'] == 4) {
            return $this->checkReturn(-3, $this->getTranslation()->_('11028'));
        }
        if ($hcData['state_code'] == 9) {
            return $this->checkReturn(-3, $this->getTranslation()->_('11031'));
        }

        $dbcon = $this->getDI()->get('db');
        $dbcon->begin();
        try {
            $resume = HrResumeModel::findFirst([
                'conditions' => 'id = :id:',
                'bind'       => [
                    'id' => $paramIn['resume_id'],
                ],
            ]);
            $resume                 = $resume ? $resume->toArray() : [];
            $is_need_salary_approve = SettingEnvServer::getSetVal('is_need_salary_approve');//是否需要薪资审批，1：需要，0或未设置：不需要

            $codes                  = SettingEnvServer::getSetVal('send_offer_white_credentials');
            $codes                  = $codes ? explode(',', $codes) : [];
            if ($is_need_salary_approve && $resume && isset($resume['credentials_num']) && !in_array($resume['credentials_num'],
                    $codes) && !in_array($hcData["hire_type"],[HrStaffInfoModel::HIRE_TYPE_OTHER,HrStaffInfoModel::HIRE_TYPE_AGENT])) {

                $params = [
                    'userinfo'                    => $this->userInfo,
                    'resume_id'                   => $paramIn['resume_id'],
                    'job_id'                      => $position_id,
                    'money'                       => $paramIn['money'] / 100,
                    'trial_salary'                => isset($paramIn['trial_salary']) ? $paramIn['trial_salary'] / 100 : 0,
                    'renting'                     => isset($paramIn['renting']) ? $paramIn['renting'] / 100 : 0,
                    'deminimis_benefits'          => isset($paramIn['deminimis_benefits']) ? $paramIn['deminimis_benefits'] / 100 : 0,
                    'other_non_taxable_allowance' => isset($paramIn['other_non_taxable_allowance']) ? $paramIn['other_non_taxable_allowance'] / 100 : 0,
                    'xinghuo_allowance'           => $xinghuo_allowance ? $xinghuo_allowance / 100 : 0,
                    'in_salary_range'             => $paramIn['in_salary_range'] ?? 0,
                ];

                $this->getDI()->get("logger")->write_log("sendOffer check " . json_encode($params,
                        JSON_UNESCAPED_UNICODE), "info");
                $apiClient = (new ApiClient('by_rpc', '', 'salary_can_approve', $this->lang));
                $apiClient->setParams($params);
                $result = $apiClient->execute();
                $this->getDI()->get("logger")->write_log("sendOffer check " . json_encode($result,
                        JSON_UNESCAPED_UNICODE), "info");
                if ($result['result'] && $result['result']['code'] != 1) {
                    return $this->checkReturn(-3, $result['result']['msg']);
                }

                // 需要进行薪资审批 并且 没有完成薪资审批
//                if (
//                    $this->isCanSubmitSalaryApprove($hcData['department_id'], $hcData['job_id'], $paramIn['in_salary_range'], $paramIn['trial_salary'])
//                ) {
//
//                    return $this->checkReturn(-3, '请先申请薪资审批');
//                }
            }

            /* 校验此offer是否存在 */
            $interviewOfferData = $this->offer->checkOffer([
                'interview_id' => $paramIn['interview_id'],
                'status'       => 1,
            ]);
            if ($interviewOfferData) {
                return $this->checkReturn(-3, $this->getTranslation()->_('4424'));
            }
            if ($hcData['state_code'] == 4) {
                return $this->checkReturn(-3, $this->getTranslation()->_('4021'));
            }
            /* 执行插入操作 */
            $success = $dbcon->insertAsDict('hr_interview_offer', $param);
            if (!$success) {
                return $this->checkReturn(-3, $this->getTranslation()->_('4701'));
            }
            $interview_******** = $dbcon->lastInsertId();
            /* 修改面试主表状态 */
            $add_hour  = $this->getDI()['config']['application']['add_hour'];
            $send_time = gmdate('Y-m-d H:i:s', time());
            $flag      = self::updateInterviewStatus([
                'id'        => $paramIn['interview_id'],
                'state'     => 25,
                'send_time' => $send_time,
            ]);
            if (!$flag) {
                $dbcon->rollback();
                return $this->checkReturn(-3, $this->getTranslation()->_('4701'));
            }

            //todo 添加入职表数据
            $this->AddEntryData($paramIn['interview_id'], $interview_********, $paramIn['resume_id'],
                $paramIn['hc_id'],$hcData['hire_type']);

            /* 修改hc剩余数-1 */
            if ($interview_********) {
                //进行hc人数操作是在重新获取一遍hc，防止hc剩余人数已发生变更
                $current_hc = $this->hc->checkHc(['hc_id' => $paramIn['hc_id'],'for_update'=>true]);
                $this->getDI()->get("logger")->write_log(['current_hc'=>$current_hc,'paramIn'=>$paramIn], "info");

                if ($current_hc['state_code'] == 3 || $current_hc['surplusnumber'] == 0) {
                    $dbcon->rollback();
                    return $this->checkReturn(-3, $this->getTranslation()->_('4425'));
                }
                if ($current_hc['surplusnumber'] == 1) {
                    /* 修改hc表状态 改为已完成 */
                    $dbcon->updateAsDict('hr_hc', ['surplusnumber' => 0, 'state_code' => 3],
                        'hc_id = ' . $paramIn['hc_id']);
                } else {
                    $dbcon->updateAsDict('hr_hc', ['surplusnumber' => $current_hc['surplusnumber'] - 1],
                        'hc_id = ' . $paramIn['hc_id']);
                }
            }

            //记录薪资日志
            (new OfferServer())->addOfferSalaryOperatorLog([
                'interview_********' => $interview_********,
                'money'              => $param,
            ]);

            //添加日志
            $this->log->addLog([
                'module_id'     => $paramIn['interview_id'],
                'module_type'   => enums::$log_module_type['offer'],
                'action'        => enums::$log_option['send'],
                'module_status' => enums::$log_status['to_be_confirmed'],
                'data_after'    => $param,
            ]);

            (new ResumeServer())->setLastOperator($paramIn['resume_id']);
            // 创建保存点
            $dbcon->commit();

            //添加hc记录
            (new HcServer())->sync_hc($paramIn['hc_id']);

            //hr操作淘汰 若客服处理进度等于待处理、延期处理、待审核
            //      点击淘汰简历、将客服处理进度改成=处理终止，并将处理终止原因标记为“招聘HR已处理”
            if (env('country_code') == 'PH') {
                (new CustomerresumeRepository())->getCustomerResumeById($paramIn['resume_id']);
            }
        } catch (\Exception $e) {
            // 发生错误，回滚操作
            $dbcon->rollback();
            $this->getDI()->get('logger')->write_log('发送offer失败:' . $e->getMessage() . $e->getTraceAsString(),
                'notice');
            return $this->checkReturn(-3, $this->getTranslation()->_('4008'));
        }

        return $this->checkReturn(['data'=>['********'=>$interview_********]]);
    }

    /**
     * 是否需要薪资审批
     * @param $departmentId 部门ID
     * @param $jobId 职位ID
     * @param $inSalaryRange 是否在薪资范围内
     * @param $salaryUnit 薪资单位
     * @param $salary 薪资
     *
     */
    public function isCanSubmitSalaryApprove($departmentId, $jobId, $inSalaryRange, $salary)
    {
        // 快递业务线判断
        if ($this->isExpressLine($departmentId)) {
            // 快递业务线
            if ($this->isFirstLineJob($departmentId, $jobId)) {
                // 一线职位
                if (
                    strtolower(env('country_code', 'Th')) == 'th' && $salary <= 30000
                    || strtolower(env('country_code', 'Th')) == 'ph' && $salary <= 45000
                    || strtolower(env('country_code', 'Th')) == 'my' && $salary <= 3750
                    || strtolower(env('country_code', 'Th')) == 'vn' && $salary <= 20477816
                    || strtolower(env('country_code', 'Th')) == 'id' && $salary <= 13453518
                ) {
                    return 0;
                }
            }
        } else {
            // fei快递业务线
            if ($this->isFirstLineJob($departmentId, $jobId)) {
                // 一线职位
                if (
                    strtolower(env('country_code', 'Th')) == 'th' && $salary <= 30000
                    || strtolower(env('country_code', 'Th')) == 'ph' && $salary <= 45000
                    || strtolower(env('country_code', 'Th')) == 'my' && $salary <= 3750
                    || strtolower(env('country_code', 'Th')) == 'vn' && $salary <= 20477816
                    || strtolower(env('country_code', 'Th')) == 'id' && $salary <= 13453518
                ) {
                    return 0;
                }
            }
        }

        return 1;
    }




    public function getFirstLineJobsConfig()
    {
        $jobs = (new SettingEnvServer())->setExpire(120)->getSetValFromCache('first_line_jobs');
        return explode(',', $jobs);
    }


    /**
     * 是否是一线职位
     * @param $departmentId
     * @param $JobId
     * @return bool
     *
     */
    public function isFirstLineJob($departmentId, $JobId)
    {
        if (empty(self::$first_line_jobs)) {
            self::$first_line_jobs = $this->getFirstLineJobsConfig();
        }
        $items = self::$first_line_jobs;
        foreach ($items as $item) {
            $i = explode("|", $item);
            if (isset($i[0]) && isset($i[1])) {
                if ($i[0] == $departmentId && $i[1] == $JobId) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 是否是推荐简历
     * @param $info
     * @return bool
     */
    public function isRecommendedResume($info)
    {
        if (empty($info['hc_id'])) {
            return null;
        }
        $departmentId = $info['department_id'];
        $jobTitleId   = $info['job_title'];

        //增加是否为总部招聘判定 START
        $jobOfficeIds  = (new SettingEnvServer())->setExpire(30)->getSetValToArrayFromCache('jointly_recruited_ids');

        if (
            isCountry() &&
            isset($info['is_head_office_recruit']) && ($info['is_head_office_recruit'] == HrResumeModel::IS_HEAD_OFFICE_YES) &&
            !empty($jobTitleId) && in_array($jobTitleId, $jobOfficeIds)
        ) {
            return true;
        }
        //增加是否为总部招聘判定 END

        $jobs  = (new SettingEnvServer())->setExpire(120)->getSetValFromCache('non_recommend_resume_ids');
        $items = explode(',', $jobs);
        foreach ($items as $item) {
            $i = explode("|", $item);
            if (isset($i[0]) && isset($i[1])) {
                if ($i[0] == $departmentId && $i[1] == $jobTitleId) {
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * 检查简历是否为打标签的简历
     * @param $info
     * @return bool
     * 公共方法
     */
    public function isHeadOfferRecruit($info)
    {
        $jobTitleId = $info['job_title'];
        $is_head_office_recruit = $info['is_head_office_recruit'] ?? HrResumeModel::IS_HEAD_OFFICE_NO;

        $jobOfficeIds  = (new SettingEnvServer())->setExpire(120)->getSetValToArrayFromCache('jointly_recruited_ids');

        if (
            isCountry() &&
            ($is_head_office_recruit == HrResumeModel::IS_HEAD_OFFICE_YES) &&
            in_array($jobTitleId, $jobOfficeIds)
        ) {
            return true;
        }

        return false;
    }

    /**
     * 面试管理-取消offer-批量
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function cancelOfferBatch($paramIn = [])
    {

        return;

        $this->blacklist = new BlacklistRepository();
        $this->log       = new LogServer();

        $offerId = $paramIn["********s"] ?? "";
        //统一的offer取消类型
        $cancelTypeOffer = $paramIn["cancel_type"] ?? "";
        //统一的offer取消原因
        $cancelReasonOffer = $paramIn["cancel_reason"] ?? "";
        //是否批量加入黑名单
        $isBlacklist = $paramIn["is_blacklist"] ?? 0;

        try {
            //校验参数
            if (!$offerId || !$cancelTypeOffer || !$cancelReasonOffer) {
                //参数错误
                throw new BusinessException($this->getTranslation()->_('miss_args'));
            }
            $offerData = $this->getOfferInfoById($offerId);
            if (!$offerData) {
                //参数错误
                throw new BusinessException('offer信息读取失败,********：' . $offerId);
            }

            //统一参数offer信息
            $offerParam = [
                'status'        => 2,
                'cancel_type'   => $cancelTypeOffer,
                'cancel_reason' => $cancelReasonOffer,
            ];


            //开启事务
            $this->getDI()->get('db')->begin();

            $this->getDI()->get('db')->updateAsDict(
                'hr_entry',
                [
                    "status" => 3,
                ],
                "interview_******** = {$offerId}"
            );

            //面试状态更改为取消面试
            $this->updateInterviewStatus([
                'id'    => $offerData['interview_id'],
                'state' => 31,
            ]);

            //取消 offer
            $this->getDI()->get('db')->updateAsDict('hr_interview_offer',
                [
                    'status'                  => 2,
                    'offer_annex_upload_time' => null,
                    'cancel_type'             => $cancelTypeOffer,
                    'cancel_reason'           => $cancelReasonOffer,
                ],
                "id ={$offerId}"
            );

            //取消薪资审批
            $approval = $this->getLastApprove($offerData['resume_id']);
            if ($approval && $approval['status'] == 2) {
                $this->getDI()->get("db")->updateAsDict("salary_approve", [
                    "status" => 9,
                ], "id = " . $approval['id']);
            }
            //取消offer签字
            $offer_sign = (new InterviewRepository())->getLastOfferSignData($offerData['resume_id']);
            if ($offer_sign && $offer_sign['approve_state'] == 2) {
                $this->getDI()->get("db")->updateAsDict("hr_interview_offer_sign_approve", [
                    "approve_state" => 0,
                ], "id = " . $offer_sign['id']);
            }

            //hc
            $hcSql = "update hr_hc set surplusnumber=surplusnumber+1,state_code=2 where hc_id = ".$offerData['hc_id'];
            $this->getDI()->get('db')->query($hcSql);


            //加入黑名单
            if ($isBlacklist == 1) {
                $this->blacklist->blacklistAdd([
                    "resume_id" => $offerData["resume_id"],
                    "type"      => 4,
                    "remark"    => $cancelReasonOffer,
                ]);
            }

            //添加日志
            $this->log->addLog([
                'module_id'   => $offerData['interview_id'],
                'module_type' => enums::$log_module_type['offer'],
                'action'      => enums::$log_option['cancel'],
                'data_after'  => $offerParam,
            ]);

            $this->addHrInterviewRecord($offerData['interview_id'],HrInterviewRecordModel::STATE_CANCEL);
            //提交
            $this->getDI()->get('db')->commit();

            //添加hc记录
            (new \FlashExpress\bi\App\Server\HcServer())->sync_hc($offerData['hc_id']);

            return true;
        } catch (BusinessException $businessException) {
            $this->getDI()->get('logger')->write_log("cancelOfferBatch 异常信息:" . $businessException->getMessage(),
                'info');
            return $this->jsonReturn($this->checkReturn(-3, $businessException->getMessage()));
        } catch (\Exception $e) {
            //回滚
            $this->getDI()->get('db')->rollback();
            return false;
        }
    }

    /**
     * 取消offer逻辑处理
     * @param array $paramIn
     * @param array $cancel_action_source 取消动作来源，offer_list:offer列表取消offer,entry_list：入职列表操作未到岗
     * @return array|bool|jsonData
     */
    public function cancelSendOffer($paramIn = [], $cancel_action_source = 'offer_list')
    {
        $this->offer     = new OfferRepository();
        $this->blacklist = new BlacklistRepository();
        $this->log       = new LogServer();

        //[1]参数定义
        $********          = $this->processingDefault($paramIn, 'id', 2);
        $cancelTypeOffer   = $this->processingDefault($paramIn, 'cancel_type', 2);
        $cancelReasonOffer = $this->processingDefault($paramIn, 'cancel_reason');
        $is_blacklist      = $this->processingDefault($paramIn, 'is_blacklist');


        $this->getDI()->get('logger')->write_log("cancelSendOffer-1:source={$cancel_action_source},param=" . var_export($paramIn,
                true), 'info');
        try {
            $this->getDI()->get('db')->begin();
            //取消动作有offer列表的取消和入职列表的未到岗取消，未到岗取消不做如下判断逻辑
            if ($cancel_action_source && $cancel_action_source == 'offer_list') {
                //增加入职状态的校验，当入职状态=待入职（且入职时间时间为当天）/已入职时不可取消offer；
                $entryData  = (new OfferRepository())->getOfferDetailData([
                    '********s'    => $paramIn['id'],
                    'offer_status' => '1,2',
                ]);
                $entry_date = date("Y-m-d", strtotime($entryData['work_time']));
                if ($entryData && $entryData['status'] == 1) {
                    throw new BusinessException($this->getTranslation()->_('4426'));

                } elseif ($entryData['status'] == 2 && date('Y-m-d') == $entry_date) {
                    throw new BusinessException($this->getTranslation()->_('4427'));
                }
            }


            $interviewOfferData = $this->offer->checkOffer([
                'id'     => $********,
                'status' => 1,
            ]);
            if (!$interviewOfferData) {
                throw new BusinessException($this->getTranslation()->_('err_msg_offer_not_exist'));
            }

            $this->getDI()->get('logger')->write_log("cancelSendOffer-2:offerdata=" . var_export($interviewOfferData,
                    true), 'info');

            $interview_id = $interviewOfferData['interview_id'] ?? 0;
            $hc_id        = $interviewOfferData['hc_id'] ?? 0;
            $resume_id    = $interviewOfferData['resume_id'] ?? 0;

            //加入黑名单
            if ($is_blacklist == 1) {
                $id = $this->blacklist->blacklistAdd([
                    "resume_id" => $interviewOfferData['resume_id'],
                    "type"      => 4,
                    "remark"    => isset($cancelReasonOffer) ? $cancelReasonOffer : '',
                ]);
                if ($id == -1) {
                    throw new BusinessException($this->getTranslation()->_('7522'), 7522);
                }
            }
            //更改offer表取消状态和取消原因
            $param   = [
                'status'        => 2,
                'cancel_type'   => $cancelTypeOffer,
                'cancel_reason' => $cancelReasonOffer,
            ];
            $success = $this->getDI()->get('db')->updateAsDict('hr_interview_offer', $param, [
                'conditions' => " id =  $********",
            ]);

            if (!$success) {
                throw new ValidationException($this->getTranslation()->_('4701'));
            }

            //面试状态更改为取消面试
            $flag = self::updateInterviewStatus([
                'id'    => $interview_id,
                'state' => 31,
            ]);
            $this->getDI()->get('logger')->write_log("cancelSendOffer-3:取消面试id:" . $interview_id . ", Offer id " . $********,
                'info');
            //取消薪资审批
            $approval = $this->getLastApprove($resume_id);
            if ($approval && $approval['status'] == 2) {
                $this->getDI()->get("db")->updateAsDict("salary_approve", [
                    "status" => 9,
                ], "id = " . $approval['id']);
            }
            //取消offer签字
            $offer_sign = (new InterviewRepository())->getLastOfferSignData($resume_id);
            if ($offer_sign && $offer_sign['approve_state'] == 2) {
                $this->getDI()->get("db")->updateAsDict("hr_interview_offer_sign_approve", [
                    "approve_state" => 0,
                ], "id = " . $offer_sign['id']);
            }

            if (!$flag) {
//                $this->getDI()->get('db')->rollback();
                throw new ValidationException($this->getTranslation()->_('4701'));
            }

            /* 修改hc剩余数+1 */
            if ($cancel_action_source && $cancel_action_source == 'entry_list') {
                //入职取消逻辑，入职取消 无需判断一线非一线，hc+1
                $update_hc = true;
            } else {
                //offer取消逻辑
                if ($offer_sign) {
                    //非一线取消offer， 必须是offer附件上传过(根据上传时间字段)
                    $update_hc = !empty($interviewOfferData['offer_annex_upload_time']) ? true : false;
                } else {
                    //一线取消offer， hc+1
                    $update_hc = true;
                }
            }

            $hcData = (new HcRepository())->checkHc(['hc_id' => $hc_id]);

            if (isCountry('TH') && !empty($hcData['task_new_hc_id']) && $hcData['state_code'] == HrhcModel::STATE_FULL_RECRUITMENT){
                // 21405 需求这里有不返还hc逻辑
                $update_hc = false;
            }
            //是否hc+1 处理
            if ($update_hc) {
                if ($hc_id && isset($hcData['surplusnumber']) && ($hcData['surplusnumber'] == 0)) {
//                if ($hc_id && $interviewOfferData['surplusnumber'] == 0) {
                    /* 修改hc表状态 改为招聘中 */
                    $hcSql = "update hr_hc set surplusnumber=surplusnumber+1,state_code=2 where hc_id = ".$hc_id;
                    $this->getDI()->get('db')->query($hcSql);
                } elseif ($interviewOfferData['hc_id']) {
                    $hcSql = "update hr_hc set surplusnumber=surplusnumber+1 where hc_id = ".$hc_id;
                    $this->getDI()->get('db')->query($hcSql);
                }
            }

            $this->getDI()->get('logger')->write_log("cancelSendOffer-4:修改hc:" . $interview_id . ", Offer id " . $********,
                'info');

            //撤消Offer时，将入职状态更新为未入职
            $entryParam = [
                "status" => 3,
            ];

            if (isCountry('PH') && $cancel_action_source == 'entry_list') {
                $entryParam['cancel_type']           = $paramIn['entry_cancel_type'] ?? HrEntryModel::CANCEL_TYPE_NON;
                $entryParam['cancel_reason_type']    = $paramIn['entry_cancel_reason_type'] ?? 0;
                $entryParam['cancel_reason_content'] = $paramIn['entry_cancel_reason_content'] ?? 0;
            }

            $this->getDI()->get('db')->updateAsDict(
                'hr_entry',
                $entryParam,
                "interview_******** = {$********}"
            );


            // 创建保存点
            $this->getDI()->get('db')->commit();
            //添加日志
            $this->log->addLog([
                'module_id'   => $interviewOfferData['interview_id'],
                'module_type' => enums::$log_module_type['offer'],
                'action'      => enums::$log_option['cancel'],
                'data_after'  => $param,
            ]);

            //添加hc记录，同步给buddy的不再使用了

            // (new \FlashExpress\bi\App\Server\HcServer())->sync_hc($interviewOfferData['hc_id']);
        } catch (BusinessException $businessException) {
            // 发生错误，回滚操作
            $this->getDI()->get('db')->rollback();
            $this->getDI()->get('logger')->write_log("cancelSendOffer 异常信息:" . $businessException->getMessage(),
                'info');
            return $this->jsonReturn($this->checkReturn(-3, $businessException->getMessage()));
        } catch (\Exception $e) {
            // 发生错误，回滚操作
            $this->getDI()->get('db')->rollback();
            $this->getDI()->get('logger')->write_log("cancelSendOffer异常信息,offerid:{$********}:" . $e->getMessage(),
                'error');
            return false;
        }

        return $this->checkReturn(1);
    }

    /**
     * 简历最新审批
     *
     *
     * @param $resumeId
     * @return mixed
     *
     */
    public function getLastApprove($resumeId)
    {
        $sql = "--
            select 
                * 
            from salary_approve 
            where id in (
                select max(`id`) from salary_approve group by resume_id
            ) and resume_id = :resume_id ";

        return $this->getDI()->get("db")->fetchOne($sql, Db::FETCH_ASSOC, [
            'resume_id' => $resumeId,
        ], [
            'resume_id' => Db\Column::BIND_PARAM_INT,
        ]);
    }

    public function offerEditV2($paramIn = [])
    {
        $this->log      = new LogServer();
        $Id             = $this->processingDefault($paramIn, 'id', 2, 1);
        $currencyId     = $this->processingDefault($paramIn, 'currency', 2, 1);
        $other          = $this->processingDefault($paramIn, 'other', 1);
        $staff_job_type = $this->processingDefault($paramIn, 'staff_job_type', 2);
        $hire_type      = $this->processingDefault($paramIn, 'hire_type', 2);
        $position       = $this->processingDefault($paramIn, 'position', 2);
        $position_id    = $this->processingDefault($paramIn, 'position_id', 2);
        $recommended    = $this->processingDefault($paramIn, 'recommended', 2);
        $shift_id       = $this->processingDefault($paramIn, 'shift_id', 2);
        $work_days       = $this->processingDefault($paramIn, 'work_days', 2);
        // 薪资项
        $money              = $this->processingDefault($paramIn, 'money', 3);
        $trial_salary       = $this->processingDefault($paramIn, 'trial_salary', 3);
        $hr_interview_offer = HrInterviewOfferModel::findFirst([
            'conditions' => 'id = :interview_********:',
            'bind'       => [
                'interview_********' => $Id,
            ],
        ]);
        if (empty($hr_interview_offer)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('8402'));
        }
        $resume_data = (new ResumeServer())->getResumeBaseInfo($hr_interview_offer->resume_id);
        if (empty($resume_data)){
            return $this->checkReturn(-3, $this->getTranslation()->_('resume_err_1'));
        }
        
        
        if ($hr_interview_offer->status != 1 && $hcData = $this->hc->checkHc(['hc_id' => $paramIn['hc_id']])) {
            if ($hcData['state_code'] == 3 || $hcData['surplusnumber'] == 0) {
                return $this->checkReturn(-3, $this->getTranslation()->_('11027'));
            }
            if ($hcData['state_code'] == 4) {
                return $this->checkReturn(-3, $this->getTranslation()->_('11028'));
            }
            if ($hcData['state_code'] == 9) {
                return $this->checkReturn(-3, $this->getTranslation()->_('11031'));
            }
        }
        //校验黑名单
        if (isCountry('TH')){
            $black_grey_list = (new BlackgreylistServer())->check_black_grey_list([
                "resume_id" => $paramIn['resume_id'],
            ]);
            if ($black_grey_list["is_black_list"]){
                return $this->checkReturn(-3, $this->getTranslation()->_('7501'));
            }
        }else{
            $isBalcklist = (new BlacklistRepository())->blacklistCheck([
                "resume_id" => $paramIn['resume_id'],
            ]);
            if ($isBalcklist > 0) {
                return $this->checkReturn(-3, $this->getTranslation()->_('7501'));
            }
        }

        (new OutsourcingBlackListServer())->check($resume_data['credentials_num'] ?? '','winhr',true,$this->lang);

        //校验 预计到港日期
        $add_hour  = $this->getDI()['config']['application']['add_hour'];
        $_now_date = gmdate('Y-m-d', time() + $add_hour * 3600);
        if (empty($paramIn['work_time']) || date("Y-m-d", strtotime($paramIn['work_time'])) < $_now_date) {
            return $this->checkReturn(-3, $this->getTranslation()->_('4428'));
        }

        $dbcon = $this->getDI()->get('db');
        $dbcon->begin();
        try {
            //参数
            $param = [
                'interview_id'         => $paramIn['interview_id'],
                'resume_id'            => $paramIn['resume_id'],
                'hc_id'                => $paramIn['hc_id'],
                'currency'             => $currencyId,
                'position_id'          => $position_id,
                'other'                => $other,
                'staff_job_type'       => $staff_job_type,
                'hire_type'            => $hire_type,
                'position'             => $position,
                'work_time'            => $paramIn['work_time'],
                'shift_id'             => $shift_id,
                'job_title_grade'      => $paramIn['job_title_grade'],
                'recommended'          => $recommended,
                'work_days'            => $work_days,
                'money'                => $money['money'],
                'trial_salary'         => $trial_salary['money'],
                'basic_salary'         => $money['basic_salary'],
                'welfare_allowance'    => $money['welfare_allowance'],
                'variable_allowance'   => $money['variable_allowance'],
                'food'                 => $money['food_allowance'],
                'attendance_allowance' => $money['attendance_allowance'],
                'transport_allowance'  => $money['transport_allowance'],
                'renting'              => $money['house_rental'],
                'xinghuo_allowance'    => $money['xinghuo_allowance'],
                'language_allowance'   => $money['language_allowance'],
                'is_computer_allowance'=> $money['is_computer_allowance'] ?? 0,
                'is_camera_allowance'  => $money['is_camera_allowance'] ?? 0,
            ];

            $dbcon->updateAsDict('hr_interview_offer', $param, [
                'conditions' => " id = " . $Id,
            ]);

            $trialParam = [
                'money'                => $trial_salary['money'],
                'basic_salary'         => $trial_salary['basic_salary'],
                'welfare_allowance'    => $trial_salary['welfare_allowance'],
                'variable_allowance'   => $trial_salary['variable_allowance'],
                'food_allowance'       => $trial_salary['food_allowance'],
                'attendance_allowance' => $trial_salary['attendance_allowance'],
                'transport_allowance'  => $trial_salary['transport_allowance'],
                'house_rental'         => $trial_salary['house_rental'],
                'xinghuo_allowance'    => $trial_salary['xinghuo_allowance'],
                'language_allowance'   => $trial_salary['language_allowance'],
                'is_computer_allowance'=> $trial_salary['is_computer_allowance'] ?? 0,
                'is_camera_allowance'  => $trial_salary['is_camera_allowance'] ?? 0,
            ];
            $dbcon->updateAsDict('offer_send_salary', $trialParam, [
                'conditions' => " ******** = " . $Id,
            ]);

            //记录薪资修改记录
            (new OfferServer())->addOfferSalaryOperatorLog([
                'interview_********' => $Id,
                'money'              => $money,
                'trial_salary'       => $trial_salary,
            ]);

            $dbcon->commit();
            //添加日志
            $this->log->addLog([
                'module_id'   => $Id,
                'module_type' => enums::$log_module_type['offer'],
                'action'      => enums::$log_option['modify'],
                'data_after'  => $param,
            ]);
        } catch (\Exception $e) {
            $dbcon->rollback();
            $this->getDI()->get('logger')->write_log('编辑offer失败: Msg '.$e->getMessage()
                .$e->getTraceAsString(), 'notice');
            return $this->checkReturn(-3, $this->getTranslation()->_('4008'));
        }

        return $this->checkReturn(1);
    }

    public function getCompanyId($company_name)
    {
        return 0;
    }


    /**
     * 编辑offer
     * @param array $paramIn
     * @return array
     */
    public function offerEdit($paramIn = [])
    {
        $this->offer = new OfferRepository();
        $this->hc    = new HcRepository();
        $this->log   = new LogServer();

        //[1]参数定义
        $Id                          = $this->processingDefault($paramIn, 'id', 2, 1);
        $currencyId                  = $this->processingDefault($paramIn, 'currency', 2, 1);
        $other                       = $this->processingDefault($paramIn, 'other', 1);
        $staff_job_type              = $this->processingDefault($paramIn, 'staff_job_type', 2);
        $hire_type                   = $this->processingDefault($paramIn, 'hire_type', 2);
        $exp                         = $this->processingDefault($paramIn, 'exp', 2);
        $food                        = $this->processingDefault($paramIn, 'food', 2);
        $position                    = $this->processingDefault($paramIn, 'position', 2);
        $rental                      = $this->processingDefault($paramIn, 'rental', 2);
        $fuel                        = $this->processingDefault($paramIn, 'fuel', 2);
        $computer                    = $this->processingDefault($paramIn, 'computer', 2);
        $renting                     = $this->processingDefault($paramIn, 'renting', 2);
        $dangerously                 = $this->processingDefault($paramIn, 'dangerously', 2);
        $position_id                 = $this->processingDefault($paramIn, 'position_id', 2);
        $gasoline_allowance          = $this->processingDefault($paramIn, 'gasoline_allowance', 2);
        $island_allowance            = $this->processingDefault($paramIn, 'island_allowance', 2);
        $performance_allowance       = $this->processingDefault($paramIn, 'performance_allowance', 2);
        $deminimis_benefits          = $this->processingDefault($paramIn, 'deminimis_benefits', 2);
        $other_non_taxable_allowance = $this->processingDefault($paramIn, 'other_non_taxable_allowance', 2);
        $other_taxable_allowance     = $this->processingDefault($paramIn, 'other_taxable_allowance', 2);
        $sales_travelling_allowance  = $this->processingDefault($paramIn, 'sales_travelling_allowance', 2);
        $recommended                 = $this->processingDefault($paramIn, 'recommended', 2);
        $phone_subsidy               = $this->processingDefault($paramIn, 'phone_subsidy', 2);
        $subsidyType                 = $this->processingDefault($paramIn, 'subsidy_type', 2, 0);

        $shift_id = $this->processingDefault($paramIn, 'shift_id', 2);

        $hr_interview_offer = HrInterviewOfferModel::findFirst([
            'conditions' => 'id = :interview_********:',
            'bind'       => [
                'interview_********' => $Id,
            ],
        ]);
        if (empty($hr_interview_offer)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('8402'));
        }
        $resume_data = (new ResumeServer())->getResumeBaseInfo($hr_interview_offer->resume_id);
        if (empty($resume_data)){
            return $this->checkReturn(-3, $this->getTranslation()->_('resume_err_1'));
        }

        //已发offer  不需HC作废、招满、过期
        /* 校验hc */
        if ($hr_interview_offer->status != 1 && $hcData = $this->hc->checkHc(['hc_id' => $paramIn['hc_id']])) {
            if ($hcData['state_code'] == 3 || $hcData['surplusnumber'] == 0) {
                return $this->checkReturn(-3, $this->getTranslation()->_('11027'));
            }
            if ($hcData['state_code'] == 4) {
                return $this->checkReturn(-3, $this->getTranslation()->_('11028'));
            }
            if ($hcData['state_code'] == 9) {
                return $this->checkReturn(-3, $this->getTranslation()->_('11031'));
            }
        }

        //校验黑名单
        if (isCountry('TH')){
            $black_grey_list = (new BlackgreylistServer())->check_black_grey_list([
                "resume_id" => $paramIn['resume_id'],
            ]);
            if ($black_grey_list["is_black_list"]){
                return $this->checkReturn(-3, $this->getTranslation()->_('7501'));
            }
        }else{
            $isBalcklist = (new BlacklistRepository())->blacklistCheck([
                "resume_id" => $paramIn['resume_id'],
            ]);
            if ($isBalcklist > 0) {
                return $this->checkReturn(-3, $this->getTranslation()->_('7501'));
            }
        }
        (new OutsourcingBlackListServer())->check($resume_data['credentials_num'],'winhr',true,$this->lang);


        //获取offer指定职位ids有设备费 需求：当入职职位为“Bike Courier”或“Van Courier”时，才展示需扣除设备费字段，其余职位不体现
//        $equipmentCostPositionIds    = env("equipmentCostPositionIds", "{}");
//        $equipmentCostPositionIdsArr = json_decode($equipmentCostPositionIds, true);
//        if (!$position_id || !in_array($position_id, array_keys($equipmentCostPositionIdsArr))) {
//            $equipment_cost = 0;
//        } else {
//            $equipment_cost = $equipmentCostPositionIdsArr[$position_id];
//
//            //新币去汇率
//            if (isCountry('Th') && $currencyId == HrInterviewOfferModel::CURRENCY_SGD) {
//                $equipment_cost = intval($equipment_cost/25/100) * 100;
//            }
//        }

        //校验 预计到港日期
        $add_hour  = $this->getDI()['config']['application']['add_hour'];
        $_now_date = gmdate('Y-m-d', time() + $add_hour * 3600);
        if (empty($paramIn['work_time']) || date("Y-m-d", strtotime($paramIn['work_time'])) < $_now_date) {
            return $this->checkReturn(-3, $this->getTranslation()->_('4428'));
        }


        //校验班次id是否正确
        /*
        $shift_list = (new SysListRepository())->getAllShiftList();
        $shift_list = array_column($shift_list, null, 'id');
        if(!isset($shift_list[$shift_id])) {
            return $this->checkReturn(-3, 'shift ID error');
        }
        */

        //参数

        $param = [
            'id'                          => $Id,
            'interview_id'                => $paramIn['interview_id'],
            'resume_id'                   => $paramIn['resume_id'],
            'hc_id'                       => $paramIn['hc_id'],
            'currency'                    => $currencyId,
            'subsidy_type'                => $subsidyType,
            'money'                       => $paramIn['money'] ?? 0,
            'internship_salary'           => $paramIn['internship_salary'] ?? 0,
            'position_id'                 => $position_id,
            'other'                       => $other,
            'exp'                         => $exp,
            'staff_job_type'              => $staff_job_type,
            'hire_type'                   => $hire_type,
            'food'                        => $food,
            'position'                    => $position,
            'rental'                      => $rental,
//            'equipment_cost'              => $equipment_cost,
            'fuel'                        => $fuel,
            'computer'                    => $computer,
            'renting'                     => $renting,
            'dangerously'                 => $dangerously,
            'work_time'                   => $paramIn['work_time'],
            // 'train_time'     => $paramIn['train_time'],
            // 'train_place'    => $train_place,
            'gasoline_allowance'          => $gasoline_allowance,
            'island_allowance'            => $island_allowance,
            'shift_id'                    => $shift_id,
            'job_title_grade'             => $paramIn['job_title_grade'],
            'performance_allowance'       => $performance_allowance,
            'deminimis_benefits'          => $deminimis_benefits,
            'other_non_taxable_allowance' => $other_non_taxable_allowance,
            'other_taxable_allowance'     => $other_taxable_allowance,
            'recommended'                 => $recommended,
            'phone_subsidy'               => $phone_subsidy,
        ];
        //gdl补贴
        if (isset($paramIn['gdl_allowance'])) {
            $param['gdl_allowance'] = intval($paramIn['gdl_allowance']);
        }

        //马来-区域补贴
        if (isset($paramIn['site_allowance'])) {
            $param['site_allowance'] = intval($paramIn['site_allowance']);
        }

        // 手机津贴
        if (isset($paramIn['mobile_allowance'])) {
            $param['mobile_allowance'] = intval($paramIn['mobile_allowance']);
        }
        // 出勤津贴
        if (isset($paramIn['attendance_allowance'])) {
            $param['attendance_allowance'] = intval($paramIn['attendance_allowance']);
        }
        //  油补津贴
        if (isset($paramIn['fuel_allowance'])) {
            $param['fuel_allowance'] = intval($paramIn['fuel_allowance']);
        }
        // 私家车车补津贴
        if (isset($paramIn['car_allowance'])) {
            $param['car_allowance'] = intval($paramIn['car_allowance']);
        }
        //  货车车补津贴
        if (isset($paramIn['vehicle_allowance'])) {
            $param['vehicle_allowance'] = intval($paramIn['vehicle_allowance']);
        }

        if (isset($paramIn['vehicle_allowance'])) {
            $param['vehicle_allowance'] = intval($paramIn['vehicle_allowance']);
        }
        if (isset($paramIn['xinghuo_allowance'])) {
            $param['xinghuo_allowance'] = intval($paramIn['xinghuo_allowance']);
        }

        if (isset($paramIn['trial_salary'])) {
            $param['trial_salary'] = intval($paramIn['trial_salary']);
        }
        //销售差旅津贴
        if (isset($paramIn['sales_travelling_allowance'])) {
            $param['sales_travelling_allowance'] = intval($paramIn['sales_travelling_allowance']);
        }

        //实习期开始时间
        if (isset($paramIn['internship_start'])) {
            $param['internship_start'] = $paramIn['internship_start'];
        }

        //实习结束时间
        if (isset($paramIn['internship_end'])) {
            $param['internship_end'] = $paramIn['internship_end'];
        }

        //公司名称
        if (isset($paramIn['company'])) {
            $param['company'] = $paramIn['company'];
            $param['company_id'] = $this->getCompanyId($paramIn['company']);
        }


        //项目期数
        if (isCountry()) {

            $param['project_num'] = !empty($paramIn['project_num']) ? $paramIn['project_num'] : null;
        }

        $success = $this->offer->offerEdit($param);

        if (!$success) {
            return $this->checkReturn(-3, $this->getTranslation()->_('4701'));
        }

        //添加日志
        $this->log->addLog([
            'module_id'   => $Id,
            'module_type' => enums::$log_module_type['offer'],
            'action'      => enums::$log_option['modify'],
            'data_after'  => $param,
        ]);

        (new ResumeServer())->setLastOperator($paramIn['resume_id']);


        return $this->checkReturn(1);
    }

    /**
     * hr_interview 状态更新
     * @param array $paramIn ['id'=>4,'state'=>25]
     * @return array
     */
    public function updateInterviewStatus($paramIn = [])
    {
        $this->interview = new InterviewRepository();

        //[1]参数定义
        $Id = $this->processingDefault($paramIn, 'id', 2);

        //[2]校验是否存在
        $update_param = [
            'state'      => $paramIn['state'],
            'updated_at' => gmdate('Y-m-d H:i:s', time()),
        ];

        if (isset($paramIn['send_time']) && $paramIn['state'] == 25) {
            $update_param['send_time'] = $paramIn['send_time'];
        }

        $validations = [
            'state' => "Required|IntIn:1,5,10,20,25,30,31,32",
        ];
        $this->validateCheck($update_param, $validations);

        $success = $this->getDI()->get('db')->updateAsDict('hr_interview', $update_param, [
            'conditions' => " interview_id = $Id ",
        ]);

        return $success;
    }

    public function getShiftInfo($shift_id): array
    {
        $shift_list = (new SysListRepository())->getAllShiftList();
        $shift_list = array_column($shift_list, null, 'id');
        if (isset($shift_list[$shift_id])) {
            $shift_type                    = $shift_list[$shift_id]['type'] ?? '';
            $returnData['shift_type']      = $shift_type;
            $returnData['shift_type_text'] = empty($shift_type) ? '' : $this->getTranslation()->_('shift_'.$shift_type);
            $returnData['shift_text']      = ($shift_list[$shift_id]['start'] ?? '').'-'.($shift_list[$shift_id]['end'] ?? '');
        } else {
            $returnData['shift_type']      = '';
            $returnData['shift_type_text'] = '';
            $returnData['shift_text']      = '';
        }
        return $returnData;
    }


    public function infoInterviewOffer($paramIn, $userinfo)
    {
        $this->offer   = new OfferRepository();
        $this->sysList = new SysListRepository();
        $this->log     = new LogServer();

        //[2]校验是否存在
        $validations = [
            'id' => "Required|Int",
        ];
        $this->validateCheck($paramIn, $validations);

        $dataList             = $this->offer->infoInterviewOffer($paramIn);

        $offerStaffJobType    = (new SysListServer())->getOfferStaffJobType();
        $offerStaffJobTypeArr = array_column($offerStaffJobType, "name", "key");
        if ($dataList) {

            //修改offer权限
            $editOfferDataStaffIds = SettingEnvServer::getSetValToArray('edit_offer_data_staff_ids');
            $is_has_permission = ($userinfo['salary_structure_permission'] == enums::$salary_structure_permission['on'] || ($dataList['submitter_id'] && $dataList['submitter_id'] == $userinfo['id'])) || in_array($userinfo['id'],$editOfferDataStaffIds);

            $shift_info = $this->getShiftInfo($dataList['shift_id']);
            $dataList = array_merge($dataList,$shift_info);
            /* 获取部门列表 */
            $departmentDataList = $this->sysList->getDepartmentList();
            $departmentDataList = array_column($departmentDataList, 'name', 'id');

            $dataList['department_name']     = $departmentDataList[$dataList['department_id']];
            $dataList['staff_job_type_name'] = isset($offerStaffJobTypeArr[$dataList['staff_job_type']]) ? $offerStaffJobTypeArr[$dataList['staff_job_type']] : '';
            $dataList['hire_type_text']      = $dataList['hire_type'] ? $this->getTranslation()->_('hire_type_'.$dataList['hire_type']) : '';

            $dataList['job_title_name']       = !empty($dataList['job_title_id']) ? sprintf("(%s) %s", $dataList['job_title_id'], $dataList['job_title_name']) : '';
            $dataList['job_title_grade']      = $dataList['job_title_grade'];
            $dataList['in_salary_range_text'] = $dataList['in_salary_range'] && isset($dataList['in_salary_range']) ? $this->getTranslation()->_('qa3002'.$dataList['in_salary_range']) : '';
            $dataList['is_express_line']      = $this->isExpressLine($dataList['department_id']) ? 1 : 0;
            $dataList['project_num']          = !empty($dataList['project_num']) ? $dataList['project_num'] : null;

            if ($dataList['job_title_id'] == enumsTh::JOB_TITLE_VAN_COURIER_PROJECT) {
                $dataList['project_num_text']     = !empty($dataList['project_num']) ? $this->getTranslation()->_(HrInterviewOfferModel::$van_courier_project_num_list[$dataList['project_num']]) : '';
            } else {
                $dataList['project_num_text']     = !empty($dataList['project_num']) ? $this->getTranslation()->_(HrInterviewOfferModel::$project_num_list[$dataList['project_num']]) : '';
            }

            //offer页面薪资查看权限
            $dataList['salary_view_permission'] = $is_has_permission ? 1 : 0;

            // 字段 null =》 0
            $dataList['gdl_allowance']     = $dataList['gdl_allowance'] ?? 0;
            $dataList['xinghuo_allowance'] = $dataList['xinghuo_allowance'] ?? 0;
            $dataList['site_allowance']    = $dataList['site_allowance'] ?? 0;
            $dataList['internship_salary'] = $dataList['internship_salary'] ?? 0;

            $paramLog                         = [
                'module_id'   => $dataList['interview_id'],
                'module_type' => 2,
            ];
            $historyData                      = $this->log->infoLog($paramLog);
            $dataList['infoInterviewHistory'] = $historyData;
            //试用期薪资根据权限判断是否隐藏
            //1 代表 没有权限

            $dataList['salary_items'] = null;
            if (isCountry('Id')) {
                $offerSendSalary =
                    OfferSendSalaryModel::findFirst([
                        'conditions' => ' ******** = :********:',
                        'bind'       => ['********' => $dataList['id']],
                    ]);
                if ($offerSendSalary) {
                    $offerSendSalary = $offerSendSalary->toArray();

                    $money = [
                        'money'                => $dataList['money'],
                        'basic_salary'         => $dataList['basic_salary'],
                        'welfare_allowance'    => $dataList['welfare_allowance'],
                        'variable_allowance'   => $dataList['variable_allowance'],
                        'food_allowance'       => $dataList['food'],
                        'attendance_allowance' => $dataList['attendance_allowance'],
                        'transport_allowance'  => $dataList['transport_allowance'],
                        'house_rental'         => $dataList['renting'],
                        'xinghuo_allowance'    => $dataList['xinghuo_allowance'],
                        'language_allowance'   => $dataList['language_allowance'],
                    ];

                    $trial_salary = [
                        'money'                => $offerSendSalary['money'],
                        'basic_salary'         => $offerSendSalary['basic_salary'],
                        'welfare_allowance'    => $offerSendSalary['welfare_allowance'],
                        'variable_allowance'   => $offerSendSalary['variable_allowance'],
                        'food_allowance'       => $offerSendSalary['food_allowance'],
                        'attendance_allowance' => $offerSendSalary['attendance_allowance'],
                        'transport_allowance'  => $offerSendSalary['transport_allowance'],
                        'house_rental'         => $offerSendSalary['house_rental'],
                        'xinghuo_allowance'    => $offerSendSalary['xinghuo_allowance'],
                        'language_allowance'   => $offerSendSalary['language_allowance'],
                    ];

                    $dataList['salary_items'] = [
                        'money'        => $money,
                        'trial_salary' => $trial_salary,
                    ];
                }
            }


            if ($dataList['submitter_id'] != $userinfo['id']) {
                // 不是发送offer的人
                if (
                    $dataList['salary_view_permission'] == 0
                ) {
                    $dataList['money']                       = '*****';//试用期工资
                    $dataList['trial_salary']                = '*****';//通过试用期工资
                    $dataList['position']                    = '*****';//职位津贴
                    $dataList['xinghuo_allowance']           = '*****';//星火激励津贴
                    $dataList['food']                        = '*****';//餐补
                    $dataList['rental']                      = '*****';//租车津贴
                    $dataList['renting']                     = '*****';//租房津贴
                    $dataList['dangerously']                 = '*****';//危险区域津贴
//                    $dataList['equipment_cost']              = '*****';//设备费
                    $dataList['computer']                    = '*****';//电脑补贴
                    $dataList['exp']                         = '*****';//经验津贴
                    $dataList['gasoline_allowance']          = '*****';//油费补贴
                    $dataList['island_allowance']            = '*****';//海岛补贴
                    $dataList['deminimis_benefits']          = '*****';//无税金
                    $dataList['performance_allowance']       = '*****';//绩效补贴
                    $dataList['recommended']                 = '*****';//推荐补贴
                    $dataList['other_taxable_allowance']     = '*****';//其他纳税
                    $dataList['other_non_taxable_allowance'] = '*****';//其他无税金补贴
                    $dataList['phone_subsidy']               = '*****';//电话补贴
                    $dataList['gdl_allowance']               = '*****';//gdl补贴
                    $dataList['mobile_allowance']            = '*****';//手机津贴
                    $dataList['attendance_allowance']        = '*****';//出勤津贴
                    $dataList['fuel_allowance']              = '*****';//g油补津贴
                    $dataList['car_allowance']               = '*****';//私家车车补津贴
                    $dataList['vehicle_allowance']           = '*****';//货车车补津贴
                    $dataList['site_allowance']              = '*****';//货车车补津贴
                    $dataList['internship_salary']           = '*****';//实习期工资
                }
            }
        }

        //添加is_th_fulfillment_internship
        $dataList['is_th_fulfillment_internship'] = 0;
        if (isCountry('TH')) {
            $dataList['is_th_fulfillment_internship'] = (new TemplateServer())->checkThFulfillmentInternship($dataList);
        }

        //查询操作日志
        $dataList['operator_salary_log'] = (new OfferServer())->getOfferSalaryOperatorLogList($dataList['id'], $dataList['resume_id'],['is_th_fulfillment_internship' => $dataList['is_th_fulfillment_internship']]);

        $data['dataList']                = $dataList;

        return $this->checkReturn(['data' => $data]);
    }

    /**
     * 面试官列表
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function interviewerList($paramIn = [])
    {
        $interviewer       = (new InterviewRepository())->getInterviewerList();
        $returnArr['data'] = $interviewer;
        return $this->checkReturn($returnArr);
    }

    /**
     * 简历提供人列表
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function supplyerList($paramIn = [])
    {
        $suppler           = (new InterviewRepository())->getResumeSupplyList();
        $returnArr['data'] = $suppler;
        return $this->checkReturn($returnArr);
    }

    /**
     * 直线上级默认逻辑
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function comboboxDefault($paramIn = [])
    {
        // 面试管理-直线上级下拉框 默认值
        $returnArr['data']['sys_store_manager_id'] = '';
        $store_id  = $paramIn['store_id'] ?? '';
        if ($store_id) {
            $store_info = (new SysStoreModel())->getSysStoreInfo($store_id);
            $manager_id = $store_info['manager_id'] ?? '';
            if ($manager_id) {
                $staff_info = (new HrStaffInfoModel())->getHrStaffInfo($manager_id);
                if ($staff_info && in_array($staff_info['state'],[HrStaffInfoModel::STAFF_STATE_IN])){
                    $returnArr['data']['sys_store_manager_id'] = $manager_id;
                }
            }
        }
        return $returnArr;
    }


    /**
     * [recordFeedback 面试反馈]
     * @param array $paramIn
     * @return array|null [type] [array]
     * @throws BusinessException
     */
    public function recordFeedback($paramIn = [])
    {
        $this->offer     = new OfferRepository();
        $this->hc        = new HcRepository();
        $this->blacklist = new BlacklistRepository();
        $this->log       = new LogServer();

        $state        = $this->processingDefault($paramIn, 'state', 2);
        $is_blacklist = $this->processingDefault($paramIn, 'is_blacklist', 2, 0);
        $out_reason   = $this->processingDefault($paramIn, 'out_reason', 1, '');
        $is_last      = $this->processingDefault($paramIn, 'is_last', 2, 0);
        unset($paramIn['is_blacklist']);
        // 验证
        if ($this->verification($paramIn)) {
            return $this->verification($paramIn);
        }

        // 验证
        if (empty($paramIn)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('4108'));
        }

        $_data = (new InterviewRepository())->getInterview([
            "interview_id" => $paramIn['interview_id'],
        ]);
        if (!$_data) {
            return $this->checkReturn(-3, $this->getTranslation()->_('resume_err_1'));
        }
        $resume_data = (new ResumeServer())->getResumeBaseInfo($_data['resume_id']);
        if (empty($resume_data)){
            return $this->checkReturn(-3, $this->getTranslation()->_('resume_err_1'));
        }
        
        //校验黑名单
        if (isCountry('TH')){
            $black_grey_list = (new BlackgreylistServer())->check_black_grey_list([
                "resume_id" => $_data['resume_id'],
            ]);
            if ($black_grey_list["is_black_list"]){
                return $this->checkReturn(-3, $this->getTranslation()->_('7500'));
            }
            (new OutsourcingBlackListServer())->check($resume_data['credentials_num'] ?? '','winhr',true,$this->lang);
        }
        if ($state == 3 && $is_blacklist == 1) {
            //加入黑名单
            if ($_data) {
                $id = $this->blacklist->blacklistAdd([
                    "resume_id" => $_data['resume_id'],
                    "type"      => 5,
                    "remark"    => $out_reason,
                ]);
                if ($id == -1) {
                    return $this->checkReturn(-3, $this->getTranslation()->_('7522'));
                }
            } else {
                return $this->checkReturn(-3, $this->getTranslation()->_('4008'));
            }
        }

        /* 校验hc */
        $hcData = $this->hc->checkHc(['hc_id' => $paramIn['hc_id']]);
        if (in_array($state, [0, 1, 2]) && ($hcData['state_code'] == 3 || $hcData['surplusnumber'] == 0)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('11027'));
        }
        if (in_array($state, [0, 1, 2]) && $hcData['state_code'] == 4) {
            return $this->checkReturn(-3, $this->getTranslation()->_('11028'));
        }
        if (in_array($state, [0, 1, 2]) && $hcData['state_code'] == 9) {
            return $this->checkReturn(-3, $this->getTranslation()->_('11031'));
        }

        //校验offer
        $data = $this->offer->checkOffer([
            'interview_id' => $paramIn['interview_id'],
            'status'       => 1,
        ]);
        if ($data) {
            return $this->checkReturn(-3, $this->getTranslation()->_('4012'));
        }

        // 面试轮次
        $level            = (new InterviewRepository())->selectLevel($paramIn['interview_id']);
        $paramIn['level'] = (int)$level + 1;

        //修改面试状态
        if (!in_array($state, [0, 1, 2, 3])) {//0:直接通过 1:终试通过 2：进入下一轮 3 不通过
            return $this->checkReturn(-3, $this->getTranslation()->_('miss_args'));
        }

        //面试预约表
        $interviewSubscribeModel = HrInterviewSubscribeModel::findFirst([
            'columns'    => 'id',
            'conditions' => 'interview_id = :interview_id:',
            'bind'       => [
                'interview_id' => $paramIn['interview_id'],
            ],
            'order'      => 'id desc',
        ]);

        //找不到记录
        if (!$interviewSubscribeModel) {
            $this->getDI()->get('logger')->write_log("recordFeedback: hr_interview_subscribe 记录不存在 interview_id：" . $paramIn['interview_id'],
                'notice');
            return $this->checkReturn(-3, $this->getTranslation()->_('4703'));
        }

        /*存入反馈信息*/
        $paramIn['operation_id'] = $this->userInfo['id'];
        $recordFeedback          = (new InterviewRepository())->recordFeedback($paramIn);
        // 反馈失败
        if (!isset($recordFeedback) or !$recordFeedback) {
            $this->getDI()->get('logger')->write_log("recordFeedback: 存入反馈信息失败 " . json_encode($paramIn), 'notice');
            return $this->checkReturn(-3, $this->getTranslation()->_('4703'));
        }
        //面试官操作表
        if ($state == 1 or $state == 2) {
            $tmp = 1;
        } elseif ($state == 3) {
            $tmp = 2;
        } else {
            $tmp = 2;
        }
        $sql = "update hr_interviewer_operation set state = {$tmp} where interview_sub_id = {$interviewSubscribeModel->id} and state = 0 and id = {$paramIn['ope_id']}";
        $res = $this->getDI()->get('db')->execute($sql);
        if (!$res) {
            return $this->checkReturn(-3, $this->getTranslation()->_('4703'));
            $this->getDI()->get('logger')->write_log("recordFeedback: 面试反馈修改记录失败 " . $sql, 'notice');
        }

        // 数据返回
        $return['data']['interview_info_id'] = $recordFeedback;

        $action       = enums::$log_option['write'];
        $module_type  = enums::$log_module_type['interview_feedback'];
        $module_level = $paramIn['level'];

        if ($state == '1' && $is_last == 1) {
            //终试通过面试
            $action       = enums::$log_option['final_pass'];
            $module_type  = enums::$log_module_type['interview'];
            $module_level = 0;
            $states       = 0;
        } elseif ($state == '1') {
            $states = enums::$log_status['final_pass'];
        } elseif ($state == '2') {
            $states = enums::$log_status['next_step'];
        } else {
            $states = enums::$log_status['not_pass'];
        }
        if ($states != enums::$log_status['not_pass']) {
            (new ResumeServer())->setLastOperator($_data['resume_id']);
        }
        //添加日志
        //当反馈状态为不通过时，这里不写到hr_log表，而是从淘汰接口写入hr_log表
        if ($state != 3) {
            $this->log->addLog([
                'module_id'     => $paramIn['interview_id'],
                'action'        => $action,
                'module_type'   => $module_type,
                'module_status' => $states,
                'data_after'    => $paramIn,
                'module_level'  => $module_level,
            ]);
        }

        // 添加面试记录
        if (in_array($states,[enums::$log_status['final_pass'],enums::$log_status['next_step']])){
            //通过
            $this->addHrInterviewRecord($paramIn['interview_id'],HrInterviewRecordModel::STATE_PASS);
        }else{
            $this->addHrInterviewRecord($paramIn['interview_id'],HrInterviewRecordModel::STATE_NO_PASS);
        }

        return $this->checkReturn($return);
    }


    /**
     * [updateFeedback 面试反馈修改]
     * @return [type] [array]
     * <AUTHOR> <[email address]>
     */
    public function updateFeedback($paramIn = [])
    {
        $this->offer     = new OfferRepository();
        $this->hc        = new HcRepository();
        $this->blacklist = new BlacklistRepository();
        $this->log       = new LogServer();

        $state        = $this->processingDefault($paramIn, 'state', 2);
        $is_blacklist = $this->processingDefault($paramIn, 'is_blacklist', 2, 0);
        $out_reason   = $this->processingDefault($paramIn, 'out_reason', 1, '');
        unset($paramIn['is_blacklist']);

        // 验证
        if ($this->verification($paramIn)) {
            return $this->verification($paramIn);
        }

        // 验证
        if (empty($paramIn)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('4108'));
        }


        $_data = (new InterviewRepository())->getInterview([
            "interview_id" => $paramIn['interview_id'],
        ]);
        if (!$_data) {
            return $this->checkReturn(-3, $this->getTranslation()->_('resume_err_1'));
        }
        $resume_data = (new ResumeServer())->getResumeBaseInfo($_data['resume_id']);
        if (empty($resume_data)){
            return $this->checkReturn(-3, $this->getTranslation()->_('resume_err_1'));
        }
        //校验黑名单
        if (isCountry('TH')){
            $black_grey_list = (new BlackgreylistServer())->check_black_grey_list([
                "resume_id" => $_data['resume_id'],
            ]);
            if ($black_grey_list["is_black_list"]){
                return $this->checkReturn(-3, $this->getTranslation()->_('7500'));
            }
            (new OutsourcingBlackListServer())->check($resume_data['credentials_num'] ?? '','winhr',true,$this->lang);
        }

        //加入黑名单
        if ($state == 3 && $is_blacklist == 1) {
            if ($_data) {
                $id = $this->blacklist->blacklistAdd([
                    "resume_id" => $_data['resume_id'],
                    "type"      => 5,
                    "remark"    => $out_reason,
                ]);
                if ($id == -1) {
                    return $this->checkReturn(-3, $this->getTranslation()->_('7522'));
                }
            } else {
                return $this->checkReturn(-3, $this->getTranslation()->_('4008'));
            }
        }

        //校验offer
        $data = $this->offer->checkOffer([
            'interview_id' => $paramIn['interview_id'],
            'status'       => 1,
        ]);
        if ($data) {
            return $this->checkReturn(-3, $this->getTranslation()->_('4012'));
        }

        $type = [5, 10, 20, 30, 31];
        /* 校验hc 终试通过才验证 */
        $hcData = $this->hc->checkHc(['hc_id' => $paramIn['hc_id']]);
        if ($state == 1 && !in_array($_data['state'],
                $type) && ($hcData['state_code'] == 3 || $hcData['surplusnumber'] == 0)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('11027'));
        }
        if ($state == 1 && !in_array($_data['state'], $type) && $hcData['state_code'] == 4) {
            return $this->checkReturn(-3, $this->getTranslation()->_('11028'));
        }
        if ($state == 1 && !in_array($_data['state'], $type) && $hcData['state_code'] == 9) {
            return $this->checkReturn(-3, $this->getTranslation()->_('11031'));
        }

        // 面试轮次
        $level = (new InterviewRepository())->selectLevel($paramIn['interview_id']);
        // $paramIn['level'] = (int)$level + 1;

        //获取面试表状态，在更新面试反馈之前，用于更新简历招聘负责人
        $interviewInfo = $this->getInterviewInfo($paramIn['interview_id']);
        $resume_id     = $interviewInfo['resume_id'] ?? 0;
        /*存入信息*/
        $paramIn['operation_id'] = $this->userInfo['id'];

        $updateFeedback = (new InterviewRepository())->updateFeedback($paramIn);

        // 验证
        if (!$updateFeedback) {
            return $this->checkReturn(-3, $this->getTranslation()->_('4703'));
        }
        //修改面试反馈-终试通过
        if ($state == 1) {
            //更换hc自动赋值招聘负责人为当前用户
            $interview_state     = $interviewInfo['state'] ?? 0;
            $resume_filter_state = 0;
            (new ResumeServer())->updateResumeRecruiter($resume_id, $this->userInfo['id'],
                $this->userInfo['node_department_id'], $resume_filter_state, $interview_state, 5);
        }

        if ($state == '1') {//终试通过
            $states = enums::$log_status['final_pass'];
        } elseif ($state == '2') { //仅修改面试反馈选项，目前该选项操作会自动进行下一轮预约面试弹框并强制执行预约面试（调用），所以也会触发自动赋值招聘负责人
            $states = enums::$log_status['next_step'];
        } else { //
            $states = enums::$log_status['not_pass'];
        }

        //添加日志
        //当反馈状态为不通过时，这里不写到hr_log表，而是从淘汰接口写入hr_log表
        if ($state != 3) {
            (new ResumeServer())->setLastOperator($_data['resume_id']);
            $this->log->addLog([
                'module_id'     => $paramIn['interview_id'],
                'module_type'   => enums::$log_module_type['interview_feedback'],
                'module_level'  => $level,
                'action'        => enums::$log_option['feedback'],
                'module_status' => $states,
                'data_after'    => $paramIn,
            ]);
        }
        // 如果状态等于终试通过或进入下一步时 需要修改面试不通过记录都已删除
        if (in_array($state, [1, 2])) {
            $db = $this->getDI()->get('db');
            $db->updateAsDict('hr_interview_pass_type_record', ["deleted" => 1], [
                'conditions' => "resume_id = $resume_id ",
            ]);
        }

        // 添加面试记录
        if (in_array($states,[enums::$log_status['final_pass'],enums::$log_status['next_step']])){
            // 通过
            $this->addHrInterviewRecord($paramIn['interview_id'],HrInterviewRecordModel::STATE_PASS);
        }else{
            $this->addHrInterviewRecord($paramIn['interview_id'],HrInterviewRecordModel::STATE_NO_PASS);
        }


        return $this->checkReturn(1);
    }


    /**
     * [feedbackList 面试反馈查看]
     * @return [type] [array]
     * <AUTHOR> <[email address]>
     */
    public function feedbackList($paramIn = [])
    {
        // 验证
        if (empty($paramIn)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('4104'));
        }
        /*查询信息*/
        $feedbackList = (new InterviewRepository())->feedbackList($paramIn);

        // 验证
        if (!$feedbackList) {
            return $this->checkReturn(-3, $this->getTranslation()->_('4104'));
        }
        if (!empty($feedbackList['working_day_rest_type'])) {
            $feedbackList['working_day_rest_type_text'] = $this->getTranslation()->_('working_day_rest_type_' . $feedbackList['working_day_rest_type']);
        } else {
            $feedbackList['working_day_rest_type']      = '';
            $feedbackList['working_day_rest_type_text'] = '';
        }


        $feedbackList['log'] = (new InterviewRepository())->getFeedbackLog($feedbackList['interview_info_id']);

        // 数据返回
        $return['data'] = $feedbackList;
        return $this->checkReturn($return);
    }


    /**
     * [verification 简单验证]
     * @return [type] [array]
     * <AUTHOR> <[email address]>
     */
    public function verification($paramIn = [])
    {
        // 评价内容
        if (isset($paramIn['evaluate']) && strlen($paramIn['evaluate']) > 900) {
            return $this->checkReturn(-3, $this->getTranslation()->_('4421'));
        }
        // 币种 1泰币 2人民币
        if (isset($paramIn['currency']) && !in_array($paramIn['currency'], [
                '1',
                '2',
            ])
        ) {
            return $this->checkReturn(-3, $this->getTranslation()->_('4422'));
        }
        // 建议薪资
        if (isset($paramIn['money']) && strlen($paramIn['money']) > 12) {
            return $this->checkReturn(-3, $this->getTranslation()->_('4423'));
        }
    }


    /**
     * 职位列表
     * @Access  public
     * @Param   request
     * @Return  array
     */
    public function positionList($paramIn = [])
    {
        $this->sysList = new SysListRepository();

        $name              = $this->processingDefault($paramIn, 'name');
        $departmentId      = $this->processingDefault($paramIn, 'department_id', 2, null);
        $interviewer       = $this->sysList->getPositionList(['name' => $name, 'department_id' => $departmentId]);
        $returnArr['data'] = $interviewer;
        return $this->checkReturn($returnArr);
    }

    public function screenedList($paramIn = [])
    {
        $Id                = $this->processingDefault($paramIn, 'id', 2);
        $interviewer       = (new InterviewRepository())->screenedList();
        $returnArr['data'] = $interviewer;
        return $this->checkReturn($returnArr);
    }

    /**
     * 获取offer列表
     */
    public function listInterviewOffer($paramIn = [])
    {
        $this->sysList = new SysListRepository();

        //[1]获取参数
        $pageSize = $this->processingDefault($paramIn, 'page_size', 2, 10);
        $pageNum  = $this->processingDefault($paramIn, 'page_num', 2, 1);
        $sort     = $this->processingDefault($paramIn, 'sort', 1);
        $staffId  = $this->processingDefault($paramIn, 'staff_id', 2);

        $staffInfo = \FlashExpress\bi\App\Models\backyard\HrStaffInfoModel::findFirst([
            'conditions' => 'staff_info_id = :staffId:',
            'bind'       => ['staffId' => $staffId],
            'columns'    => 'job_title_grade_v2, sys_store_id',
        ]);

        $staffInfo = $staffInfo ? $staffInfo->toArray() : [];

        [$deptIds, $storeIds] = $this->getStaffCheckData($staffId);
        if ($staffInfo['sys_store_id'] != -1) {//不是总部网点，则查自己所在网点
            $storeIds[] = $staffInfo['sys_store_id'];
        }

        //[3]搜索列表结果
        $searchCondition = [
            'dept_ids'     => $deptIds,
            'worknode_ids' => $storeIds,
            'status'       => $paramIn['status'],
            'staff_id'     => $staffId,
            'page_size'    => $pageSize,
            'page_num'     => $pageNum,
            'sort'         => $sort,
        ];
        $returnData      = (new InterviewRepository())->gertInterviewOfferList($searchCondition);

        $dataList = $returnData['data'];

        //获取职位列表
        $positionList = $this->sysList->getPositionList();
        $positionList = array_column($positionList, 'job_name', 'id');

        foreach ($dataList as $k => $v) {
            if (isCountry('Ph')) {

                $dataList[$k]['name'] = $v['name'];
            } else {
                $dataList[$k]['name'] = $v['first_name'] . " " . $v['last_name'];
            }
            $dataList[$k]['position_name'] = $positionList[$v['position_id']];
            $isCheck                       = false;
            if (!empty($v['record_entry'])) {
                $record_entry = json_decode($v['record_entry'], true);
                //职级小于等于当前登录人的待入职员工才可以被查看。
                if ($staffInfo['job_title_grade_v2'] != '' && $record_entry['job_title_grade_v2'] != '' && $record_entry['job_title_grade_v2'] <= $staffInfo['job_title_grade_v2']) {
                    $isCheck = true;
                }
                // 个人代理没有职级，也可以看见
                if (in_array($record_entry['hire_type'],HrStaffInfoModel::$agentTypeTogether ) && $record_entry['job_title_grade_v2'] == '') {
                    $isCheck = true;
                }
            }
            $dataList[$k]['work_time'] = $v['entry_date'] == '0000-00-00 00:00:00' || empty($v['entry_date']) ? $v['work_time'] : $v['entry_date'];
            $dataList[$k]['is_check']  = $isCheck;
            //为1 展示：身份证照片与本人不一致，需待TA处理后再操作
            $dataList[$k]['identity_personal_verification']  = $v['identity_validate_status'] == HrResumeModel::IDENTITY_VALIDATE_STATUS_FAIL ? 1 : 0;
            unset($dataList[$k]['record_entry']);
        }


        $returnArr['data']['dataList']   = $dataList;
        $returnArr['data']['pagination'] = $returnData['pagination'];
        return $this->checkReturn($returnArr);
    }

    public function getStaffCheckData($staffId)
    {
        $deptIds  = (new DepartmentRepository())->getStaffOrganizationInfo($staffId);
        $storeIds = (new SysStoreServer())->getStoreByManager($staffId);
        return [$deptIds, $storeIds];
    }

    /**
     * 对于设置过网点的区域经理 或 网点主管，会过滤状态。
     * @param $data
     * @return array
     */
    public function filterStateData($data)
    {
        $flag = false;

        //如果我设置过网点，就需要过滤
        $store_id = $this->getMySetStoreIds();
        if (!empty($store_id)) {
            $flag = true;
        } else {
            //如果是网点主管
            $cur_pos = $this->userInfo['position_category'];
            $tArr    = explode(",", $cur_pos);
            if (in_array(HcServer::ROLE_DOT_ADMIN, $tArr)) {
                $flag = true;
            }
        }

        if ($flag) {
            $t    = [];
            $tArr = [1, 5, 30, 31];
            foreach ($data as $k => $v) {
                if (in_array($v['state'], $tArr)) {
                    $t[] = $v;
                }
            }
            return $t;
        } else {
            return $data;
        }
    }

    /**
     * 获取班次列表
     *
     *
     * @param $job_title_id
     * @return mixed
     */
    public function getAllShift($department_id, $job_title_id, $store_id)
    {
        try {
            $param = ['department_id' => $department_id, 'job_title_id' => $job_title_id, 'store_id' => $store_id];
            $this->getDI()->get('logger')->write_log("getAllShift params:".json_encode($param), 'info');
            $ac = new ApiClient('hr_rpc', '', 'get_staff_config_shift', $this->lang);
            $ac->setParams([
                'department_id' => $department_id,
                'job_title_id'  => $job_title_id,
                'store_id'      => $store_id,
            ]);
            $data = $ac->execute();
            $this->getDI()->get('logger')->write_log("getAllShift response:".json_encode($data), 'info');
            $returnArr['data'] = $data['result'];
            foreach ($returnArr['data'] as $key => $value) {
                foreach ($value as $k => $v) {
                    $returnArr['data'][$key][$k]['markup'] = $v['start'].'-'.$v['end'];
                }
            }
        } catch (\Exception $e) {
            $returnArr['data'] = [];
            $this->getDI()->get('logger')->write_log("getAllShift 异常信息:" . $e->getMessage() . $e->getTraceAsString(),
                'info');
        }

        if (empty($returnArr['data'])) {
            return $this->checkReturn(-3, '班次配置不正确');
        }

        return $this->checkReturn($returnArr);
    }

    /**
     * 发送给面试官
     */
    public function sendBymsgToInterviewer($params)
    {
        //push 屏幕通知
        $msg['message_content'] = str_replace(
            ["{hr_nick}", "{hr_staff_id}", "{name}", "{cvid}", "{post_name}"],
            [
                $params['name'],//mianshi姓名
                $params['staff_info_id'],//hr工号
                $params['resume_name'],//面试者姓名
                $params['cvid'],//简历ID
                $params['job_name'],//岗位
            ],
            $this->getTranslation()->_('update_interview_msg')
        );
        $msg['message_title']   = $this->getTranslation()->_('interview_arrange_title');
        $msg['staff_info_id']   = $params['interviewer_id'];
        $msg['message_params']  = 'message_list';
        //系统推送
        $push_res = (new PublicServer())->pushMessageToScreen($msg);

        $inside_msg['message_params']  = 'message_list'; //通知跳转地址
        $inside_msg['message_content'] = $msg['message_content'];
        //发送站内信
        $inside_msg['message_content'] .= "|||{$params['interview_id']},{$params['interview_sub_id']},{$params['ope_id']}";
        $inside_msg['type']            = $params['interviewer_id'];
        $inside_msg['category']        = 41; //面试通知
        $inside_msg['category_code']   = 0; //通知类型
        $inside_msg['staff_info_id']   = $params['interviewer_id'];; //通知类型
        $inside_msg['message_title'] = $msg['message_title']; //通知类型
        $inside_res                  = (new PublicServer())->pushMessageToInterviewer($inside_msg);

        //记录操作
        $level = $push_res ? 'info' : 'notice';
        $this->getDI()->get("logger")->write_log("sendBymsgToInterviewer" . json_encode($msg,
                JSON_UNESCAPED_UNICODE) . " push res :" . json_encode($params, JSON_UNESCAPED_UNICODE), $level);

        $inside_level = $inside_res ? 'info' : 'notice';
        $this->getDI()->get("logger")->write_log("sendBymsgToInterviewer" . json_encode($msg,
                JSON_UNESCAPED_UNICODE) . " push res :" . json_encode($inside_msg, JSON_UNESCAPED_UNICODE),
            $inside_level);

    }

    /**
     * 通过interview_sub_id 获取operation_id
     * @param $data
     * @return array
     */
    public function getSubID($data)
    {
        $return  = [];
        $sub_ids = array_column($data, 'sub_id');
        if ($sub_ids = array_filter($sub_ids)) {
            $sub_ids = implode(',', $sub_ids);
            $sql     = "SELECT max(id) as id,interview_sub_id FROM hr_interviewer_operation WHERE interview_sub_id in ($sub_ids) group by interview_sub_id";
            $dataObj = $this->getDI()->get('db_rby')->query($sql);
            $data    = $dataObj->fetchAll(\Phalcon\Db::FETCH_ASSOC);
            return $data ? array_column($data, 'id', 'interview_sub_id') : [];
        }
        return $return;
    }

    public function getNewOperationState(array $id): array
    {
        return $id ? HrInterviewerOperationModel::find([
            'conditions' => ' id in ({ids:array}) ',
            'columns'    => ['interview_sub_id', 'state'],
            'bind'       => ['ids' => $id],
        ])->toArray() : [];
    }


    /**
     * 是否是一线员工职位
     * 紧根据配置的职位来判断是否一线
     * offer模块使用此逻辑判断是否一线
     * @param $job_id
     */
    public static $front_line_employee_jobids = [];

    public function isFrontLineJob($job_id)
    {

        if (empty(self::$front_line_employee_jobids)) {
            $front_line_jobids                = SettingEnvServer::getSetVal('front_line_employee_jobids');
            self::$front_line_employee_jobids = explode(',', $front_line_jobids);
        }
        if (self::$front_line_employee_jobids && in_array($job_id, self::$front_line_employee_jobids)) {
            return 1;
        }

        return 0;
    }

    /**
     * 发送offer后添加入职信息
     * @param $interview_id
     * @param $interview_********
     * @param $resume_id
     * @param $hc_id
     * @param $hire_type
     * @throws \Exception
     */
    public function AddEntryData($interview_id, $interview_********, $resume_id, $hc_id, $hire_type)
    {

        $entryData = HrEntryModel::findFirst([
            'conditions' => "deleted = 0 and resume_id =:resume_id:",
            'bind'       => ['resume_id' => $resume_id],
            'columns'    => ['entry_id'],
        ]);
        $this->getDI()->get('logger')->write_log("access training resumeID:".$resume_id, 'info');
        if (empty($entryData)) {
            /* 添加入职信息 */
            $paramEntry = [
                'interview_id'       => $interview_id,
                'interview_********' => $interview_********,
                'resume_id'          => $resume_id,
                'hc_id'              => $hc_id,
                'hire_type'          => $hire_type,
            ];
            $this->getDI()->get('logger')->write_log("access training resumeID:" . $resume_id . "add entry data:" . json_encode($paramEntry),
                'info');
            $this->getDI()->get('db')->insertAsDict(
                'hr_entry', $paramEntry
            );
            if ($this->getDI()->get('db')->affectedRows() < 1) {
                throw new \Exception('AddEntryData-添加入职失败', enums::$ERROR_CODE['1000']);
            }
        } else {
            $this->getDI()->get('logger')->write_log("access training resumeID and update:".$resume_id, 'info');

            //更新入职信息
            //将已经存在的入职日期、入职记录字段清空
            $this->getDI()->get('db')->updateAsDict(
                'hr_entry',
                [
                    'status'             => 2,
                    'record_entry'       => null,
                    'entry_date'         => '0000-00-00 00:00:00',
                    'interview_id'       => $interview_id,
                    'interview_********' => $interview_********,
                    'hc_id'              => $hc_id,
                    'hire_type'          => $hire_type,
                ],
                "entry_id = ".$entryData->entry_id
            );

            if ($this->getDI()->get('db')->affectedRows() < 1) {
                throw new \Exception("AddEntryData-更新入职失败", enums::$ERROR_CODE['1000']);
            }
        }

        return true;
    }

    /**
     * offer表详情
     * @param $resume_id
     */
    public function getOfferInfo($resume_id)
    {
        $data_obj = HrInterviewOfferModel::findFirst([
            'conditions' => " resume_id =:resume_id:",
            'bind'       => ['resume_id' => $resume_id],
            'order'      => 'id desc',
        ]);
        if ($data_obj) {
            return $data_obj->toArray();
        }

        return [];
    }

    /**
     * offer表详情
     * @param $resume_id
     */
    public function getOfferInfoById($********)
    {
        $data_obj = HrInterviewOfferModel::findFirst([
            'conditions' => " id =:********:",
            'bind'       => ['********' => $********],
        ]);
        if ($data_obj) {
            return $data_obj->toArray();
        }

        return [];
    }

    /**
     * 面试主表详情
     * @param 面试ID
     */
    public function getInterviewInfo($interview_id)
    {
        $data_obj = HrInterviewModel::findFirst([
            'conditions' => " interview_id =:interview_id:",
            'bind'       => ['interview_id' => $interview_id],
        ]);
        if ($data_obj) {
            return $data_obj->toArray();
        }

        return [];
    }

    /**
     * 面试主表详情
     * @param 面试ID
     */
    public function getInterviewInfoByResumeid($resume_id, $hc_id)
    {
        $data_obj = HrInterviewModel::findFirst([
            'conditions' => " resume_id =:resume_id: and hc_id = :hc_id:",
            'bind'       => ['resume_id' => $resume_id, 'hc_id' => $hc_id],
            'order'      => 'interview_id desc',
        ]);
        if ($data_obj) {
            return $data_obj->toArray();
        }

        return [];
    }


    /**
     * 获取非一线职位发送offer 邮件模板
     *
     * @param $resumeId
     *
     *
     */
    public function getNotFrontLineJobSendOfferEmailTpl($resumeId)
    {
        $this->sysList = new SysListRepository();

        $resumeInfo = (new ResumeServer())->getResumeBaseInfo($resumeId);
        $result     = ['resume_id' => $resumeId];
        $tmp        = "";
        if ($resumeInfo) {
            $hr_interview = $this->getInterviewInfoByResumeid($resumeId, $resumeInfo['hc_id']);
            $hc_id        = $hr_interview['hc_id'] ?? 0;
            $hc_info      = (new OfferSignServer())->getHcInfo($hc_id);

            //面试人信息
            $jobTitle              = $this->sysList->getPositionList(["id" => $hc_info['job_title']]);//面试人职位
            $interviewer_job_title = $jobTitle[0]['job_name'] ?? '';
            $interviewer_name      = $resumeInfo['first_name'] . " " . $resumeInfo['last_name']; //面试人姓名
            //获取招聘人信息
            $offer_sign_data = (new InterviewRepository())->getLastOfferSignData($resumeId);
            $recruiter_id    = $offer_sign_data['submitter_id'];
            $recruiter_obj   = HrStaffInfoModel::findFirst([
                'conditions' => "staff_info_id = :staff_info_id:",
                'bind'       => ['staff_info_id' => $recruiter_id],
                'columns'    => ['name', 'mobile', 'job_title'],
            ]);

            if (!empty($recruiter_obj)) {
                $recruiter_info      = $recruiter_obj->toArray();
                $recruiter_job_title = $this->sysList->getPositionList(["id" => $recruiter_info['job_title']]);
            }
            $recruiter_job_title = $recruiter_job_title[0]['job_name'] ?? '-';


            $tmp .= "Dear {$interviewer_name},\r\n";
            $tmp .= "\r\n";
            $tmp .= "Congratulations to you! I am happy to inform you that Flash Express would like to offer you the position of {$interviewer_job_title} \r\n";
            $tmp .= "We consider you as the potential candidate for the position after selection of all candidates. \r\n";
            $tmp .= "Please kindly find the Offer Letter attached and sign back to us as your confirmation of acceptance of this offer.\r\n";
            $tmp .= "\r\n";
            $tmp .= "Best Regards\r\n";
            $tmp .= "{$recruiter_info['name']}\r\n";
            $tmp .= "{$recruiter_job_title}\r\n";
            $tmp .= "Tel:{$recruiter_info['mobile']}\r\n";
            $tmp .= "Flash Express Co.,Ltd.\r\n";
            $tmp .= "8 floor Rama 9, 161 Unilever House, Huaikwhang, Huaikwhang Bangkok 10310 Thailand.\r\n";
            $tmp .= "Thailand Website: https://www.flashexpress.com \r\n";


            //接口返回信息
            $result['credentials_category'] = $resumeInfo['credentials_category'];
            $result['phone_area_code']      = !empty($resumeInfo['phone_area_code']) ? $resumeInfo['phone_area_code'] : '66';
            $result['email']                = $resumeInfo['email'];
            $result['phone']                = $resumeInfo['phone'];
            $result['name']                 = $interviewer_name;
            $result['content']              = $tmp;
        }

        return $result;
    }

    public function bgCheckAnnexLogs($********)
    {
        $logList = BgCheckAnnexLogModel::find([
            'columns'    => "annex_name, annex_url, operator_status, operator_id, DATE_FORMAT(CONVERT_TZ(created_at, '+00:00', '" . $this->timezone . "' ),'%Y-%m-%d %H:%i:%s') AS created_at",
            'conditions' => ' ******** = :********: ',
            'bind'       => ['********' => $********],
            'order'      => 'id asc',
        ])->toArray();

        $operatorIds = array_column($logList, 'operator_id');
        $operators   = (new StaffRepository())->checkoutStaffBatch(array_values(array_filter(array_unique($operatorIds))));
        $operators   = array_column($operators, null, 'staff_info_id');

        $logs = [];
        foreach ($logList as $item) {
            $logs[] = [
                'operator_time' => $item['created_at'] == '9999-12-31 23:59:59' ? '--' : $item['created_at'],
                'operator_id'   => $item['operator_id'],
                'operator_name' => $item['operator_id'] == 1000 ? 'superAdmin'
                    : (isset($operators[$item['operator_id']]) ? $operators[$item['operator_id']]['name'] : ''),
                'operator'      => $this->getTranslation()->t('bg_check_annex_' . $item['operator_status']),
                'annex_name'    => $item['annex_name'],
                'annex_url'     => $item['annex_url'],
            ];
        }

        return $this->checkReturn(['data' => $logs]);
    }

    public function delBgCheckAnnex($id, $operator_id)
    {

        $db = BgCheckAnnexModel::beginTransaction($this);
        try {
            $bgCheckAnnexModel = BgCheckAnnexModel::findFirst([
                'conditions' => ' id = :id: ',
                'bind'       => ['id' => $id],
            ]);
            if (!$bgCheckAnnexModel || $bgCheckAnnexModel->status == BgCheckAnnexModel::STATUS_DEL) {
                throw new BusinessException('数据不存在');
            }

            $bgCheckAnnexModel->status = BgCheckAnnexModel::STATUS_DEL;
            $bgCheckAnnexModel->save();

            $bgCheckAnnexLogModel                  = new BgCheckAnnexLogModel();
            $bgCheckAnnexLogModel->********        = $bgCheckAnnexModel->********;
            $bgCheckAnnexLogModel->annex_id        = $bgCheckAnnexModel->id;
            $bgCheckAnnexLogModel->annex_name      = $bgCheckAnnexModel->annex_name;
            $bgCheckAnnexLogModel->annex_url       = $bgCheckAnnexModel->annex_url;
            $bgCheckAnnexLogModel->operator_status = BgCheckAnnexLogModel::OPERATOR_STATUS_DEL;
            $bgCheckAnnexLogModel->operator_id     = $operator_id;
            $bgCheckAnnexLogModel->save();

            $db->commit();
        } catch (Exception $ex) {
            $db->rollback();
            // 往上抛
            throw $ex;
        }

        return $this->checkReturn(1);
    }

    /**
     * 背调列表
     *
     * @param $offerId
     * @return array
     */
    public function bgCheckAnnexList($offerId)
    {
        $list = BgCheckAnnexModel::find([
            'columns'    => 'id, ********, annex_name, annex_url',
            'conditions' => ' ******** = :********: and status = :status:',
            'bind'       => [
                '********' => $offerId,
                'status'   => BgCheckAnnexModel::STATUS_NORMAL,
            ],
            'order'      => 'id asc',
        ])->toArray();

        return $this->checkReturn(['data' => $list]);
    }

    /**
     * 批量判断offer 是否有背调附件
     *
     * @param $offerIds
     * @return array
     */
    public function hasBgCheckAnnexOffer($offerIds)
    {
        $offerIds = array_unique($offerIds);
        $offerIds = array_filter($offerIds);
        $offerIds = array_values($offerIds);

        if (!empty($offerIds)) {
            $BgCheckAnnexList = BgCheckAnnexModel::find([
                'columns'    => 'id, ********',
                'conditions' => '******** in ({********s:array}) and status = :status: ',
                'bind'       => [
                    '********s' => $offerIds,
                    'status'    => BgCheckAnnexModel::STATUS_NORMAL,
                ],
            ])->toArray();

            return array_column($BgCheckAnnexList, '********');
        }

        return [];
    }


    /**
     * 上传背调附件地址
     *
     * @return array
     * @throws BusinessException
     *
     */
    public function uploadBgCheckAnnex($params)
    {
        // 判断offer状态 已发offer 和 已入职tab 才可以上传
        $offer_data = $this->getOfferInfoById($params['********']);
        if (!isset($params['created_at'])) {
            if (empty($offer_data) || $offer_data['status'] != enums::$offer_status['offer_has_send']) {
                throw new BusinessException("offer 附件上传 offer状态异常，offerid:" . $params['********'] . ",offer状态{$offer_data['status']}");
            }
        }
        $db = BgCheckAnnexModel::beginTransaction($this);
        try {
            $bgCheckAnnexLogModel           = new BgCheckAnnexLogModel();
            $bgCheckAnnexLogModel->******** = $params['********'];
            if (isset($params['id']) && !empty($params['id'])) {
                // ID 存在 编辑
                $bgCheckAnnexModel = BgCheckAnnexModel::findFirst([
                    'conditions' => 'id = :id: and status = :status:',
                    'bind'       => [
                        'id'     => $params['id'],
                        'status' => BgCheckAnnexModel::STATUS_NORMAL,
                    ],
                ]);
                if (!$bgCheckAnnexModel) {
                    throw new BusinessException("附件不存在");
                }
                $bgCheckAnnexLogModel->operator_status = BgCheckAnnexLogModel::OPERATOR_STATUS_EDIT;
            } else {
                // 新增
                $count = BgCheckAnnexModel::count([
                    'conditions' => '******** = :********: and status = :status:',
                    'bind'       => ['********' => $params['********'],'status'=>BgCheckAnnexModel::STATUS_NORMAL],
                ]);
                if ($count && $count >= 10) {
                    throw new BusinessException("背调附件不得超过10条");
                }
                $bgCheckAnnexModel                     = new BgCheckAnnexModel();
                $bgCheckAnnexLogModel->operator_status = BgCheckAnnexLogModel::OPERATOR_STATUS_ADD;
            }
            $bgCheckAnnexModel->********   = $params['********'];
            $bgCheckAnnexModel->annex_name = $params['annex_name'];
            $bgCheckAnnexModel->annex_url  = $params['annex_url'];
            $bgCheckAnnexModel->status     = BgCheckAnnexModel::STATUS_NORMAL;
            $bgCheckAnnexModel->save();

            $bgCheckAnnexLogModel->annex_id    = $bgCheckAnnexModel->id;
            $bgCheckAnnexLogModel->annex_name  = $params['annex_name'];
            $bgCheckAnnexLogModel->annex_url   = $params['annex_url'];
            $bgCheckAnnexLogModel->operator_id = $params['operator_id'];
            if (isset($params['created_at']) && !empty($params['created_at'])) {
                $bgCheckAnnexLogModel->created_at = $params['created_at'];
            }
            $bgCheckAnnexLogModel->save();
            $db->commit();
        } catch (Exception $ex) {
            $db->rollback();
            // 往上抛
            throw $ex;
        }

        return $this->checkReturn(['data'=>['id'=>$bgCheckAnnexModel->id]]);

    }


    /**
     * offer附件上传 后 的逻辑处理
     * 判断是否是非一线职位，非一线职位则进行 插入入职管理表数据和  hc -1 处理，
     * @param $resume_id
     * @throws \Exception
     */
    public function offerAnnexUploadedDo($paramIn)
    {
        $this->hc = new HcRepository();

        //逻辑处理开始
        $db = $this->getDI()->get('db');
        $db->begin();

        try {

            $********   = $paramIn['********'] ?? 0;
            $annex_type = $paramIn['annex_type'];//1:offer附件，2：背调附件
            $annex_url  = $paramIn['annex_url'];//附件地址


            //获取最新offer数据
            $offer_data = $this->getOfferInfoById($********);

            if (empty($offer_data) || $offer_data['status'] != 1) {
                throw new BusinessException("offer 附件上传 offer状态异常，offerid:" . $******** . ",offer状态{$offer_data['status']}");
            }

            $resume_id               = $offer_data['resume_id'];
            $offer_annex_url         = $offer_data['offer_annex_url'] ?? ''; //offer附件
            $bg_annex_url            = $offer_data['bg_check_annex_url'] ?? ''; //背调附件
            $offer_annex_upload_time = $offer_data['offer_annex_upload_time'] ?? ''; //offer附件上传日期

            if ($annex_type == 1 && !empty($offer_annex_url)) {
                //提交事物
                $db->commit();
                return $this->checkReturn(-3, 'Offer Attachment already exists, no need to upload again');
            }
            if ($annex_type == 2 && !empty($bg_annex_url)) {
                //提交事物
                $db->commit();
                return $this->checkReturn(-3, 'BG Attachment already exists, no need to upload again');
            }
            //（删除）重新上传offer附件处理
            if ($annex_type == 1 && empty($offer_annex_url) && !empty($offer_annex_upload_time)) {
                //如果有offer上传时间，标识是删除后成功新上传，只需要更新附件接口
                $this->getDI()->get('logger')->write_log("重新上传offer附件操作，cvid：{$resume_id},offerid:{$********}", 'info');

                $db->updateAsDict('hr_interview_offer', ['offer_annex_url' => $annex_url], 'id = ' . $********);

                //提交事物
                $db->commit();

                return $this->checkReturn(1);
            }

            //（删除）重新上传背调附件处理
            if ($annex_type == 2 && empty($bg_annex_url)) {
                $this->getDI()->get('logger')->write_log("重新上传背调附件操作，cvid：{$resume_id},offerid:{$********}", 'info');

                $db->updateAsDict('hr_interview_offer', ['bg_check_annex_url' => $annex_url], 'id = ' . $********);

                //提交事物
                $db->commit();

                return $this->checkReturn(1);
            }

            $offerSignServer   = new OfferSignServer();

            //是否是一线职位判断标识
            if (isCountry('PH')) {
                $hcInfo            = $offerSignServer->getHcInfo($offer_data['hc_id']);
                $is_front_line_job = $this->isFirstLineJob($hcInfo['department_id'], $offer_data['position_id']);
            } else {
                $is_front_line_job = $this->isFrontLineJob($offer_data['position_id']);

                //非一线定义 泰国
                if (isCountry()) {
                    $resumeInfo = (new ResumeRepository())->resumeInfo($offer_data['resume_id']);
                    if ($this->isHeadOfferRecruit($resumeInfo)) {
                        $is_front_line_job = false;
                    }
                }
            }
            $this->getDI()->get('logger')->write_log("非一线职位上传offer附件处理开始，cvid：{$resume_id},is_front_line_job:" . $is_front_line_job,
                'info');

            $offer_up_data = [];

            if ($annex_type == 1) {
                //todo offer附件上传处理
                $offer_up_data['offer_annex_url'] = $annex_url;
                //一线职位 并且没上传过简历附件才更新附件上传时间
                if (empty($offer_annex_url) && empty($offer_annex_upload_time)) {
                    //只记录首次上传offer附件时间
                    $offer_up_data['offer_annex_upload_time'] = gmdate('Y-m-d H:i:s');
                }

                //todo 非一线职位逻辑处理
                if (!$is_front_line_job) {
                    // 保留原逻辑 进行了个优化（非一线才校验hc状态）
                    $current_hc = $this->hc->checkHc(['hc_id' => $offer_data['hc_id'],'for_update' => true]);
                    if ($current_hc['state_code'] == 3 || $current_hc['surplusnumber'] == 0) {
                        $db->rollback();
                        return $this->checkReturn(-3, $this->getTranslation()->_('11027'));
                    }
                    if (empty($offer_annex_upload_time)) {
                        /* 校验hc */
                        if ($current_hc['state_code'] == 4) {
                            $db->rollback();
                            return $this->checkReturn(-3, $this->getTranslation()->_('11028'));
                        }
                        if ($current_hc['state_code'] == 9) {
                            $db->rollback();
                            return $this->checkReturn(-3, $this->getTranslation()->_('11031'));
                        }
                    }
                    $this->getDI()->get('logger')->write_log("非一线职位上传offer附件处理开始，cvid：{$resume_id},hc信息:" . json_encode($current_hc),
                        'info');
                    //查询是否有待入职或已入职数据数据
                    $entryData = HrEntryModel::findFirst([
                        'conditions' => "deleted = 0 and interview_******** =:interview_********: and status in(1,2)",
                        'bind'       => ['interview_********' => $********],
                        'columns'    => ['entry_id'],
                    ]);
                    if (empty($entryData)) {
                        //没有已入职或待入职数据则处理入职和hc
                        $this->getDI()->get('logger')->write_log("非一线职位上传offer附件成功后入职和hc变更处理开始，cvid：{$resume_id}",
                            'info');

                        (new InterviewServer())->AddEntryData($offer_data['interview_id'], $offer_data['id'],
                            $resume_id, $offer_data['hc_id'],$current_hc['hire_type']);

                        /**
                         * 修改hc剩余数-1
                         * 一线职位ID保持逻辑不动，非一线职位要等简历附件回传后在进行人数变更
                         */

                        if ($current_hc['surplusnumber'] == 1) {
                            /* 修改hc表状态 改为已完成 */
                            $hc_res = $db->updateAsDict('hr_hc', ['surplusnumber' => 0, 'state_code' => 3],
                                'hc_id = ' . $offer_data['hc_id']);
                        } else {
                            $hc_res = $db->updateAsDict('hr_hc', ['surplusnumber' => $current_hc['surplusnumber'] - 1],
                                'hc_id = ' . $offer_data['hc_id']);
                        }

                        $this->getDI()->get('logger')->write_log("非一线职位上传offer附件成功后入职和hc变更结果，hc_res:{$hc_res},offer数据:" . json_encode($offer_data),
                            'info');


                    }
                }

            } else {

                //todo 背调附件上传处理
                $offer_up_data['bg_check_annex_url'] = $annex_url;
            }

            //todo 更新offer表字段信息
            $offer_up_res = $db->updateAsDict('hr_interview_offer', $offer_up_data, 'id = ' . $********);

            $this->getDI()->get('logger')->write_log("非一线职位上传offer附件或背调附件,cvid：{$resume_id},offer表执行结果:{$offer_up_res}, offer表更新字段信息，,offer更新字段：" . var_export($offer_up_data,
                    true), 'info');


            //提交事物
            $db->commit();

            return $this->checkReturn(1);

        } catch (BusinessException $be) {
            $db->rollback();
            throw new BusinessException('非一线职位上传offer附件成功后入职和hc变更失败:' . $be->getMessage());

        } catch (Exception $e) {
            $db->rollback();
            throw new \Exception('非一线职位上传offer附件成功后入职和hc变更失败:'.$e->getMessage());
        }
    }

    /**
     * 删除offer附件
     * @param $******** offer id
     * @param $annex_type 1:offer附件，2：背调附件
     */
    public function delOfferAnnex($********, $annex_type)
    {
        //删除offer表附件信息
        if ($annex_type == 1) {
            //offer附件
            $offer_up_data['offer_annex_url'] = '';
        } else {
            //背调附件
            $offer_up_data['bg_check_annex_url'] = '';
        }
        $this->getDI()->get('db')->updateAsDict('hr_interview_offer', $offer_up_data, 'id = ' . $********);

        return $this->checkReturn(1);
    }

    /**
     * 当前登录工号时候有offer附件权限
     * @param $staff_id
     * @return int
     */
    public function isHaveOfferAnnexPermission($staff_id)
    {

        $offer_annex_permission = SettingEnvServer::getSetVal('resume_attched_permission');
        if ($offer_annex_permission && in_array($staff_id, explode(',', $offer_annex_permission))) {
            return 1;
        }

        return 0;
    }


    /**
     * 当前登录工号时候有背调附件权限
     * @param $staff_id
     * @return int
     */
    public function isHaveBgAnnexPermission($staff_id)
    {

        $offer_annex_permission = SettingEnvServer::getSetVal('bg_attched_permission');
        if ($offer_annex_permission && in_array($staff_id, explode(',', $offer_annex_permission))) {
            return 1;
        }

        return 0;
    }

    /**
     * 批量修改最新操作人
     * @param array $paramIn
     * @return void
     */
    public function batchUpdateResumeLastOperator(array $paramIn)
    {
        /**
         * 1、验证传入的简历操作人id是否符合 拥有网络TA角色，在职，正式员工
         * 2、cvid是否都合法
         * 3、批量更新
         */
        $resume_last_operator_id = trim($paramIn['resume_last_operator_id']);
        $cvids                   = $paramIn['cvids'];
        $params                  = [
            'staff_info_id' => $resume_last_operator_id,
        ];
        $staff_info              = (new StaffRepository())->searchStaff($params, 1);
        if (empty($staff_info[0])) {
            return $this->checkReturn(-3, $this->getTranslation()->_('resume_last_operator_id_fail'));
        }

        $resume_list = HrResumeModel::find([
            'conditions' => 'id IN ({ids:array})',
            'bind'       => ['ids' => $cvids],
            'columns'    => "id, name",
        ])->toArray();
        if (empty($resume_list)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('all_cvid_fail'));
        }
        $resume_ids = array_column($resume_list, 'id');
        $diff_cvids = array_diff($cvids, $resume_ids);
        if (empty($resume_ids) || $diff_cvids) {
            return $this->checkReturn(-3,
                $this->getTranslation()->_('cvid_fail', ['cvid' => implode(', ', $diff_cvids)]));
        }

        // 更新
        $this->db->updateAsDict(
            (new HrResumeModel())->getSource(),
            [
                'resume_last_operator' => $resume_last_operator_id,
            ],
            'id IN ('.implode(',', $cvids).')'
        );

        return $this->checkReturn(1);
    }


    /**
     * new 添加面试记录
     * @param $interview_id
     * @param $state
     * @param array $paramIn
     * @return bool
     */
    public function addHrInterviewRecord($interview_id, $state = HrInterviewRecordModel::STATE_DEFAULT, $paramIn = [])
    {
        $resume_id         = $paramIn['resume_id'] ?? 0;
        $interviewer_id    = $paramIn['interviewer_id'] ?? '';
        $interview_address = $paramIn['interview_address'] ?? '';
        $interview_time    = $paramIn['interview_time'] ?? '';
        if (!$resume_id || !$interviewer_id || !$interview_address || !$interview_time) {
            // 以上信息有一个没有传 就去查询
            $builder = $this->modelsManager->createBuilder();
            $builder->columns("interview.interview_id,interview.resume_id,interview_subscribe.interviewer_id,interview_subscribe.detail_address,interview_subscribe.interview_time");
            $builder->from(['interview' => HrInterviewModel::class]);
            $builder->leftjoin(HrInterviewSubscribeModel::class,
                'interview.interview_id=interview_subscribe.interview_id', 'interview_subscribe');
            $builder->andWhere('interview.interview_id = :interview_id:', ['interview_id' => $interview_id]);
            $result         = $builder->getQuery()->execute()->getFirst();
            $interview_data = $result ? $result->toArray() : [];
            if (!$interview_data) {
                $this->getDI()->get('logger')->write_log("addHrInterviewRecord interview_data is null interview_id:" . $interview_id . ",state:" . $state . ",paramIn:" . json_encode($paramIn),
                    'error');
            }

            $resume_id         = $resume_id ? $resume_id : ($interview_data['resume_id'] ?? 0);
            $interviewer_id    = $interviewer_id ? $interviewer_id : ($interview_data['interviewer_id'] ?? 0);
            $interview_address = $interview_address ? $interview_address : ($interview_data['detail_address'] ?? '');
            $interview_time    = $interview_time ? $interview_time : ($interview_data['interview_time'] ?? '');
        }

        $interview_record_model                    = new HrInterviewRecordModel();
        $interview_record_model->interview_id      = $interview_id;
        $interview_record_model->resume_id         = $resume_id;
        $interview_record_model->operator_id       = $this->userInfo['id'];
        $interview_record_model->state             = $state;
        $interview_record_model->interviewer_id    = $interviewer_id;
        $interview_record_model->interview_address = $interview_address;
        $interview_record_model->interview_time    = $interview_time;
        return $interview_record_model->create();
    }

    /**
     * 面试取消 + 关联hc 两步并成一步 但不要日志和记录相关 只改相关流程状态
     * @param array $paramIn
     * @return array
     */
    public function interview_cancel_and_related_hc($paramIn = [])
    {
        $interview_id = $paramIn['interview_id'] ?? 0; // 面试表id
        $ope_id       = $paramIn['ope_id'] ?? 0; // 面试官操作记录表id
        $hc_id        = $paramIn['hc_id'] ?? 0;
        $db           = $this->getDI()->get('db');
        $db->begin();
        try {
            // 检查hc数据
            $hc = HrhcModel::findFirst([
                'conditions' => 'hc_id = :hc_id: and state_code = :state_code:',
                'bind'       => ['hc_id' => $hc_id, 'state_code' => HrhcModel::STATE_RECRUITING],
            ]);
            if (!$hc) {
                throw new Exception($this->getTranslation()->_('interview_cancel_and_related_hc_1'));
            }

            // 1 修改面试主表
            $interview       = HrInterviewModel::findFirst([
                'conditions' => 'interview_id = :interview_id:',
                'bind'       => ['interview_id' => $interview_id],
            ]);
            $interview_state = $interview ? $interview->state : 0;
            if (!$interview || in_array($interview_state, [HrInterviewModel::STATE_SEND_OFFER, HrInterviewModel::STATE_ENTRY])) {
                throw new Exception('面试信息缺失或面试状态不符合 interview_id : ' . $interview_id);
            }
            $resume_id        = $interview->resume_id ?? 0;
            // 检查薪资数据
            $salary_approval = (new InterviewServer())->getLastSalaryApproval([$resume_id]);
            if ($salary_approval && in_array($salary_approval[$resume_id]['status'],[SalaryApproveModel::STATUS_AGREE,SalaryApproveModel::STATUS_PENDING_APPROVAL])){
                throw new Exception('interview_cancel_and_related_hc_2');
            }

            $interview->state = HrInterviewModel::STATE_CANCEL;
            $interview->save();

            // 2 修改面试扩展表
            $interview_subscribe = HrInterviewSubscribeModel::findFirst([
                'conditions' => 'interview_id = :interview_id:',
                'bind'       => ['interview_id' => $interview_id],
            ]);
            if (!$interview_subscribe) {
                throw new Exception('面试扩展信息缺失');
            }
            $interview_subscribe->status = HrInterviewSubscribeModel::STATUS_CANCEL;
            $interview_subscribe->save();

            // 3 修改面试官记录表
            $interviewer_operation = HrInterviewerOperationModel::findFirst([
                'conditions' => 'id = :id:',
                'bind'       => ['id' => $ope_id],
            ]);
            if ($interviewer_operation) {
                $interviewer_operation->status = HrInterviewerOperationModel::STATE_CANCEL_OPERATION;
                $interviewer_operation->save();
            }
            // 4 更新简历数据
            $resume = HrResumeModel::findFirst([
                'conditions' => 'id = :id:',
                'bind'       => ['id' => $resume_id],
            ]);
            if (!$resume) {
                throw new Exception('更新简历信息失败' . $interview_id);
            }

            $resume_data = $resume->toArray();
            // 5 重新关联hc
            $is_can_change_filter_result = $resume_data['previous_hc_id'] > 0 && $resume_data['filter_state'] == enums::$resume_filter_state['not_pass'] && $hc_id != $resume_data['previous_hc_id'];
            $resume_up_data              = [];
            if ($resume_data['hc_id'] != $hc_id && $hc_id > 0) {
                $resume_up_data['hc_id']                       = $hc_id;
                $resume_up_data['is_can_change_filter_result'] = intval($is_can_change_filter_result);
                $resume_up_data['filter_state']                = 0;//简历筛选状态：0-初始状态，99-面试标识
                $resume_up_data['state_code']                  = enums::$resume_state_code['re_hc_wait_feedback'];//重新关联hc待反馈
                $resume_up_data['is_out']                      = enums::$resume_is_out['not_out'];
                $resume_up_data['recruiter_id']                = $this->userInfo['id'];
                $resume_up_data['previous_hc_id']              = $resume_data['hc_id'];
            } else {
                throw new Exception($this->getTranslation()->_('interview_cancel_and_related_hc_3'));
            }
            $db->updateAsDict('hr_resume', $resume_up_data, [
                'conditions' => 'id = ?',
                'bind'       => [$resume_id],
            ]);
            // 6 相关日志
            $addLog = [
                'module_id'    => $resume_id,
                'module_type'  => enums::$log_module_type['hc_id'],
                'action'       => enums::$log_option['modify'],
                'module_level' => $hc_id ?? 0,
                'data_after'   => ['hc_id' => $hc_id ?? 0],
            ];
            (new LogServer())->addLog($addLog);
            $db->commit();
        } catch (Exception $e) {
            $db->rollback();
            $this->getDI()->get('logger')->write_log("interview_cancel_and_related_hc interview_id " . $interview_id . " , 处理失败，失败原因:" . $e->getMessage(),
                'info');
            return $this->checkReturn(-3, $e->getMessage());
        }
        return $this->checkReturn(1);
    }
}
