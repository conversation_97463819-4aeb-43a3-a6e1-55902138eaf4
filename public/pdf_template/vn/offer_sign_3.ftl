<html>

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
  <title>Offer Letter - Flash Vietnam</title>
  <style>
    @page {
      /* switch to landscape */
      size: 210mm 297mm;
      /* set page margins */
      margin: 15mm;

      @bottom-center {
        content: element(footer);
      }

      @top-center {
        content: element(header);
      }

    }

    div.header {
      display: block;
      text-align: center;
      position: running(header);
      width: 100%;
      /* margin-bottom: 10mm; */
    }

    div.footer {
      display: block;
      text-align: center;
      position: running(footer);
      width: 100%;
    }

    .box {
      width: 100%;
      font-size: 3.5mm;
      margin: 0 auto;
      font-family: Arial Unicode MS, thailand, SimSun;
    }

    .title tr td {
      height: 15mm;
      line-height: 15mm;
      font-size: 5mm;
      background-size: 38mm;
      padding-bottom: 5mm;
      font-family: sans-serif;
    }

    .title,
    .bottom {
      border: 0;
      width: 100%;

    }

    .word-h {
      /* line-height: 10mm; */
      margin-bottom: 4mm;
    }

    .word-t {
      display: table;
      margin: 0mm auto;
      font-size: 5mm;

    }

    .word-t span {
      border-bottom: 0.5mm solid #000;
    }

    .word-span {
      margin-bottom: 4mm;
    }

    .word-span span {
      display: inline-table;
      width: 100%;
    }

    .slot {
      font-style: normal;
      padding: 0 2mm;
      font-size: 3.5mm;
      border-bottom: 0.5mm solid #000;
      font-family: Arial Unicode MS, thailand, SimSun;
      word-break: break-all;
    }

    .word-i {
        font-size: 3mm
    }

    .word-span-last {
      margin: 1.5mm 0;
    }

    .sign {
      width: 100%;
    }

    .sign tr td {
      margin-left: 10mm;
      font-size: 3.5mm;
    }

    .border-b {
      width: 50mm;
      display: inline-block;
      border-bottom: 0.5mm solid #333;
      text-align: center;
    }

    .bottom span {
      display: table;
      margin: 0 auto;
      font-size: 3mm;

    }

    .info {
      width: 100%;
    }

    .info tr td {
      text-align: left;
      font-size: 3.5mm;
      vertical-align: top
    }

    .info tr td:first-child {
      width: 50mm;
    }

    .check-box span {
      font-size: 2.5mm;
    }

    .check {
      display: inline-block;
      width: 6mm;
      height: 6mm;
      border: 0.5mm solid #000;
      margin: 2mm 3mm 0 0;
    }
  </style>
</head>

<body>

  <!-- 英文 -->
  <div class="box">
    <!-- 页头 -->
    <div class="header">
      <table class="title" border="0">
        <tr>
          <td style="text-align: left;"><strong>${company_name}</strong> </td>
          <td style="width: 30mm;"><img style="width: 30mm;height: auto;object-fit: cover;"
              src="${company_logo_url_base64}"
              alt="" /></td>
        </tr>
      </table>
    </div>

    <div class="word-h word-t">
      <span><strong>Offer Letter</strong> </span>
    </div>

    <div class="word-h">
      Date: <i class="slot">${apply_sign_date}</i>
    </div>
    <div class="word-h">
      Dear:<i class="slot">${interviewer_name}</i>
    </div>


    <div class="word-span">
      <span>
        We thank you for your interest in joining ${company_name} After thoroughly reviews of your application and
        interview results, we would like to offer you the position in our Company as per following:
      </span>
    </div>


    <table class="info" border="0" style="border-collapse:separate;">
      <tr>
        <td>Job Position:</td>
        <td> <i class="slot">${job_title_name}</i>
        </td>
      </tr>
      <tr>
        <td>Job Grade:</td>
        <td> <i class="slot">${job_title_grade}</i>
        </td>
      </tr>
      <tr>
        <td>Department:</td>
        <td> <i class="slot">${dept_name}</i>
        </td>
      </tr>
      <tr>
        <td>Company Name:</td>
        <td> <i class="slot">${company_name}</i>
        </td>
      </tr>
      <tr>
        <td>Remuneration:</td>
        <td>
        </td>
      </tr>
      <!-- 工资项 复制 -->
      <#if basic_salary !="">
        <tr>
          <td></td>
          <td>Top Base salary<i class="slot">${basic_salary}VND</i>per month before tax (during the probation including):
          </td>
        </tr>
        <#else>
          <tr>
            <td></td>
            <td></td>
          </tr>
      </#if>

      <#if performance_allowance !="">
        <tr>
          <td></td>
          <td>Monthly perfomance salary<i class="slot">${performance_allowance}VND</i>per month before tax
          </td>
        </tr>
        <#else>
          <tr>
            <td></td>
            <td></td>
          </tr>
      </#if>


      <#if computer !="">
        <tr>
          <td></td>
          <td>Laptop allowance<i class="slot">${computer}VND</i>per month before tax
          </td>
        </tr>
        <#else>
          <tr>
            <td></td>
            <td></td>
          </tr>
      </#if>


      <#if transport_allowance !="">
        <tr>
          <td></td>
          <td>Mileage allowance<i class="slot">${transport_allowance}VND</i>per month before tax
          </td>
        </tr>
        <#else>
          <tr>
            <td></td>
            <td></td>
          </tr>
      </#if>

      <#if phone_subsidy !="">
        <tr>
          <td></td>
          <td>Telephone allowance<i class="slot">${phone_subsidy}VND</i>per month before tax
          </td>
        </tr>
        <#else>
          <tr>
            <td></td>
            <td></td>
          </tr>
      </#if>

      <#if food !="">
        <tr>
          <td></td>
          <td>Meal allowance<i class="slot">${food}VND</i>per ${food_unit} before tax
          </td>
        </tr>
        <#else>
          <tr>
            <td></td>
            <td></td>
          </tr>
      </#if>

<#assign xinghuo_allowance_prefix_num=0 >
      <#if renting !="">
<#assign xinghuo_allowance_prefix_num++ >
        <tr>
          <td></td>
          <td>Housing allowance<sup>1</sup><i class="slot">${renting}VND</i>per month before tax
          </td>
        </tr>
        <#else>
          <tr>
            <td></td>
            <td></td>
          </tr>
       </#if>

      <#if xinghuo_allowance !="">

<#assign xinghuo_allowance_prefix_num++ >
        <tr>
          <td></td>
          <td>Firestarter Award<sup>${xinghuo_allowance_prefix_num}</sup><i class="slot">${xinghuo_allowance}VND</i>per month before tax
          </td>
        </tr>
        <#else>
          <tr>
            <td></td>
            <td></td>
          </tr>
      </#if>


       <#if trial_salary !="">
              <tr>
                <td></td>
                <td>After probation increase to <i class="slot">${trial_salary}VND</i>per month before tax
                </td>
              </tr>
              <#else>
                <tr>
                  <td></td>
                  <td></td>
                </tr>
       </#if>
       
      <tr style="height: 2mm;"></tr>
    </table>
    <table class="info" border="0" style="border-collapse:separate;">
      <tr>
        <td>Working days: </td>
        <td> <i class="slot">${work_days}</i>working days per week
        </td>
      </tr>
      <tr>
        <td>Other Benefits:</td>
        <td>Per Company Policy
        </td>
      </tr>
      <tr>
        <td>Starting Date:</td>
        <td> <i class="slot">${work_date}</i>
        </td>
      </tr>
      <tr style="height: 2mm;"></tr>
    </table>



    <div class="word-span">
      <span>We look forward to receiving your acceptance of this offer and we are confident that you will perform well
        in this position.</span>
    </div>
    <div class="word-span word-span-last">
      <span>Should you agree, please indicate your acceptance of this offer by signing this letter.</span>
    </div>

    <table class="sign" border="0" style="border-collapse:separate;">
      <tr>
        <td><strong>Yours faithfully,</strong> </td>
        <td><strong>Accepted by:</strong></td>
      </tr>
      <tr>
        <td><strong>For &amp; on behalf of ${company_name}</strong> </td>
        <td></td>
      </tr>


      <!-- 58699 图片-->
      <tr style="vertical-align: bottom">
        <!-- 签字图片 -->
        <td><img style="width: 30mm;height: auto;object-fit: cover;border-bottom: 0.5mm solid #333;padding:0 10mm;"
            src="data:image/png;base64,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"
            alt="" /></td>
        <td><span
            style="width: 30mm;height: auto;object-fit: cover;border-bottom: 0.5mm solid #333;padding:0 10mm;display: inline-block;"></span>
        </td>
      </tr>
      <!-- 58699 签字 -->
      <tr style="vertical-align: bottom">
        <td><span style=" display: inline-table;margin-top: 5mm; width: 100%;">Mr.Ji Kun</span>
          <span style="  display: inline-table;
                width: 100%;">General Manager of F-Commerce</span>
        </td>
        <td><span style=" display: inline-table;margin-top: 5mm; width: 100%;">${call_name} ${interviewer_name}</span>
          <span style="  display: inline-table;
          width: 100%;">${job_title_name}</span>
        </td>
      </tr>

      <tr>
        <td>Date: ${apply_sign_date}</td>
        <td>Date: </td>
      </tr>
    </table>

    <div class="word-span-last">
      <span>Note: Upon receiving an acceptance (signed copy) of this offer from you, we willproceed with your onboarding
        processes and details.</span>
    </div>

    <table class="check-box" border="0">
      <tr style="vertical-align: top">
        <td>
          <span class="check"></span>
        </td>
        <td>
           <span>I hereby declare that every statement given by me is true and correct, is done voluntarily, and I
                     authorize the company to investigate my background check.After any investigation, I also agree that If any
                     false declarations is made by me,I agree not to make any claims against the Company in civil, criminal and
                     labor laws, andmy contact of service or this offer letter may be terminated forthwith without
                     notice.</span>

        <#if renting != "" >
        <div class="word-i">
            <i>
            1: Not provided if accommodation is already provided by Company
            </i>
        </div>
        </#if>

        <#if xinghuo_allowance != "" >
        <div class="word-i">
            <i>
            ${xinghuo_allowance_prefix_num}: Subject to Compony Policy-Unpaid leave, absence from work (unpaid), out of based location, WFH due to home-visits and etc.
            </i>
         </div>
         </#if>

        </td>

      </tr>
    </table>




    <div class="footer">
      <table class="bottom" border="0">
        <tr>
          <td style="text-align: center;">
            <span>${company_address}</span>
          </td>

        </tr>
      </table>
    </div>

  </div>
</body>

</html>