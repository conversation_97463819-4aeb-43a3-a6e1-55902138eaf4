<?php

namespace FlashExpress\bi\App\Server;

use Exception;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Excel;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\HrDeliveryModel;
use FlashExpress\bi\App\Models\backyard\HrEntryModel;
use FlashExpress\bi\App\Models\backyard\HrResumeBackupModel;
use FlashExpress\bi\App\Models\backyard\HrResumeExtendModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\fle\StaffInfoModel;
use FlashExpress\bi\App\Repository\BlacklistRepository;
use FlashExpress\bi\App\Repository\DeliveryRepository;
use FlashExpress\bi\App\Repository\H5appointRepository;
use FlashExpress\bi\App\Repository\InterviewRepository;
use FlashExpress\bi\App\Repository\ResumeBackupRepository;
use FlashExpress\bi\App\Repository\ResumeRepository;
use FlashExpress\bi\App\Repository\SysListRepository;

class DeliveryServer extends BaseServer
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 投递列表
     * @param $parmas
     * @return array
     */
    public function getList($params,$isExport = false)
    {
        //定义分页熟悉
        $pageSize  = empty($params['page_size']) ? 20 : $params['page_size'];
        $pageNum   = empty($params['page_num']) ? 1 : $params['page_num'];
        $pageffset = $pageSize * ($pageNum - 1);

        //生成数据库model对象
        $builder = $this->modelsManager->createBuilder();

        //组装参数
        $builder->from(['delivery' => HrDeliveryModel::class]);
        $builder->leftjoin(HrResumeBackupModel::class," delivery.resume_backup_id = resume_backup.id","resume_backup");

        $builder->columns('count(1) as total');

        //添加where条件
        $builder = $this->formatWhere($builder, $params);

        //设置排序
        $builder->orderBy('delivery.delivery_time desc');

        $totalInfo = $builder->getQuery()->getSingleResult();
        $count     = $totalInfo->total;

        if (!empty($params['is_total'])) {
            return $count;
        }

        $columns = [
            'delivery.id as id',
            'resume_backup.old_resume_id as old_resume_id',
            'delivery.resume_id as resume_id',
            'resume_backup.old_staff_id as old_staff_id',
            'delivery.first_name as first_name',
            'delivery.last_name as last_name',
            'delivery.middle_name as middle_name',
            'delivery.suffix_name as suffix_name',
            'delivery.sex as sex',
            'delivery.phone as phone',
            'delivery.phone_area_code as phone_area_code',
            'delivery.email as email',
            'delivery.job_id as job_id',
            'delivery.address_id as address_id',
            'delivery.work_city_id as work_city_id',
            'delivery.work_district_id as work_district_id',
            'delivery.resume_file_url as resume_file_url',
            'delivery.delivery_time as delivery_time',
            'delivery.create_source as create_source',
            'delivery.status as status',
            'delivery.hire_operate_id as hire_operate_id',
            'delivery.hire_operate_time as hire_operate_time',
        ];

        $builder->limit($pageSize, $pageffset);
        $builder->columns($columns);

        //获取结果
        $items = $builder->getQuery()->execute()->toArray();

        //格式化输出
        $items = $this->formatInfo($items,$isExport);

        $data['data'] = [
            'dataList'   => $items,
            'pagination' => [
                'count'     => (int)$count,
                'pageNum'   => (String)$pageNum, //每页多少条
                'pageSize'  => (String)$pageSize,  //当前页
                'pageCount' => (int)ceil($count / $pageSize),  //总页数
            ],
        ];

        return $this->checkReturn($data);
    }


    /**
     * 组织搜索条件
     * @param $builder
     * @param $params
     * @return mixed
     */
    public function formatWhere($builder, $params)
    {
        $builder->andWhere("delivery.type = :type:", ['type' => HrDeliveryModel::TYPE_RESUME_BACKUP]);

        //旧简历ID
        if (!empty($params['old_resume_id'])) {
            $builder->andWhere("resume_backup.old_resume_id = :old_resume_id:", ['old_resume_id' => $params['old_resume_id']]);
        }

        //简历ID
        if (!empty($params['resume_id'])) {
            $builder->andWhere("delivery.resume_id = :resume_id:", ['resume_id' => $params['resume_id']]);
        }

        //旧工号
        if (!empty($params['old_staff_id'])) {
            $builder->andWhere("resume_backup.old_staff_id = :old_staff_id:", ['old_staff_id' => $params['old_staff_id']]);
        }

        //手机号
        if (!empty($params['phone'])) {
            $builder->andWhere("delivery.phone = :phone:", ['phone' => $params['phone']]);
        }

        //应聘者姓名
        if (!empty($params['name'])) {
            $params['name'] = str_replace(' ','', $params['name']);
            $builder->andWhere("delivery.name LIKE :name:", ['name' => "%{$params['name']}%"]);
        }

        //工作城市
        if (!empty($params['address_id'])) {
            $builder->andWhere("delivery.address_id = :address_id:", ['address_id' => $params['address_id']]);
        }

        //工作城市
        if (!empty($params['work_city_id'])) {
            $params['work_city_id'] = array_values(array_unique($params['work_city_id']));
            $builder->andWhere("delivery.work_city_id IN ({work_city_id:array})", ['work_city_id' => array_values($params['work_city_id'])]);
        }

        //区
        if (!empty($params['work_district_id'])) {
            $params['work_district_id'] = array_values(array_unique($params['work_district_id']));
            $builder->andWhere("delivery.work_district_id IN ({work_district_id:array})", ["work_district_id" => $params['work_district_id']]);
        }

        //岗位
        if (!empty($params['job_id'])) {
            $builder->andWhere("delivery.job_id = :job_id:", ['job_id' => $params['job_id']]);
        }

        //投递日期
        if (!empty($params['delivery_time_start'])) {
            $builder->andWhere("delivery.delivery_time >= :delivery_time_start:", ['delivery_time_start' => zero_time_zone($params['delivery_time_start'].' 00:00:00')]);
        }

        //投递日期
        if (!empty($params['delivery_time_end'])) {
            $builder->andWhere("delivery.delivery_time <= :delivery_time_end:", ['delivery_time_end' => zero_time_zone($params['delivery_time_end'].' 23:59:59')]);
        }

        //状态
        if (!empty($params['status'])) {
            $builder->andWhere("delivery.status = :status:", ['status' => $params['status']]);
        }

        return $builder;
    }

    /**
     * 格式化数据
     * @return array
     */
    public function formatInfo($data , $isExport = false): array
    {
        $sysListRepository = (new SysListRepository());

        $jobIds      = array_column($data, 'job_id');
        $jobIds      = getIdsStr($jobIds);
        $jobListData = $sysListRepository->getJobList(["ids" => $jobIds,]);
        $jobListData = array_column($jobListData, 'job_name', 'job_id');

        //查询城市
        $provinceData = $sysListRepository->getProvinceList();
        $provinceData = array_column($provinceData, null, 'code');

        $cityData = $sysListRepository->getAddressList(['address_type' => 3]);
        $cityData = array_column($cityData, 'name', 'code');

        if (isCountry('MY')) {
            $districtData = $sysListRepository->getAddressList(['address_type' => 4]);
            $districtData = array_column($districtData, 'name', 'code');
        }

        //返聘操作人
        $hireOperateId    = array_column($data, 'hire_operate_id');
        $hireOperateStaff = (new StaffServer())->getStaffListByIds($hireOperateId, ['staff_info_id', 'name']);
        $hireOperateStaff = array_column($hireOperateStaff, 'name', 'staff_info_id');

        //黑名单验证
        $oldStaffListData = $oldResumesData = [];
        if (!$isExport) {
            $oldStaffListIds = array_column($data, 'old_staff_id');
            $oldStaffListIds = array_values(array_filter(array_unique($oldStaffListIds)));
            $oldStaffListData = (new StaffServer())->getStaffListByIds($oldStaffListIds,['staff_info_id','identity']);
            $oldStaffListData = array_column($oldStaffListData, 'identity', 'staff_info_id');

            $oldResumeIds = array_unique(array_filter(array_column($data, 'old_resume_id')));
            $oldResumesData = (new ResumeServer())->getResumeListByIds($oldResumeIds,'id,credentials_num');
            $oldResumesData = array_column($oldResumesData, 'credentials_num', 'id');
        }

        $returnData = [];
        foreach ($data as $v) {
            $item = [];

            $item['id'] = $v['id'] ?? '0';

            //工号
            $item['old_resume_id'] = !empty($v['old_resume_id']) ? $v['old_resume_id'] : '';
            $item['resume_id']     = !empty($v['resume_id']) ? $v['resume_id'] : '';
            $item['old_staff_id']  = !empty($v['old_staff_id']) ? $v['old_staff_id'] : '';

            //名字
            $firstName  = $v['first_name'] ?? '';
            $lastName   = $v['last_name'] ?? '';
            $middleName = $v['middle_name'] ?? '';
            $suffixName = $v['suffix_name'] ?? '';

            $item['name'] = get_show_name($firstName, $lastName,$middleName,$suffixName);

            //性别
            $item['sex']      = $v['sex'] ?? '';
            $item['sex_text'] = enums::$sex_title[$v['sex']] ?? '';

            if (isCountry('PH')) {
                if (strlen($v['phone']) == 11 && strpos($v['phone'], "0") === 0) {
                    $v['phone'] = substr($v['phone'], 1, 11);
                }
            }

            //手机号
            $phone_area_code = !empty($v['phone_area_code']) ? '+' . $v['phone_area_code'] : '+'.get_mobile_area_code();
            $item['phone']   = $phone_area_code . ' ' . $v['phone'];

            //邮箱
            $item['email']   = $v['email'] ?? '';

            //岗位名称
            $item['job_id']   = $v['job_id'] ?? '';
            $item['job_name'] = $jobListData[$v['job_id']] ?? '';

            //工作城市
            $item['address_id']       = $v['address_id'] ?? '';
            $item['work_city_id']     = $v['work_city_id'] ?? '';
            $item['work_district_id'] = $v['work_district_id'] ?? '';

            $provinceName           = $provinceData[$v['address_id']]['name'] ?? '';
            $cityName               = $cityData[$v['work_city_id']] ?? '';

            //马来返回区信息
            if (isCountry('MY')) {
                $districtName = $districtData[$v['work_district_id']] ?? '';
                $item['work_province_city'] = trim($provinceName.' '.$cityName.' '.$districtName);
            } else {
                $item['work_province_city'] = trim($provinceName . ' ' . $cityName);
            }

            //个人简历
            $item['delivery_time'] = show_time_zone($v['delivery_time']);

            //投递来源
            $item['create_source_name'] = $v['create_source'] ? $this->getTranslation()->_('resume_src_'.$v['create_source']) : '';

            //状态
            $item['status']      = $v['status'] ?? '';
            $item['status_name'] = $this->getTranslation()->_(HrDeliveryModel::$status_list[$v['status']]);

            //返聘操作人
            $item['hire_operate_id']   = !empty($v['hire_operate_id']) ? $v['hire_operate_id'] : '';
            $item['hire_operate_name'] = $hireOperateStaff[$v['hire_operate_id']] ?? '';
            $item['hire_operate_time'] = show_time_zone($v['hire_operate_time']);

            //简历附件
            $item['resume_file_url'] = $v['resume_file_url'] ?? '';

            //黑名单
            $item['black_type_name'] = "";


            //检查是否为黑名单
            if (!$isExport) {
                $identity = '';

                if ($item['old_resume_id']) {
                    $identity = $oldResumesData[$item['old_resume_id']] ?? '';
                } elseif ($item['old_staff_id']) {
                    $identity = $oldStaffListData[$item['old_staff_id']] ?? '';
                }

                //检查证件号是否在黑名单
                if (isCountry()) {
                    if ($identity) {
                        $blackList = (new BlackgreylistServer())->checkBlackGreyListRetirement([
                            "identity" => $identity,
                        ]);

                        if ($blackList['is_grey_list']) {
                            $item['black_type_name'] = $this->getTranslation()->_('black_grey_list_behavior_type_2');
                        }
                        $outsourcingBlackList = (new OutsourcingBlackListServer())->check($identity,'winhr',false,$this->lang);
                        if ($outsourcingBlackList['is_black']){
                            $item['black_type_name'] = $this->getTranslation()->_('black_grey_list_behavior_type_3');
                        }

                        if ($blackList['is_black_list']) {
                            $item['black_type_name'] = $this->getTranslation()->_('black_grey_list_behavior_type_1');
                        }
                    }

                } else {
                    $item['black_type_name'] = $this->getTranslation()->_('is_black_no');

                    if ($identity) {
                        $outsourcingBlackList = (new OutsourcingBlackListServer())->check($identity,'winhr',false,$this->lang);
                        if ($outsourcingBlackList['is_black']){
                            $item['black_type_name'] = $this->getTranslation()->_('black_grey_list_behavior_type_3');
                        }
                        
                        $isBlackList = (new BlacklistRepository())->blacklistCheck([
                            "identity" => $identity,
                        ]);

                        if ($isBlackList) {
                            $item['black_type_name'] = $this->getTranslation()->_('is_black_yes');
                        }
                    }
                }
            }

            $returnData[] = $item;
        }

        return $returnData;
    }

    /**
     * 投递导出
     * @param $paramIn
     * @return mixed|string
     * @throws BusinessException
     */
    public function exportList($paramIn)
    {
        $this->userInfo = $paramIn['userInfo'] ?? [];
        $this->lang     = $paramIn['lang'];

        //获取数据
        $paramIn['page_size']      = 1000;
        $paramIn['page_num']       = 1;

        $exportData = [];
        while (true) {
            $returnArr = $this->getList($paramIn,true);

            $list      = $returnArr['data']['dataList'] ?? [];

            if (empty($list)) {
                break;
            }

            foreach ($list as $item) {
                $exportData[] = [
                    $item['old_resume_id'],
                    $item['resume_id'],
                    $item['old_staff_id'],
                    $item['name'],
                    $item['sex_text'],
                    $item['phone'],
                    $item['email'],
                    $item['job_name'],
                    $item['work_province_city'],
                    $item['resume_file_url'],
                    $item['delivery_time'],
                    $item['create_source_name'],
                    $item['status_name'],
                    $item['hire_operate_name'],
                    $item['hire_operate_time'],
                ];
            }

            ++$paramIn['page_num'];
        }

        $t = $this->getTranslation();

        $field = [
            $t->_('delivery.old_resume_id'),         //旧CVID
            $t->_('delivery.resume_id'),             //CVID
            $t->_('delivery.old_staff_id'),          //旧工号
            $t->_('7504'),                           //姓名
            $t->_('7023'),                           //性别
            $t->_('7025'),                           //手机号
            $t->_('8050'),                           //电子邮箱
            $t->_('8051'),                           //期望岗位
            $t->_('8052'),                           //期望城市
            $t->_('4166'),                           //个人简历
            $t->_('delivery.delivery_time'),         //投递时间
            $t->_('delivery.create_source'),         //投递来源
            $t->_('delivery.delivery_status'),       //投递状态
            $t->_('delivery.hire_operate_name'),     //操作人姓名
            $t->_('delivery.hire_operate_time'),     //操作时间
        ];

        //excel数据行
        $filePath = Excel::exportExcel($field, $exportData, $paramIn['file_name'] ?? 'contractList' . time());
        return  $filePath['object_url'] ?? '';
    }

    /**
     * 验证是否可以返聘
     * @return true
     * @throws ValidationException
     */
    public function checkGenerateResume($deliveryId): bool
    {
        //查询数据
        $deliveryInfo = (new DeliveryRepository())->getInfoById($deliveryId);

        if (empty($deliveryInfo)) {
            throw new ValidationException(self::$t->_('operation_data_not_exist'));
        }

        if (empty($deliveryInfo['resume_backup_id'])) {
            throw new ValidationException(self::$t->_('delivery_operate_resume_backup_empty'));
        }

        //查询备份简历
        $resumeBackupInfo = (new ResumeBackupRepository())->getResumeInfoById($deliveryInfo['resume_backup_id']);

        $staffInfo = (new HrStaffInfoModel())->getHrStaffInfo($resumeBackupInfo['old_staff_id'] ?? 0);

        //检查用户相关
        $this->checkStaffInfoByDeliveryInfo($staffInfo);

        if (
            !empty($resumeBackupInfo['old_resume_id'])
        ) {
            $resumeInfo = (new ResumeServer())->getResumeBaseInfo($resumeBackupInfo['old_resume_id']);

            //检查简历相关
            $this->checkResumeInfoByDeliveryInfo($resumeInfo);
        }

        return true;
    }

    /**
     * 检查简历
     * @param $resumeInfo
     * @return true
     * @throws ValidationException
     */
    public function checkResumeInfoByDeliveryInfo($resumeInfo): bool
    {
        // 若旧简历在WHR不存在，toast提示：旧简历不存在，请确认后再操作
        if (empty($resumeInfo)) {
            throw new ValidationException(self::$t->_('delivery_operate_resume_not_exist'));
        }

        //查询面试
        $interviewInfo = (new InterviewServer())->getInterviewInfoByResumeid($resumeInfo['id'],$resumeInfo['hc_id']);

        $state = (new InterviewRepository())->getInterviewState($resumeInfo['filter_state'], $interviewInfo['state'] ?? 0,$resumeInfo["is_out"],$resumeInfo['state_code']);

        //若旧简历的简历状态=待发offer/已发offer，或入职状态=待入职，toast提示：旧简历在招聘流程中，请确认后再操作
        if (in_array($state,[enums::INTERVIEW_ON_BEHALF_OFFER,enums::INTERVIEW_LSSUED_OFFER])) {
            throw new ValidationException(self::$t->_('delivery_operate_resume_not_deleted'));
        }

        //入职状态
        $entryInfo = (new EntryServer())->getInfoByResumeId($resumeInfo['id']);

        if (!empty($entryInfo) && $entryInfo['state'] == HrEntryModel::STATUS_TO_BE_EMPLOYED) {
            throw new ValidationException(self::$t->_('delivery_operate_resume_not_deleted'));
        }

        return true;
    }

    /**
     * 检查用户的
     * @param $staffId
     * @return true
     * @throws ValidationException
     */
    public function checkStaffInfoByDeliveryInfo($staffInfo): bool
    {
        //该员工不存在旧工号，不可返聘
        if (!$staffInfo) {
            throw new ValidationException(self::$t->_('delivery_operate_staff_id_empty'));
        }

        if (empty($staffInfo)) {
            throw new ValidationException(self::$t->_('delivery_operate_staff_info_empty'));
        }

        //在职/停职/待离职
        if ($staffInfo['state'] != HrStaffInfoModel::STAFF_STATE_LEAVE) {
            throw new ValidationException(self::$t->_('delivery_operate_staff_info_state_employed'));
        }

        return true;
    }

    /**
     * 获取投递数量
     * @param array $params
     * @return mixed
     */
    public function getDeliveryNum(array $params = [])
    {
        $bind = [];
        $conditions = '';

        if (!empty($params['phone'])) {
            $bind['phone'] = $params['phone'];
            $conditions .= ' phone = :phone:';
        }

        return HrDeliveryModel::count([
            'conditions' => $conditions,
            'bind'       => $bind,
        ]);
    }

    /**
     * 返聘
     * @param $deliveryId
     * @return bool
     * @throws ValidationException
     * @throws Exception
     */
    public function generateResume($deliveryId): bool
    {
        $this->writeInfoLog('返聘操作开始-'.$deliveryId);

        //查询数据
        $deliveryInfo = (new DeliveryRepository())->getInfoById($deliveryId);

        if (empty($deliveryInfo)) {
            throw new ValidationException(self::$t->_('operation_data_not_exist'));
        }

        if (empty($deliveryInfo['resume_backup_id'])) {
            throw new ValidationException(self::$t->_('delivery_operate_resume_backup_empty'));
        }

        if (
            ($deliveryInfo['status'] != HrDeliveryModel::STATUS_NOT)
            || ($deliveryInfo['type'] != HrDeliveryModel::TYPE_RESUME_BACKUP)
        ) {
            throw new ValidationException(self::$t->_('delivery_operate_error_1'));
        }

        $resumeBackupInfo = (new ResumeBackupRepository())->getResumeInfoById($deliveryInfo['resume_backup_id']);


        $staffInfo = (new HrStaffInfoModel())->getHrStaffInfo($resumeBackupInfo['old_staff_id'] ?? 0);

        //检查用户相关
        $this->checkStaffInfoByDeliveryInfo($staffInfo);

        //用户证件号
        $identity = $staffInfo['identity'] ?? 0;

        $resumeInfo = [];
        if (
            !empty($resumeBackupInfo['old_resume_id'])
        ) {
            $resumeInfo = (new ResumeServer())->getResumeBaseInfo($resumeBackupInfo['old_resume_id']);

            //简历证件号
            $identity = $resumeInfo['credentials_num'] ?? 0;
            $this->checkResumeInfoByDeliveryInfo($deliveryInfo);
        }

        //校验黑名单
        if (isCountry()) {
            $isBlackList = (new BlackgreylistServer())->checkBlackGreyListRetirement([
                "identity" => $identity,
            ]);

            if ($isBlackList["is_black_list"]) {
                throw new ValidationException(self::$t->_('delivery_operate_error_2'));
            }
        } else {
            $isBlackList = (new BlacklistRepository())->blacklistCheck([
                "identity" => $identity,
            ]);

            if ($isBlackList > 0) {
                throw new ValidationException(self::$t->_('delivery_operate_error_2'));
            }
        }
        (new OutsourcingBlackListServer())->check($identity,'winhr',true,$this->lang);

        //开启事务
        $db = $this->getDI()->get('db');
        $db->begin();

        $resumeCopyServer = (new ResumeCopyServer());
        try {

            $optionStaff = [
                'hire_operate_id'   => $this->userInfo['id'],
                'hire_operate_time' => gmdate('Y-m-d H:i:s'),
            ];

            //复制简历
            $newResumeId = 0;

            //从简历库复制
            if (!empty($resumeBackupInfo['old_resume_id'])) {
                if (!$resumeCopyServer->deleteResumeInfo($resumeInfo)) {
                    throw new BusinessException('delivery_operate_error_3');
                }

                //检查手机号是否再次进入简历库
                if ((new ResumeServer())->getResumeInfoByPhone($deliveryInfo['phone'])) {
                    throw new BusinessException(self::$t->_('delivery_operate_error_4'));
                }

                $newResumeId = $resumeCopyServer->copyResumeInfoByResume($deliveryInfo, $resumeInfo);
            } elseif (!empty($resumeBackupInfo['old_staff_id'])) {
                //检查手机号是否重复
                if ((new ResumeServer())->getResumeInfoByPhone($deliveryInfo['phone'])) {
                    throw new BusinessException(self::$t->_('delivery_operate_error_4'));
                }

                $newResumeId = $resumeCopyServer->copyResumeInfoByStaff($deliveryInfo,$staffInfo);
            }

            if (!$newResumeId) {
                throw new Exception(self::$t->_('Resume Save Error!'));
            }

            //附件简历-新CV ID、状态（变更为“已返聘”）、返聘操作人、返聘操作时间
            $resBackup = $db->updateAsDict(
                (new HrResumeBackupModel())->getSource(),
                array_merge($optionStaff, ['status' => HrResumeBackupModel::STATUS_SUCCESS,'resume_id' => $newResumeId]),
                [
                    'conditions' => 'id = ?',
                    'bind'       => [
                        $deliveryInfo['resume_backup_id'],
                    ]
                ]
            );

            if (!$resBackup) {
                throw new Exception('ResumeBackup Update Error!');
            }

            //投递-所有其他关联数据为作废
            $resDelivery = $db->updateAsDict(
                (new HrDeliveryModel())->getSource(),
                ['status' => HrDeliveryModel::STATUS_CANCEL,'resume_id' => $newResumeId],
                [
                    'conditions' => 'resume_backup_id = ?',
                    'bind'       => [
                        $deliveryInfo['resume_backup_id'],
                    ]
                ]
            );

            if (!$resDelivery) {
                throw new Exception('Other Delivery Update Error!');
            }

            //投递-更新本条数据为已返聘
            $resDeliveryOne = (new DeliveryRepository())->update(
                array_merge(
                    $optionStaff,
                    [
                        'id'        => $deliveryId,
                        'resume_id' => $newResumeId,
                        'status'    => HrDeliveryModel::STATUS_SUCCESS,
                    ]
                )
            );

            if (!$resDeliveryOne) {
                throw new Exception('Delivery Update Error!');
            }

            if (!$db->commit()) {
                throw new BusinessException('Commit Error!');
            }
        } catch (BusinessException $exception) {
            $db->rollback();
            $this->writeErrorLog($deliveryId, $exception);
            throw $exception;
        } catch (Exception $exception) {
            $db->rollback();
            $this->writeErrorLog($deliveryId, $exception);
            throw new Exception(self::$t->_('no_server'));
        }

        $this->writeInfoLog('返聘操作成功-'.$deliveryId);
        return true;
    }
}