<?php
namespace FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\Server\BlacklistServer;
use FlashExpress\bi\App\library\Excel;
use Exception;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;


class BlacklistController extends Controllers\ControllerBase
{
    protected $server;

    public function initialize()
    {
        parent::initialize();
        $this->paramIn = $this->request->getPost();
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
    }

    public function onConstruct()
    {
        parent::onConstruct();
    }

    /**
     * 黑名单列表+搜索
     * @Access  public
     * @Param   request
     * @Return  jsonData
     * @Permission(action='Blacklist.blacklist')
     */
    public function blacklistAction()
    {
        $paramIn             = $this->paramIn;
        $paramIn['userinfo'] = $this->userinfo;
    
        $returnArr = (new BlacklistServer())->blacklist($paramIn);
        return $this->jsonReturn($returnArr);
    }

    /**
     * 黑名单列表导出
     * @Access  public
     * @Param   request
     * @Return  jsonData
     * @Permission(action='Blacklist.blacklistExport')
     */
    public function blacklistExportAction()
    {
        $paramIn     = $this->paramIn;
        $returnArr   = (new BlacklistServer())->blacklistExport($paramIn);
        $fileName    = 'blacklist.' . time() . rand(1000, 9999);
        $field       = [
            //【黑名单列表信息】
            $this->getTranslation()->_('7503'),//证件号
            $this->getTranslation()->_('7504'),//姓名
            $this->getTranslation()->_('7505'),//手机号
            $this->getTranslation()->_('7021'),//工号
            $this->getTranslation()->_('7044'),//职位
            $this->getTranslation()->_('7506'),//添加操作人
            $this->getTranslation()->_('7507'),//添加时间
            $this->getTranslation()->_('7508'),//信息来源
            $this->getTranslation()->_('7509'),//说明
            $this->getTranslation()->_('7511'),//移除操作人
            $this->getTranslation()->_('7512'),//移除时间
            $this->getTranslation()->_('7513'),//移除说明
            $this->getTranslation()->_('7514'),//状态

        ];
        $header      = $field;
        $data        = $returnArr;
        $file_path = Excel::exportExcel($header, $data, 'blacklist' . time());
        $fileUrl = $file_path ? $file_path['object_url'] : '';
        $returnData = [
            'code' => 1,
            'msg'  => '',
            'data' => [
                'file_url' => $fileUrl,
            ],
        ];
        return $this->jsonReturn($returnData);
    }

    /**
     * 黑名单添加
     * @Access  public
     * @Param   request
     * @Return  jsonData
     * @Permission(action='Blacklist.blacklistAdd')
     */
    public function blacklistAddAction()
    {
        $paramIn             = $this->paramIn;
        $paramIn['userinfo'] = $this->userinfo;

        $validations = [
            "remark"   => "Required|StrLenGeLe:1, 2000|>>>:" . $this->getTranslation()->_('7520'),
        ];

        if (empty($paramIn['source']) || (!empty($paramIn['source']) && $paramIn['source'] == BlacklistServer::SOURCE_IDENTITY)) {
            $validations['identity']         = "Required|StrLenGeLe:1, 50|>>>:" . $this->getTranslation()->_('7517');
            $validations['name']             = "Required|StrLenGeLe:1, 50|>>>:" . $this->getTranslation()->_('7518');
            $validations['mobile']           = "Required|StrLenGeLe:1, 15|>>>:" . $this->getTranslation()->_('7519');
            $validations['mobile_area_code'] = "Required|StrLenGeLe:1, 20|>>>:" . $this->getTranslation()->_('7519');
        } else {
            $validations['staff_info_id'] = "Required|StrLenGeLe:1, 50|>>>:" . $this->getTranslation()->_('input_staff_id_error');
        }

        $this->validateCheck($paramIn, $validations);

        $returnArr = (new BlacklistServer())->blacklistAdd($paramIn);
        return $this->jsonReturn($returnArr);
    }

    /**
     * 黑名单移除
     * @Access  public
     * @Param   request
     * @Return  jsonData
     * @Permission(action='Blacklist.blacklistRemove')
     */
    public function blacklistRemoveAction()
    {
        $paramIn             = $this->paramIn;
        $paramIn['userinfo'] = $this->userinfo;

        $validations = [
            "id"            => "Required|Int",
            "remove_remark" => "Required|StrLenGeLe:1, 2000|>>>:" . $this->getTranslation()->_('7520'),
        ];
        $this->validateCheck($paramIn, $validations);
        
        $returnArr = (new BlacklistServer())->blacklistRemove($paramIn);
        return $this->jsonReturn($returnArr);
    }

    /**
     * 黑名单检查
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function blacklistCheckAction()
    {
        $paramIn             = $this->paramIn;
        $paramIn['userinfo'] = $this->userinfo;

        $validations = [
            "identity" => "Required|StrLenGeLe:1, 50|>>>:" . $this->getTranslation()->_('7517'),
        ];
        $this->validateCheck($paramIn, $validations);

        $returnArr = (new BlacklistServer())->blacklistCheck($paramIn);
        return $this->jsonReturn($returnArr);
    }

    /**
     * 导入黑名单导入模版
     * @return void
     */
    public function getImportTemplateAction()
    {
        $paramIn             = $this->paramIn;
        $key = empty($paramIn['source']) || (!empty($paramIn['source']) && $paramIn['source'] == BlacklistServer::SOURCE_IDENTITY) ? 'import_blacklist_template' : 'import_blacklist_template_staff';
        // 导入简历的模板连接：中、英
        $result = [
            "data" => [
                "template_url" => $this->getTranslation()->_($key),
            ],
        ];
        $this->jsonReturn($this->checkReturn($result));

    }

    /**
     * 导入黑名单
     * @return void
     * @throws Exception
     * @Permission(action='Blacklist.blacklistImport')
     */
    public function importAction(){
        $params             = $this->paramIn;
        $params['userinfo'] = $this->userinfo;
        $validations = [
            'file_url'    => 'Required|Obj'
        ];
        $this->validateCheck($params, $validations);
        $returnArr = $this->atomicLock(function () use ($params) {
            return (new BlacklistServer())->import($params);
        }, 'blacklist_import', 300);
        if ($returnArr === false) { //没有获取到锁
            $this->jsonReturn(self::checkReturn(-3, $this->getTranslation()->_('5202')));
        }
        $this->jsonReturn($returnArr);
    }


}