<?php

namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\Helper\InterviewHelper;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\ErrCodeEnums;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\BackyardBaseModel;
use Exception;
use FlashExpress\bi\App\Models\backyard\HrHcLogModel;
use FlashExpress\bi\App\Models\backyard\HrhcModel;
use FlashExpress\bi\App\Models\backyard\HrInterviewOfferSignApproveModel;
use FlashExpress\bi\App\Models\backyard\HrJobDepartmentRelationModel;
use FlashExpress\bi\App\Models\backyard\SalaryApproveModel;
use FlashExpress\bi\App\Repository\HcRepository;
use FlashExpress\bi\App\Repository\InterviewRepository;

/**
 * 面试-hc管理类
 */
class InterviewHcServer extends BaseServer
{
    /**
     * @param string $lang
     */
    public function __construct($lang = 'en')
    {
        parent::__construct($lang);
    }


    /**
     * hc替换
     * @param $param
     * @return array
     * @throws ValidationException
     * @throws Exception
     */
    public function updateResumeHc($param): array
    {
        $hcId          = $param['hc_id'] ?? 0;
        $interviewId   = $param['interview_id'] ?? 0;
        $resumeId      = $param['resume_id'] ?? 0;
        $jobTitleId    = $param['job_title_id'] ?? 0;
        $staff_info_id = $this->userInfo['id'] ?? 0;

        if (!$hcId || !$resumeId || !$jobTitleId || !$interviewId) {
            throw new ValidationException(self::$t->_('operation_data_not_exist'));
        }

        $interviewServer  = new InterviewServer();
        $hcRepository     = new HcRepository();
        $resumeServer     = new ResumeServer();

        //基础条件验证(不走事务，不锁定hc数量) ---------------------------START----------------------------------------------
        $hcInfo = HrhcModel::findFirst([
            'conditions' => 'hc_id = :hc_id:',
            'bind' => [
                'hc_id' => $hcId,
            ],
        ]);

        $hcInfo = $hcInfo ? $hcInfo->toArray() : [];

        //获取多个配置文件
        $updateHcSetting = (new SettingEnvServer())->getMultiEnvByCode(['replace_hc_jobids', 'replace_hc_hire_type', 'replace_hc_staff_ids']);
        $updateHcSettingParams = [
            'setting_job_title'     => $updateHcSetting['replace_hc_jobids'] ? explode(',', $updateHcSetting['replace_hc_jobids']) : [],
            'setting_hire_type'     => $updateHcSetting['replace_hc_hire_type'] ? explode(',', $updateHcSetting['replace_hc_hire_type']) : [],
            'setting_staff_info_id' => $updateHcSetting['replace_hc_staff_ids'] ? explode(',', $updateHcSetting['replace_hc_staff_ids']) : [],
        ];

        //验证是否可以编辑简历hc
        $updateHcSettingParams['is_first_line_job'] = empty($hcInfo['hc_id']) ? 0 : $interviewServer->isFirstLineJob($hcInfo['department_id'], $hcInfo['job_title']);
        $updateHcSettingParams['staff_info_id']     = $this->userInfo['id'] ?? 0;
        $updateHcSettingParams['job_title']         = $hcInfo['job_title'] ?? 0;
        $updateHcSettingParams['hire_type']         = $hcInfo['hire_type'] ?? 0;

        //验证是否符合配置的一线职位
        if (!InterviewHelper::isUpdateResumeHc($updateHcSettingParams)) {
            throw new ValidationException(self::$t->_('update_resume_hc_setting_inconsistent'));
        }

        //验证是否是不同职位进行更换
        if (!in_array($jobTitleId,$updateHcSettingParams['setting_job_title'])) {
            throw new ValidationException(self::$t->_('update_resume_hc_job_title_not_setting'));
        }

        //验证是否是不同职位进行更换
        if ($hcInfo['job_title'] == $jobTitleId) {
            throw new ValidationException(self::$t->_('update_resume_hc_job_title_repeat'));
        }

        //获取部门的oa职位绑定信息
        $hrJobRelation = HrJobDepartmentRelationModel::findFirst([
            'conditions' => 'department_id = :department_id: and job_id = :job_id:',
            'bind' => [
                'department_id' => $hcInfo['department_id'],
                'job_id' => $jobTitleId,
            ]
        ]);

        if (empty($hrJobRelation)) {
            throw new ValidationException(self::$t->_('update_resume_hc_job_title_not_oa_setting'));
        }

        $hrJobRelation = $hrJobRelation->toArray();

        //薪资审批验证
        $approval = $interviewServer->getLastApprove($resumeId);

        if (!empty($approval) && in_array($approval['status'],[enums::APPRAVE_STATE_REVIEWED,enums::APPRAVE_STATE_APPROVED])) {
            throw new ValidationException(self::$t->_('update_resume_hc_approve_reviewed_error'));
        }

        //offer签字验证
        $offerSign = (new InterviewRepository())->getLastOfferSignData($resumeId);
        if (!empty($offerSign) && in_array($offerSign['approve_state'],[enums::APPRAVE_STATE_REVIEWED,enums::APPRAVE_STATE_APPROVED])) {
            throw new ValidationException(self::$t->_('update_resume_hc_approve_reviewed_error'));
        }

        //基础验证 ----------------------------------------------------END-----------------------------------------------

        //开启事务
        $db = BackyardBaseModel::beginTransaction($this);

        try {
            //面试验证
            $interviewInfo = (new InterviewRepository())->getInfoByInterviewId($interviewId);

            if (!$interviewInfo || ($interviewInfo['state'] != InterviewRepository::INTERVIEW_ON_BEHALF_OFFER)) {
                throw new BusinessException(self::$t->_('update_resume_hc_interview_state_error'));
            }

            if ($interviewInfo['hc_id'] != $hcId) {
                throw new BusinessException(self::$t->_('update_resume_hc_interview_hc_error'));
            }

            //简历验证
            $resumeInfo = $resumeServer->getResumeBaseInfo($resumeId);

            if (!$resumeInfo || $resumeInfo['hc_id'] != $hcId) {
                throw new BusinessException(self::$t->_('update_resume_hc_resume_hc_error'));
            }

            //验证hc且锁定数据
            $hcInfo = HrhcModel::findFirst([
                'conditions' => 'hc_id = :hc_id:',
                'bind'       => [
                    'hc_id' => $hcId,
                ],
                'for_update' => true,
            ]);

            if (empty($hcInfo) || ($hcInfo->state_code != HrhcModel::STATE_RECRUITING)) {
                throw new BusinessException(self::$t->_('update_resume_hc_hc_state_error'));
            }

            if ($hcInfo->surplusnumber < 1 || $hcInfo->demandnumber < 1) {
                throw new BusinessException(self::$t->_('update_resume_hc_hc_demandnumber_error'));
            }

            $beforeHcInfo = $hcInfo->toArray();

            //记录hc更新日志相关日志
            $insertHcLog = [
                'hc_id'                   => $hcId,
                'before_demandnumber'     => $beforeHcInfo['demandnumber'],
                'before_priority_id'      => $beforeHcInfo['priority_id'],
                'after_priority_id'       => $beforeHcInfo['priority_id'],
                'staff_info_id'           => $staff_info_id,
                'created_at'              => gmdate('Y-m-d H:i:s'),
                'before_data'             => json_encode($beforeHcInfo, JSON_UNESCAPED_UNICODE),
                'is_save_number_priority' => HrHcLogModel::$is_save_number_priority_2,
                'type'                    => HrHcLogModel::TYPE_DEFAULT,
            ];

            if ($hcInfo->demandnumber == 1) {
                $hcInfo->state_code = HrhcModel::STATE_VOIDED;
            } elseif ($hcInfo->surplusnumber == 1) {
                $hcInfo->state_code = HrhcModel::STATE_FULL_RECRUITMENT;
            }

            //hc扣减
            $hcInfo->demandnumber = new \Phalcon\Db\RawValue('demandnumber - 1');
            $hcInfo->surplusnumber = new \Phalcon\Db\RawValue('surplusnumber - 1');

            if (!$hcInfo->save()) {
                throw new BusinessException(self::$t->_('server_error'),ErrCodeEnums::HC_UPDATE_CODE_HC_UPDATE);
            }

            //查询hc更新后的数据
            $afterHcInfo = $hcInfo->toArray();

            $afterHcInfo['demandnumber']       = $beforeHcInfo['demandnumber'] - 1;
            $afterHcInfo['surplusnumber']      = $beforeHcInfo['surplusnumber'] - 1;
            $insertHcLog['after_demandnumber'] = $afterHcInfo['demandnumber'];
            $insertHcLog['after_data']         = json_encode($afterHcInfo, JSON_UNESCAPED_UNICODE);

            $insertHcLogData = [];

            //记录作废日志
            if ($beforeHcInfo['state_code'] != $afterHcInfo['state_code'] && $afterHcInfo['state_code'] == HrhcModel::STATE_VOIDED) {
                $insertHcLog['type'] = HrHcLogModel::TYPE_STATE_CODE_VOID;
                $insertHcLogData[]  = $insertHcLog;
            }

            //记录扣减日志
            if ($beforeHcInfo['demandnumber'] != $afterHcInfo['demandnumber']) {
                $insertHcLog['type'] = HrHcLogModel::TYPE_DEMANDNUMBER;
                $insertHcLogData[]  = $insertHcLog;
            }

            //批量插入hc相关日志
            $hcRepository->batch_insert('hr_hc_log', $insertHcLogData);

            $newInfo = array_merge($afterHcInfo, [
                'jd_id'                 => $hrJobRelation['jd_id'],
                'working_day_rest_type' => $hrJobRelation['working_day_rest_type'],
                'job_title'             => $jobTitleId,
                'submitter_id'          => $staff_info_id,
            ]);

            //组装新hc的数据
            $newHcData = $this->formatNewHcData($newInfo);
            $this->logger->info('updateResumeHc newData :'.json_encode($newHcData));

            //生成新的hc
            if (!$db->insertAsDict('hr_hc', $newHcData)) {
                throw new BusinessException(self::$t->_('server_error'), ErrCodeEnums::HC_UPDATE_CODE_HC_SAVE);
            }

            $newHcId = $db->lastInsertId();

            if (!$newHcId) {
                throw new BusinessException(self::$t->_('server_error'), ErrCodeEnums::HC_UPDATE_CODE_HC_SAVE);
            }

            //更新简历
            $resumeRes = $db->updateAsDict(
                'hr_resume',
                ['hc_id' => $newHcId],
                ['conditions' => 'id = ?', 'bind' => [$resumeId]]
            );

            if (!$resumeRes){
                throw new BusinessException(self::$t->_('server_error'),ErrCodeEnums::HC_UPDATE_CODE_RESUME_UPDATE);
            }

            //记录简历更换hc日志
            (new LogServer())->addLog([
                'module_id'    => $resumeId,
                'module_type'  => enums::$log_module_type['hc_id'],
                'action'       => enums::$log_option['modify'],
                'module_level' => $newHcId,
                'data_after'   => ['hc_id' => $newHcId],
            ]);

            //更新面试相关
            $interviewRes = $db->updateAsDict(
                'hr_interview',
                ['hc_id' => $newHcId],
                ['conditions' => 'interview_id = ?', 'bind' => [$interviewId]]
            );

            if (!$interviewRes) {
                throw new BusinessException(self::$t->_('server_error'),ErrCodeEnums::HC_UPDATE_CODE_INTERVIEW_UPDATE);
            }

            $interviewInfo =$db->updateAsDict(
                'hr_interview_info',
                ['hc_id' => $newHcId],
                ['conditions' => 'interview_id = ?','bind' => [$interviewId],]
            );

            if (!$interviewInfo) {
                throw new BusinessException(self::$t->_('server_error'),ErrCodeEnums::HC_UPDATE_CODE_INTERVIEW_INFO_UPDATE);
            }

            $interviewSubscribe = $db->updateAsDict(
                'hr_interview_subscribe',
                ['hc_id' => $newHcId],
                ['conditions' => 'interview_id = ?', 'bind' => [$interviewId]]
            );

            if (!$interviewSubscribe) {
                throw new BusinessException(self::$t->_('server_error'), ErrCodeEnums::HC_UPDATE_CODE_INTERVIEW_SUB_UPDATE);
            }

            //进行打分操作
            (new ResumeAiServer())->addResumeAiScore([
                'hc_id'     => $newHcId,
                'resume_id' => $resumeId,
            ]);

            if (!$db->commit()) {
                throw new Exception(self::$t->_('server_error'));
            }

            return $this->checkReturn(['data' => ['hc_id' => $newHcId]]);
        } catch (BusinessException $e) {
            $db->rollBack();
            $this->logger->error('updateResumeHc Error message:'.$e->getMessage().' code:'.$e->getCode().' trace:'.$e->getTraceAsString());
            throw $e;
        } catch (Exception $e) {
            $db->rollBack();
            throw $e;
        }
    }

    /**
     * 格式化新的hc数据
     * @param $afterHcInfo
     * @return array
     */
    public function formatNewHcData($afterHcInfo): array
    {
        $returnData = [];

        $returnData['serial_no']           = $this->getRandomId();
        $returnData['department_id']       = $afterHcInfo['department_id'];
        $returnData['type']                = $afterHcInfo['type'];
        $returnData['worknode_id']         = $afterHcInfo['worknode_id'];
        $returnData['expirationdate']      = $afterHcInfo['expirationdate'];
        $returnData['demandnumber']        = 1;
        $returnData['surplusnumber']       = 1;
        $returnData['submitter_id']        = $afterHcInfo['submitter_id'];
        $returnData['country_code']        = $afterHcInfo['country_code'];
        $returnData['province_code']       = $afterHcInfo['province_code'];
        $returnData['city_code']           = $afterHcInfo['city_code'];
        $returnData['district_code']       = $afterHcInfo['district_code'];
        $returnData['state_code']          = HrhcModel::STATE_RECRUITING;
        $returnData['approval_state_code'] = HrhcModel::APPROVAL_STATE_CODE_AGREE;
        $returnData['priority_id']         = $afterHcInfo['priority_id'];
        $returnData['reason_type']         = $afterHcInfo['reason_type'];
        $returnData['hire_type']           = $afterHcInfo['hire_type'];
        $returnData['hire_times']          = $afterHcInfo['hire_times'];
        $returnData['job_title']           = $afterHcInfo['job_title'];
        $returnData['language_ability']    = $afterHcInfo['language_ability'];

        //working_day_rest_type获取关联表的
        if (!empty($afterHcInfo['working_day_rest_type'])) {
            $returnData['working_day_rest_type'] = $afterHcInfo['working_day_rest_type'];
        }

        //jd_id获取关联表的
        if (!empty($afterHcInfo['jd_id'])) {
            $returnData['job_id'] = $afterHcInfo['jd_id'];
        }

        return $returnData;
    }
}