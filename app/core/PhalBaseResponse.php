<?php


namespace FlashExpress\bi\App\Core;


use Phalcon\Http\Response;

class PhalBaseResponse extends Response
{
    const OK                    = 200;
    const CREATED               = 201;
    const ACCEPTED              = 202;
    const MOVED_PERMANENTLY     = 301;
    const FOUND                 = 302;
    const TEMPORARY_REDIRECT    = 307;
    const PERMANENTLY_REDIRECT  = 308;
    const BAD_REQUEST           = 400;
    const UNAUTHORIZED          = 401;
    const FORBIDDEN             = 403;
    const NOT_FOUND             = 404;
    const INTERNAL_SERVER_ERROR = 500;
    const NOT_IMPLEMENTED       = 501;
    const BAD_GATEWAY           = 502;


    const RESPONSE_TYPE_ERROR = 'error';
    const RESPONSE_TYPE_SUCCESS = 'success';
    const RESPONSE_TYPE_FAIL = 'fail';


    private static $messages = [
        200 => 'OK',
        301 => 'Moved Permanently',
        302 => 'Found',
        307 => 'Temporary Redirect',
        308 => 'Permanent Redirect',
        400 => 'Bad Request',
        401 => 'Unauthorized',
        403 => 'Forbidden',
        404 => 'Not Found',
        500 => 'Internal Server Error',
        501 => 'Not Implemented',
        502 => 'Bad Gateway',
    ];

    /**
     * @param $code
     * @return string
     */
    public function getMessageFromCode($code)
    {
        return self::$messages[$code] ?? '';
    }
    /**
     * <AUTHOR> Olaoye <<EMAIL>>
     * @param $code
     * @param int|string $http_status_code
     * @param null $message
     * @return mixed
     */
    public function sendError($code, $http_status_code = 500, $message = null)
    {
        $response = array(
            'status' => self::RESPONSE_TYPE_ERROR,
            'message' => (is_null($message)) ? $this->getMessageFromCode($code) : $message,
            'code' => $code
        );

        $this->setStatusCode($http_status_code,$this->getMessageFromCode($code));
        $this->setJsonContent($response);
        return $this->sendResponse();
    }


    /**
     * <AUTHOR> Olaoye <<EMAIL>>
     * @param $data
     * @return mixed
     */
    public function sendSuccess($data)
    {
        $this->setStatusCode(self::OK, $this->getMessageFromCode(self::OK));
        $this->setJsonContent(array(
            'status' => self::RESPONSE_TYPE_SUCCESS,
            'data' => $data
        ));

        return $this->sendResponse();
    }

    /**
     * <AUTHOR> Olaoye <<EMAIL>>
     * @param $data
     * @param int|string $http_status_code
     * @return mixed|void
     */
    public function sendFail($data, $http_status_code = 500)
    {
        $this->setStatusCode($http_status_code, $this->getMessageFromCode($http_status_code));
        $this->setJsonContent(array(
            'status' => self::RESPONSE_TYPE_FAIL,
            'data' => $data
        ));

        $this->sendResponse();
    }

    /**
     * Send response
     * <AUTHOR> Olaoye <<EMAIL>>
     */
    public function sendResponse()
    {
        $this->setContentType("application/json");
        if (!$this->isSent()) {
            $this->send();
        }
    }
}