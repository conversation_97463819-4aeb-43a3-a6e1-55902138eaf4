<?php

namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\Helper\ResumeHelper;
use FlashExpress\bi\App\library\ApiClient;
use FlashExpress\bi\App\library\DateHelper;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\EnumSingleton;
use FlashExpress\bi\App\library\ErrCodeEnums;
use FlashExpress\bi\App\library\Exception\BusinessException;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\library\ImageMosaic;
use FlashExpress\bi\App\library\RedisListEnums;
use FlashExpress\bi\App\library\RocketMQ;
use FlashExpress\bi\App\library\Tools;
use FlashExpress\bi\App\Models\backyard\BackyardBaseModel;
use FlashExpress\bi\App\Models\backyard\BankListModel;
use FlashExpress\bi\App\Models\backyard\CommunicationResumeLogModel;
use FlashExpress\bi\App\Models\backyard\HrAnnexModel;
use FlashExpress\bi\App\Models\backyard\HrBlackGreyListModel;
use FlashExpress\bi\App\Models\backyard\HrEconomyAbilityModel;
use FlashExpress\bi\App\Models\backyard\HrEntryModel;
use FlashExpress\bi\App\Models\backyard\HrFamilyModel;
use FlashExpress\bi\App\Models\backyard\HrhcModel;
use FlashExpress\bi\App\Models\backyard\HrInterestedDepartmentModel;
use FlashExpress\bi\App\Models\backyard\HrInterviewerOperationModel;
use FlashExpress\bi\App\Models\backyard\HrInterviewModel;
use FlashExpress\bi\App\Models\backyard\HrInterviewOfferModel;
use FlashExpress\bi\App\Models\backyard\HrInterviewOfferSignApproveModel;
use FlashExpress\bi\App\Models\backyard\HrInterviewPassTypeRecordModel;
use FlashExpress\bi\App\Models\backyard\HrInterviewRecordModel;
use FlashExpress\bi\App\Models\backyard\HrInterviewSubscribeModel;
use FlashExpress\bi\App\Models\backyard\HrJdModel;
use FlashExpress\bi\App\Models\backyard\HrLogModel;
use FlashExpress\bi\App\Models\backyard\HrOfferDefaultSalaryModel;
use FlashExpress\bi\App\Models\backyard\HrQuestionsModel;
use FlashExpress\bi\App\Models\backyard\HrRecruitChannelsModel;
use FlashExpress\bi\App\Models\backyard\HrResumeAiScoreModel;
use FlashExpress\bi\App\Models\backyard\HrResumeFilterModel;
use FlashExpress\bi\App\Models\backyard\HrResumeModel;
use FlashExpress\bi\App\Models\backyard\HrSchoolModel;
use FlashExpress\bi\App\Models\backyard\HrStaffManageStoreModel;
use FlashExpress\bi\App\Models\backyard\OfferSendSalaryModel;
use FlashExpress\bi\App\Models\backyard\ResumeOutLogModel;
use FlashExpress\bi\App\Models\backyard\SalaryApproveAllowanceModel;
use FlashExpress\bi\App\Models\backyard\SalaryApproveModel;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;
use FlashExpress\bi\App\Models\backyard\HrStaffItemsModel;
use FlashExpress\bi\App\Models\backyard\StaffInfoAuditCheckModel;
use FlashExpress\bi\App\Models\backyard\SysProvinceModel;
use FlashExpress\bi\App\Models\backyard\SysStoreModel;
use FlashExpress\bi\App\Models\backyard\VanContainerModel;
use FlashExpress\bi\App\Models\HrResumeBuddyModel;
use FlashExpress\bi\App\Models\HrResumeEducationModel;
use FlashExpress\bi\App\Repository\BlacklistRepository;
use FlashExpress\bi\App\Repository\CustomerresumeRepository;
use FlashExpress\bi\App\Repository\DepartmentRepository;
use FlashExpress\bi\App\Repository\EntryRepository;
use FlashExpress\bi\App\Repository\H5appointRepository;
use FlashExpress\bi\App\Repository\HcRepository;
use FlashExpress\bi\App\Repository\HrStaffAnnexInfoRepository;
use FlashExpress\bi\App\Repository\HrStaffContractRepository;
use FlashExpress\bi\App\Repository\InterviewRepository;
use FlashExpress\bi\App\Repository\MessageRepository;
use FlashExpress\bi\App\Repository\OfferRepository;
use FlashExpress\bi\App\Repository\PermissionsRepository;
use FlashExpress\bi\App\Repository\ResumeExtendRepository;
use FlashExpress\bi\App\Repository\ResumeRepository;
use FlashExpress\bi\App\Repository\StaffRepository;
use FlashExpress\bi\App\Repository\StaffSupportRepository;
use FlashExpress\bi\App\Repository\StaffWorkAttendanceAttachmentRepository;
use FlashExpress\bi\App\Repository\SysListRepository;
use FlashExpress\bi\App\Repository\TrainRepository;
use Phalcon\Db\Column;
use Phalcon\DiInterface;
use Exception;
use ZipArchive;
use FlashExpress\bi\App\Models\backyard\HrBlacklistModel;
use FlashExpress\bi\App\Models\backyard\HrStaffAnnexInfoModel;
use FlashExpress\bi\App\Models\backyard\HrResumeExtendModel;

use function Hprose\Future\isFuture;

class ResumeServer extends BaseServer
{

    protected $other_remark_info = [];

    public function setOtherRemarkInfo($remark){
        $this->other_remark_info  = $remark;
    }

    public function setUserInfo($userInfo)
    {
        $this->userInfo = $userInfo;
    }


    protected $department;
    public    $timezone;
    public    $sysList;
    public    $resume;
    public    $staff;
    public    $h5appointServer;
    public    $log;
    public    $blacklist;

    public static $networkFirstLineJD = [];//settingenv中配置的network一线岗位ID
    public static $lntJD = [];//lnt公司jd,简历字段同一线岗位
    const RESUME_CALL_NAME_MR = 1; //简历称呼-先生
    const RESUME_SEX_MALE = 1; //简历性别-男性
    const RESUME_SEX_FEMALE = 2; //简历性别-女性
    const RESUME_SEX_MISS = 3; //性别称呼-小姐

    const RECOMMEND_MSG_TYPE = 56; //简历筛选-推荐消息类型
    const RECOMMEND_CANCEL_MSG_TYPE = 57; //简历筛选-取消推荐消息类型
    const AI_IDENTITY_FAIL_NUM = 3;//ai 身份证识别打码失败次数

    const SALARY_COURIER_MAPS = [
        'TH20' => [
            'base'      => 1008000,
            'allowance' => [
                110 => 42000,
                13  => 10000,
            ],
        ],
        'TH67' => [
            'base'      => 1008000,
            'allowance' => [
                110 => 42000,
                13  => 10000,
            ],
        ],
        'TH21' => [
            'base'      => 1005000,
            'allowance' => [
                110 => 42000,
                13  => 10000,
            ],
        ],
        'TH01' => [
            'base'      => 993000,
            'allowance' => [
                110 => 42000,
                13  => 10000,
            ],
        ],
        'TH15' => [
            'base'      => 993000,
            'allowance' => [
                110 => 42000,
                13  => 8000,
            ],
        ],
        'TH16' => [
            'base'      => 993000,
            'allowance' => [
                110 => 42000,
                13  => 8000,
            ],
        ],
        'TH02' => [
            'base'      => 993000,
            'allowance' => [
                110 => 42000,
                13  => 8000,
            ],
        ],
        'TH03' => [
            'base'      => 993000,
            'allowance' => [
                110 => 42000,
                13  => 8000,
            ],
        ],
        'TH04' => [
            'base'      => 993000,
            'allowance' => [
                110 => 42000,
                13  => 8000,
            ],
        ],
        'TH68' => [
            'base'      => 990000,
            'allowance' => [
                110 => 42000,
                13  => 10000,
            ],
        ],
        'TH00' => [
            'base'      => 990000,
            'allowance' => [
                110 => 42000,
                13  => 8000,
            ],
        ],
    ];


    /**
     * 沟通记录
     *
     */
    const COMMUNICATE_STATE_SUCCESS = 1;
    const COMMUNICATE_STATE_FAIL = 2;

    const COMMUNICATE_STATE_MAPS = [
        self::COMMUNICATE_STATE_FAIL    => '沟通失败',
        self::COMMUNICATE_STATE_SUCCESS => '沟通成功',
    ];

    const NOT_MODIFY = 2;
    const SUCC_MODIFY = 1;


    // 区域定义
    public $areas = [
        1 => 'BKK',
        2 => 'Cen+East',
        3 => 'Cen+West',
        4 => 'North',
        5 => 'North East',
        6 => 'South',
        7 => 'Cen',
    ];

    public function __construct($lang = 'en')
    {
        parent::__construct($lang);
    }


    /**
     * [createReserveAction 创建简历]
     * @param $paramIn
     * @return array [type] [array]
     * @throws BusinessException
     * @throws ValidationException
     * <AUTHOR> <[email address]>
     */
    public function reserveCreate($paramIn = [])
    {
        $this->staff = new StaffRepository();
        $this->log   = new LogServer();

        $resumeParam['job_id']               = $this->processingDefault($paramIn, 'job_id', 2);
        $resumeParam['alternative_job_ids']  = $this->processingDefault($paramIn, 'alternative_job_ids');
        $resumeParam['address_id']           = $this->processingDefault($paramIn, 'address_id');
        $resumeParam['work_city_id']         = $this->processingDefault($paramIn, 'work_city_id');    //期望工作地-市
        $resumeParam['work_district_id']     = $this->processingDefault($paramIn, 'work_district_id');    //期望工作地-区
        $resumeParam['entry_salary']         = $this->processingDefault($paramIn, 'entry_salary', 2);
        $resumeParam['call_name']            = $this->processingDefault($paramIn, 'call_name');
        $resumeParam['first_name']           = $this->processingDefault($paramIn, 'first_name');
        $resumeParam['last_name']            = $this->processingDefault($paramIn, 'last_name');
        $resumeParam['first_name_en']        = $this->processingDefault($paramIn, 'first_name_en');
        $resumeParam['last_name_en']         = $this->processingDefault($paramIn, 'last_name_en');
        $resumeParam['name']                 = str_replace(" ", '', $resumeParam['first_name']) . str_replace(" ", '', $resumeParam['last_name']);//$this->processingDefault($paramIn, 'name');
        $resumeParam['phone_area_code']      = $this->processingDefault($paramIn, 'phone_area_code');
        $resumeParam['email']                = $this->processingDefault($paramIn, 'email');
        $resumeParam['phone']                = $this->processingDefault($paramIn, 'phone');
        $resumeParam['submitter_id']         = $this->processingDefault($paramIn, 'submitter_id');
        $resumeParam['credentials_num']      = $this->processingDefault($paramIn, 'credentials_num');
        $resumeParam['credentials_category'] = $this->processingDefault($paramIn, 'credentials_category');
        $resumeParam['recruit_channel']      = $this->processingDefault($paramIn, 'recruit_channel',2);
        $resumeParam['date_birth']           = $this->processingDefault($paramIn, 'date_birth');
        $resumeParam['nickname']             = $this->processingDefault($paramIn, 'nickname');
        $resumeParam['hc_id']                = $this->processingDefault($paramIn, 'hc_id', 2);
        $resumeParam['recruiter_id']         = $this->processingDefault($paramIn, 'recruiter_id', 2);
        $resumeParam['car_type']             = $this->processingDefault($paramIn, 'car_type', 2);
        $resumeParam['is_initiative']        = $this->processingDefault($paramIn, 'is_initiative', 2,2);
        $resumeParam['sex']                  = $this->getSex($paramIn);

        if (isset($paramIn['current_salary'])) {
            $resumeParam['current_salary'] = $paramIn['current_salary'];
        }

        if(isCountry('MY')){
            $resumeParam['resume_last_operator'] = $this->userInfo['id'];
            $resumeParam['resume_last_operation_time'] = gmdate('Y-m-d H:i:s');
            $resumeParam['nationality'] = $paramIn['nationality'] ?? null;
        }
        if (isCountry('MY') && !empty($resumeParam['name'])){
            $resumeParam['name'] = $this->nameToUpper($resumeParam['name']);
        }
        if (isCountry('MY') && !empty($resumeParam['first_name'])){
            $resumeParam['first_name'] = $this->nameToUpper($resumeParam['first_name']);
        }

        //简历附件
        $file_url = $this->processingDefault($paramIn, 'file_url', 3);


        // 验证是否有相关 职务
        $isJob = (new ResumeRepository())->isJob($paramIn['job_id']);
        if ($isJob === false) {
            return $this->checkReturn(-3, $this->getTranslation()->_('4105'));
        }
        // 验证录入人员
        $checkoutStaff = $this->staff->checkoutStaff($paramIn['submitter_id']);
        if ($checkoutStaff === false) {
            return $this->checkReturn(-3, $this->getTranslation()->_('4106'));
        }
        // 验证是否重复
        (new ResumeServer())->checkCredentialsNumANDPhoneNum($resumeParam['credentials_num'], $paramIn['phone'], 'whr');

        (new OutsourcingBlackListServer())->check($resumeParam['credentials_num'] ?? '','winhr',true,$this->lang);
//        if (!(new ResumeRepository())->isPhoneRepeat($paramIn['phone'], $paramIn['name'], $resumeParam['credentials_num']))
//            return $this->checkReturn(-3, $this->getTranslation()->_('4107'));

        //插入解析后的其他数据
        $db = $this->getDI()->get('db');
        $db->begin();

        try {
            // 开始创建
            $cvData = (new ResumeRepository())->createCv($resumeParam);

            //添加创建简历日志
            $this->log->addLog([
                'module_id'   => $cvData,
                'module_type' => enums::$log_module_type['resume'],
                'action'      => enums::$log_option['create'],
                'data_after'  => $resumeParam,
            ]);

            if (!$cvData) {
                throw new BusinessException($this->getTranslation()->_('4101'));
            }

            if (!empty($paramIn['resume_ai_parse_id'])) {
                $insParseData = [
                    'resume_ai_parse_id' => $paramIn['resume_ai_parse_id'],
                    'resume_id'          => $cvData,
                ];

                if (!(new ResumeAiServer())->updateResumeAiByParseId($insParseData)) {
                    $this->logger->write_log([
                        'title'  => '简历创建',
                        'opt'    => 'AI解析数据插入错误',
                        'func'   => 'reserveCreate',
                        'params' => $insParseData,
                    ]);
                }
            }

            if (!empty($resumeParam['hc_id'])) {
                (new ResumeAiServer())->addResumeAiScore([
                    'hc_id'     => $paramIn['hc_id'],
                    'resume_id' => $cvData,
                ]);
            }

            //更新AI业务表
            $res = $db->commit();

            if (!$res) {
                throw new Exception($this->getTranslation()->_('4101'));
            }

        } catch (Exception $e) {
            $db->rollback();
            throw $e;
        }

        //个人简历附件上传
        if ($file_url) {
            (new ResumeRepository())->createfile($cvData, $file_url);
        }

        // 数据返回
        $returnData['data'] = ['cv_id' => $cvData];

        if ($resumeParam['hc_id']) {
            //执行关联
            $this->log->addLog([
                'module_id'    => $cvData,
                'module_type'  => enums::$log_module_type['hc_id'],
                'action'       => enums::$log_option['relate'],
                'module_level' => $resumeParam['hc_id'],
                'data_after'   => ['hc_id' => $resumeParam['hc_id']],
            ]);
        }
        if ($resumeParam['recruiter_id']) {
            $this->log->addLog([
                'module_id'   => $cvData,
                'module_type' => enums::$log_module_type['recruiter_id'],
                'action'      => enums::$log_option['modify'],
                'data_after'  => ['recruiter_id' => $resumeParam['recruiter_id']],
            ]);
        }

        return $this->checkReturn($returnData);
    }
    
    public function nameToUpper($name)
    {
        if (empty($name) || !isCountry('MY')){
            return $name;
        }
        $name = strtoupper(trim($name));
        $specials = array(
            '。'=>'.',
            '，'=>',',
            '、'=>'.',
            '；'=>';',
            '：'=>':',
            '“'=>'"',
            '”'=>'"',
            '’'=>'\'',
            '‘'=>'\'',
            '（'=>'(',
            '）'=>')',
            '【'=>'[',
            '】'=>']',
            '＠'=>'@',
            '？'=>'?',
            '！'=>'!',
            '＃'=>'#',
            '＄'=>'$',
            '％'=>'%',
            '＆'=>'&',
            '＊'=>'*',
            '｜'=>'|',
            '｝'=>'}',
            '｛'=>'{',
            '｀'=>'`',
            '～'=>'~',
            '－'=>'-',
            '＿'=>'_',
            '＝'=>'=',
            '＋'=>'+',
        );

        foreach ($specials as $k => $v) {
            $name = str_replace($k,$v,$name);
        }
        return $name;
    }


    /**
     * [delCv 简历 - 单删批删]
     * @return [type] [array]
     * <AUTHOR> <[email address]>
     */
    public function delCv($paramIn = [])
    {
        $this->log = new LogServer();

        $delCv = (new ResumeRepository())->delCv($paramIn);
        // 数据返回
        $return['data'] = ['cv_id' => $paramIn['id']];

        //添加日志
        if (isset($paramIn['id']) && !empty($paramIn['id'])) {
            foreach ($paramIn['id'] as $v) {
                //添加日志
                $this->log->addLog([
                    'module_id'   => $v,
                    'module_type' => 5,
                    'action'      => 3,
                ]);
            }
        }

        return $this->checkReturn($return);
    }


    /**
     * [CommunicatedCv 面试 - 单删批删]
     * 该方法已废弃，根据客户端反馈和Nginx日志均不再调用了
     * @return [type] [array]
     * <AUTHOR> <[email address]>
     */
    public function CommunicatedCv($paramIn = [])
    {
        $this->log = new LogServer();

        // 判断是否可以删除
        $communication = (new ResumeRepository())->communication($paramIn);
        if ($communication === false) {
            return $this->checkReturn(-3, $this->getTranslation()->_('4103'));
        }
        // 做逻辑删除
        $CommunicatedCv = (new ResumeRepository())->CommunicatedCv($paramIn);
        if ($CommunicatedCv === false) {
            return $this->checkReturn(-3, $this->getTranslation()->_('4103'));
        }
        // 数据返回
        $return['data'] = '';

        //添加日志
        // 非空验证
        if (isset($paramIn['id']) && !empty($paramIn['id'])) {
            foreach ($paramIn['id'] as $v) {
                //添加日志
                $this->log->addLog([
                    'module_id'   => $v,
                    'module_type' => 5,
                    'action'      => 3,
                ]);
            }
        }
        return $this->checkReturn($return);
    }


    /**
     * 根据网点获取对应 所属区域
     * @return mixed
     */
    public function getSortingNoByStoreInfo($storeInfo)
    {
        // 获取区域
        return $this->areas[$storeInfo['manage_geography_code']] ?? '';
    }

    /**
     * [getCityList 获取城市列表]
     * @return [type] [array]
     * <AUTHOR> <[email address]>
     */
    public function getCityList($paramIn = [])
    {
        $return = [];

        $this->getDI()->get('logger')->write_log("getCityList param:" . json_encode($paramIn) . ' 返回数据 ==> ' . json_encode($return),
            'info');
        // 省
        if ($paramIn['type'] == 2) {
            $province = (new ResumeRepository())->province($paramIn);
            if ($province === false) {
                return $this->checkReturn(-3, $this->getTranslation()->_('4104'));
            }
            $return['data'] = $province;
            return $this->checkReturn($return);
            // 市
        } elseif ($paramIn['type'] == 3) {
            $city = (new ResumeRepository())->city($paramIn);
            if ($city === false) {
                return $this->checkReturn(-3, $this->getTranslation()->_('4104'));
            }
            $return['data'] = $city;
            return $this->checkReturn($return);
            // 乡
        } elseif ($paramIn['type'] == 4) {
            $district = (new ResumeRepository())->district($paramIn);
            $this->getDI()->get('logger')->write_log("getCityList param:" . json_encode($paramIn) . ' 返回数据 ==> ' . json_encode($district),
                'info');
            //多个邮编处理数组
            foreach ($district as $key => $item) {
                $district[$key]['postal_code'] = array_filter(explode(',', $item['postal_code'])) ?? [];
            }
            $this->getDI()->get('logger')->write_log("getCityList param:" . json_encode($paramIn) . ' 返回数据 ==> ' . json_encode($district),
                'info');

            if ($district === false) {
                return $this->checkReturn(-3, $this->getTranslation()->_('4104'));
            }
            $return['data'] = $district;
            return $this->checkReturn($return);
        } else {
            return $this->checkReturn(-3, $this->getTranslation()->_('4104'));
        }
    }


    /**
     * [verification 简单验证]
     * @return [type] [array]
     * <AUTHOR> <[email address]>
     */
    public function verification($paramIn = [])
    {
        // 邮箱
        if (isset($paramIn['email']) && strlen($paramIn['email']) > 40) {
            return $this->checkReturn(-3, $this->getTranslation()->_('4109'));
        }
        // 证件号
//        if( isset($paramIn['credentials_num'])&&strlen($paramIn['credentials_num']) > 40 ){
//            return $this->checkReturn(-3, $this->getTranslation()->_('4110'));
//        }
        // 毕业学校
//        if( isset($paramIn['graduate_school'])&&strlen($paramIn['graduate_school']) > 100 ){
//            return $this->checkReturn(-3, $this->getTranslation()->_('4111'));
//        }
        // 所学专业
//        if( isset($paramIn['discipline'])&&strlen($paramIn['discipline']) > 100 ){
//            return $this->checkReturn(-3, $this->getTranslation()->_('4112'));
//        }
        // 工作经历
//        if( isset($paramIn['work_exp'])&&strlen($paramIn['work_exp']) > 2000 ){
//            return $this->checkReturn(-3, $this->getTranslation()->_('4113'));
//        }
        // 期望薪资
        if (isset($paramIn['entry_salary']) && strlen($paramIn['entry_salary']) > 9) {
            return $this->checkReturn(-3, $this->getTranslation()->_('4114'));
        }
    }


    public function resumeList($paramIn = [])
    {
        $this->staff      = new StaffRepository();
        $this->sysList    = new SysListRepository();
        $this->blacklist  = new BlacklistRepository();
        $this->department = new DepartmentRepository();

        $interviewRepository = new InterviewRepository();

        $pageSize         = $this->processingDefault($paramIn, 'page_size', 2, 10);
        $pageNum          = $this->processingDefault($paramIn, 'page_num', 2, 1);
        $staffId          = $this->processingDefault($paramIn, 'staff_id', 2);
        $alternativeJobId = $this->processingDefault($paramIn, 'alternative_job_id', 2);
        $store_id         = $this->processingDefault($paramIn, 'store_id', 1);
        $noPage           = $this->processingDefault($paramIn, 'no_page', 2);
        $pageSize         = !$noPage ? $pageSize : 50000;
        $pageNum          = !$noPage ? $pageNum : 1;


        $param = [
            'name_tel',
            'address_id',
            'work_city_id',         //期望城市-市
            'work_district_id',     //期望城市-区
            'residence_government', //居住所在地-省
            'residence_city',       //居住所在地-市
            'job_id',
            'id',
            'state_code',
            'store_id',
            'updated_start',
            'updated_end',
            'delivery_time_start',
            'delivery_time_end',
            'created_start',
            'created_end',
            'alternative_job_id',
            'hc_id',
            'out_type',
            'source',               //招聘来源
            'recruit_channel',      //招聘渠道
            'recruit_type',         //招聘类型
            'is_relation_hc',       // 是否关联hc 1-是 2-否 0-全部
            'priority_ids',         // hc 优先级 array
            'hire_type',            // hc 雇佣类型
            'reserve_type',         // 简历类型
            'ai_score_type' // 简历附件评分类型
        ];

        $whereData = checkParam($param, $paramIn);

        if (isset($whereData['id'])) {
            $whereData['id'] = intval($whereData['id']);
        }

        //最新操作人类型  0 全部 1 为空 2 不为空
        $whereData['last_operator_query_type'] = isset($paramIn['last_operator_query_type']) ? intval($paramIn['last_operator_query_type']) : 0;

        //最新操作人
        if (!empty($paramIn['resume_last_operator'])) {
            $whereData['resume_last_operator'] = intval($paramIn['resume_last_operator']);
        }

        $is_role_permission = 0;

        $winhr_staff_role_ids = SettingEnvServer::getSetVal('winhr_staff_role_ids');
        if ($winhr_staff_role_ids && array_intersect(explode(',', $this->userInfo['position_category']),
                explode(',', $winhr_staff_role_ids))) {
            $is_role_permission = 1;
        }

        //不是管理员 并且开启了菜单权限
        $is_menu_permission = SettingEnvServer::getMenuPermission('whr_resume_menu_list');
        $authoritySql       = '';
        if ($this->userInfo['is_admin'] == enums::$is_admin['off'] && $is_menu_permission && empty($is_role_permission)) {
            $authoritySql = (new HcServer())->assembleAuthoritySql('hr_resume', $this->userInfo);
        }

        //覆盖网点限制
        if (empty($whereData['store_id']) && $this->ifRegionOrDistrictManagerRole() && !$is_menu_permission && empty($is_role_permission)) {
            $store_ids = $this->getMySetStoreIds();
            if (!empty($store_ids)) {
                $whereData['store_id'] = $store_ids;
            }
        }

        $join_str = "left join";
        if ($this->ifHrisSales) {
            //如果是hris用join,把空的过滤掉
            $join_str                   = "join";
            $whereData['department_id'] = $this->department->getDepartmentIdsByRole();
        }


        $where_and = 'AND';

        $country_value = getCountryValue(); //当前系统国家

        $resumeSql = "select 
                            hr_resume.*,
                            hr_economy_ability.graduate_school,
                            hr_economy_ability.major as discipline,
                            hr_economy_ability.education_level as education,
                            hr_jd.job_name,
                            hr_interview.interview_id,
                            hr_interview.state,
                            CONVERT_TZ(hr_resume.created_at, '+00:00', '" . $this->timezone . "' ) AS created_at,
                            CONVERT_TZ(hr_resume.updated_at, '+00:00', '" . $this->timezone . "' ) AS updated_at,
                            DATE_FORMAT(CONVERT_TZ(hr_economy_ability.graduate_time, '+00:00', '" . $this->timezone . "' ),'%Y-%m-%d') as graduate_time,
                            DATE_FORMAT(CONVERT_TZ(hr_resume.date_birth, '+00:00', '" . $this->timezone . "' ),'%Y-%m-%d') as date_birth,
                            hr_resume_extend.current_school_id,
                            hr_resume_extend.current_college,
                            hr_resume_extend.current_major,
                            hr_resume_extend.current_grade_id,
                            hr_resume_extend.work_start_date,
                            hr_resume_extend.work_end_date,
                            hr_resume_extend.tutor_email,
                            hr_resume_extend.tutor_phone,
                            hr_resume_extend.tutor_phone_area_code,
                            hr_resume_extend.interested_department_id,
                            hr_resume_extend.interested_department_other,
                            hr_resume_extend.ai_score,
                            hr_resume_extend.delivery_time,
                            hc.priority_id,
                            hc.job_title as job_title_id,
                            hc.hire_type
                            from hr_resume
                            {$join_str} hr_jd on hr_jd.job_id=hr_resume.job_id 
                            left join hr_interview on hr_interview.resume_id=hr_resume.id and hr_interview.hc_id=hr_resume.hc_id
                            left join hr_economy_ability on hr_economy_ability.resume_id = hr_resume.id
                            left join hr_hc as hc on hc.hc_id = hr_resume.hc_id
                            left join hr_resume_extend on hr_resume.id = hr_resume_extend.resume_id
                            where 
                            hr_resume.deleted=0 {$where_and}";

        $resumeSqlCount = "select
                                count(hr_resume.id) as num 
                                from hr_resume 
                                $join_str hr_jd on hr_jd.job_id=hr_resume.job_id
                                left join hr_hc as hc on hc.hc_id = hr_resume.hc_id
                                left join hr_resume_extend on hr_resume.id = hr_resume_extend.resume_id
                                where hr_resume.deleted=0 {$where_and}";

        foreach ($whereData as $k => $v) {
            //最新操作人的查询单独处理
            if (in_array($k, ['last_operator_query_type', 'resume_last_operator'])) {
                continue;
            }

            $v = str_replace('"', '', $v);
            if ($k == 'name_tel') {
                $v              = str_replace(" ", '', $v);
                $resumeSql      .= ' (hr_resume.name like ("' . $v . '%") or hr_resume.phone like ("' . $v . '%") or hr_resume.last_name_en like ("' . $v . '%") or hr_resume.first_name_en like ("' . $v . '%") or (hr_resume.credentials_category in (1,2) AND hr_resume.credentials_num like ("' . $v . '%"))) ';
                $resumeSqlCount .= ' (hr_resume.name like ("' . $v . '%") or hr_resume.phone like ("' . $v . '%") or hr_resume.last_name_en like ("' . $v . '%") or hr_resume.first_name_en like ("' . $v . '%") or (hr_resume.credentials_category in (1,2) AND hr_resume.credentials_num like ("' . $v . '%"))) ';
            } elseif ($k == 'updated_start') {
                $updated_at_s   = date("Y-m-d 00:00:00", strtotime($v));
                $resumeSql      .= ' hr_resume.updated_at>=CONVERT_TZ("' . $updated_at_s . '", "' . $this->timezone . '", "+00:00")';
                $resumeSqlCount .= ' hr_resume.updated_at>=CONVERT_TZ("' . $updated_at_s . '", "' . $this->timezone . '", "+00:00")';
            } elseif ($k == 'delivery_time_start') {
                $delivery_time_start = zero_time_zone($v . ' 00:00:00');
                $resumeSql           .= ' hr_resume_extend.delivery_time>="' . $delivery_time_start.'"';
                $resumeSqlCount      .= ' hr_resume_extend.delivery_time>="' . $delivery_time_start.'"';
            } elseif ($k == 'delivery_time_end') {
                $delivery_time_end   = zero_time_zone($v . ' 23:59:59');
                $resumeSql           .= ' hr_resume_extend.delivery_time <="' . $delivery_time_end.'"';
                $resumeSqlCount      .= ' hr_resume_extend.delivery_time <="' . $delivery_time_end.'"';
            } elseif ($k == 'ai_score_type') {
                //插入分数
                $scoreTypesList = array_map(function($item) {
                    return HrResumeAiScoreModel::$scoreInterval[$item] ?? [];
                }, $v);

                if (!empty($scoreTypesList)) {
                    // 找到最小的 start 和最大的 end
                    $minStart = min(array_column($scoreTypesList, 'start'));
                    $maxEnd   = max(array_column($scoreTypesList, 'end'));

                    $resumeSql      .= ' hr_resume_extend.ai_score >="'.$minStart.'" AND';
                    $resumeSqlCount .= ' hr_resume_extend.ai_score >="'.$minStart.'" AND';

                    $resumeSql      .= ' hr_resume_extend.ai_score <"'.$maxEnd.'"';
                    $resumeSqlCount .= ' hr_resume_extend.ai_score <"'.$maxEnd.'"';
                }
            } elseif ($k == 'updated_end') {
                $updated_at_e   = date("Y-m-d 23:59:59", strtotime($v));
                $resumeSql      .= ' hr_resume.updated_at<=CONVERT_TZ("' . $updated_at_e . '", "' . $this->timezone . '", "+00:00")';
                $resumeSqlCount .= ' hr_resume.updated_at<=CONVERT_TZ("' . $updated_at_e . '", "' . $this->timezone . '", "+00:00")';
            } elseif ($k == 'created_start') {
                $created_at_s   = date("Y-m-d 00:00:00", strtotime($v));
                $resumeSql      .= ' hr_resume.created_at>=CONVERT_TZ("' . $created_at_s . '", "' . $this->timezone . '", "+00:00")';
                $resumeSqlCount .= ' hr_resume.created_at>=CONVERT_TZ("' . $created_at_s . '", "' . $this->timezone . '", "+00:00")';
            } elseif ($k == 'created_end') {
                $created_at_e   = date("Y-m-d 23:59:59", strtotime($v));
                $resumeSql      .= ' hr_resume.created_at<=CONVERT_TZ("' . $created_at_e . '", "' . $this->timezone . '", "+00:00")';
                $resumeSqlCount .= ' hr_resume.created_at<=CONVERT_TZ("' . $created_at_e . '", "' . $this->timezone . '", "+00:00")';
            } elseif ($k == 'store_id') {
                if (is_array($v)) {
                    $tempStr        = getIdsStr($v);
                    $resumeSql      .= ' hr_resume.store_id in (' . $tempStr . ')';
                    $resumeSqlCount .= ' hr_resume.store_id in (' . $tempStr . ')';
                } else {
                    $resumeSql      .= ' hr_resume.store_id = \'' . $v . '\'';
                    $resumeSqlCount .= ' hr_resume.store_id = \'' . $v . '\'';
                }
            } elseif ($k == 'department_id') {
                if (is_array($v)) {
                    $resumeSql      .= ' hr_jd.' . $k . ' in (' . implode(",", $v) . ')';
                    $resumeSqlCount .= ' hr_jd.' . $k . ' in (' . implode(",", $v) . ')';
                } else {
                    $resumeSql      .= ' hr_jd.' . $k . '="' . $v . '"';
                    $resumeSqlCount .= ' hr_jd.' . $k . '="' . $v . '"';
                }
            } elseif ($k == 'alternative_job_id') {
                $resumeSql      .= ' find_in_set(' . $v . ', hr_resume.alternative_job_ids) ';
                $resumeSqlCount .= ' find_in_set(' . $v . ', hr_resume.alternative_job_ids) ';
            } elseif ($k == 'out_type') {
                $_sql = "";
                foreach (explode(",", $v) as $v) {
                    $_list = explode(".", $v);
                    $_sql  .= " (pass_type=" . $_list[0] . " and pass_reason=" . $_list[1] . ") or";
                }
                $_sql           = "select resume_id from hr_interview_pass_type_record where deleted = 0 and" . substr($_sql,
                        0, -2);
                $resumeSql      .= ' hr_resume.id in (' . $_sql . ')';
                $resumeSqlCount .= ' hr_resume.id in (' . $_sql . ')';
            } elseif ($k == 'hc_id') {
                $resumeSql      .= ' hr_resume.hc_id = \'' . intval($v) . '\' ';
                $resumeSqlCount .= ' hr_resume.hc_id = \'' . intval($v) . '\' ';
            } elseif ($k == 'work_city_id') {  //期望城市-市
                $ids            = getIdsStr(array_unique($v));
                $resumeSql      .= ' hr_resume.work_city_id in (' . $ids . ')';
                $resumeSqlCount .= ' hr_resume.work_city_id in (' . $ids . ')';
            } elseif ($k == 'work_district_id') {  //期望城市-区
                $ids            = getIdsStr(array_unique($v));
                $resumeSql      .= ' hr_resume.work_district_id in (' . $ids . ')';
                $resumeSqlCount .= ' hr_resume.work_district_id in (' . $ids . ')';
            } elseif ($k == 'residence_city') { //居住所在地-市
                if ($country_value == enums::COUNTRY_CODE_MY) {
                    $resumeSql      .= ' hr_resume.residence_city LIKE \'%' . $v . '%\' ';
                    $resumeSqlCount .= ' hr_resume.residence_city LIKE \'%' . $v . '%\' ';
                } else {
                    $resumeSql      .= ' hr_resume.residence_city = \'' . $v . '\' ';
                    $resumeSqlCount .= ' hr_resume.residence_city = \'' . $v . '\' ';
                }
            } elseif ($k == 'state_code') {
                if ($v == 2) { //已沟通
                    $resumeSql      .= ' hr_resume.state_code in(2,3)';
                    $resumeSqlCount .= ' hr_resume.state_code in(2,3)';
                } else {
                    $resumeSql      .= ' hr_resume.state_code =1';
                    $resumeSqlCount .= ' hr_resume.state_code =1';
                }
            } elseif ($k == 'source') {
                $resumeSql      .= " hr_resume.source = {$v} ";
                $resumeSqlCount .= " hr_resume.source = {$v} ";
            } elseif ($k == 'recruit_channel') {
                $resumeSql      .= " hr_resume.recruit_channel = {$v} ";
                $resumeSqlCount .= " hr_resume.recruit_channel = {$v} ";
            } elseif ($k == 'recruit_type') {
                $resumeSql      .= " hr_resume.recruit_type = {$v} ";
                $resumeSqlCount .= " hr_resume.recruit_type = {$v} ";
            } else if ($k == 'is_relation_hc') {
                if ($v == 1) {
                    $resumeSql .= " hr_resume.hc_id > 0";
                    $resumeSqlCount .= " hr_resume.hc_id > 0";
                }
                if ($v == 2) {
                    $resumeSql .= " hr_resume.hc_id = 0";
                    $resumeSqlCount .= " hr_resume.hc_id = 0";
                }
            } else if ($k == 'priority_ids' && is_array($v)) {
                $priority_id = implode(',', $v);
                $resumeSql      .= " hc.priority_id IN (".$priority_id.") ";
                $resumeSqlCount .= " hc.priority_id IN (".$priority_id.") ";
            }else if (($k == 'hire_type' && is_array($v))){
                $hire_type_id = implode(',', $v);
                $resumeSql      .= " hc.hire_type IN (".$hire_type_id.") ";
                $resumeSqlCount .= " hc.hire_type IN (".$hire_type_id.") ";
            }elseif ($k == 'reserve_type') {
                $resumeSql      .= " hr_resume.reserve_type = {$v} ";
                $resumeSqlCount .= " hr_resume.reserve_type = {$v} ";
            }else {
                $resumeSql      .= ' hr_resume.' . $k . '="' . $v . '"';
                $resumeSqlCount .= ' hr_resume.' . $k . '="' . $v . '"';
            }
            $resumeSql      .= ' '.$where_and;
            $resumeSqlCount .= ' '.$where_and;
        }

        $resumeSql      = rtrim($resumeSql, $where_and);
        $resumeSqlCount = rtrim($resumeSqlCount, $where_and);

        //简历最新操作人查询
        switch ($whereData['last_operator_query_type']) {
            case 0://ALL
                if (!empty($whereData['resume_last_operator']) && $whereData['resume_last_operator'] > 0) {
                    $resumeSql      .= ' AND hr_resume.resume_last_operator =  '.intval($whereData['resume_last_operator']);
                    $resumeSqlCount .= ' AND hr_resume.resume_last_operator =  '.intval($whereData['resume_last_operator']);
                }
                break;
            case 1://为空
                $resumeSql      .= ' AND hr_resume.resume_last_operator =  0';
                $resumeSqlCount .= ' AND hr_resume.resume_last_operator =  0';
                break;
            case 2://不为空
                $resumeSql      .= ' AND hr_resume.resume_last_operator >  0';
                $resumeSqlCount .= ' AND hr_resume.resume_last_operator >  0';
                if (!empty($whereData['resume_last_operator']) && $whereData['resume_last_operator'] > 0) {
                    $resumeSql      .= ' AND hr_resume.resume_last_operator =  '.intval($whereData['resume_last_operator']);
                    $resumeSqlCount .= ' AND hr_resume.resume_last_operator =  '.intval($whereData['resume_last_operator']);
                }
                break;
        }


        if ($authoritySql) {
            $resumeSql      .= ' AND (' . $authoritySql . ')';
            $resumeSqlCount .= ' AND (' . $authoritySql . ')';
        }

        $hcDataCount = (new ResumeRepository())->getResumeList($resumeSqlCount);

        /* 分页返回数据组 */
        $pageArr = [];
        //总条数
        $pageArr['count'] = (int)$hcDataCount[0]['num'];
        if (isset($paramIn['is_count']) && $paramIn['is_count'] == 1) {
            return  $pageArr['count'] ?? 0;
        }

        if (!empty($paramIn['is_count'])) {
            return  $pageArr['count'] ?? 0;
        }
//        if ($noPage && $pageArr['count'] > enums::EXPORT_MAX_NUM_LIMIT) {
//            throw new ValidationException($this->getTranslation()->_('export_down_num_error'));
//        }

        //总页数
        $pageArr['pageCount'] = ceil($pageArr['count'] / $pageSize);
        //当前页 默认1
        $pageArr['pageNum'] = $pageNum;
        //每页显示多少条默认10
        $pageArr['pageSize'] = !$noPage ? $pageSize : 50000;
        $resumeSql           .= " order by hr_resume.updated_at desc limit " . ($pageNum - 1) * $pageSize . "," . $pageSize;
        $cityArr             = [
            'address_type' => 2,
        ];
        $cityList            = $this->sysList->getAddressList($cityArr);
        $cityList            = array_column($cityList, 'name', 'code');

        //获取市列表，上面那个其实是省列表
        $city_list = (new H5appointRepository)->getCityList();
        $city_list = array_column($city_list, 'name', 'code');

        //获取区列表
        $districtList = $this->sysList->getAddressList(['address_type' => 4]);
        $districtList = array_column($districtList, 'name', 'code');

        //简历列表
        $resumeArr = (new ResumeRepository())->getResumeList($resumeSql);
        $stateArr  = [
            '1' => $this->getTranslation()->_('4007'),
            '2' => $this->getTranslation()->_('4006'),
            '3' => $this->getTranslation()->_('4006'),
        ];


        $submitterIds = array_filter(array_unique(array_column($resumeArr, "submitter_id")));
        $staffList    = $this->sysList->getStaffListByIds($submitterIds);

        $alternativeJobIds = implode(',', array_column($resumeArr, 'alternative_job_ids'));
        $alternativeJobIds = array_values(array_unique(explode(',', $alternativeJobIds)));
        $hrJds             = HrJdModel::find([
            'conditions' => ' job_id in ({ids:array})',
            'bind'       => [
                'ids' => $alternativeJobIds,
            ],
        ])->toArray();
        $hrJds             = array_column($hrJds, 'job_name', 'job_id');

        //查询网点名称 ids
        $worknodeIdsArr = array_unique(array_column($resumeArr, "store_id"));
        $worknodeIds    = getIdsStr($worknodeIdsArr);
        $worknodeData   = $this->sysList->getStoreList([
            "ids" => $worknodeIds,
        ]);
        $worknodeData   = array_column($worknodeData, 'name', 'id');

        $educationArr = [
            1 => $this->getTranslation()->_('8062'),
            2 => $this->getTranslation()->_('8063'),
            3 => $this->getTranslation()->_('8064'),
            4 => $this->getTranslation()->_('8065'),
            5 => $this->getTranslation()->_('8066'),
            6 => $this->getTranslation()->_('8067'),
        ];
        //网点信息
        $store_list = (new SysStoreServer())->getSysStoreList();

        //获取员工id对应名字数组
        $staffArr  = array_merge(array_column($resumeArr, 'submitter_id'));
        $staffArr  = array_unique(array_filter($staffArr));
        $staffData = $this->staff->checkoutStaffBatch($staffArr);
        $staffData = array_column($staffData, 'name', 'staff_info_id');

        //淘汰类型
        $out_type_list = (new SysListServer())->getOutInterviewList();
        $array_key     = array_column($out_type_list, 'key');
        $array_value   = array_column($out_type_list, 'name');
        $array         = array_combine($array_key, $array_value);

        //获取招聘渠道内容
        $recruitChannelList = HrRecruitChannelsModel::find([
            'columns' => 'id, name',
            'order'   => 'id asc',
        ])->toArray();

        $recruitChannelListKv = array_column($recruitChannelList, 'name', 'id');

        $resume_last_operator_staff_ids = array_values(array_unique(array_column($resumeArr,'resume_last_operator')));
        $resume_last_operator_res = (new StaffServer())->getStaffListByIds($resume_last_operator_staff_ids,['staff_info_id','name','nick_name']);
        $resume_last_operator_list = array_column($resume_last_operator_res, null, 'staff_info_id');

        // 简历类型
        $reserve_type_list = (new SysListServer())->reserveTypeList($this->lang);

        $reserve_type_arr = array_column($reserve_type_list,"name","key");
        unset($reserve_type_arr[0]);

        //查询职位数据
        $positionData = $this->sysList->getPositionList();
        $positionData = array_column($positionData, 'job_name', 'id');

        foreach ($resumeArr as $k => $v) {
            $phone_area_code         = !empty($v['phone_area_code']) ? "+" . $v['phone_area_code'] : '+66';
            $resumeArr[$k]['hc_id']  = $v['hc_id'] ? $v['hc_id'] : '';
            $resumeArr[$k]['phone']  = $phone_area_code . " " . $v['phone'];
            $resumeArr[$k]['source'] = $v['source'] == 2
            && isset($staffList[$v['submitter_id']])
            && $staffList[$v['submitter_id']]['department_id'] != 7 ? "3" : $v['source'];;

            $resumeArr[$k]['submitter_name'] = $staffData[$v['submitter_id']] ?? '';
            $resumeArr[$k]['address_name']   = isset($cityList[$v['address_id']]) ? $cityList[$v['address_id']] : '';

            //期望工作城市-市
            $resumeArr[$k]['work_city_name'] = isset($city_list[$v['work_city_id']]) ? $city_list[$v['work_city_id']] : ($v['work_city_id'] ?? ''); //期望城市-市

            //期望工作城市-区
            $resumeArr[$k]['work_district_name'] = isset($districtList[$v['work_district_id']]) ? $districtList[$v['work_district_id']] : ''; //期望城市-区


            $resumeArr[$k]['work_province_city'] = trim($resumeArr[$k]['address_name'] . ' ' . $resumeArr[$k]['work_city_name'] . ' ' . $resumeArr[$k]['work_district_name']); //期望城市

            $resumeArr[$k]['store_name'] = $store_list[$v['store_id']]['store_name'] ?? '';
            $resumeArr[$k]['name']       = $v['first_name'] . " " . $v['last_name'];
            if (isCountry('Ph')) {
                $resumeArr[$k]['name'] = $v['first_name'] . " " . $v['middle_name'] . " " . $v['last_name'] . " " . $v['suffix_name'];
            }
            $resumeArr[$k]['name_en']    = $v['first_name_en'] . " " . $v['last_name_en'];
            $resumeArr[$k]['store_name'] = $worknodeData[$v['store_id']] ?? "";

            //检测是否在黑名单
            if (isCountry('TH')){
                $black_grey_list = (new BlackgreylistServer())->check_black_grey_list([
                    "identity" => $v['credentials_num'],
                ]);
                $resumeArr[$k]['is_blacklist'] = $black_grey_list["is_black_list"] ? 1 : 0;
            }else{
                $_re                           = $this->blacklist->blacklistCheck([
                    "identity" => $v['credentials_num'],
                ]);
                $resumeArr[$k]['is_blacklist'] = $_re ? 1 : 0;
            }

            $resumeArr[$k]['sex_title']    = enums::$sex_title[$v['sex']] ?? '';
            $resumeArr[$k]['state_name']   = $stateArr[$v['state_code']] ?? '';//沟通状态
            //面试状态
            $resumeArr[$k]['state']          = $interviewRepository->getInterviewState($v['filter_state'], $v['state'],
                $v['is_out'], $v['state_code']);
            $resumeArr[$k]['education_name'] = $educationArr[$v['education']] ?? '';
            $resumeArr[$k]['out_type']       = !empty($resumeArr[$k]['out_type']) ? $array[$resumeArr[$k]['out_type']] : "";

            //buddy选的hc_id
            $resumeArr[$k]['buddy_hc_id'] = '';
            if (!empty($v['buddy_id'])) {
                $resumeArr[$k]['buddy_hc_id'] = HrResumeBuddyModel::getVal($v['id'], 'hc_id');
            }

            $resumeArr[$k]['is_salary_approve'] = (new PermissionsRepository())->isHasSalaryPermission($this->userInfo) ? 1 : 0;

            $resumeArr[$k]['alternative_job_names'] = $this->formatJobNames($v['alternative_job_ids'], $hrJds);
            //简历期望薪资查看权限
            $is_have_salary_permission = $this->isHaveSalaryOrAnnexPermission(
                $v['id'],
                $v['filter_state'],
                $v['state_code'],
                $v['recruiter_id'],
                $this->userInfo['id'],
                $this->userInfo['expected_salary_permission']
            );
            if (!$is_have_salary_permission) {
                $resumeArr[$k]['entry_salary'] = '*****';
            }

            if($this->checkIsNetworkFirstLineJD($v['job_id'])){
                $resumeArr[$k]['entry_salary'] = '';
            }

            //居住所在地信省市信息
            if ($v['residence_country'] == $country_value) {
                $resumeArr[$k]['residence_government_name'] = $cityList[$v['residence_government']] ?? '';
                $resumeArr[$k]['residence_city_name']       = $city_list[$v['residence_city']] ?? '';
            } else {
                $resumeArr[$k]['residence_government_name'] = $v['residence_government'];
                $resumeArr[$k]['residence_city_name']       = $v['residence_city'];
            }
            //马来居住市不进行转义
            if ($v['residence_country'] == enums::COUNTRY_CODE_MY) {
                $resumeArr[$k]['residence_city_name'] = $v['residence_city'];
            }
            $resumeArr[$k]['residence_province_city'] = trim($resumeArr[$k]['residence_government_name'] . ' ' . $resumeArr[$k]['residence_city_name']); //居住地城市

            //recruit_type 招聘类型
            $resumeArr[$k]['recruit_type_name'] = $this->getTranslation()->_(HrResumeModel::$recruitTypeList[$v['recruit_type']] ?? '');
            //recruit_channel 渠道
            $resumeArr[$k]['recruit_channel_name'] = $recruitChannelListKv[$v['recruit_channel']] ?? '';
            // 来源
            $resumeArr[$k]['source_name'] = empty($v['source']) ? '' :$this->getTranslation()->_('resume_src_'.$v['source']) ;
            $resumeArr[$k]['resume_last_operator_name'] = StaffServer::getStaffNameView($resume_last_operator_list[$v['resume_last_operator']] ?? [], 2);
            $resumeArr[$k]['priority_text'] = HcServer::formatPriority((int) $v['priority_id']);

            // hc 雇佣类型
            $resumeArr[$k]['hire_type_text'] = $v["hire_type"] ? $this->getTranslation()->_('hire_type_'.$v["hire_type"]) : "";
            // 简历类型
            $resumeArr[$k]['reserve_type_text'] = $v["reserve_type"] ? $reserve_type_arr[$v["reserve_type"]] : "";

            //投递时间
            $resumeArr[$k]['delivery_time'] = show_time_zone($v['delivery_time']);
            $resumeArr[$k]['ai_score'] = is_null($v['ai_score']) ? '' : $v['ai_score'].$this->getTranslation()->_('unit_fen');

            //职位名称
            $resumeArr[$k]['job_title_name'] = !empty($v['job_title_id']) ? sprintf("(%s) %s", $v['job_title_id'], $positionData[$v['job_title_id']] ?? '') : '';

            if ($v['recruit_type'] == HrResumeModel::RECRUIT_TYPE_SOCIAL) {
                $resumeArr[$k]['current_school_id']           = '';
                $resumeArr[$k]['current_college']             = '';
                $resumeArr[$k]['current_major']               = '';
                $resumeArr[$k]['work_start_date']             = '';
                $resumeArr[$k]['work_end_date']               = '';
                $resumeArr[$k]['tutor_phone']                 = '';
                $resumeArr[$k]['tutor_phone_area_code']       = '';
                $resumeArr[$k]['tutor_email']                 = '';
                $resumeArr[$k]['current_grade_id']            = '';
                $resumeArr[$k]['interested_department_id']    = '';
                $resumeArr[$k]['interested_department_other'] = '';
            }

            if ($v['recruit_type'] == HrResumeModel::RECRUIT_TYPE_INTERN) {
                $resumeArr[$k]['current_salary']              = '';
                $resumeArr[$k]['entry_salary']                = '';
                $resumeArr[$k]['interested_department_id']    = '';
                $resumeArr[$k]['interested_department_other'] = '';
            }

            if ($v['recruit_type'] == HrResumeModel::RECRUIT_TYPE_FRESH) {
                $resumeArr[$k]['current_salary']        = '';
                $resumeArr[$k]['work_start_date']       = '';
                $resumeArr[$k]['work_end_date']         = '';
                $resumeArr[$k]['tutor_phone']           = '';
                $resumeArr[$k]['tutor_phone_area_code'] = '';
                $resumeArr[$k]['tutor_email']           = '';
                $resumeArr[$k]['current_grade_id']      = '';
            }
        }
        $returnArr['data']['dataList']    = $resumeArr;
        $returnArr['data']['pagination']  = $pageArr;
        $returnArr['data']['permissions'] = (new PermissionsRepository())->checkRoleResumeListPermissions($this->userInfo);



        return $this->checkReturn($returnArr);
    }

    /**
     * 获取是否有当前薪资、期望薪资权限
     *
     * @param $resume_filter_state --简历推荐状态
     * @param $interview_state --面试状态
     * @param $resume_recruiter_id -- 招聘负责人工号
     * @param $login_staff_id --当前登录用户工号
     * @param $login_staff_salary_annex_permission --简历薪资或附件查看权限
     *
     * @return int 1:有权限，0：没有权限
     */
    public function isHaveSalaryOrAnnexPermission($resume_id,$resume_filter_state,$interview_state,$resume_recruiter_id,$login_staff_id,$login_staff_salary_annex_permission){

        /** step1:简历负责人为空，所有人有薪资 附件权限 **/
        if(empty($resume_recruiter_id)){
           return  1;
        }

        /** step2: 简历负责人不为空的判断逻辑  **/

        //简历推荐终止状态有 筛选不通过、取消筛选
        $resume_filter_end_state = [
            enums::$resume_filter_state['not_pass'],
            enums::$resume_filter_state['cancel'],
        ];
        //面试终止状态有 已拒绝、已取消
        $interview_end_state = [
            enums::$interview_state['rejected'],
            enums::$interview_state['canceled'],
        ];

        //判断是否是 终止状态？是：有权限
        if(in_array($resume_filter_state,$resume_filter_end_state) || in_array($interview_state,$interview_end_state)){
           return  1;
        }

        /** step3: 简历负责人是不是当前用户？是：有权限 **/
        if ($resume_recruiter_id == $login_staff_id){
            return  1;
        }

        /** step4:是否有期望薪资、当前薪资/简历附件权限 ？ 是：有权限 **/
        //
        if($login_staff_salary_annex_permission == enums::$expected_salary_permission['on'] ){
            return  1;
        }

        /** 以上都不符合，返回无权限 **/

        return 0;
    }

    public function formatJobNames($jobIdStrs, $jobsName)
    {
        $jobIds  = explode(',', $jobIdStrs);
        $results = [];
        foreach ($jobIds as $jobId) {
            if (isset($jobsName[$jobId])) {
                $results[] = $jobsName[$jobId];
            }
        }

        return implode(',', $results);
    }

    public function resumeInfo($paramIn = [], $source_type = 'winhr')
    {
        $this->log       = new LogServer();
        $this->staff     = new StaffRepository();
        $this->sysList   = new SysListRepository();
        $this->blacklist = new BlacklistRepository();
        $SysListServer   = new SysListServer();

        try {
            $resumeId = $this->processingDefault($paramIn, 'resume_id', 2);
            $resumeId = intval($resumeId);
            /* 获取简历详情 */
            $resumeInfo = (new ResumeRepository())->resumeInfo($resumeId);

            if ($resumeInfo) {
                $paramLog                           = [
                    'module_id'   => $resumeInfo['id'],
                    'module_type' => '5,6,7,8,9,10,18,19,27',
                ];
                $historyData                        = $this->log->infoLog($paramLog);
                $resumeInfo['infoInterviewHistory'] = $historyData;
            }

            //格式化简历部分信息
            $resumeInfo = $this->formatResumeInfo($resumeInfo);

            $provinceList = $this->sysList->getAddressList(['address_type' => 2]);
            $provinceList = array_column($provinceList, 'name', 'code');

            $cityList = $this->sysList->getAddressList(['address_type' => 3]);
            $cityList = array_column($cityList, 'name', 'code');

            $districtList = $this->sysList->getAddressList(['address_type' => 4]);
            $districtList = array_column($districtList, 'name', 'code');

            //获取员工id对应名字数组
            $staffData = $this->staff->checkoutStaff($resumeInfo['submitter_id']);

            //调用此函数前确认该函数已添加过对应国家code代码逻辑
            $country_value = getCountryValue();

            //所属网点名称
            $worknodeData             = (new SysListRepository())->getStoreList(['id' => $resumeInfo['store_id']]);
            $resumeInfo['store_name'] = $worknodeData['name'];

            $resumeInfo['is_editable']       = in_array($resumeInfo['state'], [25, 40]) ? 1 : 0;
            $resumeInfo['is_editable_basic'] = $resumeInfo['is_editable'];
            $resumeInfo['filter_staff_id']   = '';

            //是否推荐简历
            $resumeInfo['is_recommended'] = (new InterviewServer())->isRecommendedResume($resumeInfo);

            if (isCountry('Id')) {
                $resumeInfo['is_editable_nationality'] = 1;
                // 判断是否可以编辑国家
                $interviewServer = new InterviewServer();
                $salaryApprove   = $interviewServer->getLastApprove($resumeId);
                if ($salaryApprove && in_array($salaryApprove['status'], [1, 2])) {
                    // 审批中 或 审批完成
                    $salaryApproveModel = SalaryApproveAllowanceModel::findFirst([
                        'conditions' => 'approve_id = :approve_id: ',
                        'bind'       => ['approve_id' => $salaryApprove['id']],
                    ]);
                    if ($salaryApproveModel) {
                        // 新版数据
                        $resumeInfo['is_editable_nationality'] = 0;
                    }
                } else {
                    // 不存在审批流 或者 审批未通过
                    $interviewOffer = HrInterviewOfferModel::findFirst([
                        'conditions' => 'interview_id = :interview_id: and resume_id = :resume_id: and hc_id = :hc_id: and status = 1',
                        'bind'       => [
                            'interview_id' => $resumeInfo['interview_id'],
                            'resume_id'    => $resumeInfo['id'],
                            'hc_id'        => $resumeInfo['hc_hc_id'],
                        ],
                    ]);
                    if ($interviewOffer) {
                        // 发送offer
                        if (
                        OfferSendSalaryModel::findFirst([
                            'conditions' => 'offer_id = :offer_id:',
                            'bind'       => ['offer_id' => $interviewOffer->id],
                        ])
                        ) {
                            $resumeInfo['is_editable_nationality'] = 0;
                        }
                    }
                }
            }


            $resumeInfo['address_name'] = $provinceList[$resumeInfo['address_id']];

            //期望工作城市-市
            $resumeInfo['work_city_name'] = $cityList[$resumeInfo['work_city_id']] ? $cityList[$resumeInfo['work_city_id']] : ($resumeInfo['work_city_id'] ?? '');
            //期望工作城市-区
            $resumeInfo['work_district_name'] = $districtList[$resumeInfo['work_district_id']] ?? '';

            $resumeInfo['work_province_city'] = trim($resumeInfo['address_name'] . ' ' . $resumeInfo['work_city_name'] . ' ' . $resumeInfo['work_district_name']);

            $resumeInfo['submitter_name'] = $staffData['name'] ?? '';


            //获取村庄列表
            if (isCountry('ID')) {
                $villageList = $this->sysList->getVillageListByCodes([
                    $resumeInfo['register_village'],
                    $resumeInfo['residence_village'],
                ]);

                $villageListKy = array_column($villageList, 'name', 'code');

                if ($resumeInfo['register_country'] == $country_value) {
                    $resumeInfo['register_village_name'] = $villageListKy[$resumeInfo['register_village']] ?? '';
                } else {
                    $resumeInfo['register_village_name'] = $resumeInfo['register_village'] ?? '';
                }

                if ($resumeInfo['residence_country'] == $country_value) {
                    $resumeInfo['residence_village_name'] = $villageListKy[$resumeInfo['residence_village']] ?? '';
                } else {
                    $resumeInfo['residence_village_name'] = $resumeInfo['residence_village'] ?? '';
                }
            }

            //获取国家/地区
            $addressCountry = (new SysListServer())->getTotalDictionaryRegionByDictCode(['dict_code' => 'address_country_region']);
            $addressCountryList = array_column($addressCountry, 'label', 'value');

            /**
             * 户籍地和居住地信息：
             * 泰国：省市乡存储的是行政区域code，需要获取对应省市乡名称
             * 非泰国：省市乡存储的是用户直接录入的信息，直接使用
             */
            //户籍地字段信息：
            $resumeInfo['register_country']  = $resumeInfo['register_country'] ? trim($resumeInfo['register_country']) : trim($country_value);
            $resumeInfo['residence_country'] = $resumeInfo['residence_country'] ? trim($resumeInfo['residence_country']) : trim($country_value);

            $resumeInfo['register_country_name'] = $addressCountryList[$resumeInfo['register_country']] ?? ''; // 户籍地国家
            if ($resumeInfo['register_country'] == $country_value) {
                $resumeInfo['register_government_name'] = $provinceList[$resumeInfo['register_government']];
                $resumeInfo['register_city_name']       = $cityList[$resumeInfo['register_city']];
                $resumeInfo['register_town_name']       = $districtList[$resumeInfo['register_town']];
            } else {
                $resumeInfo['register_government_name'] = $resumeInfo['register_government'];
                $resumeInfo['register_city_name']       = $resumeInfo['register_city'];
                $resumeInfo['register_town_name']       = $resumeInfo['register_town'];
            }

            //居住地字段信息：
            $resumeInfo['residence_country_name'] = $addressCountryList[$resumeInfo['residence_country']] ?? '';// 居住地国家
            if ($resumeInfo['residence_country'] == $country_value) {
                //国家泰国的省市乡存储的是行政区域code,，非泰国国家时用户手动录入，直接展示
                $resumeInfo['residence_government_name'] = $provinceList[$resumeInfo['residence_government']];
                $resumeInfo['residence_city_name']       = $cityList[$resumeInfo['residence_city']];
                $resumeInfo['residence_town_name']       = $districtList[$resumeInfo['residence_town']];
            } else {
                $resumeInfo['residence_government_name'] = $resumeInfo['residence_government'];
                $resumeInfo['residence_city_name']       = $resumeInfo['residence_city'];
                $resumeInfo['residence_town_name']       = $resumeInfo['residence_town'];
                $resumeInfo['register_town_name']        = $resumeInfo['register_town'];
            }


            //$resumeInfo['sex'] = $this->getResumeSex($resumeInfo['sex'], $resumeInfo['call_name']);

            /* 获取相关附件详情 */
            $annexData                          = (new ResumeRepository())->annexInfo($resumeInfo['id']);
            $resumeInfo['annexList']            = $annexData;
            $resumeInfo['recruit_channel_text'] = !empty($resumeInfo['recruit_channel_text']) ? $SysListServer->getRecruitChannelList($resumeInfo['recruit_channel']) : '';
            //检测是否在黑名单
            if (isCountry('TH')){
                $black_grey_list = (new BlackgreylistServer())->check_black_grey_list([
                    "identity" => $resumeInfo['credentials_num'],
                ]);
                $resumeInfo['is_blacklist'] = $black_grey_list["is_black_list"] ? 1 : 0;
            }else{
                $_re                        = $this->blacklist->blacklistCheck([
                    "identity" => $resumeInfo['credentials_num'],
                ]);
                $resumeInfo['is_blacklist'] = $_re ? 1 : 0;
            }

            $resumeInfo['name']         = $resumeInfo['first_name'] . " " . $resumeInfo['last_name'];
            $resumeInfo['religion']     = $resumeInfo['religion'] === 0 ? 0 : $resumeInfo['religion'];
            // 招聘人姓名
            $resumeInfo['recruiter_name'] = $this->getRecruiterName($resumeInfo['recruiter_id']);
            //获取面试操作记录表ope_id
            $interview_id         = $resumeInfo['interview_id'];
            $resumeInfo['ope_id'] = $this->getInterviewerOpeId($interview_id);

            //返回简历是否允许修改 is_modify 1可以修改 2 不可以修改
            $resumeInfo['is_modify'] = $this->checkResumeModify($resumeInfo);

            //简历期望薪资、当前薪资查看权限
            if ($source_type != 'h5') {
                $is_have_salary_permission = $this->isHaveSalaryOrAnnexPermission(
                    $resumeInfo['id'],
                    $resumeInfo['filter_state'],
                    $resumeInfo['interview_state'],
                    $resumeInfo['recruiter_id'],
                    $this->userInfo['id'],
                    $this->userInfo['expected_salary_permission']
                );
                if (!$is_have_salary_permission && !$this->checkIsNetworkFirstLineJD($resumeInfo['job_id'])) {
                    $resumeInfo['entry_salary']   = '*****';
                    $resumeInfo['current_salary'] = '*****';
                }

                //权限验证 隐藏附件
                $is_have_annex_permission = $this->isHaveSalaryOrAnnexPermission(
                    $resumeInfo['id'],
                    $resumeInfo['filter_state'],
                    $resumeInfo['interview_state'],
                    $resumeInfo['recruiter_id'],
                    $this->userInfo['id'],
                    $this->checkIsNetworkFirstLineJD($resumeInfo['job_id'])?1:$this->userInfo['resume_attachment_permission']
                );
                if (!$is_have_annex_permission) {
                    foreach ($resumeInfo['annexList'] as $k => &$v) {
                        $v['object_key'] = $this->getTranslation()->_('unauthorized_picture');
                        $v['file_url']   = $this->getTranslation()->_('unauthorized_picture');
                        $v['id']         = '0';
                    }
                }

                //简历期望薪资、当前薪资权限标识
                $resumeInfo['is_have_salary_permission'] = $is_have_salary_permission;
                //简历附件权限标识
                $resumeInfo['is_have_annex_permission'] = $is_have_annex_permission;
            }

            //todo 返回简历基本信息数据
            $returnArr['data'] = $resumeInfo;
            return $this->checkReturn($returnArr);
        } catch (\Exception $e) {
            return $this->checkReturn(-3);
        }
    }

    public function getRecruiterName($recruiterId)
    {
        if ($recruiterId == 0) {
            return '';
        }
        $recruiterStaffInfo = (new StaffServer())->getInfoByStaffId($recruiterId);
        if ($recruiterStaffInfo['name']) {
            return $recruiterId . " " . $recruiterStaffInfo['name'];
        }
        return '';
    }

    /**
     * 根据面试ID获取对应面试官操作记录表最新ID
     * @param $interview_id
     * @return int
     */
    public function getInterviewerOpeId($interview_id)
    {
        $ope_id = 0;
        //面试预约表
        $interviewSubscribeModel = HrInterviewSubscribeModel::findFirst([
            'columns'    => 'id',
            'conditions' => 'interview_id = :interview_id:',
            'bind'       => [
                'interview_id' => $interview_id,
            ],
            'order'      => 'id desc',
        ]);
        //面试官操作记录表
        if ($interviewSubscribeModel) {
            $hr_interviewer_operation = (new HrInterviewerOperationModel())->findFirst([
                'conditions' => 'interview_sub_id = :interview_sub_id:',
                'bind'       => [
                    'interview_sub_id' => $interviewSubscribeModel->id,
                ],
                'order'      => 'id desc',
            ]);
            $ope_id                   = $hr_interviewer_operation->id;
        }

        return $ope_id;
    }

    /**
     * 获取简历完善状态
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function getrAnnexPerfectlist($paramIn = [])
    {
        $jobCategory = $paramIn['job_category'];//状态 (1.摩托车,2.皮卡车,3非快递员)
        $car_owner   = $paramIn['car_owner'];//状态 (1.摩托车,2.皮卡车,3非快递员)
        $annexData   = $paramIn['annexData']; //简历的附件列表
        //获取对应必填项
        if ($jobCategory == 1) {
            $requiredData = UC('resume')['motorcycle'];
            if ($car_owner !== 2) {
                unset($requiredData[8]);
            }
        } elseif ($jobCategory == 2) {
            $requiredData = UC('resume')['pickup'];
            if ($car_owner !== 2) {
                unset($requiredData[8]);
            }
        } elseif ($jobCategory == 3) {
            $requiredData = UC('resume')['nonCourier'];
        } else {
            $requiredData = [];
        }
        if (!$requiredData) {
            $returnData = [
                'annexPerfectState' => 1,
                'annexCountNum'     => 0,
                'annexCurrentNum'   => 0,
            ];
            return $returnData;
        }
        $annexData = array_column($annexData, 'file_type');
        $result    = array_diff($requiredData, $annexData);


        if (!$result) {
            //全匹配 是已完善
            $returnData = [
                'annexPerfectState' => 1,
                'annexCountNum'     => count($requiredData),
                'annexCurrentNum'   => count($requiredData),
            ];
        } else {
            $returnData = [
                'annexPerfectState' => 0,
                'annexCountNum'     => count($requiredData),
                'annexCurrentNum'   => (count($requiredData) - count($result)),
            ];
        }
        return $returnData;
    }

    /**
     * 家庭详细信息
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function familyInfo($paramIn = [])
    {
        $resumeId   = $this->processingDefault($paramIn, 'resume_id', 2);
        $resumeId = intval($resumeId);
        $familyInfo = (new ResumeRepository())->familyInfo($resumeId);
        if (!$familyInfo || empty($familyInfo['child_num'])) {
            $familyInfo['child_list'] = [];
        } else {
            $familyInfo['child_list'] = json_decode($familyInfo['child_list'], true);
        }
        $familyInfo['child_num'] = $familyInfo['child_num'] ? intval($familyInfo['child_num']) : 0;


        if (!$familyInfo) {
            $returnArr['data'] = ['resume_id' => $resumeId];
        } else {
            $returnArr['data'] = $familyInfo;
        }
        return $this->checkReturn($returnArr);
    }

    public function familyUpdate($paramIn = [])
    {
        $this->log = new LogServer();

        $family_id = (new ResumeRepository())->familyUpdate($paramIn);
        $this->setLastOperator($paramIn['resume_id']);
        //数据返回
        $return['data'] = ['family_id' => $family_id];
        //添加日志
        $this->log->addLog([
            'module_id'   => $paramIn['resume_id'],
            'module_type' => 7,
            'action'      => 2,
        ]);
        return $this->checkReturn($return);
    }

    public function ebilityInfo($paramIn = [])
    {
        $resumeId    = $this->processingDefault($paramIn, 'resume_id', 2);
        $resumeId = intval($resumeId);
        $ebilityInfo = (new ResumeRepository())->ebilityInfo($resumeId);

        if ($ebilityInfo) {
            //work_experience_sql
            $work_experience_sql = "SELECT company_name,position_name,salary,work_time_start,work_time_end,
            leave_reason,witness_1,witness_2,witness_mobile,witness_position FROM `hr_work_experience` WHERE `hr_work_experience`.`economy_ablity_id` = " . $ebilityInfo['id'];

            $dataObj         = $this->getDI()->get('db_rby')->query($work_experience_sql);
            $work_experience = $dataObj->fetchAll(\Phalcon\Db::FETCH_ASSOC);

            $languages_sql = "SELECT languages,ability,lang_remarks,languages_level FROM `hr_languages` WHERE `hr_languages`.`economy_ablity_id` = " . $ebilityInfo['id'];

            $dataObj   = $this->getDI()->get('db_rby')->query($languages_sql);
            $languages = $dataObj->fetchAll(\Phalcon\Db::FETCH_ASSOC);

            $relationship_sql = "SELECT relate_first_name,relate_last_name,relate_deparment,relate_mobile FROM `hr_relationship` WHERE `hr_relationship`.`economy_ablity_id` = " . $ebilityInfo['id'];

            $dataObj      = $this->getDI()->get('db_rby')->query($relationship_sql);
            $relationship = $dataObj->fetchAll(\Phalcon\Db::FETCH_ASSOC);

            $ebilityInfo['work_experience'] = $work_experience;

            $ebilityInfo['languages'] = $languages;

            $ebilityInfo['relationship'] = $relationship;
        }
        $edus                                = (new ResumeRepository())->getEdu($resumeId);
        $ebilityInfo['education_experience'] = $edus;

        if (!$ebilityInfo) {
            $returnArr['data'] = ['resume_id' => $resumeId];
        } else {
            $returnArr['data'] = $ebilityInfo;
        }

        $returnArr['data'] = $ebilityInfo;
        return $this->checkReturn($returnArr);
    }


    public function getSurvey($paramIn = [])
    {
        $resumeId   = $this->processingDefault($paramIn, 'resume_id', 2);
        $surveyList = (new ResumeRepository())->getSurvey($resumeId);

        if (count($surveyList) > 0) {
            //Truck快递员 显示Van相关联的问卷问题 van 问题是 2;
            //https://l8bx01gcjr.feishu.cn/docs/doccn1QnwNu6j4aaxerzaod44PQ
            foreach ($surveyList as $k => &$v) {
                $v['type'] = in_array($v['type'], [5, 6, 7]) ? 2 : $v['type'];
                $v['type'] = in_array($v['type'], [4]) ? 1 : $v['type'];
            }
            $returnArr['data'] = ['dataList' => $surveyList];
        } else {
            $returnArr['data'] = ['resume_id' => $resumeId];
        }

        return $this->checkReturn($returnArr);
    }

    /**
     * 调查问卷
     * @param array $paramIn
     * @param string $source
     * @return array
     * @throws BusinessException
     */
    public function createSurvey($paramIn = [], $source = '')
    {
        $this->log = new LogServer();

        if (!isset($paramIn['resume_id']) || empty($paramIn['resume_id']) || !is_numeric($paramIn['resume_id'])) {
            return $this->checkReturn(-3, $this->getTranslation()->_('4008'));
        }

//        if (!isset($paramIn['answer_survey']) || empty($paramIn['answer_survey']) || !is_array($paramIn['answer_survey'])) {
//            return $this->checkReturn(-3, $this->getTranslation()->_('4008'));
//        }
        //处理问卷问题
        foreach ($paramIn['answer_survey'] as &$item) {
            $item['question_text'] = trim($item['question_text']);
            if ($source && $source == 'h5') {
                if (!isset($item['is_required']) || !isset($item['question_no']) || !isset($item['display'])) {
                    $this->checkReturn(-3, 'question param error');
                }

                //问卷必填校验
                if (in_array($item['is_required'],
                        [1, 2]) && $item['display'] == 'block' && empty($item['question_value'])) {
                    $question_msg = $this->getTranslation()->_('question_prefix') . $this->getTranslation()->_($item['question_no']);
                    $this->checkReturn(-3, $question_msg);
                }
            }

            //残疾证编号验证 ：【0-9】13位长度
            if ($item['type'] == '3' && $item['answer_key'] == '14' && $item['question_value'] == '1' && !empty($item['question_text'])) {
                if (!preg_match('/^[0-9]{13}$/', $item['question_text'])) {
                    return $this->checkReturn(-3, $this->getTranslation()->_('disability_certificate'));
                }
            }
        }

        $resumeId = $this->processingDefault($paramIn, 'resume_id', 2);

        $answer_survey = $this->processingDefault($paramIn, 'answer_survey');

        (new ResumeRepository())->answerAdd($answer_survey, $resumeId);

        $return['data'] = ['resume_id' => $resumeId];

        //添加日志
        $this->log->addLog([
            'module_id'   => $resumeId,
            'module_type' => enums::$log_module_type['questionnaires'],
            'action'      => enums::$log_option['create'],
            'data_after'  => $answer_survey,
        ]);
        $this->setLastOperator($resumeId);

        return $this->checkReturn($return);
    }

    /**
     * @description:by 端保存问卷问题
     * @param null
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2021/8/2 14:37
     */
    public function createSurveyBy($paramIn = [])
    {
        $this->log = new LogServer();

        if (!isset($paramIn['resume_id']) || empty($paramIn['resume_id']) || !is_numeric($paramIn['resume_id'])) {
            return $this->checkReturn(-3, $this->getTranslation()->_('4008'));
        }

        if (!isset($paramIn['answer_survey']) || empty($paramIn['answer_survey']) || !is_array($paramIn['answer_survey'])) {
            return $this->checkReturn(-3, $this->getTranslation()->_('4008'));
        }
        //处理问卷问题
        foreach ($paramIn['answer_survey'] as &$item) {
            $item['question_text'] = trim($item['question_text']);
        }

        $resumeId = $this->processingDefault($paramIn, 'resume_id', 2);

        $answer_survey = $this->processingDefault($paramIn, 'answer_survey');

        (new ResumeRepository())->answerUpdateBy($answer_survey, $resumeId);

        $return['data'] = ['resume_id' => $resumeId];

        //添加日志
        $this->log->addLog([
            'module_id'   => $resumeId,
            'module_type' => 9,
            'action'      => 1,
            'data_after'  => $answer_survey,
        ]);

        return $this->checkReturn($return);
    }


    /**
     * 单个附件上传
     *
     * @Access  public
     * @param array $paramIn
     * @return array
     * @throws ValidationException
     */
    public function uploadAnnex($paramIn = [])
    {
        $this->log = new LogServer();

        $id       = $this->processingDefault($paramIn, 'id', 2);
        $file_url = $this->processingDefault($paramIn, 'file_url', 3);
        if (!$file_url) {
            return $this->checkReturn(-3, $this->getTranslation()->_('4188'));
        }

        $this->getDI()->get('logger')->write_log($paramIn, 'info');
        //判断附件数量是否异常，每个类型的不能超过20张
        $file_nums      = [];
        $file_name_nums = [];
        $residence_booklet_first = '';//户籍照第一页
        $residence_booklet_second = '';//户籍照应聘者本人信息页
        $identity_front_url = '';//身份证正面照
        foreach ($file_url as $v) {
            $name_key = $v['type'] . '_' . $v['file_type'] . $v['file_name'] . '_nums';
            $key      = $v['type'] . '_' . $v['file_type'] . '_nums';
            //同一附件类型文件数量
            if (!isset($file_nums[$key])) {
                $file_nums[$key] = 1;
            } else {
                $file_nums[$key]++;
            }
            //同一文件名文件出现的数量
            if (!isset($file_name_nums[$name_key])) {
                $file_name_nums[$name_key] = 1;
            } else {
                $file_name_nums[$name_key]++;
            }

            //户籍照第一页
            if(!empty($v['type']) == HrAnnexModel::TYPE_RESUME && $v['file_type'] == HrAnnexModel::FILE_TYPE_RESIDENCE_BOOKLET_FIRST) {
                $residence_booklet_first = $v['object_url'];
            }
            //户籍照应聘者本人信息页
            if(!empty($v['type']) == HrAnnexModel::TYPE_RESUME && $v['file_type'] == HrAnnexModel::FILE_TYPE_RESIDENCE_BOOKLET_SECOND) {
                $residence_booklet_second = $v['object_url'];
            }

            //身份证正面照编辑简历附件
            if($paramIn["uploadAnnex_from"] == 'win_hr' && !empty($v['type']) == HrAnnexModel::TYPE_RESUME && $v['file_type'] == HrAnnexModel::FILE_TYPE_IDENTITY_FRONT) {
                $identity_front_url = $v['file_url'];
            }
        }
        //同一类型附件数量最多10张
        foreach ($file_nums as $nums) {
            if ($nums > 10) {
                $this->getDI()->get('logger')->write_log("uploadAnnexAction 上传附件数量异常,cvid:{$paramIn['id']},附件信息：" . json_encode($file_nums),
                    'info');
                return $this->checkReturn(-3, '附件数量异常');
            }
        }
//        //同一附件类型的文件名只能有一张，防止脏数据
//        foreach ($file_name_nums as $nums) {
//            if ($nums > 1) {
//                $this->getDI()->get('logger')->write_log("uploadAnnexAction 上传附件数量异常,cvid:{$paramIn['id']},附件信息：" . json_encode($file_name_nums), 'info');
//                return $this->checkReturn(-3, '附件文件名重复');
//            }
//        }
        $uploadAnnex_from = $paramIn["uploadAnnex_from"] ?? 'win_hr';
        if ($uploadAnnex_from != 'h5') {
            // 权限认证
            /* 获取简历详情 */
            $resumeInfo               = (new ResumeRepository())->resumeInfo($id);
            $interveiw_info           = (new InterviewServer())->getInterviewInfoByResumeid($id, $resumeInfo['hc_id']);
            $resumeServer = new ResumeServer() ;
            $is_have_annex_permission = $resumeServer->isHaveSalaryOrAnnexPermission(
                $id,
                $resumeInfo['filter_state'],//入职页面简历筛选阶段已走过，无需在判断
                $interveiw_info['interview_state'],
                $resumeInfo['recruiter_id'],
                $this->userInfo['id'],
                $resumeServer->checkIsNetworkFirstLineJD($resumeInfo['job_id'])?1:$this->userInfo['resume_attachment_permission']
            );
            if (!$is_have_annex_permission) {
                return $this->checkReturn(-3, $this->getTranslation()->_('2107'));
            }
        }
        //如果 非个人代理 并且 是van 必须要传车厢信息字段
        $json = $paramIn['json_data'];

        // v21270 去掉车厢信息提交
        $vanFlag = false;
//        $vanFlag = isCountry('TH') && !empty($json) && !in_array($json['hire_type'], HrStaffInfoModel::$agentTypeTogether)
//                    && in_array($json['formal'], [HrStaffInfoModel::FORMAL_1,HrStaffInfoModel::FORMAL_INTERN])
//                    && $json['job_title'] == enums::$job_title['Van Courier'];
        if($vanFlag){
            $exist = VanContainerModel::findFirst([
                'conditions' => 'staff_info_id = :staff_id:',
                'bind'       => ['staff_id' => $paramIn['staff_id']],
                'order'      => 'id desc',
            ]);
            if(empty($exist) || $exist->state == 3){
                if(empty($paramIn['container_type'])){
                    return $this->checkReturn(-3, $this->getTranslation()->_('4188'));
                }
                if($paramIn['container_type'] == VanContainerModel::HAVE_ONE && empty($paramIn['container_video_url'])){
                    return $this->checkReturn(-3, $this->getTranslation()->_('4188') . 'container_video_url');
                }
                //保存一条带审批车厢状态的记录
                $this->saveContainerInfo($paramIn['staff_id'], $paramIn['container_type'], $paramIn['container_video_url']);
            }
        }

        $data           = (new ResumeRepository())->createfile($id, $file_url);
        $return['data'] = $data;
        $this->setLastOperator($id);
        if ($data) {
            // 上传银行卡附件
            $this->uploadBankCardAnnex($paramIn);

            //户口簿，员工信息审核
            if(!empty($paramIn['staff_id']) && $uploadAnnex_from == "h5" && isCountry('TH')) {
                $conditions            = 'staff_info_id = :staff_info_id: and type = :type:';
                $bind['staff_info_id'] = $paramIn['staff_id'];
                $bind['type']          = HrStaffAnnexInfoModel::TYPE_RESIDENCE_BOOKLET_CARD;
                $annexInfo  = HrStaffAnnexInfoRepository::getStaffAnnexInfoInfo('*', $conditions, $bind);

                $db = $this->getDI()->get("db");

                $updateData['annex_path_front'] = $residence_booklet_first;
                $updateData['annex_path_rear']  = $residence_booklet_second;
                $updateData['audit_state']  = HrStaffAnnexInfoModel::AUDIT_STATE_WAIT;
                //存在审核表，并且 非 待审核+审核通过，则 判断，是否跟 原来的文件地址一致，如有一个文件地址不一致则更新。
                if(!empty($annexInfo) && !in_array($annexInfo['audit_state'], [HrStaffAnnexInfoModel::AUDIT_STATE_WAIT, HrStaffAnnexInfoModel::AUDIT_STATE_PASS]) && ($residence_booklet_first != $annexInfo['annex_path_front'] || $residence_booklet_second != $annexInfo['annex_path_rear'])) {
                    $db->updateAsDict("hr_staff_annex_info", $updateData,
                        ["conditions" => "id='{$annexInfo['id']}'"]);
                }

                if(empty($annexInfo)) {
                    $updateData['staff_info_id'] = $paramIn['staff_id'];
                    $updateData['type'] = HrStaffAnnexInfoModel::TYPE_RESIDENCE_BOOKLET_CARD;
                    $db->insertAsDict("hr_staff_annex_info", $updateData);
                }
            }

            if($paramIn["uploadAnnex_from"] == 'win_hr' && !empty($identity_front_url) && isCountry(['TH', 'PH', 'MY'])) {
                $identityInfo['resume_id']    = $id;
                $identityInfo['identity_url'] = $identity_front_url;
                if(isCountry('MY')) {
                    $nationality = enums::IS_MY_NATIONALITY;

                } elseif(isCountry('PH')) {
                    $nationality = enums::IS_PH_NATIONALITY;

                } else {
                    $nationality = enums::IS_TH_NATIONALITY;
                }

                $this->getIdentityFrontAiMosaic($identityInfo, $nationality);
            }

            //添加日志
            $this->log->addLog([
                'module_id'   => $id,
                'module_type' => enums::$log_module_type['attachment'],
                'action'      => enums::$log_option['create'],
            ]);
            return $this->checkReturn($return);
        } else {
            return $this->checkReturn(-3, $this->getTranslation()->_('4188'));
        }
    }

    //van职位 生成一条带审批的车厢信息
    public function saveContainerInfo($staffId, $type, $url = '')
    {
        $insert['staff_info_id'] = $staffId;
        $insert['date_at']       = date('Y-m-d');
        $insert['source_type']   = VanContainerModel::SOURCE_TYPE_WIN;
        $insert['type']          = (int)$type;
        $insert['video_url']     = ($insert['type'] == VanContainerModel::HAVE_ONE) ? $url : '';
        $model                   = new VanContainerModel();
        return $model->create($insert);
    }

    /**
     * 上传银行卡附件 菲律宾可以上传附件的同时更改银行卡类型和银行卡号 其他国家不可以更改银行卡类型和银行卡号
     * @param $paramIn
     * @return bool
     * @throws ValidationException
     */
    public function uploadBankCardAnnex($paramIn = [])
    {
        $this->getDI()->get('logger')->write_log("uploadBankCardAnnex paramIn :" . json_encode($paramIn, JSON_UNESCAPED_UNICODE), 'info');

        if (!isCountry(['MY', 'TH'])) {
            return true;
        }

        $staff_id           = $paramIn['staff_id'] ?? '';
        $bank_no            = $paramIn['bank_no'] ?? '';
        $bank_type          = $paramIn['bank_type'] ?? '';
        $bank_card_file_url = $paramIn['bank_card_file_url'] ?? '';
        $staff_hire_type    = $paramIn['staff_hire_type'] ?? '';
        $is_bank_no         = $paramIn['is_bank_no'] ?? 0;

        if (!$is_bank_no) {
            return true;
        }

        if (
            empty($staff_id)
            || empty($bank_card_file_url)
            || empty($bank_no)
            || empty($bank_type)
        ) {
            return true;
        }

        //验证
        $conditions            = 'staff_info_id = :staff_info_id: and type = :type:';
        $bind['staff_info_id'] = $paramIn['staff_id'];
        $bind['type']          = HrStaffAnnexInfoModel::TYPE_BANK_CARD;
        $annex_info  = HrStaffAnnexInfoRepository::getStaffAnnexInfoInfo('*', $conditions, $bind);

        if (isset($annex_info['audit_state']) && ($annex_info['audit_state'] != HrStaffAnnexInfoModel::AUDIT_STATE_REJECT)) {
            return true;
        }
        (new SysServer())->bankNoVerify($paramIn['bank_type'],$paramIn['bank_no']);
        //组装数据
        $data = [
            'staff_info_id' => $staff_id,
            'bank_type'     => $bank_type,
            'bank_no'       => $bank_no,
            'operater'      => $staff_id,
        ];

        $ac = new ApiClient('hr_rpc', '', 'update_staff_info', $this->lang);
        $ac->setParams($data);
        $result = $ac->execute();

        if (!isset($result['result']['code']) || $result['result']['code'] != 1) {
            throw new ValidationException(implode('', $result['result']['msg']));
        }

        $db = BackyardBaseModel::beginTransaction($this);

        try {
            $annex_info = HrStaffAnnexInfoModel::findFirst([
                'conditions' => 'staff_info_id = :staff_info_id: and type = :type:',
                'bind'       => [
                    'staff_info_id' => $staff_id,
                    'type'          => HrStaffAnnexInfoModel::TYPE_BANK_CARD,
                ],
            ]);

            if (empty($annex_info)) {
                $annex_info                = new HrStaffAnnexInfoModel();
                $annex_info->staff_info_id = $staff_id;
                $annex_info->type          = HrStaffAnnexInfoModel::TYPE_BANK_CARD;
            }

            $annex_info->annex_path_front = $bank_card_file_url;
            $annex_info->card_number      = $bank_no;
            if (isCountry('TH') && isset($paramIn['ai_audit_state']) && $paramIn['ai_audit_state'] == HrStaffAnnexInfoModel::AUDIT_STATE_PASS){
                $annex_info->audit_state         = HrStaffAnnexInfoModel::AUDIT_STATE_PASS;
                $annex_info->ai_audit_state      = HrStaffAnnexInfoModel::AI_AUDIT_STATE_PASS;
                $annex_info->ai_audit_state_date = date('Y-m-d H:i:s');
            }else{
                $annex_info->audit_state      = HrStaffAnnexInfoModel::AUDIT_STATE_WAIT;
            }
            $annex_info->save();
            // 添加ai识别记录结果
            $auditCheckInfo = StaffInfoAuditCheckModel::findFirst([
                'conditions' => 'staff_info_id = :staff_info_id: and type = :type: ',
                'bind'       => [
                    'staff_info_id' => $staff_id,
                    'type'          => StaffInfoAuditCheckModel::TYPE_BANK_CAR,
                ],
                'order'      => 'id desc',
            ]);
            if (!empty($auditCheckInfo) && $auditCheckInfo->ai_recognition_data) {
                $auditCheckInfo->ai_recognition_end_data = $auditCheckInfo->ai_recognition_data;
                if ($auditCheckInfo->save() !== true) {
                    $this->getDI()->get('logger')->write_log('staff_info_audit_check 保存失败 staff_info_id : ' . $staff_id,
                        'error');
                }
            }
            return $db->commit();
        } catch (Exception $e) {
            $db->rollback();
            $this->getDI()->get('logger')->write_log("uploadBankCardAnnex Error " . $e->getMessage(), 'error');
            return false;
        }
    }

    /**
     * 获取问卷
     * 摩托车:1，皮卡车:2,与你相关的问题:3
     */
    public function getSurveyByResumeId($resumeId, $type = 1)
    {
        return (new ResumeRepository())->getSurvey($resumeId, $type) ?? [];
    }

    /**
     * 简历-完善状态
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function uploadPerfect($resumeInfo, $source = '')
    {
        $resumeId = $resumeInfo['id'] ?? 0;

        if (!$resumeId) {
            throw new ValidationException(self::$t->_('resume_info_non_existent'));
        }
        $resume_data = $this->getResumeBaseInfo($resumeId);
        if (empty($resume_data)){
            throw new ValidationException(self::$t->_('resume_err_1'));
        }

        //检测黑名单
        if (isCountry('TH')){
            $black_grey_list = (new BlackgreylistServer())->check_black_grey_list([
                "resume_id" => $resumeId,
            ]);
            if ($black_grey_list["is_black_list"]){
                throw new ValidationException(self::$t->_('7500'));
            }
        }else{
            $isBalcklist = (new BlacklistRepository())->blacklistCheck([
                "resume_id" => $resumeId,
            ]);

            if ($isBalcklist > 0) {
                throw new ValidationException(self::$t->_('7500'));
            }
        }
        (new OutsourcingBlackListServer())->check($resume_data['credentials_num'] ?? '','winhr',true,$this->lang);


        /* 获取相关附件详情 */
        $annexData = (new ResumeRepository())->annexInfo($resumeId);

        $ebilityInfo = (new ResumeRepository())->ebilityInfo($resumeId);
        $import_img  = [];
        if (isset($resumeInfo['jd_type'])) {
            $_type = $resumeInfo['jd_type'];
            //必填项目
            $import_img['file_type'][1] = 1;//半身照必填
            $import_img['file_type'][2] = 1;//身份证正面
            // $import_img['file_type'][3] = 1;//身份证反面 9313需求去掉此限制
            $import_img['file_type'][4] = 1;//户籍照第一页面
            $import_img['file_type'][5] = 1;//户籍照应聘者本人信息
            $import_img['file_type'][7] = 1;//成绩单

            $import_img['file_type'][10] = 1;//驾驶证正面
            $import_img['file_type'][11] = 1;//驾驶证反面
            $import_img['file_type'][12] = 1;//车辆登记
            $import_img['file_type'][14] = 0;//车辆使用授权//错误定义 但就这么写的

            $import_img['file_type'][17] = 0;//残疾人证明-正面
            $import_img['file_type'][18] = 0;//残疾人证明-反面

            if ((new ResumeVerificationServer())->checkDeformityByCvid($resumeId)) {
                $import_img['file_type'][17] = 1;//残疾人证明-正面
                $import_img['file_type'][18] = 1;//残疾人证明-反面
            }

            //员工自有车辆 & 公司车辆不需要 车辆使用授权
            //聂梦轩需求：公司车辆不需要验证车辆使用授权
            if (!empty($ebilityInfo) && $ebilityInfo['car_owner'] == enums::$car_own_type['borrow_car']) {
                $import_img['file_type'][14] = 1;
            }

            switch ($_type) {
                //1摩托车快递员
                case '1':
                    $import_img['file_type'][7] = 0;//成绩单
                    break;
                //2皮卡车快递员
                case '2':
                case '5':
                    $import_img['file_type'][7] = 0;//成绩单
                    break;
                //3非快递员
                case '3':
                    $import_img['file_type'][10] = 0;//驾驶证正面
                    $import_img['file_type'][11] = 0;//驾驶证反面
                    $import_img['file_type'][12] = 0;//车辆登记
                    $import_img['file_type'][14] = 0;//车辆使用授权
                    break;
                //4 其他
                case '4':
                    $import_img['file_type'][7] = 0;//成绩单
                    break;
            }

            foreach ($annexData as $key => $item) {
                $import_img['file_type'][$item['file_type']] = 0;
            }

            if (array_sum($import_img['file_type']) > 0) {
                $this->getDI()->get('logger')->write_log("uploadPerfect:" . json_encode(['resume_id' => json_encode($resumeId)]) . "，参数： " . json_encode($import_img),
                    'info');
                return ['code' => 0, 'msg' => self::$t->_('4188'), 'data' => $import_img];
            }
        }

        return ['code' => 1, 'msg' => 'success', 'data' => []];
    }


    /**
     * 更新完善状态
     * @param $resumeId
     * @return bool
     */
    public function updatePerfect($resumeId)
    {

        if (!$resumeId) {
            return false;
        }
        $updateData = [
            'is_perfect' => 1,
        ];

        if (isCountry(['TH','PH','MY']) && !empty($this->userInfo['id'])) {
            $updateData['resume_last_operator']       = $this->userInfo['id'];
            $updateData['resume_last_operation_time'] = gmdate('Y-m-d H:i:s');
        }

        $this->getDI()->get('db')->updateAsDict(
            (new HrResumeModel())->getSource(),
            $updateData,
            [
                'conditions' => 'id = ?',
                'bind'       => [$resumeId],
            ]
        );

        return true;
    }

    /**
     * 附件详情
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function annexInfo($paramIn = [])
    {
        $resumeId = $this->processingDefault($paramIn, 'resume_id', 2);
        $data     = (new ResumeRepository())->annexInfo($resumeId);
        //获取问卷
        $surveyList = (new ResumeRepository())->getSurvey($resumeId);
        /* 获取简历详情 */
        $resumeInfo = (new ResumeRepository())->resumeInfo($resumeId);

        //获取工作与经历
        $ebilityInfo = (new ResumeRepository())->ebilityInfo($resumeId);

        //$returnArr['data']['resumeInfo']['sex'] = $resumeInfo['sex'];
        if ($surveyList) {
            foreach ($surveyList as $k => $v) {
                // job_category，1=摩托车 2=皮卡车 3=快递员
                if ($resumeInfo['job_category'] == 1) {
                    if ($v['answer_key'] == 8 && $v['type'] == 1) {
                        //是否有犯罪记录
                        $returnArr['data']['resumeInfo']['criminal_record'] = $v['question_value'];
                    }
                } elseif ($resumeInfo['job_category'] == 2) {
                    if ($v['answer_key'] == 10 && $v['type'] == 2) {
                        //是否有犯罪记录
                        $returnArr['data']['resumeInfo']['criminal_record'] = $v['question_value'];
                    }
                }
                if ($v['answer_key'] == 10 && $v['type'] == 3) {
                    //是否兵役过
                    $returnArr['data']['resumeInfo']['military_service'] = $v['question_value'];
                } elseif ($v['answer_key'] == 7 && $v['type'] == 3) {
                    //是否有犯罪记录
                    $returnArr['data']['resumeInfo']['criminal_record'] = $v['question_value'];
                }
            }
        } else {
            $returnArr['data']['resumeInfo']['military_service'] = "";
            $returnArr['data']['resumeInfo']['criminal_record']  = "";
        }
        $entryInfo = HrEntryModel::findFirst([
            'columns'    => 'status',
            'conditions' => 'resume_id = :resume_id:',
            'bind'       => [
                'resume_id' => $resumeId,
            ],
        ]);
        $entryInfo = empty($entryInfo) ? [] : $entryInfo->toArray();

        $is_have_entry = !empty($entryInfo['status']) && $entryInfo['status'] == HrEntryModel::STATUS_EMPLOYED ? 1 : 2;

        //车主 1=本人自有车,2=借用车辆
        $returnArr['data']['resumeInfo']['car_owner'] = $ebilityInfo['car_owner'];
        //性别 性别：1男、2女、3其他
        $returnArr['data']['resumeInfo']['sex'] = $resumeInfo['sex'];
        //是否已入职
        $returnArr['data']['resumeInfo']['is_entryed'] = $is_have_entry;
        //车辆是否为本人所有
        $returnArr['data']['resumeInfo']['is_the_car_owner'] = $resumeInfo['is_the_car_owner'];

        //权限验证 隐藏附件
        $interveiw_info           = (new InterviewServer())->getInterviewInfoByResumeid($resumeId,
            $resumeInfo['hc_id']);
        $resumeServer = new ResumeServer();
        $is_have_annex_permission = $resumeServer->isHaveSalaryOrAnnexPermission(
            $resumeId,
            $resumeInfo['filter_state'],//入职页面简历筛选阶段已走过，无需在判断
            $interveiw_info['interview_state'],
            $resumeInfo['recruiter_id'],
            $this->userInfo['id'],
            $resumeServer->checkIsNetworkFirstLineJD($resumeInfo['job_id'])?1:
            $this->userInfo['resume_attachment_permission']
        );

        if (!$is_have_annex_permission) {
            foreach ($data as $k => &$v) {
                $v['object_key'] = $this->getTranslation()->_('unauthorized_picture');
                $v['file_url']   = $this->getTranslation()->_('unauthorized_picture');
                $v['id']         = '0';
            }
        }
        $identity_status_text = '';
        if(!empty($entryInfo['status']) && $entryInfo['status'] == HrEntryModel::STATUS_TO_BE_EMPLOYED && $resumeInfo['identity_validate_status'] == HrResumeModel::IDENTITY_VALIDATE_STATUS_FAIL) {
            $identity_status_text = $this->getTranslation()->_('identity_different_self');
        }

        if(!empty($entryInfo['status']) && $entryInfo['status'] == HrEntryModel::STATUS_TO_BE_EMPLOYED && $resumeInfo['identity_validate_status'] == HrResumeModel::IDENTITY_VALIDATE_STATUS_UNTREATED) {
            $identity_status_text = $this->getTranslation()->_('identity_ai_fail_upload');

        }

        //编辑简历附件，身份证正面照是否仅支持上传图片:1仅图片，0 全部文件类型
        $switch = (new SettingEnvServer())->getSetVal('edit_resume_identity_front_file_type');
        //简历附件，提示语
        foreach ($data as $key => $oneData) {
            $data[$key]['tip_msg'] = $oneData['file_type'] == HrAnnexModel::FILE_TYPE_IDENTITY_FRONT ? $identity_status_text : '';
        }
        //My 身份证正面文件，不受文件类型开关限制，只能上传图片格式文件。
        $returnArr['data']['resumeInfo']['is_identity_front_support_image_only'] = (!empty($switch) || isCountry('MY')) ? true : false;
        //简历附件权限标识
        $returnArr['data']['resumeInfo']['is_have_annex_permission'] = $is_have_annex_permission;
        $returnArr['data']['dataList']                               = $data;
        return $this->checkReturn($returnArr);
    }

    /**
     * 简历淘汰
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function out($paramIn = [])
    {
        //逻辑处理开始

        $this->blacklist = new BlacklistRepository();
        // 开始淘汰
        $is_blacklist = isset($paramIn['is_blacklist']) ? $paramIn['is_blacklist'] : 0;
        //加入黑名单
        if ($is_blacklist == 1) {
            $id = $this->blacklist->blacklistAdd([
                "resume_id" => $paramIn['id'],
                "type"      => 5,
                "remark"    => isset($paramIn['out_reason']) ? $paramIn['out_reason'] : '',
            ]);
            if ($id == -1) {
                return $this->checkReturn(-3, $this->getTranslation()->_('7522'));
            }
        }
        unset($paramIn['is_blacklist']);
        $resume_id   = $paramIn['id'];
        $resume_info = $this->getResumeBaseInfo($resume_id);
        $resume_hcid = $resume_info['hc_id'] ?? 0;
        //淘汰简历
        $out_res = (new ResumeRepository())->out($paramIn);

        //hr操作淘汰 若客服处理进度等于待处理、延期处理、待审核
        //      点击淘汰简历、将客服处理进度改成=处理终止，并将处理终止原因标记为“招聘HR已处理”
        if (env('country_code') == 'PH') {
            (new CustomerresumeRepository())->getCustomerResumeById($resume_id);
        }
        //检查是否有面试中数据
        $interview = HrInterviewModel::findFirst([
            'conditions' => "resume_id = :resume_id: and hc_id = :hc_id:",
            'bind'       => [
                'resume_id' => $resume_id,
                'hc_id'     => $resume_hcid,
            ],
        ]);

        if ($interview) {
            //修改面试表状态
            $interview->state = 30;
            $interview->save();

            //写入hr_log表，面试不通过情况写，调用面试反馈时feedback和feedbackupdate不再写入hr_log表
            //因为hr_log和resume_out_log没有做唯一关联，此次修正：resume_out_log新增hr_log_id字段，用来和hr_log关联
            //之前的产品设计和开发逻辑我也不清楚
            $action       = enums::$log_option['write'];
            $module_type  = enums::$log_module_type['interview_feedback'];

            $states = enums::$log_status['not_pass'];
            $level = (new InterviewRepository())->selectLevel($interview->interview_id);

            $module_level = (int)$level + 1;
            $hr_log_id    = (new LogServer())->addLog([
                'module_id'     => $interview->interview_id,
                'action'        => $action,
                'module_type'   => $module_type,
                'module_status' => $states,
                'data_after'    => $paramIn,
                'module_level'  => $module_level,
            ]);
            (new InterviewServer())->addHrInterviewRecord($interview->interview_id,HrInterviewRecordModel::STATE_NO_PASS);

            //写入resume_out_log表
            $resumeOutLogModel               = new ResumeOutLogModel();
            $resumeOutLogModel->interview_id = $interview->interview_id;
            $resumeOutLogModel->resume_id    = $interview->resume_id;
            $resumeOutLogModel->hc_id        = $interview->hc_id;
            //$resumeOutLogModel->out_type = $paramIn['out_type'];  //面试不通过/淘汰写入pass_type_record表
            $resumeOutLogModel->hr_log_id = $hr_log_id; //添加hr_log_id，和hr_log_id做关联
            $resumeOutLogModel->save();

            //面试不通过/淘汰写入pass_type_record表
            $resume_out_log_id = $this->getDI()->get('db')->lastInsertId();
            $db                = $this->getDI()->get('db');
            $db->updateAsDict('hr_interview_pass_type_record', ["deleted" => 1], [
                'conditions' => "resume_id = $resume_id ",
            ]);
            $pass_record_model = new  HrInterviewPassTypeRecordModel();
            if ($paramIn['is_pass'] == 1) {
                if (!empty($paramIn['out_type_list'])) {
                    foreach ($paramIn['out_type_list'] as $key => $val) {
                        $batch_data[] = [
                            'business_type' => 3,
                            'business_id' => $resume_out_log_id,
                            'resume_id' => $resume_id,
                            'out_type' => 1,
                            'deleted' => 0,
                            'pass_type' => $val['pass_type'],
                            'pass_reason' => $val['out_type'],
                            'operater' => $this->userInfo['id'],
                            'pass_remark' => !empty($val['pass_remark']) ? $val['pass_remark'] : '',
                        ];
                    }
                    $pass_record_model->batch_insert($batch_data);
                }
            } else {
                foreach ($paramIn['out_type_list'] as $key => $val) {
                    $batch_data[] = [
                        'business_type' => 3,
                        'business_id' => $resume_out_log_id,
                        'resume_id' => $resume_id,
                        'out_type' => 2,
                        'deleted' => 0,
                        'pass_type' => $val['pass_type'],
                        'pass_reason' => $val['out_type'],
                        'operater' => $this->userInfo['id'],
                        'pass_remark' => !empty($val['pass_remark']) ? $val['pass_remark'] : '',
                    ];
                }
                $pass_record_model->batch_insert($batch_data);
            }

            $sql                    = "select * from hr_interview_subscribe where interview_id =" . $interview->interview_id;
            $data                   = $this->getDI()->get('db_rby')->query($sql);
            $interviewSubscribeData = $data->fetch(\Phalcon\Db::FETCH_ASSOC);
            if ($interviewSubscribeData) {
                $sql = "update hr_interview_subscribe set status=2 where interview_id = " . $interview->interview_id;
                $this->getDI()->get('db')->query($sql);

                //修改面试官操作表
                $paramIn['ope_id']     = $paramIn['ope_id'] ?? 0;
                $interviewer_operation = HrInterviewerOperationModel::findFirst([
                    'conditions' => 'id = :id:',
                    'bind'       => ['id' => $paramIn['ope_id']],
                ]);
                //这块注释逻辑和by不一致，应该是错的，进行修改
                //$operation_state = $paramIn['out_type'] == 3 ? 2 : $paramIn['out_type'];
                $operation_state = 2;
                if ($interviewer_operation) {
                    $interviewer_operation->state = $operation_state;
                    $res                          = $interviewer_operation->save();
                    //日志
                    $level = $res ? 'info' : 'notice';
                    $this->getDI()->get('logger')->write_log("resume-out:淘汰简历 interview_sub_id-" . $interviewSubscribeData['id'],
                        $level);
                } else {
                    $this->getDI()->get('logger')->write_log("resume-out:淘汰简历-是否是旧数据，请查证 interview_sub_id-" . $interviewSubscribeData['id'],
                        'info');
                }
            }
        }

        // 数据返回
        $returnData['data'] = ['resume_id' => $resume_id];
        return $this->checkReturn($returnData);
    }

    /**
     * 删除附件
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function deleteAnnex($paramIn = [])
    {
        $id             = $this->processingDefault($paramIn, 'id', 2);
        $data           = (new ResumeRepository())->deleteAnnex($id);
        $annex = HrAnnexModel::findFirst($id);
        if ($annex) {
            (new ResumeServer())->setLastOperator($annex->oss_bucket_key);
        }
        $return['data'] = ['id' => $data];
        return $this->checkReturn($return);
    }

    /**
     * 删除附件
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function deleteAnnexH5($paramIn = [])
    {
        $id        = $this->processingDefault($paramIn, 'id', 2);
        $resume_id = $this->processingDefault($paramIn, 'resume_id', 2);
        $annexObj  = HrAnnexModel::findFirst([
            'conditions' => "id = :annex_id:",
            'bind'       => ['annex_id' => $id],
            'columns'    => ['id', 'oss_bucket_key'],
        ]);
        if (!empty($annexObj) && $resume_id == $annexObj->oss_bucket_key) {
            $data           = (new ResumeRepository())->deleteAnnex($id);
            $return['data'] = ['id' => $data];
            return $this->checkReturn($return);
        }

        return $this->checkReturn(-3, 'system error:cvid invalid');
    }

    /**
     * 简历信息（全）
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function resumeInfoAll($paramIn = [], $souce_type = 'winhr')
    {
        $this->h5appointServer = new H5appointServer();

        $resumeId = $this->processingDefault($paramIn, 'resume_id', 2);
        if (!$resumeId) {
            $phone = $this->processingDefault($paramIn, 'phone', 2);
            if ($phone) {
                $resumeId = (new ResumeRepository())->getResumeId($phone);
            }
        }
        if (empty($resumeId)) {
            $this->getDI()->get('logger')->write_log("resumeInfoAll,简历ID为空:" . json_encode(['resume_id' => json_encode($resumeId)]) . "，参数： " . json_encode($paramIn),
                'info');
            return $this->checkReturn(-3, 'resume id empty');
        }
        $resumeId = intval($resumeId);
        $resumeArr = [
            'resume_id' => $resumeId,
        ];
        //获取简历附件全类型
        //$fileType = self::getAnnexFileType();
        //简历基本信息
        $resumeInfoArr = self::resumeInfo($resumeArr, $souce_type);
        $resumeInfo = $resumeInfoArr['data'] ?? [];
        //工作经历与能力
        $ebilityInfoArr = self::ebilityInfo($resumeArr);
        //家庭信息
        $familyInfoArr = self::familyInfo($resumeArr);

        //问卷答案
        $surveyArr = $this->getSurvey($resumeArr)['data']['dataList'] ?? [];

        //固化的问题列表
        $oldQuestionList = $this->getOldQuestionList($surveyArr);

        //问卷列表
        $display_platform = $paramIn['display_platform'] ?? '';
        $questionListObj  = new H5appointServer($this->lang, $this->timezone);
        //最新问题列表
        $questionListArr = $questionListObj->getQuestionList([
                'type'             => 0,
                'display_platform' => $display_platform,
                'jd_id'            => $resumeInfo["job_id"],
                'souce_type'       => $souce_type,
                'reserve_type'     => $resumeInfo['reserve_type'],
            ])['data']['dataList'] ?? [];
        foreach ($questionListArr as $k => $v) {

            if (($v['type'] != $resumeInfo['job_category']) && ($v['type'] != 3)) {
                unset($questionListArr[$k]);
            }
        }

        $questionListArr = array_values($questionListArr);
        //获取问卷
        $surveyList = (new ResumeRepository())->getSurvey($resumeId);
        //获取工作与经历
        $ebilityInfo = (new ResumeRepository())->ebilityInfo($resumeId);

        $returnArr                      = [];
        $resumeInfo['military_service'] = "";
        $resumeInfo['criminal_record']  = "";
        if ($surveyList) {
            foreach ($surveyList as $k => $v) {
                // job_category，1=摩托车 2=皮卡车 3=快递员
                if ($resumeInfo['job_category'] == 1) {
                    if ($v['answer_key'] == 10 && $v['type'] == 1) {
                        //是否兵役过
                        $resumeInfo['military_service'] = $v['question_value'];
                    } elseif ($v['answer_key'] == 8 && $v['type'] == 1) {
                        //是否有犯罪记录
                        $resumeInfo['criminal_record'] = $v['question_value'];
                    }
                } elseif ($resumeInfo['job_category'] == 2) {
                    if ($v['answer_key'] == 12 && $v['type'] == 2) {
                        //是否兵役过
                        $resumeInfo['military_service'] = $v['question_value'];
                    } elseif ($v['answer_key'] == 10 && $v['type'] == 2) {
                        //是否有犯罪记录
                        $resumeInfo['criminal_record'] = $v['question_value'];
                    }
                } elseif ($resumeInfo['job_category'] == 3) {
                    if ($v['answer_key'] == 10 && $v['type'] == 3) {
                        //是否兵役过
                        $resumeInfo['military_service'] = $v['question_value'];
                    } elseif ($v['answer_key'] == 7 && $v['type'] == 3) {
                        //是否有犯罪记录
                        $resumeInfo['criminal_record'] = $v['question_value'];
                    }
                }
            }
        }
        //车主 1=本人自有车,2=借用车辆
        $resumeInfo['car_owner'] = $ebilityInfo['car_owner'];
        if (isCountry('My') && !empty($resumeInfo['advertising_channel']) && !empty($resumeInfo['resume_driving_license_type'])) {
            $resumeInfo['driving_license_type'] = $resumeInfo['resume_driving_license_type'];
        } else {
            $resumeInfo['driving_license_type'] = $ebilityInfo['driving_license_type'];
        }

        //性别 性别：1男、2女、3其他
        // $resumeInfo['sex'] = (new \FlashExpress\bi\App\Server\ResumeServer())->getResumeSex($resumeInfo['sex'], $resumeInfo['call_name']);

        $childResumeServer = Tools::reBuildCountryInstance(new ResumeServer());
        // 获取教育
        $edus                                      = (new ResumeRepository())->getEdu($resumeId);
        $data                                      = \FlashExpress\bi\App\library\enums::$phone_area_code_enums;
        $returnArr['data']['education_experience'] = $edus;
        $returnArr['data']                         = [
            'phone_area_code_list' => $data,
            'resumeInfo'           => $resumeInfo,
            'ebility'              => $ebilityInfoArr['data'],
            'family'               => $familyInfoArr['data'],
            'survey'               => $surveyArr,              //答案
            'questionList'         => $questionListArr,        //最新问题列表
            'oldQuestionList'      => $oldQuestionList,        //固化的问题列表
            'annex_type'           => self::getAnnexFileType(),//获取简历附件全类型
            'annexList'            => $childResumeServer->annexListLang(),   //获取简历附件全类型
        ];

        return $this->checkReturn($returnArr);
    }

    /**
     * 简历信息by（全）
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function resumeInfoAllBy($paramIn = [])
    {
        $this->h5appointServer = new H5appointServer();

        $resumeId = $this->processingDefault($paramIn, 'resume_id', 2);
        if (!$resumeId) {
            $phone = $this->processingDefault($paramIn, 'phone', 2);
            if ($phone) {
                $resumeId = (new ResumeRepository())->getResumeId($phone);
            }
        }
        if (empty($resumeId)) {
            $this->getDI()->get('logger')->write_log("resumeInfoAll,简历ID为空:" . json_encode(['resume_id' => json_encode($resumeId)]) . "，参数： " . json_encode($paramIn),
                'info');
            return $this->checkReturn(-3, 'resume id empty');
        }

        $resumeArr = [
            'resume_id' => $resumeId,
        ];

        //简历基本信息
        $resumeInfoArr = self::resumeInfo($resumeArr, 'h5');
        //工作经历与能力
        $ebilityInfoArr = self::ebilityInfo($resumeArr);
        //家庭信息
        $familyInfoArr = self::familyInfo($resumeArr);
        //问卷答案
        $surveyArr   = $this->getSurvey($resumeArr);
        $_survey_all = $surveyArr['data']['dataList'] ?? [];
        $survey_all  = [];
        //答案列表
        foreach ($_survey_all as $key => $item) {
            $survey_all['type_' . $item['type']][] = $item;
        }
        //问卷列表
        $type                       = $resumeInfoArr['data']['job_category'];
        $display_platform           = $paramIn['display_platform'] ?? '';
        $questionListObj            = new H5appointServer($this->lang, $this->timezone);
        $questionListArr            = $questionListObj->getQuestionList([
            'type'             => $type,
            'display_platform' => $display_platform,
        ]);
        $questionListType           = [];
        $questionListType['type_1'] = $this->h5appointServer->getQuestionList([
                'type'             => 1,
                'display_platform' => $display_platform,
            ])['data']['dataList'] ?? [];
        $questionListType['type_2'] = $this->h5appointServer->getQuestionList([
                'type'             => 2,
                'display_platform' => $display_platform,
            ])['data']['dataList'] ?? [];
        $questionListType['type_3'] = $this->h5appointServer->getQuestionList([
                'type'             => 3,
                'display_platform' => $display_platform,
            ])['data']['dataList'] ?? [];
        //俊飞要求$questionListArr 这里不显示 3 的列表
        if ($type == 3) {
            $questionListArr['data']['dataList'] = [];
        }

        /* 获取简历详情 */
        $resumeInfo = (new ResumeRepository())->resumeInfo($resumeId);
        //获取问卷
        $surveyList = (new ResumeRepository())->getSurvey($resumeId);
        //获取工作与经历
        $ebilityInfo = (new ResumeRepository())->ebilityInfo($resumeId);

        $returnArr                                           = [];
        $returnArr['data']['resumeInfo']['military_service'] = "";
        $returnArr['data']['resumeInfo']['criminal_record']  = "";
        if ($surveyList) {
            foreach ($surveyList as $k => $v) {
                // job_category，1=摩托车 2=皮卡车 3=快递员
                if ($resumeInfo['job_category'] == 1) {
                    if ($v['answer_key'] == 10 && $v['type'] == 1) {
                        //是否兵役过
                        $returnArr['data']['resumeInfo']['military_service'] = $v['question_value'];
                    } elseif ($v['answer_key'] == 8 && $v['type'] == 1) {
                        //是否有犯罪记录
                        $returnArr['data']['resumeInfo']['criminal_record'] = $v['question_value'];
                    }
                } elseif ($resumeInfo['job_category'] == 2) {
                    if ($v['answer_key'] == 12 && $v['type'] == 2) {
                        //是否兵役过
                        $returnArr['data']['resumeInfo']['military_service'] = $v['question_value'];
                    } elseif ($v['answer_key'] == 10 && $v['type'] == 2) {
                        //是否有犯罪记录
                        $returnArr['data']['resumeInfo']['criminal_record'] = $v['question_value'];
                    }
                } elseif ($resumeInfo['job_category'] == 3) {
                    if ($v['answer_key'] == 10 && $v['type'] == 3) {
                        //是否兵役过
                        $returnArr['data']['resumeInfo']['military_service'] = $v['question_value'];
                    } elseif ($v['answer_key'] == 7 && $v['type'] == 3) {
                        //是否有犯罪记录
                        $returnArr['data']['resumeInfo']['criminal_record'] = $v['question_value'];
                    }
                }
            }
        }
        //车主 1=本人自有车,2=借用车辆
        $returnArr['data']['resumeInfo']['car_owner']            = $ebilityInfo['car_owner'];
        $returnArr['data']['resumeInfo']['driving_license_type'] = $ebilityInfo['driving_license_type'];
        //性别 性别：1男、2女、3其他
        $returnArr['data']['resumeInfo']['sex'] = $resumeInfo['sex'];
        // 获取教育
        $edus                                      = (new ResumeRepository())->getEdu($resumeId);
        $data                                      = \FlashExpress\bi\App\library\enums::$phone_area_code_enums;
        $returnArr['data']['education_experience'] = $edus;

        if (isCountry('PH')) {
            /**
             * winHr 编辑简历时，公积金、社保卡、医疗保险卡 当填写了卡号时，又选择了否
             * 客户端依旧上报了卡号，这里在h5回显时，做个清空处理
             */
            // 存在公积金 且状态为否
            if (isset($resumeInfoArr['data']['is_have_fund']) && HrResumeExtendModel::IS_HAVE_FUND_NO == $resumeInfoArr['data']['is_have_fund']) {
                // 强制清空
                $resumeInfoArr['data']['fund_num'] = "";
            }

            // 存在社保 且状态为否
            if (isset($resumeInfoArr['data']['is_have_social_security']) && HrResumeExtendModel::IS_HAVE_SOCIAL_SECURITY_NO == $resumeInfoArr['data']['is_have_social_security']) {
                // 强制清空
                $resumeInfoArr['data']['social_security_num'] = "";
            }

            // 存在医疗保险号 且状态为否
            if (isset($resumeInfoArr['data']['is_have_medical_insurance']) && HrResumeExtendModel::IS_HAVE_MEDICAL_INSURANCE_NO == $resumeInfoArr['data']['is_have_medical_insurance']) {
                // 强制清空
                $resumeInfoArr['data']['medical_insurance_num'] = "";
            }
        }
        $identity_annex_info_obj = HrStaffAnnexInfoModel::findFirst([
            'conditions' => 'staff_info_id = :staff_info_id: and type = :type:',
            'bind'       => [
                'staff_info_id' => $paramIn['staff_id'],
                'type' => HrStaffAnnexInfoModel::TYPE_ID_CARD,
            ],
        ]);

        $identity_annex_info = !empty($identity_annex_info_obj) ? $identity_annex_info_obj->toArray() : [];
        //ai 识别身份证
        $resumeInfoArr['data']['ai_id_card_state'] = '0';
        if (!empty($identity_annex_info) && $identity_annex_info['audit_state'] == 1) {
            $resumeInfoArr['data']['ai_id_card_state'] = '1';
        }

        if (!empty($identity_annex_info) && !is_null($identity_annex_info['audit_state'])){
            $resumeInfoArr['data']['staff_id_card_audit_state'] = $identity_annex_info['audit_state'];
        }else{
            $resumeInfoArr['data']['staff_id_card_audit_state'] = HrStaffAnnexInfoModel::AUDIT_STATE_TO_BE_UPLOAD;
        }

        $childResumeServer = Tools::reBuildCountryInstance(new ResumeServer());
        $annexListLang = $childResumeServer->annexListLang();
        $annexListLang['file_type2']['url'] = 'https://fle-staging-asset-internal.oss-ap-southeast-1.aliyuncs.com/workOrder/1631694858-6c267d7ada7148f88a0a2bf70a068329.png';

        //注入员工信息
        $staffInfo = (new HrStaffInfoModel())->getHrStaffInfo($paramIn['staff_id']);
        $returnArr['data']['resumeInfo']['staff_hire_type'] = $staffInfo['hire_type'] ?? 0;

        // 银行卡照片
        if (isCountry(['TH','MY','PH']) && !empty($paramIn['staff_id'])){
            $annex_info = HrStaffAnnexInfoModel::findFirst([
                'conditions' => 'staff_info_id = :staff_info_id: and type = :type:',
                'bind' => [
                    'staff_info_id' => $paramIn['staff_id'],
                    'type' => HrStaffAnnexInfoModel::TYPE_BANK_CARD,
                ],
            ]);
            $annex_info = $annex_info ? $annex_info->toArray() : [];
            $bankList = BankListModel::find()->toArray();
            $bankList = array_column($bankList,'bank_name','bank_id');

            $returnArr['data']['resumeInfo']['staff_bank_card_url'] = $annex_info['annex_path_front'] ?? '';
            $returnArr['data']['resumeInfo']['staff_bank_no'] = $staffInfo['bank_no'] ?? '';
            $returnArr['data']['resumeInfo']['staff_bank_type'] = $staffInfo['bank_type'] ?? 0;
            $returnArr['data']['resumeInfo']['staff_bank_type_name'] = $bankList[$staffInfo['bank_type']] ?? '';
            if (!empty($annex_info) && !is_null($annex_info['audit_state'])){
                $returnArr['data']['resumeInfo']['staff_bank_audit_state'] = $annex_info['audit_state'];
            }else{
                $returnArr['data']['resumeInfo']['staff_bank_audit_state'] = HrStaffAnnexInfoModel::AUDIT_STATE_TO_BE_UPLOAD;
            }
            
        }

        $conditions            = 'staff_info_id = :staff_info_id: and type = :type:';
        $bind['staff_info_id'] = $paramIn['staff_id'];
        $bind['type']          = HrStaffAnnexInfoModel::TYPE_RESIDENCE_BOOKLET_CARD;
        $residenceBookletInfo  = HrStaffAnnexInfoRepository::getStaffAnnexInfoInfo('*', $conditions, $bind);
        //户口簿 审核状态
        $resumeInfoArr['data']['residence_booklet_audit_status'] = empty($residenceBookletInfo) ? HrStaffAnnexInfoModel::AUDIT_STATE_TO_BE_UPLOAD : $residenceBookletInfo['audit_state'];

        //如果是非个人代理 并且 van 职位 前端需要显示 //如果存在 带审批记录 不展示 车厢状态相关字段 https://flashexpress.feishu.cn/docx/Fp7ldI2VVoZMNuxhMXTcrq0YnIe
        $isShowContainer = false;
        // v21270 去掉此页面车厢状态/车厢视频的表单项
//        if(isCountry('TH') && !in_array($staffInfo['hire_type'], HrStaffInfoModel::$agentTypeTogether)
//            && $staffInfo['job_title'] == enums::$job_title['Van Courier']
//            && in_array($staffInfo['formal'],[HrStaffInfoModel::FORMAL_1,HrStaffInfoModel::FORMAL_INTERN])){
//            $exist = VanContainerModel::findFirst([
//                'conditions' => 'staff_info_id = :staff_id:',
//                'bind'       => ['staff_id' => $paramIn['staff_id']],
//                'order'      => 'id desc',
//            ]);
//            if(empty($exist) || $exist->state == 3){
//                $isShowContainer = true;
//            }
//        }

        $returnArr['data'] = [
            'phone_area_code_list' => $data,
            'resumeInfo'           => array_merge($resumeInfoArr['data'], $returnArr['data']['resumeInfo']),
            'ebility'              => $ebilityInfoArr['data'],
            'family'               => $familyInfoArr['data'],
            'survey'               => $surveyArr['data']['dataList'] ?? [],
            'survey_all'           => $survey_all,
            'questionList'         => $questionListArr['data']['dataList'],
            'question_list_type'   => $questionListType,
            'annex_type'           => self::getAnnexFileType(),//获取简历附件全类型
            'annexList'            => $annexListLang,
            'isShowContainer'      => $isShowContainer,//是否显示车厢相关字段
        ];
        return $this->checkReturn($returnArr);
    }

    /**
     * ai 审核身份证
     * @param $paramIn
     * @return array
     */
    public function aiIdCardAudit($paramIn)
    {
        $staff_info_id       = $paramIn['staff_id'];
        $file_url            = $paramIn['file_url'];
        $id_card_audit_param = [
            'staff_id' => $staff_info_id,
            'file_url' => $file_url,
        ];

        //泰国原逻辑不变，新增逻辑：除泰国外其他国家走人工审核逻辑
        $country_code = getCountryValue();
        if ($country_code == 1) { //泰国
            $hr_staff_item = HrStaffItemsModel::findFirst([
                'conditions' => "staff_info_id = :staff_info_id: and item = 'NATIONALITY'",
                'bind'       => [
                    'staff_info_id' => $staff_info_id,
                ],
            ]);
            $hr_staff_item = empty($hr_staff_item) ? [] : $hr_staff_item->toArray();
            if (empty($hr_staff_item)) {
                $nationality = 0;
            } else {
                $nationality = $hr_staff_item['value'];
            }
        } else {
            $hr_staff_info = HrStaffInfoModel::findFirst([
                'conditions' => "staff_info_id = :staff_info_id:",
                'bind'       => [
                    'staff_info_id' => $staff_info_id,
                ],
            ]);
            $hr_staff_info = empty($hr_staff_info) ? [] : $hr_staff_info->toArray();
            if (!empty($hr_staff_info)) {
                $nationality = 0;
            }
        }

        if ($nationality == 1) {
            $apiClient = new ApiClient('by_rpc', '', 'AiIdCardAudit', $this->lang);
            $apiClient->setParams($id_card_audit_param);
            $result = $apiClient->execute();
            $this->getDI()->get('logger')->write_log("aiIdCardAudit 参数:" . json_encode($id_card_audit_param) . '结果：' . json_encode($result),
                'info');
            if ($result['result']['code'] == 0) {
                return $this->checkReturn(-3, $result['result']['msg']);
            }
            //0识别失败1识别成功
            if ($result['result']['data']['code'] == 1) {
                //识别成功
                $result['result']['data']['result']['staff_id'] = $staff_info_id;
                $submit_param                                   = $result['result']['data']['result'];
                $submit_apiClient                               = new ApiClient('by_rpc', '', 'AiIdCardSubmit',
                    $this->lang);
                $submit_apiClient->setParams($submit_param);
                $submit_result = $submit_apiClient->execute();
                $this->getDI()->get('logger')->write_log("aiIdCardAudit AiIdCardSubmit参数:" . json_encode($submit_param) . '结果：' . json_encode($submit_result),
                    'info');
                //1 审核成功 2 审核失败
//                    if($result['result']['data']['result']['status'] == 2) {
//                        $result['result']['data']['result']['msg'] = $this->getTranslation()->_('id_card_ai_audit_error');
//                    }

                //记录审核日志
//                $identity_annex_info = HrStaffAnnexInfoModel::findFirst([
//                    'conditions' => 'staff_info_id = :staff_info_id: and type = :type:',
//                    'bind'       => [
//                        'staff_info_id' => $staff_info_id,
//                        'type' => HrStaffAnnexInfoModel::TYPE_ID_CARD,
//                    ],
//                ]);
//                //记录审核前状态
//                $audit_before_state = $identity_annex_info->audit_state ?? 0;
//                $audit_log          = [
//                    'staff_info_id'      => $staff_info_id,
//                    'audit_id'           => 10000,
//                    'audit_name'         => 'AI审核',
//                    'audit_before_state' => $audit_before_state,
//                    'audit_after_state'  => $result['result']['data']['result']['status'],
//                ];
//                $audit_log_result   = $this->getDI()->get('db')->insertAsDict('staff_identity_annex_audit_log',
//                    $audit_log);
//                if (!$audit_log_result) {
//                    $msg = "identity annex audit log insert fail:" . var_export($audit_log, true) . PHP_EOL;
//                    $this->getDI()->get("logger")->write_log($msg, 'info');
//                }
            }
        } else {
            $identity_annex_info = HrStaffAnnexInfoModel::findFirst([
                'conditions' => 'staff_info_id = :staff_info_id: and type = :type:',
                'bind'       => [
                    'staff_info_id' => $staff_info_id,
                    'type' => HrStaffAnnexInfoModel::TYPE_ID_CARD,
                ],
            ]);
            if (empty($identity_annex_info)) {
                $identity_annex_info                = new HrStaffAnnexInfoModel();
                $identity_annex_info->staff_info_id = $staff_info_id;
            }
            $identity_annex_info->audit_state         = 0;
            $identity_annex_info->ai_audit_state      = 2;
            $identity_annex_info->annex_path_front     = $file_url;
            $identity_annex_info->ai_audit_state_date = date('Y-m-d H:i:s');
            if ($identity_annex_info->save() !== true) {
                $this->getDI()->get('logger')->write_log('aiIdCardAudit 非泰国籍 保存失败，参数：' . json_encode($id_card_audit_param),
                    'error');
                return $this->checkReturn(-3, 'error');
            } else {
                $this->getDI()->get('logger')->write_log('aiIdCardAudit 非泰国籍 保存成功，参数：' . json_encode($id_card_audit_param),
                    'info');
                $result['result']['data'] = [
                    'code'   => 1,
                    'msg'    => '',
                    'result' => [
                        'status'        => 2,
                        'upload_count'  => 4,
                        'file_url'      => $file_url,
                        'msg'           => $this->getTranslation()->_('id_card_upload_success'),
                        'staff_info_id' => $staff_info_id,
                    ],
                ];
            }
        }
        return $this->checkReturn($result['result']);
    }

    /**
     * 附件翻译
     * @return array
     */
    public function annexListLang(): array
    {
        $data = [
            'file_type1'  => [
                'title' => $this->getTranslation()->_('4151'),
                'text'  => $this->getTranslation()->_('4165'),
                'url'   => 'https://fle-staging-asset-internal.oss-ap-southeast-1.aliyuncs.com/backyardUpload/1559812569-2afe9c73999d4c4984878588370d4b7b.jpg',
            ],
            'file_type2'  => [
                'title' => $this->getTranslation()->_('4152'),
                'text'  => $this->getTranslation()->_('4152'),
                'url'   => 'https://fle-staging-asset-internal.oss-ap-southeast-1.aliyuncs.com/backyardUpload/1559812697-156b387c531943c09abb21662454aa7a.jpg',
            ],
            'file_type3'  => [
                'title' => $this->getTranslation()->_('4153'),
                'text'  => $this->getTranslation()->_('4153'),
                'url'   => 'https://fle-staging-asset-internal.oss-ap-southeast-1.aliyuncs.com/backyardUpload/1559812759-635cc86cb9bd445488242ba82259f2ef.jpg',
            ],
            'file_type4'  => [
                'title' => $this->getTranslation()->_('4154'),
                'text'  => $this->getTranslation()->_('4154'),
                'url'   => 'https://tc-static-asset-internal.flashexpress.com/workOrder/1715766335-0dfe926d811b4a24b79873c7ac6f190a.jpeg',
            ],
            'file_type5'  => [
                'title' => $this->getTranslation()->_('4155'),
                'text'  => $this->getTranslation()->_('4155_text'),
                'url'   => 'https://tc-static-asset-internal.flashexpress.com/workOrder/1715766335-0dfe926d811b4a24b79873c7ac6f190a.jpeg',
            ],
            'file_type6'  => [
                'title' => $this->getTranslation()->_('4156'),
                'text'  => $this->getTranslation()->_('4165'),
                'url'   => 'https://fle-staging-asset-internal.oss-ap-southeast-1.aliyuncs.com/backyardUpload/1559813010-291dae54378a41deab10942444094e47.jpg',
            ],
            'file_type7'  => [
                'title' => $this->getTranslation()->_('4157'),
                'text'  => $this->getTranslation()->_('4165'),
                'url'   => 'https://fle-staging-asset-internal.oss-ap-southeast-1.aliyuncs.com/backyardUpload/1559813062-9dca1378ba3644acabf8e812331a6e54.jpg',
            ],
            'file_type8'  => [
                'title' => $this->getTranslation()->_('4158'),
                'text'  => $this->getTranslation()->_('4165'),
                'url'   => 'https://fle-staging-asset-internal.oss-ap-southeast-1.aliyuncs.com/backyardUpload/1559813180-bd02cab70a7341dca0fa85814f4729f6.jpg',
            ],
            'file_type9'  => [
                'title' => $this->getTranslation()->_('4159'),
                'text'  => $this->getTranslation()->_('4165'),
                'url'   => 'https://fle-staging-asset-internal.oss-ap-southeast-1.aliyuncs.com/backyardUpload/1559812569-2afe9c73999d4c4984878588370d4b7b.jpg',
            ],
            'file_type10' => [
                'title' => $this->getTranslation()->_('4160'),
                'text'  => $this->getTranslation()->_('4165'),
                'url'   => 'https://fle-staging-asset-internal.oss-ap-southeast-1.aliyuncs.com/backyardUpload/1559813180-bd02cab70a7341dca0fa85814f4729f6.jpg',
            ],
            'file_type11' => [
                'title' => $this->getTranslation()->_('4161'),
                'text'  => $this->getTranslation()->_('4165'),
                'url'   => 'https://fle-staging-asset-internal.oss-ap-southeast-1.aliyuncs.com/backyardUpload/1559813235-54a62f382b2e4c1c82a2a323b4fe53ec.jpg',
            ],
            'file_type12' => [
                'title' => $this->getTranslation()->_('4162'),
                'text'  => $this->getTranslation()->_('4162_text'),
                'url'   => 'https://fle-staging-asset-internal.oss-ap-southeast-1.aliyuncs.com/backyardUpload/1559813300-3742977730f9477085f70dea85281257.jpg',
            ],
            'file_type13' => [
                'title' => $this->getTranslation()->_('4163'),
                'text'  => $this->getTranslation()->_('4165'),
                'url'   => 'https://fle-staging-asset-internal.oss-ap-southeast-1.aliyuncs.com/backyardUpload/1559813336-a507472359014a9a930cbd331e4ca994.jpg',
            ],
            'file_type14' => [
                'title' => $this->getTranslation()->_('4164'),
                'text'  => $this->getTranslation()->_('4165'),
                'url'   => 'https://fle-staging-asset-internal.oss-ap-southeast-1.aliyuncs.com/backyardUpload/1559813336-a507472359014a9a930cbd331e4ca994.jpg',
            ],
            'file_type16' => [
                'title' => $this->getTranslation()->_('4166'),
                'text'  => $this->getTranslation()->_('4167'),
                'url'   => '',
            ],
            'file_type17' => [
                'title' => $this->getTranslation()->_('4168'),//残疾人证-正面
                'text'  => $this->getTranslation()->_('4168'),
                'url'   => 'https://fle-staging-asset-internal.oss-ap-southeast-1.aliyuncs.com/workOrder/1604903436-ea372fe9aa4b4552a4f324713a2cc181.png',
            ],
            'file_type18' => [
                'title' => $this->getTranslation()->_('4169'),//残疾人证-反面
                'text'  => $this->getTranslation()->_('4169'),
                'url'   => 'https://sai.flashexpress.com/workOrder/1604975937-7acc98e3a9cf472c808e8e5906171605.png',
            ],
            'file_type19' => [
                'title' => $this->getTranslation()->_('annex_4170'),//最高学历证书
                'text'  => $this->getTranslation()->_('annex_4170'),
                'url'   => '',
            ],
            'file_type20' => [
                'title' => $this->getTranslation()->_('annex_4171'),//GDL驾照正面(马来新增）
                'text'  => $this->getTranslation()->_('annex_4171'),
                'url'   => '',
            ],
            'file_type21' => [
                'title' => 'OR',//GDL驾照正面(马来新增）
                'text'  => 'OR',
                'url'   => '',
            ],
            'file_type22' => [
                'title' => $this->getTranslation()->_('annex_commitment'),//GDL驾照正面(马来新增）
                'text'  => $this->getTranslation()->_('annex_commitment'),
                'url'   => '',
            ],
            // 社保卡
            'file_type27' => [
                'title' => $this->getTranslation()->_('annex_27'),
                'text'  => $this->getTranslation()->_('annex_27'),
                'url'   => 'https://fex-ph-asset-pro.oss-ap-southeast-1.aliyuncs.com/workOrder/1681181273-c2ca7afe2b94458b8cd5dc13c360ca8d.jpeg',
            ],
            // 医保卡
            'file_type24' => [
                'title' => $this->getTranslation()->_('annex_24'),
                'text'  => $this->getTranslation()->_('annex_24'),
                'url'   => 'https://fex-ph-asset-pro.oss-ap-southeast-1.aliyuncs.com/workOrder/1681181327-ae2057fb52e24448841e4e36a3374353.jpeg',
            ],
            // 公积金卡
            'file_type25' => [
                'title' => $this->getTranslation()->_('annex_25'),
                'text'  => $this->getTranslation()->_('annex_25'),
                'url'   => 'https://fex-ph-asset-pro.oss-ap-southeast-1.aliyuncs.com/workOrder/1681181306-6d53b2faa35f4e9aa232408975bc7f6e.jpeg',
            ],
            // 税卡
            'file_type26' => [
                'title' => $this->getTranslation()->_('annex_26'),
                'text'  => $this->getTranslation()->_('annex_26'),
                'url'   => 'https://fex-ph-asset-pro.oss-ap-southeast-1.aliyuncs.com/workOrder/1681181339-c8344fd186a649b4b28eb9331bac016e.jpeg',
            ],
            // 薪资证明
            'file_type28' => [
                'title' => $this->getTranslation()->_('annex_28'),
                'text'  => $this->getTranslation()->_('annex_common_28'),
                'url'   => 'https://tc-static-asset-internal.flashexpress.com/workOrder/1688549019-5cd2c913a0044ffeba54b9a5d0386db1.jpeg',
            ],
            // 银行卡
            'file_type29' => [
                'title' => $this->getTranslation()->_('annex_29'),
                'text'  => $this->getTranslation()->_('annex_common_29'),
                'url'   => '',
            ],
        ];
        return $data;
    }


    /**
     * 创建简历基本信息
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function createInfo($paramIn = [])
    {
        $this->log = new LogServer();

        $this->getDI()->get('logger')->write_log("createResumeInfo 信息:" . json_encode($paramIn, JSON_UNESCAPED_UNICODE),
            'info');

        if (isset($paramIn['car_owner'])) {
            $ebilityParam['car_owner'] = $this->processingDefault($paramIn, 'car_owner',2);
        }
        if (isset($paramIn['car_number'])) {
            $ebilityParam['car_number'] = $this->processingDefault($paramIn, 'car_number');
        }
        if (isset($paramIn['driver_number'])) {
            $ebilityParam['driver_number'] = $this->processingDefault($paramIn, 'driver_number');
        }
        if (isset($paramIn['place_cards'])) {
            $ebilityParam['place_cards'] = $this->processingDefault($paramIn, 'place_cards');
        }
        if (isset($paramIn['car_engine_number'])) {
            $ebilityParam['car_engine_number'] = $this->processingDefault($paramIn, 'car_engine_number');
        }
        if (isset($paramIn['driving_license_type'])) {
            $ebilityParam['driving_license_type'] = $this->processingDefault($paramIn, 'driving_license_type');
        }


        $resumeParam['phone_area_code']      = $this->processingDefault($paramIn, 'phone_area_code', 2);
        $resumeParam['phone']                = $this->processingDefault($paramIn, 'phone', 2);
        $resumeParam['address_id']           = $this->processingDefault($paramIn, 'address_id');
        $resumeParam['work_city_id']         = $this->processingDefault($paramIn, 'work_city_id');    //期望工作市
        //马来增加期望工作城市区信息
        if (isCountry('MY')) {
            $resumeParam['work_district_id'] = $this->processingDefault($paramIn, 'work_district_id');    //期望工作城市-区
        }
        $resumeParam['job_type']             = $this->processingDefault($paramIn, 'job_type', 2, 0);
        $resumeParam['job_id']               = $this->processingDefault($paramIn, 'job_id');
        $resumeParam['alternative_job_ids']  = $this->processingDefault($paramIn, 'alternative_job_ids');
        $resumeParam['expected_arrivaltime'] = $this->processingDefault($paramIn, 'expected_arrivaltime');
        $resumeParam['call_name']            = $this->processingDefault($paramIn, 'call_name');
        $resumeParam['sex']                  = getSex($paramIn['call_name'] ?? '', $paramIn['sex'] ?? '');
        $resumeParam['first_name']           = $this->processingDefault($paramIn, 'first_name');
        $resumeParam['last_name']            = $this->processingDefault($paramIn, 'last_name');
        if (isCountry('ph')) {
            $resumeParam['name'] = $this->processingDefault($paramIn, 'name');
            $resumeParam['name'] = str_replace(" ", '', $resumeParam['name']);
        } else {
            $resumeParam['name'] = str_replace(" ", '', $resumeParam['first_name']) . str_replace(" ", '',
                    $resumeParam['last_name']);//$this->processingDefault($paramIn, 'name');
        }
        $resumeParam['nationality']          = $this->processingDefault($paramIn, 'nationality');
        $resumeParam['working_country']      = $this->processingDefault($paramIn, 'working_country',2,getCountryValue());
        $resumeParam['first_name_en']        = $this->processingDefault($paramIn, 'first_name_en');
        $resumeParam['first_name_en']        = $this->processingDefault($paramIn, 'first_name_en');
        $resumeParam['last_name_en']         = $this->processingDefault($paramIn, 'last_name_en');
        $resumeParam['credentials_category'] = $this->processingDefault($paramIn, 'credentials_category');
        $resumeParam['credentials_num']      = $this->processingDefault($paramIn, 'credentials_num');
        $resumeParam['expiration_date']      = $this->processingDefault($paramIn, 'expiration_date');
        $resumeParam['cert_place']           = $this->processingDefault($paramIn, 'cert_place');
        $resumeParam['permit_number']        = $this->processingDefault($paramIn, 'permit_number');
//        $resumeParam['date_birth']            = $this->processingDefault($paramIn, 'date_birth');
        $resumeParam['date_birth']            = $paramIn['date_birth'] ?: null;
        $resumeParam['religion']              = $this->processingDefault($paramIn, 'religion');
        $resumeParam['email']                 = $this->processingDefault($paramIn, 'email');
        $resumeParam['line_id']               = $this->processingDefault($paramIn, 'line_id');
        $resumeParam['register_house_num']    = $this->processingDefault($paramIn, 'register_house_num');
        $resumeParam['register_village_num']  = $this->processingDefault($paramIn, 'register_village_num');
        $resumeParam['register_village']      = $this->processingDefault($paramIn, 'register_village');
        $resumeParam['register_alley']        = $this->processingDefault($paramIn, 'register_alley');
        $resumeParam['register_street']       = $this->processingDefault($paramIn, 'register_street');
        $resumeParam['register_government']   = $this->processingDefault($paramIn, 'register_government');
        $resumeParam['register_city']         = $this->processingDefault($paramIn, 'register_city');
        $resumeParam['register_town']         = $this->processingDefault($paramIn, 'register_town');
        $resumeParam['register_postcodes']    = $this->processingDefault($paramIn, 'register_postcodes');
        $resumeParam['fit']                   = $this->processingDefault($paramIn, 'fit', 2);
        $resumeParam['residence_house_num']   = $this->processingDefault($paramIn, 'residence_house_num');
        $resumeParam['residence_village_num'] = $this->processingDefault($paramIn, 'residence_village_num');
        $resumeParam['residence_village']     = $this->processingDefault($paramIn, 'residence_village');
        $resumeParam['residence_alley']       = $this->processingDefault($paramIn, 'residence_alley');
        $resumeParam['residence_street']      = $this->processingDefault($paramIn, 'residence_street');
        $resumeParam['residence_government']  = $this->processingDefault($paramIn, 'residence_government');
        $resumeParam['residence_city']        = $this->processingDefault($paramIn, 'residence_city');
        $resumeParam['residence_town']        = $this->processingDefault($paramIn, 'residence_town');
        $resumeParam['residence_postcodes']   = $this->processingDefault($paramIn, 'residence_postcodes');
        $resumeId                             = $this->processingDefault($paramIn, 'id');
        $resumeParam['nickname']              = $this->processingDefault($paramIn, 'nickname');
        $resumeParam['register_country']      = $this->processingDefault($paramIn, 'register_country', 2);
        $resumeParam['residence_country']     = $this->processingDefault($paramIn, 'residence_country', 2);
        $resumeParam['recruit_channel']       = $this->processingDefault($paramIn, 'recruit_channel', 2);
        $resumeParam['recruiter_id']          = $this->processingDefault($paramIn, 'recruiter_id', 2);
        $resumeParam['car_type']              = $this->processingDefault($paramIn, 'car_type', 2);
        $resumeParam['middle_name']           = $this->processingDefault($paramIn, 'middle_name');
        $resumeParam['suffix_name']           = $this->processingDefault($paramIn, 'suffix_name');
        $resumeParam['recruit_type']          = $this->processingDefault($paramIn, 'recruit_type', 2, 1);

        if (isCountry()) {
            $resumeParam['is_head_office_recruit'] = $this->processingDefault($paramIn, 'is_head_office_recruit', 2, 0);
        }

        if (isCountry(['TH', 'PH', 'MY']) && !empty($this->userInfo['id'])) {
            $resumeParam['resume_last_operator']       = $this->userInfo['id'];
            $resumeParam['resume_last_operation_time'] = gmdate('Y-m-d H:i:s');
        }
 
        //印尼增加邻组信息
        if (isCountry('ID')) {
            $resumeParam['residence_rt'] = $this->processingDefault($paramIn, 'residence_rt');
            $resumeParam['residence_rw'] = $this->processingDefault($paramIn, 'residence_rw');
            $resumeParam['register_rt']  = $this->processingDefault($paramIn, 'register_rt');
            $resumeParam['register_rw']  = $this->processingDefault($paramIn, 'register_rw');
        }
        
        if (isCountry('MY') && !empty($resumeParam['name'])){
            $resumeParam['name'] = $this->nameToUpper($resumeParam['name']);
        }
        if (isCountry('MY') && !empty($resumeParam['first_name'])){
            $resumeParam['first_name'] = $this->nameToUpper($resumeParam['first_name']);
        }

//        if (isCountry('TH')) {
//            $resumeParam['reserve_type'] = $this->processingDefault($paramIn, 'reserve_type', 2, 1);
//        }

        if (isset($paramIn['tax_no'])) {
            $resumeParam['tax_no'] = $paramIn['tax_no'];
        }

        //公积金号
        if (isset($paramIn['fund_num'])) {
            $resumeParam['fund_num'] = $paramIn['fund_num'];
        }

        if (isset($paramIn['race'])) {
            $resumeParam['race'] = $this->processingDefault($paramIn, 'race',2);
        }
        if (isset($paramIn['register_detail_address'])) {
            $resumeParam['register_detail_address'] = $this->processingDefault($paramIn, 'register_detail_address');
        }
        if (isset($paramIn['residence_detail_address'])) {
            $resumeParam['residence_detail_address'] = $this->processingDefault($paramIn, 'residence_detail_address');
        }
        if (isset($paramIn['religion'])) {
            $resumeParam['religion'] = $this->processingDefault($paramIn, 'religion', 2);
        }
        //期望薪资是必填项，但是没有薪资权限的人编辑简历时会删除该字段，需要判断，否则会强制更新为0
        if (isset($paramIn['entry_salary'])) {
            $resumeParam['entry_salary'] = $this->processingDefault($paramIn, 'entry_salary', 2);
        }

        if (isset($paramIn['current_salary'])) {
            $resumeParam['current_salary'] = $paramIn['current_salary'] === '' ? null : (int)$paramIn['current_salary'];
        }

        if (isset($paramIn['hc_id']) && !empty($paramIn['hc_id'])) {
            $resumeParam['hc_id'] = $paramIn['hc_id'];
        }

        $resume_extend_param = [];

        // 是否同时拥有 or和cr
        if (isset($paramIn['is_have_or_cr'])) {
            $resume_extend_param['is_have_or_cr'] = $this->processingDefault($paramIn, 'is_have_or_cr', 2,2);
        }
        // 是否有社保号
        if (isset($paramIn['is_have_social_security'])) {
            $resume_extend_param['is_have_social_security'] = $paramIn['is_have_social_security'];
        }
        // 社保号
        if (isset($paramIn['social_security_num'])) {
            $resume_extend_param['social_security_num'] = $paramIn['social_security_num'];
        }
        // 是否有公积金号
        if (isset($paramIn['is_have_fund'])) {
            $resume_extend_param['is_have_fund'] = $paramIn['is_have_fund'];
        }

        // 是否有医疗保险
        if (isset($paramIn['is_have_medical_insurance'])) {
            $resume_extend_param['is_have_medical_insurance'] = $paramIn['is_have_medical_insurance'];
        }
        // 医疗保险号
        if (isset($paramIn['medical_insurance_num'])) {
            $resume_extend_param['medical_insurance_num'] = $paramIn['medical_insurance_num'];
        }

        //扩展表字段
        if (isset($paramIn['current_school_id'])) {
            $resume_extend_param['current_school_id'] = $this->processingDefault($paramIn, 'current_school_id', 2, 0);
        }

        if (isset($paramIn['current_college'])) {
            $resume_extend_param['current_college'] = $this->processingDefault($paramIn, 'current_college');
        }

        if (isset($paramIn['current_major'])) {
            $resume_extend_param['current_major'] = $this->processingDefault($paramIn, 'current_major');
        }

        if (isset($paramIn['current_grade_id'])) {
            $resume_extend_param['current_grade_id'] = $this->processingDefault($paramIn, 'current_grade_id', 2, 0);
        }

        if (isset($paramIn['work_start_date'])) {
            $resume_extend_param['work_start_date'] = !empty($paramIn['work_start_date']) ? $paramIn['work_start_date'] : null;
        }

        if (isset($paramIn['work_end_date'])) {
            $resume_extend_param['work_end_date'] = !empty($paramIn['work_end_date']) ? $paramIn['work_end_date'] : null;
        }

        if (isset($paramIn['tutor_email'])) {
            $resume_extend_param['tutor_email'] = $this->processingDefault($paramIn, 'tutor_email');
        }

        if (isset($paramIn['tutor_phone_area_code'])) {
            $resume_extend_param['tutor_phone_area_code'] = $this->processingDefault($paramIn, 'tutor_phone_area_code');
        }

        if (isset($paramIn['tutor_phone'])) {
            $resume_extend_param['tutor_phone'] = $this->processingDefault($paramIn, 'tutor_phone');
        }

        if (isset($paramIn['interested_department_id'])) {
            $resume_extend_param['interested_department_id'] = $this->processingDefault($paramIn,
                'interested_department_id', 2, 0);
        }

        if (isset($paramIn['interested_department_other'])) {
            $resume_extend_param['interested_department_other'] = $this->processingDefault($paramIn,
                'interested_department_other');
        }

        if (!$resumeId) {
            $resumeId = (new ResumeRepository())->getResumeId($resumeParam['phone']);
        }

        if ($resumeId) {
            $resumeParam['id'] = $resumeId;
            //查询简历信息
            $resumeInfo = (new ResumeRepository())->getResumeInfo($resumeParam);

            //关联hc，讲下方代码改为统一调用同一个更换hc方法，方便后期维护变更hc逻辑
            if (isset($resumeParam['hc_id']) && $resumeInfo['hc_id'] != $resumeParam['hc_id']) {
                $res = $this->relateHcId($resumeId, $resumeParam['hc_id']);
                if ($res['code'] != '1') {
                    return $this->checkReturn($res);
                }

                //进行打分，不走事务直接走打分操作，不中断流程
                (new ResumeAiServer())->addResumeAnnexScore($resumeId, $resumeParam['hc_id']);
            }

            //更新简历基本信息
            $resumeId = (new ResumeRepository())->updateResumeInfo($resumeParam, $resume_extend_param);

            //泰国字段比较多验证队列
            if (isset($resumeInfo['is_perfect']) && $resumeInfo['is_perfect'] == 1 && isCountry('TH')) {
                $this->pushResumeDataToRedisList($resumeId);
            }

            //添加日志
            $this->log->addLog([
                'module_id'   => $resumeId,
                'module_type' => enums::$log_module_type['base_info'],
                'action'      => enums::$log_option['modify'],
                'data_after'  => $resumeParam,
            ]);

            // 记录招聘人修改
            if ($resumeInfo['recruiter_id'] != $resumeParam['recruiter_id']) {
                $addLog = [
                    'module_id'    => $resumeId,
                    'module_type'  => enums::$log_module_type['recruiter_id'],
                    'action'       => enums::$log_option['modify'],
                    'module_level' => $resumeInfo['recruiter_id'] ?? 0,
                    'data_after'   => ['recruiter_id' => $resumeInfo['recruiter_id'] ?? 0],
                ];
                $this->log->addLog($addLog);
            }
        } else {
            //简历-所属网点
//            $resumeParam['store_id'] = $this->getResumeStoreId();

            $resumeId = (new ResumeRepository())->createResumeInfo($resumeParam);
            //添加日志
            $this->log->addLog([
                'module_id'   => $resumeId,
                'module_type' => enums::$log_module_type['base_info'],
                'action'      => enums::$log_option['create'],
                'data_after'  => $resumeParam,
            ]);

            if (isset($paramIn['hc_id']) && $paramIn['hc_id']) {
                $this->log->addLog([
                    'module_id'    => $resumeId,
                    'module_type'  => enums::$log_module_type['hc_id'],
                    'action'       =>  enums::$log_option['relate'],
                    'module_level' => $paramIn['hc_id'],
                    'data_after'   => ['hc_id' => $paramIn['hc_id']],
                ]);
            }
        }
        // h5端过来的请求中会有这些字段信息
        if (isset($ebilityParam)) {
            $ebilityParam['resume_id'] = $resumeId;
            $ebilityParam['is_log']    = 1;
            //插入经济与能力
            self::createEbility($ebilityParam);
        }
        $returnData['data'] = ['id' => $resumeId];
        return $this->checkReturn($returnData);
    }

    /**
     * @description:by 判断是否能够编辑信息
     * @param null
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2021/7/28 16:50
     */
    public function isByUpdate($paramIn = [])
    {
        try {
            if (empty($paramIn['msg_id']) || empty($paramIn['staff_id'])) {
                throw new \Exception("by-isByUpdate-参数不对：" . json_encode($paramIn));
            }
            //获取判断是否可以填写简历
            $apiClient = (new ApiClient('by_rpc', '', 'get_message_info', $this->lang));
            $apiClient->setParams([
                'msg_id'   => $paramIn['msg_id'],
                'staff_id' => $paramIn['staff_id'],
            ]);
            $result = $apiClient->execute();
            $this->getDI()->get('logger')->write_log("by-isByUpdate-get_message_info-获取消息信息:" . json_encode($result),
                'info');
            if (!isset($result['result']['code']) || $result['result']['code'] != 1) {
                throw new \Exception("by-isByUpdate-get_message_info-获取消息信息失败,接口返回：" . json_encode($result));
            }
            if (!isset($result['result']['data']['read_state']) || $result['result']['data']['read_state'] == 1) {
                $this->getDI()->get('logger')->write_log("by-isByUpdate-get_message_info-消息已经读取不能编辑:" . json_encode($result),
                    'info');
                return false;
            }
            return true;
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("by isByUpdate 异常信息:" . $e->getMessage(), 'error');
            return false;
        }
    }

    /**
     * 关联hc处理逻辑
     * @param $resumeId
     * @param $hcId
     * @param null $source_type
     * @return array
     */
    public function relateHcId($resumeId, $hcId, $source_type = 'winhr')
    {
        $this->log             = new LogServer();
        if (!empty($this->other_remark_info)) {
            $this->log->setParams($this->other_remark_info);
        }

        $InterviewRepository = new InterviewRepository();

        $resumeInfo = HrResumeModel::findFirst(
            [
                'conditions' => "id = :resume_id:",
                'bind'       => ['resume_id' => $resumeId],
            ]
        );
        if (!$resumeInfo) {
            return $this->checkReturn(-3, $this->getTranslation()->_('miss_args'));
        }

        $resumeInfo = $resumeInfo->toArray();

        $hcId = empty($hcId) ? 0 : $hcId;

        if ($hcId != 0) {
            $hc_info = $this->getHcInfo($hcId);
            if (empty($hc_info)) {
                return $this->checkReturn(-3, 'hc not exist');
            }
            if ($hc_info['state_code'] != 2){
                return $this->checkReturn(-3, $this->getTranslation()->_('update_resume_hc_hc_state_error'));
            }
        }

        //已沟通的简历hc不能删除，只能更换hc,
        if ($source_type == 'winhr' && $resumeInfo['state_code'] != enums::$resume_state_code['uncommunicated'] && $hcId == 0) {
            return $this->checkReturn(-3, $this->getTranslation()->_('hc_empty_error'));
        }

        //已沟通简历
        if ($source_type == 'winhr' && $resumeInfo['hc_id'] != $hcId && $hcId > 0) {
            $resume_current_hc_interview = (new InterviewServer())->getInterviewInfoByResumeid($resumeId,
                $resumeInfo['hc_id']);
            $interview_state             = $InterviewRepository->getInterviewState($resumeInfo['filter_state'],
                $resume_current_hc_interview['state'] ?? 0, $resumeInfo['is_out'], $resumeInfo['state_code']);
            $end_state                   = [
                enums::$interview_state['no_communication'],
                enums::$interview_state['filter_un_passed'],
                enums::$interview_state['filter_un_passed'],
                enums::$interview_state['filter_cancel'],
                enums::$interview_state['rejected'],
                enums::$interview_state['canceled'],
            ];
            //如果简历筛选状态是终止状态 则可以更新负责人
            if (!in_array($interview_state, $end_state)) {
                $this->getDI()->get('logger')->write_log("relateHcId异常:Resume status has been changed, please re-screen resumes，cvid:{$resumeId},当前状态：{$interview_state}",
                    'info');
                return $this->checkReturn(-3, 'Resume status has been changed, please re-screen resumes');
            }
        }

        $is_can_change_filter_result = $resumeInfo['previous_hc_id'] > 0 && $resumeInfo['filter_state'] == enums::$resume_filter_state['not_pass'] && $hcId != $resumeInfo['previous_hc_id'];


        //如果是更换hc,查询是否有面试中的数据，如果该hc有面试记录则是99，没有面试记录，新的hc则置为初始值0，如果在更换回来也就是有面试记录则还是99
        $resume_hc_is_interview = 1;//简历是否有面试表数据记录；
        if ($resumeInfo['hc_id'] != $hcId && $hcId > 0) {
            $resume_hc_have_interview = (new InterviewServer())->getInterviewInfoByResumeid($resumeId, $hcId);
            if (empty($resume_hc_have_interview)) {
                $resume_hc_is_interview = 0;
            }
        }
        //简历筛选终止状态集合
        $end_filter_state = [enums::$resume_filter_state['not_pass'], enums::$resume_filter_state['cancel']];

        $resume_up_data = [
            'hc_id'                       => $hcId,
            'is_can_change_filter_result' => intval($is_can_change_filter_result),

        ];
        //todo 当简历变更hc时更新简历筛选状态和沟通状态变更逻辑处理

        if ($resumeInfo['hc_id'] != $hcId && $hcId > 0) {
            /***更换新hc筛选状态处理**/
            $filter_state = 0;//默认初始化状态,默认为0
            if ($resume_hc_is_interview) { //关联的历史hc已经进入到面试环节的直接用面试状态，简历筛选状态不在显示具体值
                $filter_state = 99; //面试中状态（已预约过面试的会置为99）
            } else {
                //判断改简历关联的hc的历史推荐状态，用于简历再次关联回历史hc(没有进行到面试环节的hc的场景)的简历状态显示
                $resume_hc_filter = $InterviewRepository->getResumeFilterLastedDataByHc($resumeId, $hcId);
                if ($resume_hc_filter && in_array($resume_hc_filter['filter_state'], $end_filter_state)) {
                    $filter_state = $resume_hc_filter['filter_state'];//
                }
            }
            $resume_up_data['filter_state'] = $filter_state;//简历筛选状态：0-初始状态，99-面试标识

            /*** 更换新hc简历沟通状态处理**/

            //查询简历是否有过历史其他hc面试记录，
            $history_interview = HrInterviewModel::findFirst([
                'conditions' => " resume_id =:resume_id: ",
                'bind'       => ['resume_id' => $resumeId],
            ]);
            //简历是否已沟通字段
            $resume_is_communicated = $resumeInfo['state_code'] != enums::$resume_state_code['uncommunicated'] ? true : false;

            //$filter_state = 0 的简历有可能情况：
            //1:未沟通状态下变更hc(人才库下的关联hc操作），变更的hc是一个新的未曾关联的hc,这个时候不应该改变沟通状态
            //2：已沟通状态下变更hc，这个时候关联一个新的未曾关联过的hc,这个时候沟通状态在面试应该重置为"重新关联hc待反馈"

            //沟通状态为"已沟通"的简历，更换新的hc,如果该hc没有过面试记录或者没有过历史筛选记录，则 显示"重新关联hc待反馈"
            if ($resume_is_communicated && (($history_interview && $resume_hc_is_interview == 0) || $filter_state == 0)) {
                $resume_up_data['state_code'] = enums::$resume_state_code['re_hc_wait_feedback'];//重新关联hc待反馈
                $resume_up_data['is_out']     = enums::$resume_is_out['not_out'];
            }
        }


        $flag = $this->getDI()->get('db')->updateAsDict('hr_resume', $resume_up_data, [
            'conditions' => 'id = ?',
            'bind'       => [$resumeId],
        ]);

        if ($source_type != 'by_recommend_resume') {//菲律宾by内推简历，不要此逻辑
            //关联hc-自动赋值招聘负责人
            $interviewInfo   = (new InterviewServer())->getInterviewInfoByResumeid($resumeId, $resumeInfo['hc_id']);
            $interview_state = $interviewInfo['state'] ?? 0;
            $this->updateResumeRecruiter($resumeId, $this->userInfo['id'], $this->userInfo['node_department_id'],
                $resumeInfo['filter_state'], $interview_state, 1);
        }

        //判断提交hc_id 与数据库存储的hc_id 是否一致
        if ($resumeInfo['hc_id'] != $hcId) {
            if ($hcId == 0) {
                $addLog = [
                    'module_id'    => $resumeId,
                    'module_type'  => enums::$log_module_type['hc_id'],
                    'action'       => enums::$log_option['delete'],
                    'module_level' => $resumeInfo['hc_id'] ?? 0,
                    'data_after'   => ['hc_id' => $hcId],
                ];
            } else {
                $addLog = [
                    'module_id'    => $resumeId,
                    'module_type'  => enums::$log_module_type['hc_id'],
                    'action'       => enums::$log_option['relate'],
                    'module_level' => $hcId ?? 0,
                    'data_after'   => ['hc_id' => $hcId ?? 0],
                ];

                $conditions = ' module_id = :resume_id: and module_type = :module_type: and action = :action:';
                $binds      = [
                    'resume_id'   => $resumeId,
                    'module_type' => enums::$log_module_type['hc_id'],
                    'action'      => enums::$log_option['relate'],
                ];
                //查询有无action为关联hc_id 的log
                $logInfo = (new LogServer())->getLogOneInfo($conditions, $binds);
                if ($logInfo) {
                    $addLog = [
                        'module_id'    => $resumeId,
                        'module_type'  => enums::$log_module_type['hc_id'],
                        'action'       => enums::$log_option['modify'],
                        'module_level' => $hcId ?? 0,
                        'data_after'   => ['hc_id' => $hcId ?? 0],
                    ];
                }
            }
            $this->log->addLog($addLog);
        }

        return $this->checkReturn(1, '');
    }

    /**
     * @description:获取简历 id
     * @param null
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2021/7/28 17:53
     */
    public function getEntryInfo($paramIn)
    {
        //获取简历 id
        $entry     = new EntryRepository();
        $EntryInfo = $entry->getStaffIdInfo($paramIn);
        return $EntryInfo;
    }


    /**
     * By修改简历基本信息
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function updateInfoBy($paramIn = [])
    {
        $this->log = new LogServer();

        //获取简历 id
        $entry     = new EntryRepository();
        $EntryInfo = $entry->getStaffIdInfo($paramIn);
        if (!isset($EntryInfo['resume_id']) || empty($EntryInfo['resume_id'])) {
            return $this->checkReturn(-3, $this->getTranslation()->_('4012'));
        }

        $resumeId = $EntryInfo['resume_id'];

        $resumeArr = [
            'resume_id' => $resumeId,
        ];
        //校验上牌地点
        $resumeInfoArr = self::resumeInfo($resumeArr);
        $type          = $resumeInfoArr['data']['job_category'];
        if (in_array($type, [1, 2])) {
            if (empty($this->processingDefault($paramIn, 'place_cards'))) {
                return $this->checkReturn(-3, $this->getTranslation()->_('4172'));
            }
        }

        $resumeParam['register_country']     = $this->processingDefault($paramIn, 'register_country', 2);  //户口所在地国家
        $resumeParam['register_house_num']   = $this->processingDefault($paramIn, 'register_house_num');  //户口所在地门牌号
        $resumeParam['register_village_num'] = $this->processingDefault($paramIn, 'register_village_num');  //户口所在地村号
        $resumeParam['register_village']     = $this->processingDefault($paramIn, 'register_village');  //户口所在地村庄
        $resumeParam['register_alley']       = $this->processingDefault($paramIn, 'register_alley');  //户口所在地巷子
        $resumeParam['register_street']      = $this->processingDefault($paramIn, 'register_street');   //户口所在地街道
        $resumeParam['register_government']  = $this->processingDefault($paramIn, 'register_government'); //户口所在地府
        $resumeParam['register_city']        = $this->processingDefault($paramIn, 'register_city');  //户口所在地市
        $resumeParam['register_town']        = $this->processingDefault($paramIn, 'register_town');  //户口所在地-镇
        $resumeParam['register_postcodes']   = $this->processingDefault($paramIn, 'register_postcodes');   //户口所在地邮编
        $resumeParam['fit']                  = $this->processingDefault($paramIn, 'fit',
            2);  //居住地是否和户口所在地一致? 1=一致,0=不一致
        $resumeParam['permit_number']        = $this->processingDefault($paramIn, 'permit_number');  //工作证号
//		$resumeParam['place_cards'] 		  = $this->processingDefault($paramIn, 'place_cards'); //上牌地点
        $resumeParam['recruiter_id'] = $this->processingDefault($paramIn, 'recruiter_id', 2);

        if (isset($paramIn['register_detail_address'])) {
            $resumeParam['register_detail_address'] = $this->processingDefault($paramIn, 'register_detail_address');
        }

        if (isCountry('ID')) {
            $resumeParam['register_rt'] = $this->processingDefault($paramIn, 'register_rt');
            $resumeParam['register_rw'] = $this->processingDefault($paramIn, 'register_rw');
        }

        $resumeParam['id'] = $resumeId;
        $resumeId          = (new ResumeRepository())->updateResumeInfo($resumeParam);
        //添加日志
        $this->log->addLog([
            'module_id'   => $resumeId,
            'module_type' => 6,
            'action'      => 2,
            'data_after'  => $resumeParam,
        ]);
        // h5端过来的请求中会有这些字段信息
        if (isset($paramIn['place_cards'])) {
            $ebilityParam['place_cards'] = $this->processingDefault($paramIn, 'place_cards');
        }
        if (isset($paramIn['car_engine_number'])) {
            $ebilityParam['car_engine_number'] = $this->processingDefault($paramIn, 'car_engine_number');
        }
        if (isset($paramIn['car_owner'])) {
            $ebilityParam['car_owner'] = $this->processingDefault($paramIn, 'car_owner');
        }
        if (isset($paramIn['car_number'])) {
            $ebilityParam['car_number'] = $this->processingDefault($paramIn, 'car_number');
        }
        if (isset($paramIn['driver_number'])) {
            $ebilityParam['driver_number'] = $this->processingDefault($paramIn, 'driver_number');
        }
        if (isset($paramIn['place_cards'])) {
            $ebilityParam['place_cards'] = $this->processingDefault($paramIn, 'place_cards');
        }
        if (isset($paramIn['car_engine_number'])) {
            $ebilityParam['car_engine_number'] = $this->processingDefault($paramIn, 'car_engine_number');
        }
        if (isset($paramIn['driving_license_type'])) {
            $ebilityParam['driving_license_type'] = $this->processingDefault($paramIn, 'driving_license_type');
        }
        if (isset($paramIn['computer_other'])) {
            $ebilityParam['computer_other'] = $this->processingDefault($paramIn, 'computer_other');
        }

        if (isset($ebilityParam)) {
            $ebilityParam['resume_id'] = $resumeId;
            $ebilityParam['is_log']    = 1;
            $ebilityParam['is_not_check_black']    = $paramIn['is_not_check_black'] ?? false;
            //插入经济与能力
            self::createEbility($ebilityParam);
        }
        $returnData['data'] = ['id' => $resumeId];
        return $this->checkReturn($returnData);
    }

    /**
     * 创建家庭信息
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function createFamily($paramIn = [])
    {
        $this->log = new LogServer();

        $familyParam['resume_id']            = $this->processingDefault($paramIn, 'resume_id');
        $familyParam['dad_alive']            = $this->processingDefault($paramIn, 'dad_alive', 2);
        $familyParam['dad_call_name']        = $this->processingDefault($paramIn, 'dad_call_name');
        $familyParam['dad_last_name']        = $this->processingDefault($paramIn, 'dad_last_name');
        $familyParam['dad_first_name']       = $this->processingDefault($paramIn, 'dad_first_name');
        $familyParam['dad_age']              = $this->processingDefault($paramIn, 'dad_age');
        $familyParam['dad_job_name']         = $this->processingDefault($paramIn, 'dad_job_name');
        $familyParam['dad_mobile_area_code'] = $this->processingDefault($paramIn, 'dad_mobile_area_code');
        $familyParam['dad_mobile']           = $this->processingDefault($paramIn, 'dad_mobile');

        $familyParam['mum_alive']            = $this->processingDefault($paramIn, 'mum_alive', 2);
        $familyParam['mum_call_name']        = $this->processingDefault($paramIn, 'mum_call_name');
        $familyParam['mum_last_name']        = $this->processingDefault($paramIn, 'mum_last_name');
        $familyParam['mum_first_name']       = $this->processingDefault($paramIn, 'mum_first_name');
        $familyParam['mum_age']              = $this->processingDefault($paramIn, 'mum_age');
        $familyParam['mum_job_name']         = $this->processingDefault($paramIn, 'mum_job_name');
        $familyParam['mum_mobile_area_code'] = $this->processingDefault($paramIn, 'mum_mobile_area_code');
        $familyParam['mum_mobile']           = $this->processingDefault($paramIn, 'mum_mobile');

        $familyParam['marital']                 = $this->processingDefault($paramIn, 'marital', 2);
        $familyParam['spouse_call_name']        = $this->processingDefault($paramIn, 'spouse_call_name');
        $familyParam['spouse_last_name']        = $this->processingDefault($paramIn, 'spouse_last_name');
        $familyParam['spouse_first_name']       = $this->processingDefault($paramIn, 'spouse_first_name');
        $familyParam['spouse_job_name']         = $this->processingDefault($paramIn, 'spouse_job_name');
        $familyParam['spouse_mobile_area_code'] = $this->processingDefault($paramIn, 'spouse_mobile_area_code');

        $familyParam['spouse_mobile']                 = $this->processingDefault($paramIn, 'spouse_mobile');
        $familyParam['child_num']                     = $this->processingDefault($paramIn, 'child_num');
        $familyParam['child_list']                    = $this->processingDefault($paramIn, 'child_list');
        $familyParam['relationship']                  = $this->processingDefault($paramIn, 'relationship', 2);
        $familyParam['relationship_call_name']        = $this->processingDefault($paramIn, 'relationship_call_name');
        $familyParam['relationship_last_name']        = $this->processingDefault($paramIn, 'relationship_last_name');
        $familyParam['relationship_first_name']       = $this->processingDefault($paramIn, 'relationship_first_name');
        $familyParam['relationship_mobile_area_code'] = $this->processingDefault($paramIn,
            'relationship_mobile_area_code');
        $familyParam['relationship_mobile']           = $this->processingDefault($paramIn, 'relationship_mobile');

        foreach ($familyParam as $k => $v) {
            if ($v !== 0 && empty($v)) {
                unset($familyParam[$k]);
            }
        }

        if (!$familyParam['resume_id']) {
            return $this->checkReturn(-3);
        }
        $resume_data = $this->getResumeBaseInfo($familyParam['resume_id']);
        if (empty($resume_data)){
            return $this->checkReturn(-3, $this->getTranslation()->_('resume_err_1'));
        }

        //检测黑名单
        if (isCountry('TH')){
            $black_grey_list = (new BlackgreylistServer())->check_black_grey_list([
                "resume_id" => $familyParam['resume_id'],
            ]);
            if ($black_grey_list["is_black_list"]){
                return $this->checkReturn(-3, $this->getTranslation()->_('7500'));
            }
        }else{
            $isBalcklist = (new BlacklistRepository())->blacklistCheck([
                "resume_id" => $familyParam['resume_id'],
            ]);
            if ($isBalcklist > 0) {
                return $this->checkReturn(-3, $this->getTranslation()->_('7500'));
            }
        }
        (new OutsourcingBlackListServer())->check($resume_data['credentials_num'],'winhr',true,$this->lang);


        $sql        = "select * from hr_family where resume_id=" . $familyParam['resume_id'];
        $data       = $this->getDI()->get('db_rby')->query($sql);
        $returnData = $data->fetch(\Phalcon\Db::FETCH_ASSOC);
        if ($returnData) {
            $familyParam['id'] = $returnData['id'];
            $cvData            = (new ResumeRepository())->familyUpdate($familyParam);
            //添加日志
            $this->log->addLog([
                'module_id'   => $familyParam['resume_id'],
                'module_type' => enums::$log_module_type['family_info'],
                'action'      => enums::$log_option['modify'],
                'data_after'  => $familyParam,
            ]);
        } else {
            $cvData = (new ResumeRepository())->createFamily($familyParam);
            //添加日志
            $this->log->addLog([
                'module_id'   => $familyParam['resume_id'],
                'module_type' => enums::$log_module_type['family_info'],
                'action'      => enums::$log_option['create'],
                'data_after'  => $familyParam,
            ]);
        }

        // 数据返回
        $returnData['data'] = ['family_id' => $cvData];

        return $this->checkReturn($returnData);
    }


    /**
     * 修改家庭信息
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function updateFamilyBy($paramIn = [])
    {
        $this->log = new LogServer();

        //获取简历 id
        $entry     = new EntryRepository();
        $EntryInfo = $entry->getStaffIdInfo($paramIn);
        if (!isset($EntryInfo['resume_id']) || empty($EntryInfo['resume_id'])) {
            return $this->checkReturn(-3, $this->getTranslation()->_('4012'));
        }
        $familyParam['resume_id']            = $EntryInfo['resume_id'];
        $familyParam['dad_alive']            = $this->processingDefault($paramIn, 'dad_alive', 2);
        $familyParam['dad_call_name']        = $this->processingDefault($paramIn, 'dad_call_name');
        $familyParam['dad_last_name']        = $this->processingDefault($paramIn, 'dad_last_name');
        $familyParam['dad_first_name']       = $this->processingDefault($paramIn, 'dad_first_name');
        $familyParam['dad_age']              = $this->processingDefault($paramIn, 'dad_age');
        $familyParam['dad_job_name']         = $this->processingDefault($paramIn, 'dad_job_name');
        $familyParam['dad_mobile_area_code'] = $this->processingDefault($paramIn, 'dad_mobile_area_code');
        $familyParam['dad_mobile']           = $this->processingDefault($paramIn, 'dad_mobile');

        $familyParam['mum_alive']            = $this->processingDefault($paramIn, 'mum_alive', 2);
        $familyParam['mum_call_name']        = $this->processingDefault($paramIn, 'mum_call_name');
        $familyParam['mum_last_name']        = $this->processingDefault($paramIn, 'mum_last_name');
        $familyParam['mum_first_name']       = $this->processingDefault($paramIn, 'mum_first_name');
        $familyParam['mum_age']              = $this->processingDefault($paramIn, 'mum_age');
        $familyParam['mum_job_name']         = $this->processingDefault($paramIn, 'mum_job_name');
        $familyParam['mum_mobile_area_code'] = $this->processingDefault($paramIn, 'mum_mobile_area_code');
        $familyParam['mum_mobile']           = $this->processingDefault($paramIn, 'mum_mobile');

        $familyParam['marital']                 = $this->processingDefault($paramIn, 'marital', 2);
        $familyParam['spouse_call_name']        = $this->processingDefault($paramIn, 'spouse_call_name');
        $familyParam['spouse_last_name']        = $this->processingDefault($paramIn, 'spouse_last_name');
        $familyParam['spouse_first_name']       = $this->processingDefault($paramIn, 'spouse_first_name');
        $familyParam['spouse_job_name']         = $this->processingDefault($paramIn, 'spouse_job_name');
        $familyParam['spouse_mobile_area_code'] = $this->processingDefault($paramIn, 'spouse_mobile_area_code');

        $familyParam['spouse_mobile']                 = $this->processingDefault($paramIn, 'spouse_mobile');
        $familyParam['child_num']                     = $this->processingDefault($paramIn, 'child_num', 2);
        $familyParam['child_list']                    = $this->processingDefault($paramIn, 'child_list');
        $familyParam['relationship']                  = $this->processingDefault($paramIn, 'relationship', 2);
        $familyParam['relationship_call_name']        = $this->processingDefault($paramIn, 'relationship_call_name');
        $familyParam['relationship_last_name']        = $this->processingDefault($paramIn, 'relationship_last_name');
        $familyParam['relationship_first_name']       = $this->processingDefault($paramIn, 'relationship_first_name');
        $familyParam['relationship_mobile_area_code'] = $this->processingDefault($paramIn,
            'relationship_mobile_area_code');
        $familyParam['relationship_mobile']           = $this->processingDefault($paramIn, 'relationship_mobile');

        foreach ($familyParam as $k => $v) {
            if ($v !== 0 && empty($v)) {
                unset($familyParam[$k]);
            }
        }
        if (empty($familyParam['child_num'])) {
            $familyParam['child_list'] = [];
        }

        if (!$familyParam['resume_id']) {
            return $this->checkReturn(-3);
        }


        $sql        = "select * from hr_family where resume_id=" . $familyParam['resume_id'];
        $data       = $this->getDI()->get('db_rby')->query($sql);
        $returnData = $data->fetch(\Phalcon\Db::FETCH_ASSOC);
        if ($returnData) {
            $familyParam['id'] = $returnData['id'];
            $cvData            = (new ResumeRepository())->familyUpdate($familyParam);
            //添加日志
            $this->log->addLog([
                'module_id'   => $familyParam['resume_id'],
                'module_type' => 7,
                'action'      => 2,
                'data_after'  => $familyParam,
            ]);
        } else {
            $cvData = (new ResumeRepository())->createFamily($familyParam);
            //添加日志
            $this->log->addLog([
                'module_id'   => $familyParam['resume_id'],
                'module_type' => 7,
                'action'      => 2,
                'data_after'  => $familyParam,
            ]);
        }

        // 数据返回
        $returnData['data'] = ['family_id' => $cvData];

        return $this->checkReturn($returnData);
    }


    /**
     * 创建工作经历与能力
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    public function createEbility($paramIn = [])
    {
        $this->staff = new StaffRepository();
        $this->log   = new LogServer();

        $ebilityParam['resume_id']         = $this->processingDefault($paramIn, 'resume_id', 2);
        $ebilityParam['education_level']   = $this->processingDefault($paramIn, 'education_level');
        $ebilityParam['graduate_school']   = $this->processingDefault($paramIn, 'graduate_school');
        $ebilityParam['major']             = $this->processingDefault($paramIn, 'major');
        $ebilityParam['graduate_time']     = $this->processingDefault($paramIn, 'graduate_time');
        $ebilityParam['word']              = $this->processingDefault($paramIn, 'word');
        $ebilityParam['excel']             = $this->processingDefault($paramIn, 'excel');
        $ebilityParam['bool_relation']     = $this->processingDefault($paramIn, 'bool_relation');
        $ebilityParam['recommender']       = $this->processingDefault($paramIn, 'recommender');
        $ebilityParam['car_owner']         = $this->processingDefault($paramIn, 'car_owner');
        $ebilityParam['car_number']        = $this->processingDefault($paramIn, 'car_number');
        $ebilityParam['driver_number']     = $this->processingDefault($paramIn, 'driver_number');
        $ebilityParam['place_cards']       = $this->processingDefault($paramIn, 'place_cards');
        $ebilityParam['work_experience']   = $this->processingDefault($paramIn, 'work_experience');
        $ebilityParam['languages']         = $this->processingDefault($paramIn, 'languages');
        $ebilityParam['relationship']      = $this->processingDefault($paramIn, 'relationship');
        $ebilityParam['car_engine_number'] = $this->processingDefault($paramIn, 'car_engine_number');
        $ebilityParam['computer_other']    = $paramIn['computer_other'] ?? [];
        if (isset($paramIn['driving_license_type'])) {
            $ebilityParam['driving_license_type'] = $this->processingDefault($paramIn, 'driving_license_type', 2);
        }
        $is_log = $paramIn['is_log'] ? $paramIn['is_log'] : 0;

        foreach ($ebilityParam as $k => $v) {
            if ($v !== 0 && empty($v)) {
                unset($ebilityParam[$k]);
            }
        }
        //检测员工是否存在
        if ($ebilityParam['recommender']) {
            $recommenderFlag = $this->staff->checkoutStaff($ebilityParam['recommender']);
            if (!$recommenderFlag) {
                return $this->checkReturn(-3, $this->getTranslation()->_('1001'));
            }
        }

        //检查工作经历与能力是否存在
        $economyAbilityId = '';
        $resumeId         = $ebilityParam['resume_id'] ?? '';
        if ($ebilityParam['resume_id']) {
            $economyAbilityData = (new ResumeRepository())->getEbilityId($ebilityParam['resume_id']);
            $economyAbilityId   = $economyAbilityData['id'];
        }

        if (!empty($resumeId)){
            $resume_data = HrResumeModel::findFirst([
                'conditions' => 'id = :resume_id: and deleted=0',
                'bind'       => [
                    'resume_id' => $resumeId,
                ],
            ]);
            if (empty($resume_data)){
                return $this->checkReturn(-3, $this->getTranslation()->_('resume_err_1'));
            }
            $resume_data = $resume_data->toArray();
        }else{
            $resume_data= [];
        }
        

        //检测黑名单
        if (empty($paramIn['is_not_check_black'])){
            if (isCountry('TH')){
                $black_grey_list = (new BlackgreylistServer())->check_black_grey_list([
                    "resume_id" => $resumeId,
                ]);
                if ($black_grey_list["is_black_list"]){
                    return $this->checkReturn(-3, $this->getTranslation()->_('7500'));
                }
            }else{
                $isBalcklist = (new BlacklistRepository())->blacklistCheck([
                    "resume_id" => $resumeId,
                ]);
                if ($isBalcklist > 0) {
                    return $this->checkReturn(-3, $this->getTranslation()->_('7500'));
                }
            }
            (new OutsourcingBlackListServer())->check($resume_data['credentials_num'] ?? '','winhr',true,$this->lang);
        }


        if (isset($ebilityParam['languages'])) {
            if (!is_array($ebilityParam['languages'])) {
                return $this->checkReturn(-3, $this->getTranslation()->_('4008'));
            } elseif (count($ebilityParam['languages']) > 20) {
                return $this->checkReturn(-3, ' uppper limit 20');
            }
        }

        if (isset($ebilityParam['computer_other'])) {
            if (!is_array($ebilityParam['computer_other'])) {
                return $this->checkReturn(-3, $this->getTranslation()->_('4008'));
            } elseif (count($ebilityParam['computer_other']) > 20) {
                return $this->checkReturn(-3, ' uppper limit 20');
            }
        }

        if (!$economyAbilityId) {
            unset($ebilityParam['id']);
            // 创建工作经历与能力
            $economyAbilityId = (new ResumeRepository())->createEbility($ebilityParam);
            //创建工作经历
            if ($ebilityParam['work_experience']) {
                (new ResumeRepository())->workexperienceAdd($ebilityParam['work_experience'], $economyAbilityId,
                    $resumeId);
            }
            //创建外语能力
            if ($ebilityParam['languages']) {
                (new ResumeRepository())->languagesAdd($ebilityParam['languages'], $economyAbilityId, $resumeId);
            }
            //创建亲属信息
            if ($ebilityParam['relationship']) {
                (new ResumeRepository())->relationshipAdd($ebilityParam['relationship'], $economyAbilityId, $resumeId);
            }
            if (empty($is_log)) {
                //添加日志
                $this->log->addLog([
                    'module_id'   => $resumeId,
                    'module_type' => enums::$log_module_type['study_ab_and_exp'],
                    'action'      => enums::$log_option['modify'],
                ]);
            }
        } else {
            $ebilityParam['id'] = $economyAbilityId;
            // 修改工作经历与能力
            $economyAbilityId = (new ResumeRepository())->ebilityUpdate($ebilityParam);
            //修改工作经历
            if ($ebilityParam['work_experience']) {
                (new ResumeRepository())->workexperienceAdd($ebilityParam['work_experience'], $economyAbilityId,
                    $resumeId);
            }
            //修改外语能力
            if ($ebilityParam['languages']) {
                (new ResumeRepository())->languagesAdd($ebilityParam['languages'], $economyAbilityId, $resumeId);
            }
            //修改亲属信息
            if ($ebilityParam['relationship']) {
                (new ResumeRepository())->relationshipAdd($ebilityParam['relationship'], $economyAbilityId, $resumeId);
            }
            if (empty($is_log)) {
                //添加日志
                $this->log->addLog([
                    'module_id'   => $resumeId,
                    'module_type' => enums::$log_module_type['study_ab_and_exp'],
                    'action'      => enums::$log_option['modify'],
                ]);
            }
        }
        $this->setLastOperator($resumeId);
        // 数据返回
        $returnData['data'] = ['ebility_id' => $economyAbilityId];

        return $this->checkReturn($returnData);
    }

    /**
     * 检验h5登陆状态
     * @Access  public
     * @Param   request
     * @Return  jsonData
     */
    function checkH5LoginState($paramIn = [])
    {
        $phone    = $paramIn['phone'];
        $resumeId = $paramIn['id'];
        if (!$phone || !$resumeId) {
            return false;
        }
        $sql        = "select * from hr_resume where id=" . $resumeId . " and phone='" . $phone . "'";
        $data       = $this->getDI()->get('db_rby')->query($sql);
        $returnData = $data->fetch(\Phalcon\Db::FETCH_ASSOC);
        if ($returnData) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 指派网点处理
     * @Param   request
     * @Return  jsonData
     */
    public function updateResume($paramIn = [])
    {
        $this->log = new LogServer();

        //校验简历id
        if (isset($paramIn["id"])) {
            $resumeInfo = (new ResumeRepository())->getResumeInfo([
                "id" => $paramIn["id"],
            ]);
            if (!$resumeInfo) {
                return $this->checkReturn(-3, $this->getTranslation()->_('miss_args'));
            }
        }
        //校验指派网点
        if (isset($paramIn["store_id"])) {
            $storeId = $resumeInfo["store_id"] ?? "";
            //判断提交者是否是网点主管or网点经理
            if ($resumeInfo["submitter_id"]) {
                $staffInfo = (new StaffServer())->getStaffInfo($resumeInfo["submitter_id"]);
                if (!$staffInfo) {
                    return $this->checkReturn(-3, $this->getTranslation()->_('miss_args'));
                } else {
                    $positionCategoryArr = explode(',', $staffInfo["position_category"]);
                    //3和18代表网点经理和网点主管
                    $_flag = ((in_array(3, $positionCategoryArr)) || in_array(18, $positionCategoryArr)) ? 1 : 0;
                    if ($_flag && $storeId) {
                        return $this->checkReturn(-3, $this->getTranslation()->_('7801'));
                    }
                }
            }

            //查询offer
            $offerData = (new OfferRepository())->getOfferData([
                "resume_ids" => $resumeInfo["id"],
            ]);
            if ($offerData[0]["status"] == 1) {
                //如果该简历offer状态等于发送则不能修改简历所属网点
                return $this->checkReturn(0, $this->getTranslation()->_('7801'));
            }
        }

        $_re = (new ResumeRepository())->updateResumeInfo($paramIn);

        if ($_re) {
            //添加日志
            if ($paramIn["id"]) {
                $this->log->addLog([
                    'module_id'   => $paramIn["id"],
                    'module_type' => 5,
                    'action'      => 2,
                ]);
            }
            return $this->checkReturn(1);
        } else {
            return $this->checkReturn(0, $this->getTranslation()->_('4102'));
        }
    }

    /**
     * 获取性别
     * @param int $sex 简历中的性别
     * @param int $callName 简历中的称呼
     * @return int
     */
    public function getResumeSex($sex, $callName): int
    {
        if (isset($sex) && $sex) { //如果性别字段有值
            return $sex;
        } else { //性别字段无值，根据称呼字段判断
            if ($callName == self::RESUME_CALL_NAME_MR) {
                return self::RESUME_SEX_MALE;
            } elseif (in_array($callName, [self::RESUME_SEX_MISS, self::RESUME_SEX_FEMALE])) {
                return self::RESUME_SEX_FEMALE;
            } else {
                return 0;
            }
        }
    }

    /**
     * 获取简历附件列表
     * @Param   paramIn
     * @Return  array
     */
    public function getAnnexList(string $resumeIds = '')
    {
        $returnData = [];

        $resumeIdsArr = explode(",", $resumeIds);
        if (!is_array($resumeIdsArr) || !$resumeIdsArr) {
            return $returnData;
        }
        $data = (new ResumeRepository())->getAnnexList([
            "resume_ids" => $resumeIds,
        ]);
        if (!$data) {
            return $returnData;
        }
        $img_prefix = env("img_prefix", "http://fle-staging-asset-internal.oss-ap-southeast-1.aliyuncs.com/");
        $fileType   = self::getAnnexFileType();
        foreach ($data as $k => $v) {
            $key      = $v["credentials_num"];
            $filename = explode('/', $v['object_key']);
            $filename = end($filename);

            if ($returnData[$key]) {
                $_arr = [
                    "file_url"  => $img_prefix . $v['object_key'],
                    "file_name" => $fileType[$v['file_type']] . "_" . $filename,
                ];
            } else {
                $returnData[$key]["user_id"]         = $v["id"];
                $returnData[$key]["credentials_num"] = $v["credentials_num"];
                $_arr                                = [
                    "file_url"  => $img_prefix . $v['object_key'],
                    "file_name" => $fileType[$v['file_type']] . "_" . $filename,
                ];
            }
            $returnData[$key]["data"][] = $_arr;
        }

        return $returnData;
    }

    /**
     * 删除简历
     * 物理删除
     * @param $paramIn
     * @return array
     */
    public function delCVReal($paramIn = [])
    {
        $this->blacklist = new BlacklistRepository();

        $superAdministratorIds    = env('superAdministratorIds', "");
        $superAdministratorIdsArr = explode(',', $superAdministratorIds);
        if (!in_array($this->userInfo['id'],
                $superAdministratorIdsArr) && !$this->userInfo['resume_remove_permission']) {
            //必须超管或有简历清除权限的人(才能删除
            return $this->checkReturn(-3, $this->getTranslation()->_('4009'));
        }

        $idArr = $paramIn["id"] ?? [];
        if (empty($idArr) || !is_array($idArr)) {
            return $this->checkReturn(-1, "id");
        }

        $entry = new EntryRepository();
        $msg   = $entry->ifCanDelEntry($idArr);
        if (!empty($msg)) {
            return $this->checkReturn(-3, $this->getTranslation()->_('8800'));
        }

        $msg = [];

        //删除失败会返回错误信息。
        $path_name = date("Ymd_His");

        //black必须在第一个，因为要去resume表查数据
        $msg[] = $this->blacklist->delBlackList($idArr, $path_name);
        $msg[] = (new OfferRepository())->delInterviewOffer($idArr, $path_name);
        $msg[] = (new InterviewRepository())->delInterview($idArr, $path_name);
        $msg[] = $entry->delEntry($idArr, $path_name);
        $msg[] = (new TrainRepository())->delTraining($idArr, $path_name);
        $msg[] = (new ResumeRepository())->delResume($idArr, $path_name);

        $this->getDI()->get('logger')->write_log("delCVReal:" . json_encode($paramIn), 'info');
        $res = "";
        foreach ($msg as $item) {
            if ($item === true) {
                continue;
            }
            $res .= $msg;
        }


        if (empty($res)) {
            return $this->checkReturn(1);
        } else {
            return $this->checkReturn(-3, $res);
        }
    }

    /**
     *
     *
     * @param $resumeId
     * @param $jobTitle
     * @param $work_time
     */
    public function courierSalary($resumeId, $jobTitle,$work_time = '' , $project_num = 0,$company = '')
    {
        $sql = "
        --
        select
            hr_hc.worknode_id,
            hr_hc.province_code,
            hr_resume.id
        from
            hr_resume
            left join hr_interview on hr_interview.resume_id = hr_resume.id and hr_interview.hc_id = hr_resume.hc_id
            left join hr_hc on hr_hc.hc_id = hr_interview.hc_id
        where
            hr_resume.id = :resume_id order by hr_interview.interview_id desc limit 1 ";

        $province = $this->getDI()->get("db")->fetchOne($sql, \PDO::FETCH_ASSOC, [
            "resume_id" => $resumeId,
        ], [
            "resume_id" => Column::BIND_PARAM_INT,
        ]);
        // 由配置读取改为数据流读取
        $HrOfferDefaultSalary = HrOfferDefaultSalaryModel::find(
            [
                'conditions' => "deleted = :deleted:",
                'bind'       => [
                    'deleted' => 0,
                ],
                'columns'    => ['province_code', 'base_salary', 'position_allowance'],
            ]
        )->toArray();
        $map                  = [];
        if ($HrOfferDefaultSalary) {
            foreach ($HrOfferDefaultSalary as $k => $v) {
                $map[$v["province_code"]]["base"]      = $v["base_salary"];
                $map[$v["province_code"]]["allowance"] = json_decode($v["position_allowance"], true);
            }
        } else {
            $this->getDI()->get('logger')->write_log("courierSalary-HrOfferDefaultSalary is empty", 'error');
        }
        // $map = self::SALARY_COURIER_MAPS;

        if (!isset($map['TH00']['allowance'][$jobTitle])) {
            return [];
        }
        $result = [
            'money'  => $map['TH00']['base'],
            'rental' => $map['TH00']['allowance'][$jobTitle],
            'food'   => 4500,
        ];
        if ($province && isset($map[$province['province_code']])) {
            $result['money']  = $map[$province['province_code']]['base'];
            $result['rental'] = $map[$province['province_code']]['allowance'][$jobTitle];
        }
        if (isCountry("TH") || isCountry("MY") || isCountry("PH")){
            // 客户端要这个遍历用 要求trial_salary和money 一致 需求号：17077
            $result['trial_salary'] = $result['money'];
        }
        $return['salary'] = $result;

        $return['in_salary_range'] = 0;
        $resumeApprove             = $this->getLastSalaryApproveData($resumeId);
        if ($resumeApprove && $resumeApprove['in_salary_range']) {
            $return['in_salary_range'] = $resumeApprove['in_salary_range'];
        }

        return $return;
    }

    /**
     * 获取薪资审批数据
     * @param $resume_id
     */
    public function getLastSalaryApproveData($resume_id)
    {
        //获取薪资审批中的当前薪资信息
        $salaryApproval = SalaryApproveModel::FindFirst([
            'conditions' => 'resume_id=:resume_id:',
            'bind'       => ['resume_id' => $resume_id],
            'order'      => 'id desc',
        ]);
        if ($salaryApproval) {
            $salaryApproval = $salaryApproval->toArray();
        } else {
            $salaryApproval = [];
        }
        return $salaryApproval;
    }

    /**
     * 获取快递员岗位类型
     * @param $resume_id
     * @return int
     */
    public function getJdTypeByResumeId($resume_id)
    {
        $type = (new ResumeRepository())->get_resume_type($resume_id);
        return $type ? intval($type) : 0;
    }

    /**
     * 验证发送机号规则
     * @param $type 1.摩托车,2.皮卡车,3非快递员
     * @param $car_engine_number 发动机号
     *
     */
    public function validate_car_engine_number($type, $car_engine_number)
    {
        if ($type == 1) { //摩托车快递员
            if (!empty($car_engine_number) && !preg_match('/^[0-9A-Za-z]{10,16}$/', $car_engine_number)) {
                return 'car_engine_number_error_1';
            }
        }
        if ($type == 2) { //皮卡车快递员
            if (!empty($car_engine_number) && !preg_match('/^[0-9A-Za-z]{10,12}$/', $car_engine_number)) {
                return 'car_engine_number_error_2';
            }
        }
        return '';
    }

    /**
     * 保存教育
     * Created by: Lqz.
     * @param $educationExperience
     * CreateTime: 2020/9/3 0003 18:21
     */
    public function saveEducation($educationArr, $resumeId)
    {
        // 先删除
        $edus = (new ResumeRepository())->getEdu($resumeId);
        if ($edus) {
            foreach ($edus as $edu) {
                if (isset($edu['id']) && $edu['id']) {
                    $eduObj            = HrResumeEducationModel::findFirst([
                        'conditions' => 'id = :id:',
                        'bind'       => [
                            'id' => $edu['id'],
                        ],
                    ]);
                    $eduObj->is_delete = 1;
                    $eduObj->save();
                }
            }
        }
        // 再添加和修改
        foreach ($educationArr as &$education) {
            if (isset($education['id']) && $education['id']) {
                $mode = HrResumeEducationModel::findFirst([
                    'conditions' => 'id = :id:',
                    'bind'       => [
                        'id' => $education['id'],
                    ],
                ]);
            } else {
                $mode = new HrResumeEducationModel();
            }
            $education['resume_id'] = $resumeId;
            $eduId                  = (new ResumeRepository())->saveEdu($mode, $education);
            $education['id']        = $eduId;
        }
        return $educationArr;
    }

    /**
     * 根据入职表中的员工工号获取其简历中信息
     * @param $staff_id
     * @return array
     */
    public function getResumeInfoByStaffId($staff_id)
    {
        //根据员工工号获取员工简历ID

        $staff_id     = intval($staff_id);
        $hr_entry_obj = HrEntryModel::findFirst([
            'columns'    => 'entry_id,resume_id,staff_id',
            'conditions' => 'staff_id = ?1',
            'bind'       => [
                1 => $staff_id,
            ],
        ]);
        if (empty($hr_entry_obj)) {
            return [];
        }
        $resume_id = $hr_entry_obj->resume_id ?? 0;
        // 根据员工简历ID获取员工简历 中姓名、手机号等信息
        $hr_resume_obj = HrResumeModel::findFirst([
            'columns'    => 'first_name,last_name,phone',
            'conditions' => 'id = ?1',
            'bind'       => [
                1 => $resume_id,
            ],
        ]);
        if (empty($hr_resume_obj)) {
            return [];
        }

        return $hr_resume_obj->toArray();
    }

    /**
     * 添加沟通记录
     *
     * @param $cvId
     * @param $status
     * @param $content
     * @param $userId
     * @param $failure_reason_type
     * @return mixed
     * @throws BusinessException
     */
    public function addCommunicateLog($cvId, $status, $content, $userId, $failure_reason_type)
    {
        //同步更新简历推荐表
        $db = $this->getDI()->get('db');
        $db->updateAsDict('communication_resume_log', ["newest" => 0], [
            'conditions' => "resume_id = $cvId ",
        ]);

        $communicationResumeLog                      = new CommunicationResumeLogModel();
        $communicationResumeLog->resume_id           = $cvId;
        $communicationResumeLog->status              = $status;
        $communicationResumeLog->content             = $content;
        $communicationResumeLog->failure_reason_type = $failure_reason_type ?? 0;
        $communicationResumeLog->submiter_id         = $userId;
        $communicationResumeLog->newest              = 1;
        $communicationResumeLog->save();
        $this->setLastOperator($cvId);

        return $communicationResumeLog->id;
    }

    /**
     * 获取沟通记录
     *
     * @param $cvId
     */
    public function getCommunicateLogs($cvId)
    {
        $resumeLogs = CommunicationResumeLogModel::find([
            'conditions' => ' resume_id = :resume_id: ',
            'bind'       => [
                'resume_id' => $cvId,
            ],
            'order'      => 'create_time asc',
        ])->toArray();

        $add_hour = $this->getDI()['config']['application']['add_hour'];
        $results = [];
        if ($resumeLogs) {
            $submiterIds = array_values(array_unique(array_column($resumeLogs, 'submiter_id')));
            $staffs      = HrStaffInfoModel::find([
                'conditions' => ' staff_info_id in ({staff_ids:array}) ',
                'bind'       => [
                    'staff_ids' => $submiterIds,
                ],
            ])->toArray();
            $staffs      = array_column($staffs, null, 'staff_info_id');

            foreach ($resumeLogs as $resumeLog) {
                $submiter_name = StaffServer::getStaffNameView($staffs[$resumeLog['submiter_id']] ?? [],3);
                $content = $resumeLog['content'] ?? '';

                if ($resumeLog['status'] == CommunicationResumeLogModel::STATUS_AFRESH) {
                    $submiter_name = 10000;

                    if ($content == CommunicationResumeLogModel::STATUS_AFRESH_DEFAULT_CONTENT) {
                        $content = $this->getTranslation()->t('communicate_state_content_4_1');
                    } else {
                        $content = $this->getTranslation()->t('communicate_state_content_4');
                    }
                }

                $res = [
                    'resume_id'                => $resumeLog['resume_id'],
                    'submiter_id'              => $resumeLog['submiter_id'],
                    'status'                   => $resumeLog['status'],
                    'failure_reason_type'      => $resumeLog['failure_reason_type'],
                    'failure_reason_type_name' => $this->getTranslation()->t('communication_failure_reason_' . $resumeLog['failure_reason_type']),
                    'status_desc'              => $this->getTranslation()->t('communicate_state_' . $resumeLog['status']),
                    'create_time'              => date("Y-m-d H:i:s",
                        strtotime($resumeLog['create_time']) + $add_hour * 3600),
                    'submiter_name'            => $submiter_name,
                    'content'                  => $content,
                ];

                if (isCountry('PH')) {
                    $res['success_reason_type']      = !empty($resumeLog['success_reason_type']) ? json_decode($resumeLog['success_reason_type'],true) : [];
                    $res['success_reason_type_name'] = $this->getSuccessReasonTypeName($res['success_reason_type']);
                }

                $results[] = $res;
            }
        }

        return $results;
    }

    /**
     * 添加沟通记录
     * addCommunicatelog不在用于winhr界面插入
     */
    public function addCommunicateData($params)
    {
        $resumeInfo = HrResumeModel::findFirst([
            'conditions' => "id = :resume_id:",
            'bind'       => ['resume_id' => $params['id']],
            'columns'    => ['id','job_id'],
        ]);

        if (empty($resumeInfo)) {
            throw new ValidationException(self::$t->_('operation_data_not_exist'));
        }

        $formTypeKey = (new ResumeServer())->getFormTypeKey($resumeInfo['job_id']);

        //一线验证
        if (
            $formTypeKey == HrQuestionsModel::TYPE_EXTENSION_NETWORK_FIRST_LINE_JD
            && $params['status'] == CommunicationResumeLogModel::STATUS_SUCCESS
            && empty($params['success_reason_type'])
        ) {
            throw new ValidationException(self::$t->_('success_reason_type_empty'));
        }

        //同步更新简历推荐表
        $db = $this->getDI()->get('db');
        $db->begin();

        try {
            $db->updateAsDict('communication_resume_log', ["newest" => CommunicationResumeLogModel::NEWST_NO], [
                'conditions' => "resume_id = {$params['id']} ",
            ]);

            $communicationResumeLog                      = new CommunicationResumeLogModel();
            $communicationResumeLog->resume_id           = $params['id'];
            $communicationResumeLog->status              = $params['status'];
            $communicationResumeLog->content             = $params['content'] ?? '';
            $communicationResumeLog->failure_reason_type = $params['failure_reason_type'] ?? 0;
            $communicationResumeLog->success_reason_type = !empty($params['success_reason_type']) ? json_encode($params['success_reason_type']) : '';
            $communicationResumeLog->submiter_id         = $this->userInfo['id'] ?? 0;
            $communicationResumeLog->newest              = CommunicationResumeLogModel::NEWST_YES;
            $communicationResumeLog->save();
            $this->setLastOperator($params['id']);

            if (!$db->commit()) {
                throw new Exception('addCommunicateData Save Error');
            }

            return $communicationResumeLog->id;
        } catch (Exception $exception) {
            $db->rollback();
            throw $exception;
        }
    }

    /**
     * 获取成功原因字符
     */
    public function getSuccessReasonTypeName($successReasonTypes)
    {
        if (empty($successReasonTypes)) {
            return '';
        }

        $data = [];
        foreach ($successReasonTypes as $v) {
            $data[] =  $this->getTranslation()->_('communication_success_reason_' . $v);
        }

        return implode('; ' , $data);
    }

    /**
     *
     *
     * @param $resumeId
     *
     */
    public function resumesLogs($resumeId)
    {
        $interviews   = HrInterviewModel::find([
            'conditions' => ' resume_id = :resume_id:',
            'bind'       => [
                'resume_id' => $resumeId,
            ],
        ])->toArray();
        $interviewIds = array_column($interviews, 'interview_id');

        $offer   = HrInterviewOfferModel::find([
            'conditions' => ' resume_id = :resume_id:',
            'bind'       => [
                'resume_id' => $resumeId,
            ],
        ])->toArray();
        $offerIds = array_column($offer, 'id');

        if ($interviewIds) {
            $conditions = ' (module_id = :resume_id: and module_type in (5,6,7,8,9,10,17,18,19,20,21,22,23,24,25,26,27)) or (module_id in ({interview_ids:array}) and module_type in (1, 2, 3, 4, 13,15, 16)) ';
            $binds      = [
                'resume_id'     => $resumeId,
                'interview_ids' => $interviewIds,
            ];
            //修改offer
            if($offerIds){
                $conditions .= " or ( module_id in ({offer_id:array}) and module_type = 2 and action  = 2 ) ";
                $binds['offer_id'] = $offerIds;
            }

        } else {
            $conditions = ' module_id = :resume_id: and module_type in (5,6,7,8,9,10,17,18,19,20,21,22,23,24,25,26,27) ';
            $binds      = [
                'resume_id' => $resumeId,
            ];
        }

        $data = HrLogModel::find([
            'columns'    => 'id, CONVERT_TZ(created_at, \'+00:00\', \'' . $this->timezone . '\' ) AS created_at, staff_info_id, module_id, module_type,module_level, module_status, module_level, action, user_ip,data_after',
            'conditions' => $conditions,
            'bind'       => $binds,
            'order'      => 'id asc',
        ])->toArray();

        $logServer = new LogServer();

        //淘汰类型
        $out_type_list = (new SysListServer())->getOutInterviewList();
        $array_key     = array_column($out_type_list, 'key');
        $array_value   = array_column($out_type_list, 'name');
        $array         = array_combine($array_key, $array_value);

        //获取用户名
        $staffIds = array_column($data,'staff_info_id');

        $staffList = (new StaffServer())->getStaffListByIds($staffIds,['staff_info_id','name','nick_name','mobile']);
        $staffList = array_column($staffList,null,'staff_info_id');

        foreach ($data as $k => $v) {
            $out_type    = '';
            $pass_record = '';
            if ($v['module_status'] == 2) {
                //获取不通过原因
                //先根据hr_log_id字段查resume_out_log字段
                //如果查询为空按旧方式查询
                //为兼容新老数据
                $out_type = (new ResumeOutLogModel())->findFirst([
                    'conditions' => "hr_log_id=:hr_log_id:",
                    'bind'       => ['hr_log_id' => $v['id']],
                    'columns'    => ['id'],
                ]);
                if (!$out_type) {
                    $interview_log = (new \FlashExpress\bi\App\Models\backyard\HrInterviewInfoModel())->findFirst([
                        'conditions' => "level = :level: and interview_id =:module_id:",
                        'bind'       => ['level' => $v['module_level'], 'module_id' => $v['module_id']],
                        'columns'    => ['hc_id'],
                    ]);
                    if ($interview_log) {
                        $out_type = (new ResumeOutLogModel())->findFirst([
                            'conditions' => "hc_id = :hc_id: and interview_id =:interview_id:",
                            'bind'       => ['hc_id' => $interview_log->hc_id, 'interview_id' => $v['module_id']],
                            'columns'    => ['id'],
                        ]);
                    }
                }
                if ($out_type) {
                    //查询pass_type_record表
                    $pass_list = HrInterviewPassTypeRecordModel::find([
                        'conditions' => "business_type=3 AND business_id=:business_id:",
                        'bind'       => ['business_id' => $out_type->id],
                        'columns'    => ['pass_type, pass_reason, pass_remark'],
                    ])->toArray();
                }
                $pass_records = [];
                if (!empty($pass_list)) {
                    foreach ($pass_list as $key => $val) {
                        if ($val['pass_type'] == 0) {
                            $pass_records[] = $array[$val['pass_reason']];
                        } elseif (!empty($val['pass_remark'])) {
                            if ($val['pass_reason'] == 0) {
                                $pass_records[] = $this->getTranslation()->_(enums::INTERVIEW_PASS_TYPE_PREFIX . $val['pass_type']) . "(" . $val['pass_remark'] . ")";
                            } else {
                                $pass_records[] = $this->getTranslation()->_(enums::INTERVIEW_PASS_TYPE_REASON_PREFIX . $val['pass_type'] . '_' . $val['pass_reason']) . "(" . $val['pass_remark'] . ")";
                            }
                        } else {
                            $pass_records[] = $this->getTranslation()->_(enums::INTERVIEW_PASS_TYPE_REASON_PREFIX . $val['pass_type'] . '_' . $val['pass_reason']);
                        }
                    }
                    $pass_record = implode(',', $pass_records);
                }
            }
            $staffInfo = $staffList[$v['staff_info_id']] ?? [];

            if (!$v['module_level']) {
                $module_level = '';
            } else {
                $module_level = $v['module_level'];
            }
            if (!$v['module_status']) {
                $module_status = '';
            } else {
                $module_status = ',' . $logServer->interviewStatusArr[$v['module_status']];
            }

            $staffName = StaffServer::getStaffNameView($staffInfo, 4, $v['staff_info_id'] ?? 0);


            if ($v['action'] == 4 && $v['module_type'] == 1){
                // 取消面试 终止面试
                $data[$k]['interview_log'] = $this->getTranslation()->_('4605') . ' ' . $staffName . ' ' . $logServer->actionArr[$v['action']] . $logServer->moduleTypeArr[$v['module_type']] . $module_level .'('.$this->getTranslation()->_('termination_of_interview') . ')';
            }elseif ($v['action'] == 29 && $v['module_type'] == 1){
                // 取消面试 取消本轮面试
                $data[$k]['interview_log'] = $this->getTranslation()->_('4605') . ' ' . $staffName . ' ' . $this->getTranslation()->_('7528') . $module_level . '('.$logServer->actionArr[$v['action']] . ')';
            }else{
                $data[$k]['interview_log'] = $this->getTranslation()->_('4605') . ' ' . $staffName . ' ' . $logServer->actionArr[$v['action']] . $logServer->moduleTypeArr[$v['module_type']] . $module_level . $module_status . ' ' . $pass_record;  //$out_type;
            }

            //取消推荐 18724新增内容 
            if (
                $v['action'] == enums::$log_option['cancel_recommend']
                && $v['module_type'] == enums::$log_module_type['resume']
            ) {
                $cancelInfo = !empty($v['data_after']) ? json_decode($v['data_after'],true) : [];

                if (!empty($cancelInfo['cancel_type'])) {
                    $cancel_type_str = $this->getTranslation()->_(HrResumeFilterModel::$cancel_type_list[$cancelInfo['cancel_type']] ?? '');

                    if ($cancel_type_str) {
                        $data[$k]['interview_log'] .= '('.$cancel_type_str.')';
                    }
                }
            }

            //组装log返回格式
            if (isCountry('PH')
                && $v['module_type'] == enums::$log_module_type['entry']
                && $v['action'] == enums::$log_option['confirmed']
                && $v['module_status'] == enums::$log_status['missing']
            ) {
                $cancelInfo = !empty($v['data_after']) ? json_decode($v['data_after'], true) : [];

                $cancel_type        = $cancelInfo['cancel_type'] ?? 0;
                $cancel_reason_type = $cancelInfo['cancel_reason_type'] ?? 0;

                $cancel_type_name        = $this->getTranslation()->_(HrEntryModel::$cancel_type_list[$cancel_type] ?? '');
                if ($cancel_reason_type == HrEntryModel::CANCEL_REASON_TYPE_OTHER) {
                    $cancel_reason_type_name = $cancelInfo['cancel_reason_content'] ?? '';
                } else {
                    $cancel_reason_type_name = $this->getTranslation()->_(HrEntryModel::$cancel_reason_type_list[$cancel_reason_type] ?? '');
                }

                $cancel_reason_type_name = $cancel_reason_type_name ? '(' . $cancel_reason_type_name . ')' : '';

                $data[$k]['interview_log'] .= $cancel_type_name . $cancel_reason_type_name;
            }

            //简历分数修改
            if (
                $v['module_type'] == enums::$log_module_type['resume_score'] &&
                $v['action'] == enums::$log_option['modify']
            ) {
                $aftInfo = !empty($v['data_after']) ? json_decode($v['data_after'], true) : [];

                $keyName = $scoreName = '';
                if (!empty($aftInfo['key']) && isset($aftInfo['old_score']) && isset($aftInfo['new_score'])) {
                    $keyName = $this->getTranslation()->_('resume_ai_score_title');
                    $keyName .= '-'.$this->getTranslation()->_(HrResumeAiScoreModel::$score_key_list[$aftInfo['key']] ?? '');

                    $unit      = $this->getTranslation()->_('unit_fen');
                    $scoreName = $aftInfo['old_score'].$unit.' > '.$aftInfo['new_score'].$unit;
                }

                $data[$k]['interview_log'] .= $keyName.' '.$scoreName;
            }

            unset($data[$k]['data_after']);
        }

        // 追加淘汰记录
        $out_data = HrInterviewPassTypeRecordModel::find([
            'conditions' => "business_type=:business_type: AND resume_id=:resume_id: AND deleted = 0",
            'bind' => ['business_type' => HrInterviewPassTypeRecordModel::BUSINESS_TYPE_RESUME,'resume_id' => $resumeId],
            'columns' => ['CONVERT_TZ(created_at, \'+00:00\', \'' . $this->timezone . '\' ) AS created_at, operater, out_type, resume_id, pass_reason'],
        ])->toArray();

        $resume_data = HrResumeModel::findFirst([
            'conditions' => "id=:resume_id:",
            'bind' => ['resume_id' => $resumeId],
            'columns' => ['out_reason'],
        ]);
        $_out_data = [];
        foreach ($out_data as $k => $v){
            if ($v['operater']) {
                $staffInfo = (new HrStaffInfoModel())->getHrStaffInfo($v['operater']);
                $staffName = StaffServer::getStaffNameView($staffInfo, 4, $v['operater']);
            } else {
                $staffName = '';
            }
            $_out_data[$k]['id'] = '';
            $_out_data[$k]['created_at'] = $v['created_at'];
            $_out_data[$k]['staff_info_id'] = $v['operater'];
            $_out_data[$k]['module_id'] = '';
            $_out_data[$k]['module_type'] = '';
            $_out_data[$k]['module_level'] = '';
            $_out_data[$k]['module_status'] = '';
            $_out_data[$k]['action'] = '';
            $_out_data[$k]['user_ip'] = '';
            $_out_data[$k]['interview_log'] = $this->getTranslation()->_('4605') . ' ' . $staffName . ' ' . $this->getTranslation()->_('out_resume') .';'.$this->getTranslation()->_('out_reason').':'.($array[$v['pass_reason']] ?? '').';'.$this->getTranslation()->_('out_illustrate'). ':'.($resume_data?$resume_data->out_reason : '');
        }
        // 根据创建时间刷下排序
        $data = array_merge($data,$_out_data);
        $sort = array_column($data,'created_at');
        array_multisort($sort,SORT_ASC,$data);
        return $data;
    }

    /**
     * 验证手机号和简历是否匹配
     * @param $resume_id
     * @param $phone
     * @return bool
     */
    public function ResumeIsMatchPhone($resume_id, $phone)
    {
        $hr_resume_obj = HrResumeModel::findFirst([
            'columns'    => 'id',
            'conditions' => 'id = :resume_id: and phone =:phone:',
            'bind'       => [
                'resume_id' => $resume_id,
                'phone'     => $phone,
            ],
        ]);

        return empty($hr_resume_obj) ? false : true;
    }


    /**
     * @description:变更消息状态
     * @param null
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2021/7/28 20:05
     */
    public function updateMessage($paramIn)
    {
        try {
            if (empty($paramIn['msg_id']) || empty($paramIn['staff_id'])) {
                throw new \Exception("by-updateMessage-参数不对：" . json_encode($paramIn));
            }

            //数据至同步 bi'
            //获取简历 id
            $entry               = new EntryRepository();
            $EntryInfo           = $entry->getStaffIdInfo($paramIn);
            $paramIn['entry_id'] = $EntryInfo['entry_id'];
            $result              = $this->SynchronizeBi($paramIn);
            if ($result['code'] != 1) {
                throw new \Exception("by-updateMessage-同步bi 信息失败!：" . json_encode($paramIn));
            }

            //消息变为已读
            $apiClient = (new ApiClient('by_rpc', '', 'has_read_operation_by_staffid', $this->lang));
            $apiClient->setParams([
                'msg_id'   => $paramIn['msg_id'],
                'staff_id' => $paramIn['staff_id'],
            ]);
            $result = $apiClient->execute();
            $this->getDI()->get('logger')->write_log("by-updateMessage-has_read_operation_by_staffid-获取消息信息:" . json_encode($result),
                'info');
            if (!isset($result['result']['code']) || $result['result']['code'] != 1) {
                throw new \Exception("by-updateMessage-has_read_operation_by_staffid-获取消息信息失败,接口返回：" . json_encode($result));
            }
            return true;
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("by updateMessage 异常信息:" . $e->getMessage(), 'error');
            return false;
        }
    }

    /**
     * @description: 同步bi 数据
     * @param null
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2021/7/28 23:37
     */

    public function SynchronizeBi($paramIn)
    {
        try {
            $staffId  = $paramIn['staff_id'];
            $entry_id = $paramIn['entry_id'];


            //需要同步到 bi 的数据
            $entrySql  = "--
                select
                    hr_entry.*,
                    hr_interview_offer.staff_job_type
                from hr_entry
                LEFT JOIN hr_interview_offer on hr_interview_offer.id = hr_entry.interview_offer_id
                where hr_entry.entry_id=" . $entry_id;
            $data      = $this->getDI()->get('db_rby')->query($entrySql);
            $entryData = $data->fetch(\Phalcon\Db::FETCH_ASSOC);
            if (empty($entryData)) {
                throw new \Exception($this->getTranslation()->_('4012'), 4012);
            }


            // 查询简历数据
            $resumeId = $entryData['resume_id'];
            $resume   = HrResumeModel::findFirst([
                'conditions' => 'id = :id:',
                'bind'       => [
                    'id' => $resumeId,
                ],
            ]);

            if (empty($resume)) {
                throw new \Exception("SynchronizeBi:The staff 【resume_id:{$resumeId}】 in table HrResume not found!");
            }
            // 查找简历信息
            $edu = \FlashExpress\bi\App\Models\backyard\HrResumeEducationModel::findFirst([
                'conditions' => 'resume_id = :resumeId: and is_delete = 0',
                'bind'       => [
                    'resumeId' => $resumeId,
                ],
                'order'      => 'education_level DESC',
            ]);

            $hr_ecomomy_ability = HrEconomyAbilityModel::findFirst([
                'conditions' => 'resume_id = :resume_id:',
                'bind'       => [
                    'resume_id' => $resumeId,
                ],
            ]);
            if (empty($edu)) {
                $edu = $hr_ecomomy_ability;
            }

//			if (empty($edu)) {
//				throw new \Exception("SynchronizeBi:The staff 【resume_id:{$resumeId}】 education in table HrResumeEducation and HrEconomyAbility  not found!");
//			}

            $family = HrFamilyModel::findFirst([
                'conditions' => 'resume_id = :resume_id:',
                'bind'       => [
                    'resume_id' => $resumeId,
                ],
            ]);
//			if (empty($family)) {
//				throw new \Exception("SynchronizeBi:The staff 【resume_id:{$resumeId}】 education in table HrResumeEducation and HrEconomyAbility  not found!");
//			}

            $register_country        = $resume->register_country ?? '';    //国家
            $register_province       = $resume->register_government ?? ''; //户口所在省
            $register_city           = $resume->register_city ?? '';       //户口所在市
            $register_district       = $resume->register_town ?? '';       //户口所在乡
            $register_postcodes      = $resume->register_postcodes ?? '';  //户口所在邮编
            $register_house_num      = $resume->register_house_num ?? '';  //户口所在门牌号
            $register_village_num    = $resume->register_village_num ?? '';//户口所在村号
            $register_village        = $resume->register_village ?? '';    //户口所在村
            $register_alley          = $resume->register_alley ?? '';      //户口所在巷
            $register_street         = $resume->register_street ?? '';     //户口所在街道
            $register_rt             = $resume->register_rt ?? '';         //户口所在居委会
            $register_rw             = $resume->register_rw ?? '';         //户口所在邻组
            $register_detail_address = $resume->register_detail_address ?? '';//户口所在地详细地址
            $dad_first_name          = $family->dad_first_name ?? '';      //父亲名
            $dad_last_name           = $family->dad_last_name ?? '';       //父亲姓
            $mum_first_name          = $family->mum_first_name ?? '';      //母亲名
            $mum_last_name           = $family->mum_last_name ?? '';       //母亲姓
            $relatives_relationship  = $family->relationship ?? '';
            $relatives_first_name    = $family->relationship_first_name ?? '';
            $relatives_last_name     = $family->relationship_last_name ?? '';
            $relatives_call_name     = $family->relationship_call_name ?? '';
            $relatives_mobile        = $family->relationship_mobile ?? '';

            //教育
            $graduate_school = $edu->graduate_school ?? '';
            $major           = $edu->major ?? '';
            $graduate_time   = $edu->graduate_time ?? '';

//			$recordEntry = json_decode($entryData['record_entry'], true);
//			//获取残疾人照片
//			$disability_pic                                = $this->resume->get_disability_pic([$resumeId]);
//			//17 是正面  18 是反面
//			$recordEntry['disability_certificate_front']   = $disability_pic[$resumeId]['17']['object_key'] ?? '';
//			$recordEntry['disability_certificate_reverse'] = $disability_pic[$resumeId]['18']['object_key'] ?? '';

            $residence_country        = $resume->residence_country ?? '';
            $residence_province       = $resume->residence_government ?? '';
            $residence_city           = $resume->residence_city ?? '';
            $residence_district       = $resume->residence_town ?? '';
            $residence_postcodes      = $resume->residence_postcodes ?? '';
            $residence_house_num      = $resume->residence_house_num ?? '';
            $residence_village_num    = $resume->residence_village_num ?? '';
            $residence_village        = $resume->residence_village ?? '';
            $residence_alley          = $resume->residence_alley ?? '';
            $residence_street         = $resume->residence_street ?? '';
            $residence_detail_address = $resume->residence_detail_address ?? '';
            $race                     = $resume->race ?? '';        //种族
            $religion                 = $resume->religion ?? '';    //宗教

            $recordEntry['register_country']        = $register_country;    //国家
            $recordEntry['register_province']       = $register_province;   //户口所在省
            $recordEntry['register_city']           = $register_city;       //户口所在市
            $recordEntry['register_district']       = $register_district;   //户口所在乡
            $recordEntry['register_postcodes']      = $register_postcodes;  //户口所在邮编
            $recordEntry['register_house_num']      = $register_house_num;  //户口所在门牌号
            $recordEntry['register_village_num']    = $register_village_num;//户口所在村号
            $recordEntry['register_village']        = $register_village;    //户口所在村
            $recordEntry['register_alley']          = $register_alley;      //户口所在巷
            $recordEntry['register_street']         = $register_street;     //户口所在街道
            $recordEntry['dad_first_name']          = $dad_first_name;      //父亲名
            $recordEntry['dad_last_name']           = $dad_last_name;       //父亲姓
            $recordEntry['mum_first_name']          = $mum_first_name;      //母亲名
            $recordEntry['mum_last_name']           = $mum_last_name;       //母亲姓
            $recordEntry['register_detail_address'] = $register_detail_address; //户口所在地详细地址
            $recordEntry['graduate_school']         = $graduate_school;
            $recordEntry['major']                   = $major;
            $recordEntry['graduate_time']           = $graduate_time;
            $recordEntry['staff_info_id']           = $staffId;
            $recordEntry['data_from']               = 'winhr_by';

            $recordEntry['residence_country']        = $residence_country;
            $recordEntry['residence_province']       = $residence_province;
            $recordEntry['residence_city']           = $residence_city;
            $recordEntry['residence_district']       = $residence_district;
            $recordEntry['residence_postcodes']      = $residence_postcodes;
            $recordEntry['residence_house_num']      = $residence_house_num;
            $recordEntry['residence_village_num']    = $residence_village_num;
            $recordEntry['residence_village']        = $residence_village;
            $recordEntry['residence_alley']          = $residence_alley;
            $recordEntry['residence_street']         = $residence_street;
            $recordEntry['race']                     = $race;//种族
            $recordEntry['religion']                 = $religion; //宗教
            $recordEntry['nick_name']                = $resume->nickname ?? '';
            $recordEntry['operater']                 = $staffId;

            if (isCountry('ID')) {
                $recordEntry['register_rt'] = $register_rt;     //户口所在邻组
                $recordEntry['register_rw'] = $register_rw;     //户口所在居委会
            }

            if(isCountry('MY')){
                $recordEntry['residence_detail_address'] = $residence_detail_address;
                $recordEntry['relatives_relationship']  = $relatives_relationship;
                $recordEntry['relatives_first_name']    = $relatives_first_name;
                $recordEntry['relatives_last_name']     = $relatives_last_name;
                $recordEntry['relatives_call_name']     = $relatives_call_name;
                $recordEntry['relatives_mobile']        = $relatives_mobile;
            }

            /*********** end  需要同步到 bi 的数据************/
            //获取工号当前班次
            $this->getDI()->get('logger')->write_log("update_staff_info-同步 hris请求信息:" . json_encode($recordEntry),
                'info');
            $ac = new ApiClient('hr_rpc', '', 'update_staff_info', $this->lang);
            $ac->setParams($recordEntry);
            $result = $ac->execute();
            $this->getDI()->get('logger')->write_log("winhr_by_cv_synchronize-同步 hris返回信息:" . json_encode($result),
                'info');
            if (isset($result['result']['code']) && $result['result']['code'] == 1) {
                //添加日志
                (new \FlashExpress\bi\App\Repository\LogRepository())->addLog([
                    'module_id'     => $entryData['interview_id'],
                    'module_type'   => enums::$log_module_type['by_complete'],
                    'action'        => enums::$log_option['modify'],
                    'staff_info_id' => $staffId,
                    'data_after'    => "",
                    'module_status' => enums::$log_status['modify'],
                ]);
            } else {
                $this->getDI()->get('logger')->write_log("winhr_by_cv_synchronize-失败 hris返回信息:" . json_encode($recordEntry) . '==>' . json_encode($result),
                    'error');
            }

            return [
                'code' => 1,
                'msg'  => '',
                'data' => [],
            ];
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("by SynchronizeBi 异常信息:" . $e->getMessage(), 'error');
            return [
                'code' => 0,
                'msg'  => '',
                'data' => [],
            ];
        }
    }

    /**
     * 获取简历相关附件类型
     */
    public function getAnnexFileType()
    {
        //获取简历附件全类型
        $fileType = [
            //本人半身照
            1  => $this->getTranslation()->_('4151'),
            //身份证正面
            2  => $this->getTranslation()->_('4152'),
            //身份证反面
            3  => $this->getTranslation()->_('4153'),
            //户籍照第一页
            4  => $this->getTranslation()->_('4154'),
            //户籍照应聘者本人信息页
            5  => $this->getTranslation()->_('4155'),
            //兵役服役证明
            6  => $this->getTranslation()->_('4156'),
            //成绩报告单
            7  => $this->getTranslation()->_('4157'),
            //清白证明书
            8  => $this->getTranslation()->_('4158'),
            //补充附件
            9  => $this->getTranslation()->_('4159'),
            //驾驶证正面
            10 => $this->getTranslation()->_('4160'),
            //驾驶证反面
            11 => $this->getTranslation()->_('4161'),
            //车辆登记薄
            12 => $this->getTranslation()->_('4162'),
            //车辆照片
            13 => $this->getTranslation()->_('4163'),
            //车辆使用授权
            14 => $this->getTranslation()->_('4164'),
            // 个人简历附件
            16 => $this->getTranslation()->_('4166'),
            //残疾人证正面
            17 => $this->getTranslation()->_('4168'),
            //残疾人证背面
            18 => $this->getTranslation()->_('4169'),
            //最高学历证书(马来新增）
            19 => $this->getTranslation()->_('annex_4170'),
            //GDL驾照正面(马来新增）
            20 => $this->getTranslation()->_('annex_4171'),
            21 => "OR",
            22 => $this->getTranslation()->_('annex_commitment'),//承诺书
            24 => $this->getTranslation()->_('annex_24'),//医保卡
            25 => $this->getTranslation()->_('annex_25'),//公积金卡
            26 => $this->getTranslation()->_('annex_26'),//税卡
            27 => $this->getTranslation()->_('annex_27'),//社保卡
            28 => $this->getTranslation()->_('annex_28'),//薪资证明
            29 => $this->getTranslation()->_('annex_29'),//银行卡
        ];

        return $fileType;
    }

    /**
     * [isPhoneRepeat 简历电话重复]
     * @return [type] [array]
     * <AUTHOR> <[email address]>
     */
    public function isPhoneRepeat($phone = '', $name = '', $identity = '')
    {
        $this->resume = new ResumeRepository();
        $data         = $this->resume->isPhoneRepeat($phone, $name, $identity);
        return $data;
    }

    /**
     * 判断附件信息中是否包含
     * @param $file_url
     * @param $type
     */
    public function isHaveOfferAnnex($file_url)
    {
        //如果是非一线职位，则判断上传附件类型是不是offer附件
        $is_offer_annex = false;
        foreach ($file_url as $v) {
            if ($v['type'] == 5) {
                $is_offer_annex = true;
                break;
            }
        }

        return $is_offer_annex;
    }

    /**
     * 根据简历ID获取简历表数据
     * @param $resumeIds
     * @param string $columns
     * @return array
     */
    public function getResumeListByIds($resumeIds, $columns = '*'): array
    {
        if (empty($resumeIds)) {
            return [];
        }

        $resumeObj = HrResumeModel::find([
            'columns'    => $columns,
            'conditions' => "id IN ({resume_id:array}) and deleted = :deleted:",
            'bind'       => [
                'resume_id' => array_values($resumeIds),
                'deleted'   => enums::IS_DELETED_NO,
            ],
        ]);

        return $resumeObj->toArray();
    }

    /**
     * 根据简历ID获取简历表数据
     * @param $resume_id
     * @return array
     */
    public function getResumeBaseInfo($resume_id)
    {
        $resumeObj = HrResumeModel::findFirst([
            'conditions' => "id = :resume_id: and deleted = 0",
            'bind'       => ['resume_id' => $resume_id],
        ]);
        if ($resumeObj) {
            return $resumeObj->toArray();
        } else {
            return [];
        }
    }

    /**
     * 根据手机号获取简历表数据
     * @param $phone
     * 手机号处理请放在方法外，例如菲律宾补0
     * @return array
     */
    public function getResumeInfoByPhone($phone): array
    {
        $phone   = trim($phone);
        $dataObj = HrResumeModel::findFirst([
            'conditions' => 'deleted = :deleted: and phone=:phone: ',
            'bind'       => [
                'phone'   => $phone,
                'deleted' => enums::IS_DELETED_NO,
            ],
            'order'      => 'id DESC',
        ]);

        return $dataObj ? $dataObj->toArray():[];
    }

    /**
     * 内推简历审核接口
     * @param $params
     * @param $userid
     */
    public function resumeAppraval($params, $auditor_staff_id)
    {
        $this->log = new LogServer();

        $resume_id = $params['id'];

        $approve_state = intval($params['approve_state']);//审核状态 2：通过，3-驳回
        $reject_reason = $params['reject_reason'] ?? '';

        $update_param = ['approve_state' => $approve_state]; //简历表更新字段

        $audit_time             = gmdate('Y-m-d H:i:s', time());
        $recommend_update_param = ['auditor' => $auditor_staff_id, 'audit_time' => $audit_time]; //简历推荐表更新字段

        if ($approve_state == enums::$resume_apprave_state['approved']) {
            $log_action = enums::$log_option['approve_passed'];
        }
        //审核驳回操作
        if ($approve_state == enums::$resume_apprave_state['rejected']) {
            $update_param['approve_reject_reason']   = $reject_reason;
            $recommend_update_param['reject_reason'] = $reject_reason;
            $log_action                              = enums::$log_option['approve_rejected'];
        }

        $db = $this->getDI()->get('db');
        $db->begin();
        try {
            //更新简历表
            $db->updateAsDict('hr_resume', $update_param, [
                'conditions' => " id = $resume_id ",
            ]);

            //同步更新简历推荐表
            $db->updateAsDict('hr_resume_recommend', $recommend_update_param, [
                'conditions' => "resume_id = $resume_id ",
            ]);

            //添加日志
            $this->log->addLog([
                'module_id'   => $resume_id,
                'module_type' => enums::$log_module_type['resume'],
                'action'      => $log_action,
            ]);
            $db->commit();
        } catch (\Exception $e) {
            $db->rollback();
            throw new \Exception($e->getMessage());
        }
    }

    /**
     * 简历筛选推荐操作
     * @param $params
     * @throws BusinessException
     */
    public function recommendResume($params)
    {
        $this->log = new LogServer();

        //获取简历基本信息
        $this->getDI()->get('logger')->write_log("recommendResume params:" . json_encode($params, true), 'info');
        $resume_id = $params['resume_id'] ?? 0;
        $resume    = $this->getResumeBaseInfo($params['resume_id']);

        //todo 验证逻辑处理
        if (empty($resume)) {
            throw new BusinessException("简历不存在，当前cvid：{$resume_id}");
        }

        if (isCountry("TH")){
            // 验证黑灰名单
            $black_grey_list = (new BlackgreylistServer())->check_black_grey_list([
                "resume_id" => $resume_id,
            ]);
            if ($black_grey_list["is_black_list"]){
                throw new BusinessException($this->getTranslation()->_('identity_black_list_existed'));
            }
            (new OutsourcingBlackListServer())->check($resume['credentials_num'] ?? '','winhr',true,$this->lang);
        }

        $hc_info = $this->getHcInfo($resume['hc_id']);
        if (empty($hc_info)) {
            throw new BusinessException("hc不存在，当前hcid：{$resume['hc_id']}");
        }

        $hcData = (new HcRepository())->checkHc(['hc_id' => $resume['hc_id']]);
        if ($hcData['state_code'] == 3 || $hcData['surplusnumber'] == 0) {
            throw new BusinessException($this->getTranslation()->_('11027'));
        }
        if ($hcData['state_code'] == 4) {
            throw new BusinessException($this->getTranslation()->_('11028'));
        }
        if ($hcData['state_code'] == 9) {
            throw new BusinessException($this->getTranslation()->_('11031'));
        }

        $interviewServer = new InterviewServer();
        $hcData['is_head_office_recruit'] = $resume['is_head_office_recruit'] ?? HrResumeModel::IS_HEAD_OFFICE_NO;
        $isRecommended    = $interviewServer->isRecommendedResume($hcData);
        if (!$isRecommended) {
            throw new BusinessException($this->getTranslation()->_('11032', ['hc_id' => $resume['hc_id'] ?? null]));
        }
        //增加校验推荐表中是否有待推荐状态的数据
        $is_exist = HrResumeFilterModel::findFirst([
            'conditions' => "resume_id = :resume_id: and filter_state= :filter_state: and is_deleted = 0",
            'bind'       => [
                'resume_id'    => $resume_id,
                'filter_state' => enums::$resume_filter_state['waiting_feedback'],
            ],
            'order'      => 'id desc',
        ]);

        if ($is_exist || $resume['filter_state'] == enums::$resume_filter_state['waiting_feedback']) {
            throw new BusinessException("简历已推荐，无需重复推荐:简历id:{$resume_id},当前状态:{$resume['filter_state']}");
        }

        //数据变更开始
        $db = $this->getDI()->get('db');
        $db->begin();

        $filter_state = enums::$resume_filter_state['waiting_feedback']; //筛选待反馈
        try {
            //更新简历表
            //推荐简历 修改简历表筛选状态为"筛选待反馈"、沟通状态改为"已沟通"(沟通状态已沟通后hc 无法再变更，原来是预约面试节点后职位已沟通）、淘汰状态置为未淘汰
            $resume_up_data = ['filter_state'=>$filter_state,'state_code'=>enums::$resume_state_code['communicated'],'is_out'=>HrResumeModel::IS_OUT_NO,'hc_id'=>$resume['hc_id']];

            if (isCountry(['TH', 'PH', 'MY']) && !empty($this->userInfo['id'])) {
                $resume_up_data['resume_last_operation_time'] = gmdate('Y-m-d H:i:s');
                $resume_up_data['resume_last_operator']       = $this->userInfo['id'];
            }

            $db ->updateAsDict('hr_resume', $resume_up_data, ['conditions' => " id = $resume_id "]);

            //更新面试表
            $interviewInfo = $interviewServer->getInterviewInfoByResumeid($resume_id, $resume['hc_id']);
            if ($interviewInfo) {
                $db->updateAsDict('hr_interview', ['state' => $filter_state],
                    ['conditions' => " resume_id = $resume_id "]);
            }
            //插入简历推荐表一条推荐待反馈数据
            $db->insertAsDict(
                'hr_resume_filter', [
                    'resume_id'            => $resume_id,
                    'recommender_staff_id' => $params['recommender_staff_id'] ?? 0, //推荐人（ta)
                    'filter_staff_id'      => $params['filter_staff_id'] ?? 0,// 简历筛选人
                    'filter_state'         => $filter_state, //筛选状态，更新为待反馈
                    'hc_id'                => $resume['hc_id'] ?? 0, //简历关联的hc
                    'recommend_reason'     => $params['recommend_reason'] ?? '', //推荐原因
                    'recommend_time'       => gmdate('Y-m-d H:i:s', time()), //推荐操作时间，计算反馈等待时长使用（存储0时区时间）
                ]
            );
            $resume_filter_id = $db->lastInsertId();//简历筛选表ID

            //更换hc自动赋值招聘负责人为当前用户
            $interview_state = $interviewInfo['state'] ?? 0;
            $this->updateResumeRecruiter($resume_id, $this->userInfo['id'], $this->userInfo['node_department_id'],
                $resume['filter_state'], $interview_state, 3);


            //添加日志

            $this->log->addLog([
                'module_id'   => $resume_id,
                'module_type' => enums::$log_module_type['resume'],//推荐
                'action'      => enums::$log_option['recommend'], //简历
            ]);
            $db->commit();
        } catch (\Exception $e) {
            $db->rollback();
            throw new \Exception($e->getMessage());
        }


        //todo 发送简历推荐通知消息
        if ($params['is_send_msg'] == 1 && $resume_filter_id) {
            $job_title  = (new OfferSignServer())->getJobTitleInfo($hc_info['job_title']);
            $user_lang  = (new StaffServer())->getLanguage($params['filter_staff_id']);
            $this->lang = $user_lang;
            $msg_title  = $this->getTranslation()->_('recommend_msg_title');

            //消息内容参数
            $trans_bind = [
                'recommender'    => $params['recommender_staff_name'] . '-' . $params['recommender_staff_id'],
                //推荐人信息（whr登录用户的信息）
                'candidate_name' => trim($resume['first_name'] . ' ' . $resume['last_name']),
                'job_title_name' => $job_title['job_name'] ?? '',
            ];
            if (isCountry('Ph')) {
                $trans_bind['candidate_name'] = $resume['name'];
            }
            $msg_content = $this->getTranslation()->_('recommend_msg_content', $trans_bind);

            //发送推荐消息
            $this->sendResumeFilterMsg($params['filter_staff_id'], $params['recommender_staff_id'], $msg_title,
                $msg_content, self::RECOMMEND_MSG_TYPE, $resume_filter_id);
        }
    }

    /**
     * 简历推荐 取消操作
     * @param $resume_id
     * @param $admin_staff_id
     * @throws BusinessException
     */
    public function recommendCancelResume($params)
    {
        $this->log = new LogServer();

        //获取简历基本信息
        $resume_id     = $params['resume_id'] ?? 0;
        $cancel_type   = $this->processingDefault($params, 'cancel_type', 2);
        $cancel_reason = $this->processingDefault($params, 'cancel_reason');

        $resume = $this->getResumeBaseInfo($resume_id);

        //只有待反馈、已通过 两种状态可以进行取消撤回操作
        $allowed_state = [enums::$resume_filter_state['waiting_feedback'], enums::$resume_filter_state['pass']];
        if (!in_array($resume['filter_state'], $allowed_state)) {
            throw new BusinessException("当前简历筛选状态不符合该操作要求（recommendCancelResume-取消推荐）:简历id:{$resume_id},当前状态:{$resume['filter_state']}");
        }

        $hc_info         = $this->getHcInfo($resume['hc_id']);
        $interviewServer = new InterviewServer();

        //todo 数据变更开始
        $db = $this->getDI()->get('db');
        $db->begin();

        $filter_state = enums::$resume_filter_state['cancel']; //取消筛选
        try {
            //更新简历表
            $db->updateAsDict('hr_resume', ['filter_state' => enums::$resume_filter_state['cancel']],
                ['conditions' => " id = $resume_id "]);

            //更新简历推荐表
            $hr_resume_filter = (new InterviewRepository())->getResumeFilterLastedData($resume_id);
            $resume_filter_id = $hr_resume_filter['id'] ?? 0;
            if (empty($resume_filter_id)) {
                throw new BusinessException("数据异常，当前简历({$resume_id})没有要取消推荐的数据");
            }

            $cancelInfo = [
                'filter_state'  => $filter_state,
                'cancel_type'   => $cancel_type,
                'cancel_reason' => $cancel_reason,
            ];

            $db->updateAsDict('hr_resume_filter',
                $cancelInfo,
                ['conditions' => " id = {$resume_filter_id} "]
            );

            $interviewInfo = $interviewServer->getInterviewInfoByResumeid($resume_id, $resume['hc_id']);
            if ($interviewInfo) {
                $db->updateAsDict('hr_interview', ['state' => $filter_state],
                    ['conditions' => " interview_id = '{$interviewInfo['interview_id']}' "]);
            }
            //添加日志
            $this->log->addLog([
                'module_id'   => $resume_id,
                'module_type' => enums::$log_module_type['resume'],//简历
                'action'      => enums::$log_option['cancel_recommend'], //撤回推荐
                'cancel_info' => $cancelInfo,
            ]);

            $db->commit();
        } catch (\Exception $e) {
            $db->rollback();
            throw new \Exception($e->getMessage());
        }


        //todo 发送取消简历推荐通知消息
        $job_title  = (new OfferSignServer())->getJobTitleInfo($hc_info['job_title']);
        $user_lang  = (new HrStaffContractRepository())->getByStaffLang($hr_resume_filter['filter_staff_id']);
        $this->lang = $user_lang;
        $msg_title  = $this->getTranslation()->_('recommend_cancel_msg_title');
        //消息内容参数
        $trans_bind = [
            'recommender'    => $params['recommender_staff_name'] . '-' . $params['recommender_staff_id'],
            //推荐人信息（whr登录用户的信息）
            'candidate_name' => trim($resume['first_name'] . ' ' . $resume['last_name']),
            'job_title_name' => $job_title['job_name'] ?? '',
        ];
        if (isCountry('Ph')) {
            $trans_bind['candidate_name'] = $resume['name'];
        }

        $msg_content = $this->getTranslation()->_('recommend_cancel_msg_content', $trans_bind);

        //发送撤回推荐消息
        $this->sendResumeFilterMsg($hr_resume_filter['filter_staff_id'], $params['recommender_staff_id'], $msg_title,
            $msg_content, self::RECOMMEND_CANCEL_MSG_TYPE, $resume_filter_id);
    }


    /**
     * 简历推荐 填写/修改反馈结果（52-通过/53-不通过）处理
     *
     * 1、首次填写反馈（根据简历当前状态是否是 51=待反馈）
     * 2、修改反馈结果（状态为52-通过，53-不通过）
     * 3、首次填写和修改反馈结果处理字段信息不一致，首次填写反馈记录反馈时间和固化简历信息，修改反馈结果只更新状态和原因俩字段
     *
     * @param $resume_id
     * @param $filter_state 要修改成的状态
     * @param $admin_staff_id
     * @param $feedback_source whr:winhr系统操作,by_rpc:by面试管操作
     * @throws BusinessException
     */
    public function resumeFilterFeedback(
        $resume_id,
        $filter_state,
        $feedback_reason,
        $admin_staff_id,
        $feedback_source = 'whr'
    ) {
        if (mb_strlen($feedback_reason) > 500) {
            throw new ValidationException($this->getTranslation()->_('feedback_reason_length_error'));
        }

        $this->log = new LogServer();

        //获取简历基本信息
        $resume_id = intval($resume_id);
        $resume    = $this->getResumeBaseInfo($resume_id);
        //todo 验证逻辑处理
        //只有待反馈、已通过、不通过三个操作可以进行反馈操作
        //by简历筛选人只能操作待反馈状态下的简历推荐
        if ($feedback_source == 'by_rpc') {
            $allowed_state = [
                enums::$resume_filter_state['waiting_feedback'],
            ];
        } else {
            $allowed_state = [
                enums::$resume_filter_state['waiting_feedback'],
                enums::$resume_filter_state['pass'],
                enums::$resume_filter_state['not_pass'],
            ];
        }

        if (!in_array($resume['filter_state'], $allowed_state)) {
            throw new BusinessException("当前简历筛选状态不符合该操作要求（recommendFeedback-反馈（通过/不通过））:简历id:{$resume_id},当前状态:{$resume['filter_state']}");
        }
        //
        $hc_info         = $this->getHcInfo($resume['hc_id']);
        $hc_info['is_head_office_recruit'] = $resume['is_head_office_recruit'] ?? HrResumeModel::IS_HEAD_OFFICE_NO;
        $interviewServer = new InterviewServer();
        $isRecommended   = $interviewServer->isRecommendedResume($hc_info);
        if (!$isRecommended) {
            throw new BusinessException($this->getTranslation()->_('resume_filter_recommended_disable'));
        }

        //获取固化的简历基本信息
        $resume_data = $this->buildResumeData($resume);


        //todo 数据变更开始
        $db = $this->getDI()->get('db');
        $db->begin();

        try {
            //筛选状态，更新为待反馈

            //更新简历表
            $resume_up_data = ['filter_state' => $filter_state, 'previous_hc_id' => $resume['hc_id']];

            if ($filter_state == enums::$resume_filter_state['pass']) {
                //修改筛选结果为已通过
                //简历最新操作人
                if (isCountry(['TH','PH','MY']) && !empty($this->userInfo['id'])) {
                    $resume_up_data['resume_last_operator']       = $this->userInfo['id'];
                    $resume_up_data['resume_last_operation_time'] = gmdate('Y-m-d H:i:s');
                }
                $interviewInfo   = (new InterviewServer())->getInterviewInfoByResumeid($resume_id, $resume['hc_id']);
                $interview_state = $interviewInfo['state'] ?? 0;
                //更换hc自动赋值招聘负责人为当前用户
                $this->updateResumeRecruiter($resume_id, $this->userInfo['id'], $this->userInfo['node_department_id'],
                    $resume['filter_state'], $interview_state, 2);
            }
            //更新面试表简历筛选状态
            $db->updateAsDict('hr_resume', $resume_up_data, ['conditions' => " id = $resume_id "]);

            //更新简历推荐表改简历最新一条数据
            $hr_resume_filter = (new InterviewRepository())->getResumeFilterLastedData($resume_id);
            $resume_filter_id = $hr_resume_filter['id'] ?? 0;
            if (empty($resume_filter_id)) {
                throw new BusinessException("数据异常，当前简历({$resume_id})没有要取消推荐的数据");
            }

            //只有首次填写反馈结果时固化简历信息和记录反馈时间，后续修改反馈结果，仅更新状态和原因
            $filter_update_data = [
                'filter_state'    => $filter_state,//筛选状态：52通过，53不通过
                'feedback_reason' => $feedback_reason, //通过、不通过 原因（筛选评价）
            ];
            //首次填写反馈结果（根据简历当前推荐状态是否是待反馈来判断是否是首次）增加如下字段信息更新
            if ($resume['filter_state'] == enums::$resume_filter_state['waiting_feedback']) {
                $filter_update_data['resume_data']   = json_encode($resume_data); //固化的简历基本字段信息
                $filter_update_data['feedback_time'] = gmdate('Y-m-d H:i:s'); //通过、不通过 原因（筛选评价）
            }
            $db->updateAsDict('hr_resume_filter', $filter_update_data, ['conditions' => " id = {$resume_filter_id} "]);

            //如果已经参加过面试 面试表的状态也要同步一下
            $interviewInfo = $interviewServer->getInterviewInfoByResumeid($resume_id, $resume['hc_id']);
            if ($interviewInfo) {
                $db->updateAsDict('hr_interview', ['state' => $filter_state],
                    ['conditions' => " interview_id = '{$interviewInfo['interview_id']}' "]);
            }
            //添加日志

            if ($feedback_source == 'by_rpc' || $resume['filter_state'] == enums::$resume_filter_state['waiting_feedback']) {
                $action = enums::$log_option['feedback'];
            } else {
                $action = enums::$log_option['modify'];
            }

            if ($filter_state == enums::$resume_filter_state['pass']) {
                $module_status = enums::$log_status['filter_pass'];
            } else {
                $module_status = enums::$log_status['filter_not_pass'];
            }

            $this->log->addLog([
                'module_id'     => $resume_id,
                'staff_id'      => $admin_staff_id,
                'action'        => $action, //反馈或修改筛选结果
                'module_type'   => enums::$log_module_type['resume'],//简历
                'module_status' => $module_status, //筛选结果为(通过/不通过）
            ]);

            $db->commit();
        } catch (\Exception $e) {
            $db->rollback();
            throw new \Exception($e->getMessage());
        }
    }

    /**
     * 固化简历推荐-反馈操作（通过/不通过）时的简历基本信息
     * @param $resume_data
     * @param $hc_data
     */
    private function buildResumeData($resume_data)
    {
        //获取工作城市
        $work_city_code = $resume_data['address_id'];//目前工作城市存储的是省code码
        $province_obj   = SysProvinceModel::findFirst([
            'columns'    => 'code,name,en_name',
            'conditions' => 'code = :code:',
            'bind'       => ['code' => $work_city_code],
        ]);
        $province_data  = $province_obj ? $province_obj->toArray() : [];

        //获取个人简历附件信息
        $annexObj     = HrAnnexModel::findFirst([
            'conditions' => "oss_bucket_key = :resume_id: and file_type=:file_type: and deleted = 0",
            'bind'       => ['resume_id' => $resume_data['id'], 'file_type' => enums::$resume_file_type['resume_cv']],
        ]);
        $resume_annex = $annexObj ? $annexObj->toArray() : [];

        $img_perfix = env('img_prefix');//资源路径前缀

        $resume_data = [
            'resume_id'       => $resume_data['id'],
            //简历ID
            'candidate_name'  => trim($resume_data['first_name'] . ' ' . $resume_data['last_name']),
            //候选人姓名（应聘者）
            'candidate_sex'   => $this->getResumeSex($resume_data['sex'], $resume_data['call_name']),
            //候选人性别 1-男，2-女
            'candidate_age'   => empty($resume_data['date_birth']) ? '' : DateHelper::howOld($resume_data['date_birth']),
            //候选人年龄 33
            'work_city_name'  => $province_data['name'] ?? '',
            //工作城市
            'current_salary'  => $resume_data['current_salary'] ? ($resume_data['current_salary'] / 100) : '',
            //当前薪资
            'expected_salary' => $resume_data['entry_salary'] ? ($resume_data['entry_salary'] / 100) : '',
            //期望薪资
            'resume_annex'    => [
                'file_name' => $resume_annex['original_name'] ?? '',
                //原始文件名称
                'file_type' => $resume_annex['file_type'] ?? '',
                //文件类型
                'file_path' => isset($resume_annex['object_key']) && $resume_annex['object_key'] ? $img_perfix . $resume_annex['object_key'] : '',
                //完整可访问路径
            ],

        ];

        return $resume_data;
    }

    /**
     * 发送消息
     * @param $staff_id
     * @param $admin_user_id
     * @param $msg_title
     * @param $msg_content
     * @param $msg_type
     * @param $related_id 业务ID，消息绑定的业务表主键
     * @return bool
     */
    public function sendResumeFilterMsg(
        $target_staff_id,
        $admin_user_id,
        $msg_title,
        $msg_content,
        $msg_type,
        $related_id
    ) {
        //todo 生成签字消息
        $message_param = [
            'staff_info_ids_str' => $target_staff_id,
            'staff_users'        => [['id' => $target_staff_id]],
            'message_title'      => $msg_title,
            'message_content'    => $msg_content,
            'add_userid'         => $admin_user_id,
            'related_id'         => $related_id,
            'category'           => $msg_type, //56：简历筛选推荐-推荐通知消息，57：简历筛选推荐-取消推荐通知消息
        ];
        $apiClient     = new ApiClient('bi_rpc', '', 'add_kit_message', $this->lang);
        $apiClient->setParams($message_param);
        $result = $apiClient->execute();
        $this->getDI()->get('logger')->write_log("sendResumeFilterMsg消息，request:" . json_encode($message_param) . ", response：" . json_encode($result) . ",url:" . env('svc_call'),
            'info');
        if (isset($result['result']['code']) && $result['result']['code'] == 1) {
            // 发送成功
            $msg_id = $result['result']['data'][0];
            return $msg_id;
        } else {
            // 发送失败
            $this->getDI()->get('logger')->write_log("sendResumeFilterMsg失败，消息返回:" . json_encode($result) . ",消息参数：" . json_encode($message_param) . ",url:" . env('svc_call'),
                'info');

            return 0;
        }
    }


    /**
     * 获取hc信息
     * @param $hc_id
     * @return array
     */
    public function getHcInfo($hc_id)
    {
        //获取薪资审批中的当前薪资信息
        $hc_info = HrhcModel::FindFirst([
            'conditions' => 'hc_id=:hc_id:',
            'bind'       => ['hc_id' => $hc_id],
        ]);
        if ($hc_info) {
            $hc_info = $hc_info->toArray();
        } else {
            $hc_info = [];
        }
        return $hc_info;
    }

    /**
     * 打包压缩简历附件并发送至当前用户企业邮箱
     * @param $annex_list 多条简历附件数据，每个简历多条附件
     * @param $to_email
     */
    public function zipResumeAnnex2Email($annex_list, $to_email)
    {
        //压缩包文件名称
        $tmpFile = sys_get_temp_dir() . '/resume_annex-' . time() . mt_rand(1000, 9999) . ".zip";
        //todo 压缩附件内容值压缩包
        $zip = new ZipArchive();
        $zip->open($tmpFile, ZipArchive::CREATE);
        foreach ($annex_list as $resume_annex) {
            $resume_id = $resume_annex['user_id'];
            foreach ($resume_annex['data'] as $annex) {
                // download file
                $fileContent = file_get_contents($annex['file_url']);

                $zip->addFromString("cvid-" . $resume_id . "-annex/" . $annex['file_name'], $fileContent);
            }
        }
        $zip->close();
        $oss_result  = $this->uploadFileOss($tmpFile);
        $oss_zip_url = $oss_result['object_url'] ?? '';//oss地址
        if (empty($oss_zip_url)) {
            echo "zipResumeAnnex2Email邮件附件zip上传oss失败！";
            $this->getDI()->get('logger')->write_log("zipResumeAnnex2Email邮件附件导出上传oss失败！", 'error');
            die;
        }

        //删除压缩包临时文件
        unlink($tmpFile);

        //todo 发送邮件
        $title     = 'Flash Express: resumes annex';
        $content   = "please click the link to download," . $oss_zip_url;
        $sendEmail = (new MailServer())->send_mail('default', $to_email, $title, $content);
        if (!$sendEmail) {
            echo "发送邮箱失败:{$to_email}，请检查邮箱是否有效！";
            $this->getDI()->get('logger')->write_log("zipResumeAnnex2Email 发送邮箱失败:{$to_email}，请检查邮箱是否有效！", 'info');
        }
    }

    /**
     * 更新简历招聘负责人
     * @param $resume_id
     * @param $login_staff_id 当前登录用户ID
     */
    public function updateResumeRecruiter(
        $resume_id,
        $login_staff_id,
        $login_staff_dept,
        $resume_filter_state,
        $interview_state,
        $source = 0
    ) {
        $can_update  = false;
        $source_name = [
            0 => '--',
            1 => '关联hc',
            2 => '修改筛选结果为通过',
            3 => '推荐简历',
            4 => '预约面试或修改面试反馈-进入下一轮',
            5 => '修改面试反馈-终试通过操作',
        ];
        //获取配置的简历招聘负责人取值返回的所有部门ID
        $resume_recruiter_depts = (new SysListServer())->getResumeRecruiterDeptsData();
        if (!in_array($login_staff_dept, $resume_recruiter_depts)) {
            $this->getDI()->get('logger')->write_log("updateResumeRecruiter_{$resume_id},是否变更:否，用户所属部门（{$login_staff_dept}）非招聘负责人部门：" . var_export($resume_recruiter_depts,
                    true), 'info');

            return;
        }

        //如果简历筛选状态是终止状态 则可以更新负责人
        if ($resume_filter_state && in_array($resume_filter_state,
                [
                    enums::$resume_filter_state['not_pass'],
                    enums::$resume_filter_state['cancel'],
                ])
        ) {
            $can_update = true;
        }

        //面试状态是终止状态
        if ($interview_state && in_array($interview_state,
                [
                    enums::$interview_state['rejected'],
                    enums::$interview_state['canceled'],
                ]
            )
        ) {
            $can_update = true;
        }

        $log_data = [
            'resume_id'           => $resume_id,
            'recruiter_id'        => $login_staff_id,
            'resume_filter_state' => $resume_filter_state,
            'interview_state'     => $interview_state,
            'source'              => $source_name[$source],
        ];


        if ($can_update) {
            $this->getDI()->get('db')->updateAsDict("hr_resume",
                ['recruiter_id' => $login_staff_id],
                ["conditions" => "id='{$resume_id}'"]
            );
        }

        $this->getDI()->get('logger')->write_log("updateResumeRecruiter_{$resume_id},是否变更:" . ($can_update ? '是' : '否') . ",参数信息:" . var_export($log_data,
                true), 'info');
    }

    /**
     * 检查简历是否可以更新
     */
    public function checkResumeModify($resumeInfo)
    {
        $interRep = new InterviewRepository();

        if (in_array($resumeInfo['state'], [$interRep::INTERVIEW_LSSUED_OFFER, $interRep::INTERVIEW_EMPLOYEED])) {
            return self::NOT_MODIFY;
        }

        if (isCountry('TH')) {
            //验证是不是
            $offerSignData = HrInterviewOfferSignApproveModel::findFirst([
                'columns'    => 'resume_id,approve_state',
                'conditions' => 'resume_id=:resume_id:',
                'bind'       => ['resume_id' => $resumeInfo['id']],
                'order'      => 'id desc',
            ]);

            if ($offerSignData && in_array($offerSignData->approve_state, [1, 2])) {
                return self::NOT_MODIFY;
            }
        }

        return self::SUCC_MODIFY;
    }


    /**
     * @param string $credentialsNum 证件号码
     * @param string $phoneNum 手机号
     * @param int $credentialsCategory 证件类型
     * @param bool $switchValidation 默认 都走开关，到岗确认，不需要走。
     * @throws ValidationException
     */
    public function checkCredentialsNumANDPhoneNum($credentialsNum = '', $phoneNum = '', $from = 'whr', $resumeId = 0, $switchValidation = true)
    {
        if (empty($phoneNum)) {
            throw new ValidationException($this->getTranslation()->_('4117')); // 手机号录入有误！
        }

        if (env('country_code') == 'PH') {
            //菲律宾需求 h5端补充零逻辑修复，替别人修复问题
            $phoneNum = str_pad($phoneNum, 11, "0", STR_PAD_LEFT);
        }

        // 简历表查询
        if (!empty($credentialsNum)) {
            $resumeInfo = HrResumeModel::find(
                [
                    'conditions' => "(phone = :phone: OR credentials_num = :credentials_num:) AND id != :resumeId: AND deleted = :deleted: limit 1",
                    'bind'       => [
                        'phone'           => $phoneNum,
                        'credentials_num' => $credentialsNum,
                        'resumeId'        => (int)$resumeId,
                        'deleted'         => 0,
                    ],
                    'columns'    => ['id', 'phone', 'credentials_num'],
                ]
            )->toArray();
        } else {
            $resumeInfo = HrResumeModel::find(
                [
                    'conditions' => "phone = :phone: AND id != :resumeId: AND deleted = :deleted: limit 1",
                    'bind'       => [
                        'phone'    => $phoneNum,
                        'resumeId' => (int)$resumeId,
                        'deleted'  => 0,
                    ],
                    'columns'    => ['id', 'phone', 'credentials_num'],
                ]
            )->toArray();
        }

        $resumeInfoPhone          = array_column($resumeInfo, null, 'phone');
        $resumeInfoCredentialsNum = array_column($resumeInfo, null, 'credentials_num');

        // 简历库中存在，则需要查询黑名单
        if (!empty($credentialsNum)) {
            if (isCountry("TH")){
                $backList = [];
                    $listCheck = (new BlackgreylistServer())->check_black_grey_list(['identity' => $credentialsNum,'resume_id'=>$resumeId]);
                    if ($listCheck['is_black_list']) {
                        $backList         = HrBlackGreyListModel::find(
                            [
                                'conditions' => "identity = :identity: AND status = :status: limit 1",
                                'bind'       => [
                                    'identity' => $credentialsNum,
                                    'status'   => HrBlackGreyListModel::STATUS_ON,
                                ],
                                'columns'    => ['id', 'mobile', 'identity'],
                            ]
                        )->toArray();
                  }
            }else{
                $backList         = HrBlacklistModel::find(
                    [
                        'conditions' => "identity = :identity: AND status = :status: limit 1",
                        'bind'       => [
                            'identity' => $credentialsNum,
                            'status'   => 1,
                        ],
                        'columns'    => ['id', 'mobile', 'identity'],
                    ]
                )->toArray();
            }
            $backListMobile   = array_column($backList, null, 'mobile');
            $backListIdentity = array_column($backList, null, 'identity');
        } else {
            $backListMobile   = [];
            $backListIdentity = [];
        }
        // h5 只要在简历库存在，不管在不在黑名单提示都一样
        if ('h5' == $from && !empty($resumeInfoPhone[$phoneNum])) {
            // 手机号XXXXXXX的简历已经在系统中，请联系HR
            throw new ValidationException(sprintf($this->getTranslation()->_('h5_phone_number_is_duplicated'),
                $phoneNum));
        }

        if (!empty($credentialsNum) && 'h5' == $from && !empty($resumeInfoCredentialsNum[$credentialsNum])) {
            // 证件号XXXXXXX的简历已经在系统中，请联系HR
            throw new ValidationException(sprintf($this->getTranslation()->_('h5_id_number_is_duplicated'),
                $credentialsNum));
        }

        // 手机号在黑名单 且在简历库
//        if (!empty($credentialsNum) && 'whr' == $from && !empty($resumeInfoPhone[$phoneNum]) && !empty($backListMobile[$phoneNum])) {
//            // 手机号XXXXXXX的简历已被加入黑名单
//            throw new ValidationException(sprintf($this->getTranslation()->_('phone_number_is_duplicated_black'),$phoneNum));
//        }

        // 证件号在黑名单 whr提示
        if ('whr' == $from && !empty($credentialsNum) && !empty($backListIdentity[$credentialsNum])) {
            // 身份证号XXXXXXX的简历已被加入黑名单
            throw new ValidationException(sprintf($this->getTranslation()->_('id_number_is_duplicated_black'),
                $credentialsNum));
        }

        // 证件号在黑名单 h5提示
        if ('h5' == $from && !empty($credentialsNum) && !empty($backListIdentity[$credentialsNum])) {
            // 证件号XXXXXXX的简历已经在系统中，请联系HR
            throw new ValidationException(sprintf($this->getTranslation()->_('h5_id_number_is_duplicated'),
                $credentialsNum));
        }

        // 手机号在简历库中
        if ('whr' == $from && !empty($resumeInfoPhone[$phoneNum])) {
            // 证件号/手机号XXXXXXXXX在人才库中已存在
            throw new ValidationException(sprintf($this->getTranslation()->_('phone_number_is_duplicated_resume'),
                $phoneNum));
        }

        // 证件号在简历库中
        if (!empty($credentialsNum) && 'whr' == $from && !empty($resumeInfoCredentialsNum[$credentialsNum])) {
            // 证件号/手机号XXXXXXXXX在人才库中已存在
            throw new ValidationException(sprintf($this->getTranslation()->_('id_number_is_duplicated_resume'),
                $credentialsNum));
        }
        // 查询入职表
        $entryInfo = HrEntryModel::find(
            [
                'conditions' => " resume_id = :resumeId: AND status = :status: limit 1",
                'bind'       => [
                    'resumeId' => (int)$resumeId,
                    'status'   => 1,
                ],
                'columns'    => ['staff_id'],
            ]
        )->toArray();

        //19097【PH|WHR&BY】员工状态校验规则变更
        //创建简历时校验开关-N代表关，Y代表开
        $create_resume_verify = SettingEnvServer::getSetVal('create_resume_verify');

        //如果所有国家都走开关，则把 国家判断去掉
        $conditionsSwitch = '';
        if($switchValidation && !empty($create_resume_verify) && strtoupper($create_resume_verify) === 'N' && isCountry('PH')) {
            $conditionsSwitch = 'formal in ({formals:array}) and ';
            $bind['formals'] = [HrStaffInfoModel::FORMAL_1, HrStaffInfoModel::FORMAL_INTERN];
        }


        if (!empty($credentialsNum)) {
            if ($entryInfo) {
                //员工信息表查询 - 根据用户传入的身份证号、证件号还有排除已入职的工号查询数据

                $conditions = $conditionsSwitch . "(identity = :identity: OR mobile = :mobile:) and staff_info_id!=:staff_info_id: limit 1";

                $bind['identity']      = $credentialsNum;
                $bind['staff_info_id'] = $entryInfo[0]["staff_id"];
                $bind['mobile']        = $phoneNum;

                $hrStaffInfo = HrStaffInfoModel::find([
                    'conditions' => $conditions,
                    'bind'       => $bind,
                    'columns'    => [
                        'id',
                        'staff_info_id',
                        'state',
                        'identity',
                        'mobile',
                        'wait_leave_state',
                        'formal',
                    ],
                ])->toArray();
            } else {
                //员工信息表查询 - 根据用户传入的身份证号、证件号查询数据
                $conditions = $conditionsSwitch . "(identity = :identity: OR mobile = :mobile:) limit 1";

                $bind['identity']      = $credentialsNum;
                $bind['mobile']        = $phoneNum;

                $hrStaffInfo = HrStaffInfoModel::find([
                    'conditions' => $conditions,
                    'bind'       => $bind,
                    'columns'    => ['id', 'state', 'identity', 'mobile', 'wait_leave_state', 'formal'],
                ])->toArray();
            }
        } else {
            if ($entryInfo) {
                //员工信息表查询 - 根据用户传入的身份证号、证件号还有排除已入职的工号查询数据

                $conditions = $conditionsSwitch . "mobile = :mobile: and staff_info_id!=:staff_info_id: limit 1";

                $bind['staff_info_id']      = $entryInfo[0]["staff_id"];
                $bind['mobile']             = $phoneNum;

                $hrStaffInfo = HrStaffInfoModel::find([
                    'conditions' => $conditions,
                    'bind'       => $bind,
                    'columns'    => [
                        'id',
                        'staff_info_id',
                        'state',
                        'identity',
                        'mobile',
                        'wait_leave_state',
                        'formal',
                    ],
                ])->toArray();
            } else {
                //员工信息表查询 - 根据用户传入的身份证号、证件号查询数据

                $conditions = $conditionsSwitch . "mobile = :mobile: limit 1";

                $bind['mobile']             = $phoneNum;

                $hrStaffInfo = HrStaffInfoModel::find([
                    'conditions' => $conditions,
                    'bind'       => $bind,
                    'columns'    => ['id', 'state', 'identity', 'mobile', 'wait_leave_state', 'formal'],
                ])->toArray();
            }
        }

        $hrStaffInfoIdentity = array_column($hrStaffInfo, null, 'identity');
        $hrStaffInfoPhone    = array_column($hrStaffInfo, null, 'mobile');

        // 证件号重复且是在职、停职、待离职状态
        if (!empty($credentialsNum) && 'whr' == $from && !empty($hrStaffInfoIdentity[$credentialsNum]) &&
            (($hrStaffInfoIdentity[$credentialsNum]['state'] == 1 && $hrStaffInfoIdentity[$credentialsNum]['wait_leave_state'] == 0) || // 在职
                ($hrStaffInfoIdentity[$credentialsNum]['state'] == 1 && $hrStaffInfoIdentity[$credentialsNum]['wait_leave_state'] == 1) || // 待离职
                ($hrStaffInfoIdentity[$credentialsNum]['state'] == 3)   // 停职
            )
        ) {
            // 证件号XXXXXXX已被在职员工使用
            $formal = $this->getTranslation()->_(enums::STAFF_INFO_FORMALS[$hrStaffInfoIdentity[$credentialsNum]['formal']]);
            throw new ValidationException(sprintf($this->getTranslation()->_('id_number_is_duplicated'),
                $credentialsNum, $formal));
        }

        // 手机号重复且是在职、停职、待离职状态
        if ('whr' == $from && !empty($hrStaffInfoPhone[$phoneNum]) &&
            (($hrStaffInfoPhone[$phoneNum]['state'] == 1 && $hrStaffInfoPhone[$phoneNum]['wait_leave_state'] == 0) || // 在职
                ($hrStaffInfoPhone[$phoneNum]['state'] == 1 && $hrStaffInfoPhone[$phoneNum]['wait_leave_state'] == 1) || // 待离职
                ($hrStaffInfoPhone[$phoneNum]['state'] == 3)   // 停职
            )
        ) {
            // 手机号XXXXXXX已被在职员工使用
            $formal = $this->getTranslation()->_(enums::STAFF_INFO_FORMALS[$hrStaffInfoPhone[$phoneNum]['formal']]);
            throw new ValidationException(sprintf($this->getTranslation()->_('phone_number_is_duplicated'), $phoneNum,
                $formal));
        }

        if (!empty($credentialsNum) && 'h5' == $from && !empty($hrStaffInfoIdentity[$credentialsNum]) && ($hrStaffInfoIdentity[$credentialsNum]['state'] == 1 || $hrStaffInfoIdentity[$credentialsNum]['state'] == 3)) {
            // 您的简历已经在系统中，请联系HR
            //throw new ValidationException($this->getTranslation()->_('resume_exists'));
            // 证件号XXXXXXX的简历已经在系统中，请联系HR
            throw new ValidationException(sprintf($this->getTranslation()->_('h5_id_number_is_duplicated'),
                $credentialsNum));
        }

        if ('h5' == $from && !empty($hrStaffInfoPhone[$phoneNum]) && ($hrStaffInfoPhone[$phoneNum]['state'] == 1 || $hrStaffInfoPhone[$phoneNum]['state'] == 3)) {
            //throw new ValidationException($this->getTranslation()->_('resume_exists'));
            // 手机号XXXXXXX的简历已经在系统中，请联系HR
            throw new ValidationException(sprintf($this->getTranslation()->_('h5_phone_number_is_duplicated'),
                $phoneNum));
        }

        return true;
    }

    /**
     * 验证简历是否为个人代理的简历
     * winhr验证 hc和签约类型
     * h5 验证签约类型
     * @param $parmas
     * @return int
     */
    public function isResumeHireTypeAgentMY($parmas = [])
    {
        if (!isCountry('MY')) {
            return 0;
        }
        $source_type = $parmas['source_type'] ?? 'winhr';
        $hire_type = $parmas['hire_type'] ?? '';
        $reserve_type = $parmas['reserve_type'] ?? '';

        if ($source_type == 'winhr' && !empty($hire_type) && $hire_type == HrStaffInfoModel::HIRE_TYPE_AGENT){
            return 1;
        }elseif ($source_type == 'winhr' && !empty($hire_type) && $hire_type != HrStaffInfoModel::HIRE_TYPE_AGENT){
            return 0;
        }

        if (in_array($source_type,['winhr','h5']) && $reserve_type == HrResumeModel::RESERVE_TYPE_AGENT) {
            return 1;
        }

        return 0;
    }

    /**
     * 格式化简历信息
     */
    public function formatResumeInfo($info)
    {
        $info['recruit_type_name'] = self::$t->_(HrResumeModel::$recruitTypeList[$info['recruit_type']] ?? '');

        //学校信息
        $info['current_school_id']   = !empty($info['current_school_id']) ? $info['current_school_id'] : '';
        $info['current_school_name'] = '';
        if (!empty($info['current_school_id'])) {
            $schoolInfo                  = HrSchoolModel::findFirst([
                'conditions' => 'id = :id:',
                'columns'    => 'id,name',
                'bind'       => [
                    'id' => $info['current_school_id'],
                ],
            ]);
            $info['current_school_name'] = $schoolInfo->name ?? '';
        }

        //年级
        $info['current_grade_id']   = !empty($info['current_grade_id']) ? $info['current_grade_id'] : '';
        $info['current_grade_name'] = self::$t->_(enums::$gradeList[$info['current_grade_id']] ?? '');

        //感兴趣的部门
        $info['interested_department_id'] = !empty($info['interested_department_id']) ? $info['interested_department_id'] : '';

        $info['interested_department_name'] = '';
        if (!empty($info['interested_department_id'])) {
            if ($info['interested_department_id'] == HrInterestedDepartmentModel::DEPARTMENT_OTHER) {
                $info['interested_department_name'] = '其他';
            } else {
                $departInfo                         = HrInterestedDepartmentModel::findFirst([
                    'conditions' => 'id = :id:',
                    'columns'    => 'id,name',
                    'bind'       => [
                        'id' => $info['interested_department_id'],
                    ],
                ]);
                $info['interested_department_name'] = $departInfo->name ?? '';
            }
        }


        $info['summary_over_all']    = $info['summary_over_all'] ?? '';
        $info['summary_over_view']   = $info['summary_over_view'] ?? '';
        $info['summary_over_detail'] = $info['summary_over_detail'] ?? '';

        return $info;
    }

    /**
     * 保存待发送简历数据
     * @param $data
     * @return mixed
     */
    public function pushResumeDataToRedisList($resumeId)
    {
        $this->getDI()->get('logger')->write_log('pushResumeDataToRedisList  ' . $resumeId, 'info');
        return $this->getDI()->get('redisLib')->lpush(RedisListEnums::AFTER_RESUME_PERFECT, $resumeId);
    }

    /**
     * 获取待发送站简历的数据
     * @throws ValidationException
     */
    public function popResumeDataToRedisList()
    {
        $redis  = self::getDI()->get('redisLib');
        $params = $redis->rpop(RedisListEnums::AFTER_RESUME_PERFECT);

        $this->getDI()->get('logger')->write_log(['popResumeDataToRedisList' => $params], 'info');

        if (empty($params)) {
            return false;
        }

        return $params;
    }

    /**
     * 根据答案获取历史问卷列表
     */
    public function getOldQuestionList($survey)
    {
        if (empty($survey)) {
            return [];
        }

        $surveyKv = $oldQuestionList = [];

        foreach ($survey as $v) {
            $surveyKv[$v['type']][$v['answer_key']] = $v;
        }

        //查询所有的
        $questionList = HrQuestionsModel::find()->toArray();

        foreach ($questionList as $key => $item) {
            //排除问卷
            if (!isset($surveyKv[$item['type']][$item['no']])) {
                continue;
            }

            $item['question'] = self::$t->_($item['question_no']);
            //问题列表
            $options = json_decode($item['options'], true) ?? [];
            foreach ($options as $_key => $_val) {
                $options[$_key]['option'] = self::$t->_($_val['option']);
            }

            $item['options'] = $options;

            //附属选项
            $item['relation'] = json_decode($item['relation'], true) ?? [];

            //是否显示
            $item['question_text'] = json_decode($item['question_text'], true) ?? [];

            //添加暗文
            $item['question_tip'] = self::$t->exists('question_tip_' . $item['question_no']) ? self::$t->_('question_tip_' . $item['question_no']) : '';

            $oldQuestionList[] = $item;
        }

        return $oldQuestionList;
    }

    /**
     * 获取是否一线职位标识符号
     */
    public function getFormTypeKey($jdId)
    {
        $info = $this->getFormType($jdId);
        return $info['data']['form_type'] ?? HrQuestionsModel::$type_extension[HrQuestionsModel::type_extension_default];
    }

    /**
     * 表单显示逻辑
     *
     * @param $jd_id 简历期望岗位
     * @return  array
     */
    public function getFormType($jd_id)
    {
        $_return = ["form_type" => HrQuestionsModel::$type_extension[HrQuestionsModel::type_extension_default]];

        if (empty($jd_id)) {
            return  ["data"=>$_return];
        }
        //循环调用减少查库
        if(empty(self::$networkFirstLineJD)){
            $first_line_jd_ids     = (new SettingEnvServer())->getSetValFromCache('winhr_first_line_jd_ids') ?? '';
            self::$networkFirstLineJD = explode(',', $first_line_jd_ids);
        }
        //循环调用减少查库
        if (isCountry('MY') && empty(self::$lntJD)) {
            $lnt_jds     = (new SettingEnvServer())->getSetValFromCache('lnt_recruitment_jd_ids') ?? '';
            self::$lntJD = explode(',', $lnt_jds);
        }

        if (in_array($jd_id, self::$networkFirstLineJD) || in_array($jd_id, self::$lntJD)) {
            $_return["form_type"] = HrQuestionsModel::$type_extension[HrQuestionsModel::type_extension_network_first_line_jd];
        }
        return ["data"=>$_return];
    }

    /**
     * 是否为network 一线岗位
     * @param $jd_id
     * @param $resume_id
     * @return bool
     */
    public function checkIsNetworkFirstLineJD($jd_id,$resume_id = 0): bool
    {
        //这里可能要加上线时间的判断 上线时间之前的简历 也要返回false

        if($resume_id < env('16706_take_effect_resume_id',0)){
            return false;
        }

        if (!(isCountry('TH') || isCountry('PH') || isCountry('MY'))) {
            return false;
        }

        $_return = $this->getFormType($jd_id);
        return $_return['data']["form_type"] == HrQuestionsModel::$type_extension[HrQuestionsModel::type_extension_network_first_line_jd];
    }


    /**
     * 设置简历最新操作人
     */
    public function setLastOperator($resume_id, $staff_id = 0)
    {
        if (!isCountry(['TH', 'PH', 'MY'])) {
            return true;
        }
        if (empty($resume_id)) {
            return false;
        }
        $staff_id = $staff_id ? :$this->userInfo['id'];

        if(empty($staff_id)){
            return false;
        }
        return $this->getDI()->get('db')->updateAsDict(
            'hr_resume',
            [
                'resume_last_operator'       => $staff_id,
                'resume_last_operation_time' => gmdate('Y-m-d H:i:s'),
            ],
            [
                "conditions" => 'id = ?',
                'bind'       => [$resume_id],
            ]
        );
    }

    /**
     * 查询简历总结
     * @param $params
     * @return string[]
     * @throws ValidationException
     * <AUTHOR>
     * @time 2024/1/17
     */
    public function summaryInfo($params): array
    {
        $resumeId = $params['resume_id'] ?? 0;

        if (!$resumeId) {
            throw new ValidationException(self::$t->_('miss_args'));
        }

        $returnData = [
            'resume_id' => (int) $resumeId,
        ];

        $resumeInfo = (new ResumeExtendRepository())->getInfoByResumeId($resumeId);

        $returnData['summary_over_all']    = $resumeInfo['summary_over_all'] ?? '';
        $returnData['summary_over_view']   = $resumeInfo['summary_over_view'] ?? '';
        $returnData['summary_over_detail'] = $resumeInfo['summary_over_detail'] ?? '';

        return $returnData;
    }

    /**
     * 更新简历总结
     * @param - array $params 参数
     * @return true
     * @throws Exception
     * <AUTHOR>
     * @time 2024/1/17
     */
    public function updateSummary($params): bool
    {
        $resumeId = $params['resume_id'];

        $resumeInfo = (new ResumeServer())->getResumeBaseInfo($resumeId);

        if (empty($resumeInfo)) {
            throw new ValidationException(self::$t->_('operation_data_not_exist'));
        }

        // 更新简历总结信息
        //更新简历扩展表
        $resBaseExt = (new ResumeExtendRepository())->saveByResumeId($resumeId, [
            'summary_over_all'    => $params['summary_over_all'],
            'summary_over_view'   => $params['summary_over_view'],
            'summary_over_detail' => $params['summary_over_detail'],
        ]);

        if (!$resBaseExt) {
            throw new Exception('Resume Base Ext Info Save Error ');
        }

        return true;
    }

    /**
     * 简历筛选里面点击hc关联请求的接口
     * 删除简历筛选信息 + 关联新的hc
     * @param $resume_id
     * @param $hc_id
     * @return array
     */
    public function delete_resume_filter_and_related_hc($resume_id, $hc_id){
        $db           = $this->getDI()->get('db');
        $db->begin();
        try {
            // 检查数据
            $hc = HrhcModel::findFirst([
                'conditions' => 'hc_id = :hc_id: and state_code = :state_code:',
                'bind'       => ['hc_id' => $hc_id, 'state_code' => HrhcModel::STATE_RECRUITING],
            ]);
            if (!$hc) {
                throw new Exception($this->getTranslation()->_('interview_cancel_and_related_hc_1'));
            }

            $interview       = HrInterviewModel::findFirst([
                'conditions' => 'resume_id = :resume_id: and hc_id = :hc_id:',
                'bind'       => ['resume_id' => $resume_id, 'hc_id' => $hc_id],
            ]);
            if ($interview) {
                throw new Exception($this->getTranslation()->_('4008'));
            }

            $resume = HrResumeModel::findFirst([
                'conditions' => 'id = :id:',
                'bind'       => ['id' => $resume_id],
            ]);
            if (!$resume) {
                throw new Exception($this->getTranslation()->_('4008'));
            }
            $resume_fileter = HrResumeFilterModel::findFirst([
                'conditions' => 'resume_id = :resume_id:',
                'bind'       => ['resume_id' => $resume_id],
            ]);
            if (!$resume_fileter) {
                throw new Exception($this->getTranslation()->_('4008'));
            }

            // 软删简历筛选信息
            $db->updateAsDict('hr_resume_filter', ['is_deleted' => 1], [
                'conditions' => 'resume_id = ?',
                'bind'       => [$resume_id],
            ]);

            $resume_data = $resume->toArray();
            // 更新简历数据 重新关联hc
            $is_can_change_filter_result = $resume_data['previous_hc_id'] > 0 && $resume_data['filter_state'] == enums::$resume_filter_state['not_pass'] && $hc_id != $resume_data['previous_hc_id'];
            $resume_up_data              = [];
            if ($resume_data['hc_id'] != $hc_id && $hc_id > 0) {
                $resume_up_data['hc_id']                       = $hc_id;
                $resume_up_data['is_can_change_filter_result'] = intval($is_can_change_filter_result);
                $resume_up_data['filter_state']                = 0;//简历筛选状态：0-初始状态，99-面试标识
                $resume_up_data['state_code']                  = enums::$resume_state_code['re_hc_wait_feedback'];//重新关联hc待反馈
                $resume_up_data['is_out']                      = enums::$resume_is_out['not_out'];
                $resume_up_data['recruiter_id']                = $this->userInfo['id'];
                $resume_up_data['previous_hc_id']              = $resume_data['hc_id'];
            } else {
                throw new Exception($this->getTranslation()->_('interview_cancel_and_related_hc_3'));
            }
            $db->updateAsDict('hr_resume', $resume_up_data, [
                'conditions' => 'id = ?',
                'bind'       => [$resume_id],
            ]);
            // 6 相关日志
            $addLog = [
                'module_id'    => $resume_id,
                'module_type'  => enums::$log_module_type['hc_id'],
                'action'       => enums::$log_option['modify'],
                'module_level' => $hc_id ?? 0,
                'data_after'   => ['hc_id' => $hc_id ?? 0],
            ];
            (new LogServer())->addLog($addLog);
            $db->commit();
        } catch (Exception $e) {
            $db->rollback();
            $this->getDI()->get('logger')->write_log("interview_cancel_and_related_hc resume " . $resume_id . " , 处理失败，失败原因:" . $e->getMessage(),
                'info');
            return $this->checkReturn(-3, $e->getMessage());
        }
        return $this->checkReturn(1);
    }

    /**
     * 验证简历是否为个人代理的简历
     * winhr验证 hc和签约类型
     * h5 验证签约类型
     * @param $parmas
     * @return int
     */
    public function isResumeHireTypeAgent($parmas = [])
    {
        if (!isCountry('TH')) {
            return 0;
        }
        $source_type = $parmas['source_type'] ?? 'winhr';
        $hire_type = $parmas['hire_type'] ?? '';
        $reserve_type = $parmas['reserve_type'] ?? '';

        if ($source_type == 'winhr' && !empty($hire_type) && $hire_type == HrStaffInfoModel::HIRE_TYPE_AGENT){
            return 1;
        }elseif ($source_type == 'winhr' && !empty($hire_type) && $hire_type != HrStaffInfoModel::HIRE_TYPE_AGENT){
            return 0;
        }

        if (in_array($source_type,['winhr','h5']) && $reserve_type == HrResumeModel::RESERVE_TYPE_AGENT) {
            return 1;
        }

        return 0;
    }

    public function getResumeHcInterviewData($resume_id)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('interview.interview_id,resume.id,hc.worknode_id,hc.department_id,hc.hire_type,hc.job_title,ss.province_code,ss.city_code,ss.district_code,hc.job_id');
        $builder->from(['resume' => HrResumeModel::class]);
        $builder->leftjoin(HrInterviewModel::class,"interview.resume_id = resume.id and interview.hc_id = resume.hc_id","interview");
        $builder->leftjoin(HrhcModel::class,"hc.hc_id = resume.hc_id","hc");
        $builder->leftjoin(SysStoreModel::class,"hc.worknode_id = ss.id","ss");
        $builder->where('resume.id = '.$resume_id);
        $builder->orderBy('interview.interview_id desc');
        $result = $builder->getQuery()->execute()->getFirst();
        return $result ? $result->toArray() : [];
    }

    /**
     * 车补不乘系数
     * @param $param
     * @return array
     * @throws \ReflectionException
     */
    public function updateRental($param)
    {
        $this->getDI()->get('logger')->write_log('updateRental param = '.json_encode($param,JSON_UNESCAPED_UNICODE), 'info');
        if (!isCountry('TH') || empty($param['staff_id'])){
            return self::checkReturn([]);
        }
        $hrEntryInfo = HrEntryModel::findFirst([
            'columns'    => 'resume_id,entry_date,interview_offer_id',
            'conditions' => 'staff_id = :staff_id: and status = :status: and deleted = :deleted:',
            'bind'       => [
                'staff_id' => $param['staff_id'],
                'deleted' => enums::IS_DELETED_NO,
                'status' => HrEntryModel::STATUS_EMPLOYED,
            ],
            'order'      => 'entry_id DESC',
        ]);
        if (empty($hrEntryInfo)){
            return self::checkReturn([]);
        }
        $hrEntryInfo = $hrEntryInfo->toArray();
        $staffInfo = HrStaffInfoModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id:',
            'bind'       => [
                'staff_id' => $param['staff_id'],
            ],
        ]);
        if (empty($staffInfo)){
            return self::checkReturn([]);
        }
        $resume_data = (new ResumeServer())->getResumeHcInterviewData($hrEntryInfo['resume_id']);
        if (empty($resume_data) || empty($staffInfo->hire_date)){
            return self::checkReturn([]);
        }
        // 取hcm 一线员工薪资配置
        $server = Tools::reBuildCountryInstance(new ResumeServer());
        $new_offer_default_salary = $server->offer_default_salary(
            [
                'effective_date'=>$staffInfo->hire_date,
                'resume_data'=>$resume_data,
                'call_source'=>'by',
            ]
        );
        if (isset($new_offer_default_salary['rental']) && $resume_data['job_title']  == enums::$job_title['Van Courier']){
            $update_data = ['rental'=>$new_offer_default_salary['rental']];
            $this->getDI()->get('logger')->write_log('updateRental staff_id = '.$param['staff_id'].',update_data = '.json_encode($update_data,JSON_UNESCAPED_UNICODE), 'info');
            $re = $this->getDI()->get('db')->updateAsDict(
                'hr_interview_offer',
                $update_data,
                "id = " . $hrEntryInfo["interview_offer_id"]
            );
            if (!$re){
                return self::checkReturn([]);
            }
            (new OfferServer())->addOfferSalaryOperatorLog([
                'interview_offer_id' => $hrEntryInfo["interview_offer_id"],
                'money'              => $update_data,
                'operator_id'        => 10000,
            ]);
        }
        return self::checkReturn(1);
    }

    /**
     * 获取身份证比对信息
     * @param $paramIn
     * @return array
     * @throws ValidationException
     */
    public function identityAuditInfo($paramIn)
    {
        $resumeInfo = ResumeRepository::getOne(['id' => $paramIn['resume_id']], ['id', 'name', 'identity_validate_status']);

        if (empty($resumeInfo)) {
            throw new ValidationException('resume_id:' . $paramIn['resume_id'] . ';' . $this->getTranslation()->_('8402'));
        }

        $resumeExtendInfo = (new ResumeExtendRepository())->getInfoByResumeId($paramIn['resume_id']);
        if (empty($resumeExtendInfo)) {
            throw new ValidationException('resume_id:' . $paramIn['resume_id'] . ';' . $this->getTranslation()->_('8402'));
        }

        $annexInfo = (new ResumeRepository())->annexInfo($paramIn['resume_id']);

        $annexInfoToType = array_column($annexInfo, null, 'file_type');

        //未找到身份证正面照
        if (!isset($annexInfoToType[HrAnnexModel::FILE_TYPE_IDENTITY_FRONT])) {
            throw new ValidationException($this->getTranslation()->_('no_found_identity_img'));
        }

        $identityImg['name'] = $this->getTranslation()->_('identity_img');
        $identityImg['url']  = isset($annexInfoToType[HrAnnexModel::FILE_TYPE_IDENTITY_FRONT]) ? $annexInfoToType[HrAnnexModel::FILE_TYPE_IDENTITY_FRONT]['file_url'] : '';
        $img_list[]          = $identityImg;

        $entryInfo = EntryRepository::getOne(['resume_id' => $paramIn['resume_id']], ['resume_id', 'staff_id', 'status']);

        //暂未录入{姓名}人脸照片，无法确认
        $title = $this->getTranslation()->_('tips_no_found_facial_photo', ['staff_name' => $resumeInfo['name']]);
        //已入职才可能有人脸照片
        $need_submit = false;
        if (!empty($entryInfo) && $entryInfo['status'] == HrEntryModel::STATUS_EMPLOYED && !empty($entryInfo['staff_id'])) {
            $attachmentInfo = StaffWorkAttendanceAttachmentRepository::getOne(['staff_info_id' => $entryInfo['staff_id']]);
            if (!empty($attachmentInfo)) {
                $identityImg['name'] = $this->getTranslation()->_('facial_photo');
                $identityImg['url']  = empty($attachmentInfo['work_attendance_path']) ? '' : $this->getDI()->getConfig()->application['img_prefix'] . $attachmentInfo['work_attendance_path'];
                $img_list[]          = $identityImg;

                $need_submit = true;

                //未处理：请确认{姓名}身份证照片是否与本人相同
                if ($resumeInfo['identity_validate_status'] == HrResumeModel::IDENTITY_VALIDATE_STATUS_UNTREATED) {
                    $title = $this->getTranslation()->_('tips_confirm_identity_face',
                        ['staff_name' => $resumeInfo['name']]);
                }
            }
        }

        //已通过：已确认{姓名}身份证照片与本人相同，确认将身份证确认状态改成“未通过”吗？
        if ($resumeInfo['identity_validate_status'] == HrResumeModel::IDENTITY_VALIDATE_STATUS_PASS) {
            $title = $this->getTranslation()->_('tips_identity_validate_status_pass',
                ['staff_name' => $resumeInfo['name']]);
            $need_submit = true;
        }

        //未通过：已确认{姓名}身份证照片与本人不同，确认将身份证确认状态改成“已通过”吗？
        if ($resumeInfo['identity_validate_status'] == HrResumeModel::IDENTITY_VALIDATE_STATUS_FAIL) {
            $title = $this->getTranslation()->_('tips_identity_validate_status_fail',
                ['staff_name' => $resumeInfo['name']]);
            $need_submit = true;
        }

        $returnArr['title']       = $title;
        $returnArr['img_list']    = $img_list;
        $returnArr['status']      = $resumeInfo['identity_validate_status'];
        $returnArr['need_submit'] = $need_submit;

        return $this->checkReturn(['data' => $returnArr]);
    }

    /**
     * 修改身份证确认状态
     * @param $paramIn
     * @return mixed
     * @throws ValidationException
     */
    public function identityStatusUpdate($paramIn)
    {
        $resumeInfo = ResumeRepository::getOne(['id' => $paramIn['resume_id']], ['id', 'identity_validate_status', 'recruiter_id', 'name', 'hc_id']);

        if (empty($resumeInfo)) {
            throw new ValidationException('resume_id:' . $paramIn['resume_id'] . ';' . $this->getTranslation()->_('8402'));
        }
        //winHr 端请求 待确认 状态下，不允许修改
        if (empty($paramIn['source']) && in_array($resumeInfo['identity_validate_status'],
            [HrResumeModel::IDENTITY_VALIDATE_STATUS_PENDING, HrResumeModel::IDENTITY_VALIDATE_STATUS_NO_NEED])) {
            throw new ValidationException($this->getTranslation()->_('tips_identity_validate_status_limit'));
        }
        //无需确认 状态下，不允许修改
        if (in_array($resumeInfo['identity_validate_status'],
                [HrResumeModel::IDENTITY_VALIDATE_STATUS_NO_NEED])) {
            throw new ValidationException($this->getTranslation()->_('tips_identity_validate_status_limit'));
        }

        //如果当前状态与要修改的状态相同，则无需修改
        if ($resumeInfo['identity_validate_status'] == $paramIn['status']) {
            throw new ValidationException($this->getTranslation()->_('tips_identity_validate_status_limit'));
        }

        $db = $this->getDI()->get("db");
        try {
            //申请表插入数据
            $db->begin();
            $db->updateAsDict("hr_resume", ['identity_validate_status' => $paramIn['status']],
                [
                    'conditions' => 'id = ?',
                    'bind'       => [$paramIn['resume_id']],
                ]
            );

            $updateExtend['identity_confirm_staff_id'] = $paramIn['staff_id'];
            $updateExtend['identity_confirm_source']   = !empty($paramIn['source']) && $paramIn['source'] == 'by' ? HrResumeExtendModel::IDENTITY_CONFIRM_SOURCE_BY : HrResumeExtendModel::IDENTITY_CONFIRM_SOURCE_WIN_HR;

            //ph 会传，BY到岗确认，选择未到岗，已到岗，都可能会传
            if(!empty($paramIn['hand_identity_url'])) {
                $updateExtend['hand_identity_url'] = $paramIn['hand_identity_url'];
            }

            $db->updateAsDict("hr_resume_extend", $updateExtend,
                [
                    'conditions' => 'resume_id = ?',
                    'bind'       => [$paramIn['resume_id']],
                ]
            );

            //状态：未处理 日志
            if($resumeInfo['identity_validate_status'] == HrResumeModel::IDENTITY_VALIDATE_STATUS_UNTREATED) {
                $module_type = $paramIn['status'] == HrResumeModel::IDENTITY_VALIDATE_STATUS_PASS ? enums::$log_module_type['winhr_identity_confirm_same'] : enums::$log_module_type['winhr_identity_confirm_different'];
                $action      = enums::$log_option['modify'];
            //状态：处理过 日志
            } else {
                $module_type = $paramIn['status'] == HrResumeModel::IDENTITY_VALIDATE_STATUS_PASS ? enums::$log_module_type['winhr_identity_confirm_same_update'] : enums::$log_module_type['winhr_identity_confirm_different_update'];
                $action      = enums::$log_option['confirm'];
            }

            //by的日志。
            if(!empty($paramIn['source']) && $paramIn['source'] == 'by') {
                $module_type = $paramIn['status'] == HrResumeModel::IDENTITY_VALIDATE_STATUS_PASS ? enums::$log_module_type['by_identity_confirm_same'] : enums::$log_module_type['by_identity_confirm_different'];
                $action      = enums::$log_option['confirm'];
            }

            $addLogRes = (new LogServer())->addLog([
                'module_id'   => $paramIn['resume_id'],
                'module_type' => $module_type,
                'action'      => $action,
                'staff_id'    => $paramIn['staff_id'],
            ]);

            if (empty($addLogRes)) {
                throw new \Exception($this->getTranslation()->_('no_server'));
            }

            $db->commit();

            if (isset($paramIn['source']) && $paramIn['source'] == 'by' && $paramIn['status'] == HrResumeModel::IDENTITY_VALIDATE_STATUS_FAIL) {
                $hcInfo                = HcRepository::getOne(['hc_id' => $resumeInfo['hc_id']]);
                $data['resume_id']     = $resumeInfo['id'];
                $data['name']          = $resumeInfo['name'];
                $data['recruiter_id']  = $resumeInfo['recruiter_id'];
                $data['department_id'] = $hcInfo['department_id'] ?? '';
                $data['store_id']      = $hcInfo['worknode_id'] ?? '';
                $this->sendValidateIdentityMessage($data);
            }

            return $this->checkReturn(['data' => ['resume_id' => $paramIn['resume_id']]]);
        } catch (\Exception $e) {
            $db->rollback();
            $this->getDi()->get('logger')->write_log('identityStatusUpdate:' .
                [
                    'Err_Msg'  => $e->getMessage(),
                    'Err_Line' => $e->getLine(),
                    'Err_File' => $e->getFile(),
                    'Err_Code' => $e->getCode(),
                ], 'error');
            throw new \Exception($e->getMessage());
        }
    }

    /**
     * 身份证确认本人不同 发送消息。
     * @param $data
     * @return bool
     */
    public function sendValidateIdentityMessage($data)
    {
        $this->sendMessageRecruiter($data);

        //仅PH 给 hrbp 发送消息
        if(isCountry('PH')) {
            $this->sendMessageToHrbp($data);
        }

        return true;
    }

    /**
     * 给招聘负责人发送消息
     * @param $data
     * @return bool
     */
    public function sendMessageRecruiter($data)
    {
        $this->logger->write_log(['sendMessageRecruiter' => $data],'info');

        if(empty($data['recruiter_id'])) {
            return false;
        }

        $staffInfo = (new StaffRepository())->getStaffInfoById($data['recruiter_id']);
        if(empty($staffInfo)) {
            return false;
        }

        //获取员工语言环境
        $staffServer                       = new StaffServer();
        $staffLang                         = $staffServer->getLanguage($data['recruiter_id']);

        $title   = $this->getTranslation($staffLang)->_('identity_reject_title', ['resume_id' => $data['resume_id']]);
        $content = $this->getTranslation($staffLang)->_('identity_reject_content', [
            'recruiter_name' => $staffInfo['name'],
            'staff_name' => $data['name'],
            'resume_id' => $data['resume_id'],
        ]);

        $content = '<meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,minimum-scale=1.0,user-scalable=no,viewport-fit=cover"><p style="font-size: 15px;">' . $content . "</p>";

        $message_params['id']              = time() . $data['recruiter_id'] . rand(1000000, 9999999);
        $message_params['staff_users']     = [$data['recruiter_id']];
        $message_params['message_title']   = $title;
        $message_params['message_content'] = $content;
        $message_params['staff_info_ids_str'] = $data['recruiter_id'];
        $message_params['category']           = -1;//系统消息
        (new MessageServer())->add_kit_message($message_params);

        return true;
    }

    /**
     * 给Hrbp 发送消息
     * @param $data
     * @return bool
     */
    public function sendMessageToHrbp($data)
    {
        if(empty($data['department_id']) || empty($data['store_id'])) {
            return false;
        }

        $staffServer                       = new StaffServer();

        $hrbp_data = $staffServer->findHRBPToBackyard(['department_id' => $data['department_id'], 'store_id' => $data['store_id']]);

        if ($hrbp_data['result']['code'] == 0 || empty($hrbp_data['result']['data']['hrbp'])) {
            return false;
        }
        $hrbp_ids = explode(',', $hrbp_data['result']['data']['hrbp']);
        if(empty($hrbp_ids)) {
            return false;
        }
        $hrbp_ids = array_values($hrbp_ids);

        $staffInfos = (new StaffRepository())->checkoutStaffBatch($hrbp_ids);
        if(empty($staffInfos)) {
            return false;
        }

        $staffInfosToId = array_column($staffInfos, 'name', 'staff_info_id');

        //获取员工语言环境
        $staffLangs                         = $staffServer->getStaffsLanguage($hrbp_ids);

        $this->logger->write_log(['sendMessageRecruiter' => ['data' => $data, 'hrbp' => $hrbp_ids]],'info');

        foreach ($staffInfos as $oneHr) {
            $title = $this->getTranslation($staffLangs[$oneHr['staff_info_id']])->_('identity_reject_title', ['resume_id' => $data['resume_id']]);
            $content = $this->getTranslation($staffLangs[$oneHr['staff_info_id']])->_('identity_reject_content_to_hrbp', [
                'hr_name' => $staffInfosToId[$oneHr['staff_info_id']] ?? '',
                'staff_name' => $data['name'],
                'resume_id' =>  $data['resume_id'],
            ]);

            $content = '<meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,minimum-scale=1.0,user-scalable=no,viewport-fit=cover"><p style="font-size: 15px;">' . $content . "</p>";

            $message_params['id']              = time() . $oneHr['staff_info_id'] . rand(1000000, 9999999);
            $message_params['staff_users']     = [$oneHr['staff_info_id']];
            $message_params['message_title']   = $title;
            $message_params['message_content'] = $content;
            $message_params['staff_info_ids_str'] = $oneHr['staff_info_id'];
            $message_params['category']           = -1;//系统消息
            (new MessageServer())->add_kit_message($message_params);
        }

        return true;
    }

    /**
     * ai 身份证证明照 打码
     * @param $paramIn
     * @param $nationality
     * @return bool
     * @throws ValidationException
     */
    public function getIdentityFrontAiMosaic($paramIn, $nationality = enums::IS_TH_NATIONALITY)
    {
        $switch = (new SettingEnvServer())->getSetVal('identity_front_ai_mosaic_switch');
        if(empty($switch)) {
            return false;
        }

        $this->getDI()->get('logger')->write_log(['getIdentityFrontAiMosaic' => ['params' => $paramIn, 'nationality' => $nationality]], 'info');

        $entryInfo = EntryRepository::getOne(['resume_id' => $paramIn['resume_id']], ['resume_id', 'staff_id', 'status']);
        if(empty($entryInfo)) {
            return false;
        }

        if($entryInfo['status'] == HrEntryModel::STATUS_EMPLOYED) {
            return false;
        }

        $resumeInfo = ResumeRepository::getOne(['id' => $paramIn['resume_id']]);
        if (empty($resumeInfo)) {
            throw new ValidationException('resume_id:' . $paramIn['resume_id'] . ';' . $this->getTranslation()->_('8402'));
        }

        $hcInfo = HcRepository::getOne(['hc_id' => $resumeInfo['hc_id']]);

        $idcard_verification_branch   = SettingEnvServer::getSetValToArray('idcard_verification_branch', ',');
        $idcard_verification_position = SettingEnvServer::getSetValToArray('idcard_verification_position', ',');

        $db = $this->getDI()->get('db');
        if(!($resumeInfo['nationality'] == $nationality && array_intersect([HrStaffManageStoreModel::$all_id, $hcInfo['worknode_id']], $idcard_verification_branch) && in_array($hcInfo['job_title'], $idcard_verification_position))) {
            //无需处理
            $resumeUpdate['identity_validate_status'] = HrResumeModel::IDENTITY_VALIDATE_STATUS_NO_NEED;
            $db->updateAsDict(
                'hr_resume',
                $resumeUpdate,
                'id = ' . $paramIn['resume_id']
            );

            return true;
        }
        //PH 不走AI.
        if(isCountry('PH')) {
            $resumeUpdate['identity_validate_status'] = HrResumeModel::IDENTITY_VALIDATE_STATUS_PENDING;

            $db->updateAsDict(
                'hr_resume',
                $resumeUpdate,
                'id = ' . $paramIn['resume_id']
            );

            return true;
        }

        $redis     = $this->getDI()->get('redisLib');
        $redis_key = 'RESUME_AI_ID_CARD_MOSAIC_COUNT_' . $paramIn['resume_id'];
        $redis->expire($redis_key, 30 * 60);
        $count = $redis->incr($redis_key);

        if($count <= self::AI_IDENTITY_FAIL_NUM) {
            $mosaicUrl = $this->getIdentityFrontAiMosaicUrl($paramIn);
            if(empty($mosaicUrl)) {
                $this->getDI()->get('logger')->write_log(['getIdentityFrontAiMosaic-ai-fail' => ['resume_id' => $paramIn['resume_id'], 'count' => $count, 'identity' => $paramIn['identity_url'], 'mosaicUrl' => $mosaicUrl]], 'info');
                throw new ValidationException($this->getTranslation()->_('identity_img_not_regulations'));
            }

            $redis->delete($redis_key);
            $resumeExtendUpdate['ai_identity_status'] = HrResumeExtendModel::AI_IDENTITY_STATUS_SUCCESS;
            $resumeExtendUpdate['identity_code_url'] = $mosaicUrl;
            (new ResumeExtendRepository())->saveByResumeId($paramIn['resume_id'], $resumeExtendUpdate);

            $resumeUpdate['identity_validate_status'] = HrResumeModel::IDENTITY_VALIDATE_STATUS_PENDING;

            $db->updateAsDict(
                'hr_resume',
                $resumeUpdate,
                'id = ' . $paramIn['resume_id']
            );
            $this->getDI()->get('logger')->write_log(['getIdentityFrontAiMosaic-success' => ['resume_id' => $paramIn['resume_id'], 'count' => $count, 'identity' => $paramIn['identity_url'], 'mosaicUrl' => $mosaicUrl]], 'info');

            return true;
        }

        //失败
        $resumeExtendUpdate['ai_identity_status'] = HrResumeExtendModel::AI_IDENTITY_STATUS_FAIL;
        $resumeExtendUpdate['identity_code_url'] = '';
        (new ResumeExtendRepository())->saveByResumeId($paramIn['resume_id'], $resumeExtendUpdate);

        $resumeUpdate['identity_validate_status'] = HrResumeModel::IDENTITY_VALIDATE_STATUS_UNTREATED;

        $db->updateAsDict(
            'hr_resume',
            $resumeUpdate,
            'id = ' . $paramIn['resume_id']
        );
        $this->getDI()->get('logger')->write_log(['getIdentityFrontAiMosaic-fail' => ['resume_id' => $paramIn['resume_id'], 'count' => $count, 'mosaicUrl' => '']], 'info');

        return false;
    }

    /**
     * AI 识别，oss 打码
     * @param $paramIn
     * @return bool|string
     */
    public function getIdentityFrontAiMosaicUrl($paramIn)
    {
        //请求AI--返回坐标数据
        //todo ai 地址
        $ai_result = (new AIServer())->ai_id_card_ocr_post($paramIn['identity_url'], [
            'ai_id_card_ocr_domain',
            'ai_id_card_ocr_url',
            'ai_id_card_ocr_secret_id',
            'ai_id_card_ocr_secret_key',
        ]);
        if((empty($ai_result) || (isset($ai_result['status']) && strtoupper($ai_result['status']) != 'OK'))) {
            $this->getDI()->get('logger')->write_log(['getIdentityFrontAiMosaic' => ['resume_id' => $paramIn['resume_id'], 'identity_url' => $paramIn['identity_url'], 'ai_result' => $ai_result]], 'error');
            return '';
        }
        $this->getDI()->get('logger')->write_log(['getIdentityFrontAiMosaic-ai_id_card_ocr_post' => ['resume_id' => $paramIn['resume_id'], 'identity_url' => $paramIn['identity_url'], 'ai_result' => $ai_result]], 'info');

        $mosaic_rects_data['points'] = $ai_result['result']['mosaic_rects'];
        $mosaic_rects_data['url'] = $paramIn['identity_url'];

        $ai_mosaic_result = (new AIServer())->ai_id_card_mosaic_post($mosaic_rects_data);


        if((empty($ai_mosaic_result) || (isset($ai_mosaic_result['status']) && strtoupper($ai_mosaic_result['status']) != 'OK'))) {
            $this->getDI()->get('logger')->write_log(['getIdentityFrontAiMosaic-ai_id_card_mosaic_post' => ['resume_id' => $paramIn['resume_id'], 'identity_url' => $paramIn['identity_url'], 'ai_result' => $ai_mosaic_result]], 'error');
            return '';
        }
        if(empty($ai_mosaic_result['result']['image'])) {
            $this->getDI()->get('logger')->write_log(['getIdentityFrontAiMosaic-ai_id_card_mosaic_post-image-empty' => ['resume_id' => $paramIn['resume_id'], 'identity_url' => $paramIn['identity_url'], 'ai_result' => $ai_mosaic_result]], 'error');
            return '';
        }
        //请求--生成 打码图片。
        $base64Image = $ai_mosaic_result['result']['image'] ?? '';

        // 对 Base64 字符串进行解码
        $imageData = base64_decode($base64Image);

        // 检查解码是否成功
        if ($imageData === false) {
            $this->getDI()->get('logger')->write_log(['getIdentityFrontAiMosaic-create' => ['resume_id' => $paramIn['resume_id'], 'identity_url' => $paramIn['identity_url'], 'result' => 'Base64 解码失败，请检查输入的 Base64 字符串是否正确。']], 'error');
            return '';
        }

        // 将解码后的图片数据写入临时文件
        $tempFile = tempnam(sys_get_temp_dir(), 'img');
        if (file_put_contents($tempFile, $imageData) === false) {
            $this->getDI()->get('logger')->write_log(['getIdentityFrontAiMosaic-create' => ['resume_id' => $paramIn['resume_id'], 'identity_url' => $paramIn['identity_url'], 'result' => '无法将图片数据写入临时文件，请检查文件权限或路径是否正确。']], 'error');
            return '';
        }

        // 获取图片类型
        $imageType = exif_imagetype($tempFile);

        // 根据图片类型确定文件扩展名
        switch ($imageType) {
            case IMAGETYPE_JPEG:
                $extension = '.jpg';
                break;
            case IMAGETYPE_PNG:
                $extension = '.png';
                break;
            default:
                $extension = '';
                break;
        }

        // 生成最终的图片文件名，可以根据需要自定义文件名和路径
        $finalFileName = sys_get_temp_dir() . '/' . uniqid('resume_identity_' . $paramIn['resume_id'] . time() .mt_rand(1000, 9999)) . $extension;

        // 将临时文件移动到最终位置
        if (rename($tempFile, $finalFileName) === false) {
            $this->getDI()->get('logger')->write_log(['getIdentityFrontAiMosaic-move' => ['resume_id' => $paramIn['resume_id'], 'identity_url' => $paramIn['identity_url'], 'result' => '无法将临时文件移动到最终位置，请检查文件权限或路径是否正确。']], 'error');
            return '';
        }

        //上传oss
        $result     = $this->uploadFileOss($finalFileName, 'WORK_ORDER');
        return empty($result['object_url']) ? '' : $result['object_url'];
    }

    /**
     * rpc by 确认身份证是否本人
     * 选择‘不同’时请求的。
     * @param $local
     * @param $params
     * @return mixed
     */
    public function identityStatusSubmit($local, $params)
    {
        $this->getTranslation($local['locale']);

        try {
            $paramsIn['staff_id']  = $params['staff_id'];
            $paramsIn['resume_id'] = $params['resume_id'];
            $paramsIn['status']    = $params['identity_status'];
            if (!empty($paramsIn['status']) && $paramsIn['status'] != HrResumeModel::IDENTITY_VALIDATE_STATUS_FAIL) {
                throw new ValidationException($this->getTranslation()->_('identity_confirm_status_empty'));
            }

            $paramsIn['source']    = 'by';
            return $this->identityStatusUpdate($paramsIn);
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("svc identityStatusSubmit 异常信息:" . $e->getMessage(), 'notice');
            $return['code'] = -1;
            $return['msg']  = $e->getMessage();
            return $return;
        }
    }

    /**
     * 主账号
     * @param $staff_id
     * @param $msg_id
     * @return mixed
     */
    public function getSupportMasterInfo($staff_id, $msg_id)
    {
        //子账号，查询主账号信息
        if(isCountry(['TH', 'MY'])) {
            $supportStaffInfo = StaffSupportRepository::getSupportInfoBySubStaff($staff_id);
            if ($supportStaffInfo && !empty($supportStaffInfo['staff_info_id'])) {
                $staff_id = $supportStaffInfo['staff_info_id'];
                //主账号的消息id 传到了 子账号的消息 内容里
                $subMessageCourier = MessageRepository::getMessageCourierInfo(['id' => $msg_id]);
                $subMessageContent = MessageRepository::getMessageContentInfo(['id' => $subMessageCourier['message_content_id']]);
                $messageParams = ['id' => $subMessageContent['message'], 'staff_info_id' => $supportStaffInfo['staff_info_id']];
                $message = MessageRepository::getMessageCourierInfo($messageParams);
                if(!empty($message)){
                    $msg_id = $subMessageContent['message'];
                }
            }
        }

        $data['staff_id'] = $staff_id;
        $data['msg_id']   = $msg_id;

        return $data;
    }

    /**
     * 性别取值差异化
     * @param $paramIn
     * @return int|mixed|string|null
     */
    public function getSex($paramIn)
    {
        if (isCountry(['MY', 'VN'])) {
            return $paramIn['sex'] ?? 3;
        } else {
            return getSex($paramIn['call_name'] ?? '', $paramIn['sex'] ?? '');
        }
    }

}