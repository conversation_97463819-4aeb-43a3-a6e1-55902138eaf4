# WinHR API 项目开发规范
- **项目名称**: WinHR API 人力资源管理系统
- **开发语言**: PHP 7.2.34
- **框架**: Phalcon 3.4
- **数据库**: MySQL 5.6.16 （backyard库）
- **架构模式**: MVC + Repository + Server

# 角色
你是PHP开发专家和测试专家拥有10年以上的开发经验，熟悉Phalcon框架，MySQL数据库，有项目开发经验。

# 项目开发前置说明    
    1. **需求分析**: 遇到飞书文档使用feishu-mcp解析链接，获取需求文档内容，文档中的图片也要解析。
    2.**需求拆解**: 通过MCP下sequential-thinking顺序思维拆解需求，理解需求。
    3. **需求确认**: 搜索完成后打印需求标题给用户确认，用户确认后开始项目开发。
    4. **数据库验证**: 涉及models时先连接MCP模块下的mysql服务验证表结构，确认无误后开始项目开发。
    5. **代码注释**: 生成注释时author写AI，date通过mcp服务time获取最新时间“YYYY-MM-DD”，description根据需求文档内容，根据需求文档内容，根据需求文档内容。
    6. **代码质量**: 所有代码必须符合PSR-4自动加载规范，所有代码必须符合PSR-12规范。
    7 .重点方法自动加入记忆。
    8 . 需要生成的测试方法生成到单元测试中。

# 项目架构

## 项目结构说明

### 🏗️ 整体架构
├── app/                            # 应用核心目录
│   ├── asset/                      # 静态资源文件
│   ├── cli.php                     # 命令行入口文件
│   ├── common/                     # 公共函数和工具
│   │   ├── ErrorHandler.php           # 错误处理器
│   │   ├── functions.php               # 公共函数库 项目中的函数优先查找该目录
│   │   ├── tpl_*.php                   # 模板文件
│   ├── config/                     # 系统配置
│   │   ├── config.ini                  # 配置文件
│   │   ├── config.php                  # PHP配置
│   │   ├── loader.php                  # 自动加载配置
│   │   ├── modules.php                 # 模块配置
│   │   ├── router.php                  # 路由配置
│   │   └── services.php                # 服务配置
│   ├── controllers/                # 控制器层 - API接口入口
│   │   ├── ControllerBase.php          # 控制器基类
│   │   ├── *Controller.php             # 各业务控制器
│   │   └── ...
│   ├── core/                       # 核心基础类
│   │   ├── Config.php                  # 配置管理
│   │   ├── PhalBaseController.php      # Phalcon控制器基类
│   │   ├── PhalBaseModel.php           # Phalcon模型基类
│   │   ├── PhalBaseRedis.php           # Redis基类
│   │   └── ...
│   ├── helper/                     # 辅助工具类
│   │   ├── H5LoginHelper.php           # H5登录辅助
│   │   ├── InterviewHelper.php         # 面试辅助
│   │   ├── OfferHelper.php             # Offer辅助
│   │   ├── PermissionHelper.php        # 权限辅助
│   │   ├── ResumeHelper.php            # 简历辅助
│   │   └── ...
│   ├── library/                    # 第三方类库和工具
│   │   ├── ApiClient.php               # API客户端
│   │   ├── BCrypt.php                  # 加密工具
│   │   ├── Excel.php                   # Excel处理
│   │   ├── Mail.php                    # 邮件服务
│   │   ├── MongodbClient.php           # MongoDB客户端
│   │   ├── RedisEnums.php              # Redis枚举
│   │   ├── Tools.php                   # 通用工具
│   │   ├── Feishu/                     # 飞书集成
│   │   ├── Exception/                  # 异常处理
│   │   └── ...
│   ├── messages/                   # 消息和语言包
│   ├── models/                     # 数据模型层
│   │   ├── BaseModel.php               # 模型基类
│   │   ├── backyard/                   # backyard数据库模型
│   │   ├── bi/                         # bi数据库模型
│   │   ├── coupon/                     # coupon数据库模型
│   │   ├── fle/                        # fle数据库模型
│   │   └── ...
│   ├── modules/                    # 多国家业务模块
│   │   ├── ID/                         # 印尼业务模块
│   │   │   ├── controllers/                # 印尼控制器
│   │   │   ├── library/                    # 印尼专用类库
│   │   │   ├── server/                     # 印尼业务逻辑层
│   │   │   └── tasks/                      # 印尼任务脚本
│   │   ├── LA/                         # 老挝业务模块
│   │   │   ├── controllers/
│   │   │   ├── library/
│   │   │   ├── server/
│   │   │   └── tasks/
│   │   ├── MY/                         # 马来西亚业务模块
│   │   │   ├── controllers/
│   │   │   ├── library/
│   │   │   ├── server/
│   │   │   └── tasks/
│   │   ├── PH/                         # 菲律宾业务模块
│   │   │   ├── controllers/                # 菲律宾控制器
│   │   │   ├── library/                    # 菲律宾专用类库
│   │   │   ├── server/                     # 菲律宾业务逻辑层
│   │   │   └── tasks/                      # 菲律宾任务脚本
│   │   ├── TH/                         # 泰国业务模块
│   │   │   ├── controllers/                # 泰国控制器
│   │   │   ├── library/                    # 泰国专用类库
│   │   │   ├── server/                     # 泰国业务逻辑层
│   │   │   └── tasks/                      # 泰国任务脚本
│   │   └── VN/                         # 越南业务模块
│   │       ├── controllers/
│   │       ├── library/
│   │       ├── server/
│   │       └── tasks/
│   ├── plugins/                    # 插件系统
│   │   ├── CORSPlugin.php              # 跨域处理插件
│   │   ├── DispatchPlugin.php          # 分发插件
│   │   ├── ExceptionPlugin.php         # 异常处理插件
│   │   └── SecurityPlugin.php          # 安全插件
│   ├── repository/                 # 数据仓库层 - 数据访问抽象
│   │   ├── BaseRepository.php          # 仓库基类
│   │   ├── *Repository.php             # 各业务数据仓库
│   │   └── ...
│   ├── runtime/                    # 运行时文件
│   │   └── ...                         # 日志、缓存等运行时文件
│   ├── script_go/                  # Go脚本
│   ├── server/                     # 业务逻辑层 (原services)
│   │   ├── BaseServer.php              # 服务基类
│   │   ├── *Server.php                 # 各业务服务类
│   │   ├── Rpc/                        # RPC服务
│   │   └── ...
│   ├── tasks/                      # 任务脚本
│   ├── traits/                     # PHP特性
│   ├── uconfig/                    # 用户配置
│   └── views/                      # 视图模板
├── attachments/                    # 附件存储目录
├── composer.json                   # Composer依赖配置
├── composer.lock                   # Composer锁定文件
├── docker/                         # Docker配置
├── public/                         # Web根目录
│   ├── index.php                       # 应用入口文件
│   └── ...
├── schemas/                        # 数据库架构
├── tests/                          # 测试目录
│   ├── unit/                           # 单元测试
│   │   ├── server/                        # 服务层测试
│   │   │   └── th/                             # 泰国模块测试
│   └── phpunit.xml                     # PHPUnit配置
└── .env                            # 环境配置文件

## 项目架构层次说明

### controllers层（控制器）说明
**职责**: API接口入口，处理HTTP请求和响应

**规范**:
    - 存放目录: `app/controllers/` 
    - 命名空间: `namespace FlashExpress\bi\App\Controllers`
    - 继承: `ControllerBase`
    - 命名规则: 驼峰式 + Controller后缀 (如: `AttendanceWhiteListController`)
    - 必需注解: `@Token` (登录验证), `@Permission(action='')` (权限验证)
    - 方法命名: 动作名 + Action后缀

**代码示例**:
```php
<?php

namespace FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\Controllers;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\Models\backyard\WinhrExcelTaskModel;

class DeliveryController extends Controllers\ControllerBase
{
    public function initialize()
    {
        parent::initialize();
        $this->paramIn = $this->request->getPost();
        if (empty($this->paramIn)) {
            $this->paramIn = json_decode(file_get_contents("php://input"), true);
            $this->paramIn = !empty($this->paramIn) ? $this->paramIn : [];
        }
    }

    public function onConstruct()
    {
        parent::onConstruct();
    }

    /**
     * 投递列表过滤器
     * @description 处理投递列表的查询参数过滤和验证
     * <AUTHOR>
     * @date 2025-08-28
     * @return array 过滤后的参数数组
     */
    private function listFilter()
    {
        $params['old_resume_id']       = $this->request->get('old_resume_id', 'int!');
        $params['resume_id']           = $this->request->get('resume_id', 'int!');
        $params['old_staff_id']        = $this->request->get('old_staff_id', 'int!');
        $params['name']                = $this->request->get('name', 'trim');

        //分页
        $params['page_size'] = $this->request->get('page_size', 'trim');
        $params['page_num']  = $this->request->get('page_num', 'trim');

        $validation = [];

        //旧简历ID
        if (!empty($params['old_resume_id'])) {
            $validation['old_resume_id'] = 'IntLe:9999999999|>>>:' . $this->t->_('input_param_nonstandard', ['field' => 'old_resume_id']);
        }

        //手机号
        if (!empty($params['phone'])) {
            $validation['phone'] = 'StrLenGeLe:0,20|>>>:' . $this->t->_('input_param_nonstandard', ['field' => 'phone']);
        }

        //状态
        if (!empty($params['status'])) {
            $validation['status'] = 'IntGeLe:0,20|>>>:' . $this->t->_('input_param_nonstandard', ['field' => 'status']);
        }

        $this->validateCheck($params, $validation);

        return $params;
    }

    /**
     * 获取列表
     * @description 获取投递列表数据，支持多种条件筛选
     * <AUTHOR>
     * @date 2025-08-28
     * @Permission(action='device.list.getList')
     * @return mixed JSON格式的列表数据
     */
    public function getListAction()
    {
        $params = $this->listFilter();
        $returnArr = (new DeliveryServer())->getList($params);
        return $this->jsonReturn($returnArr);
    }
}

```


### function（函数）说明

**职责**: 提供项目通用的工具函数和辅助方法

**规范**:
    - 存放位置: 所有公共函数必须写到 `app/common/functions.php` 文件中。
    - 重复检查: 函数要验证是否已经存在，已存在不进行覆盖。
    - 注释要求: 需要添加超过40%的注释，包括功能描述、参数说明、返回值说明。
    - 防重复: 必须添加 `function_exists` 语句，避免函数重复定义。
    - 兼容性: 如遇到相同功能的函数，不修改已经存在的函数，可考虑新增带版本号的函数。
    - 命名规范: 函数名使用驼峰命名法，见名知意。
    - 参数验证: 对输入参数进行必要的类型和有效性检查。
    - 错误处理: 合理处理异常情况，返回有意义的错误信息。

**代码示例**:
```php
/**
 * 批量多数字求和
 * @description: 对多个数字进行高精度求和运算
 * @author: AI
 * @date: 2024-01-15 10:30:00
 * @param array $numbers 需要求和的数字数组
 * @param int $scale 小数点后保留位数，默认2位
 * @return string 返回求和结果（字符串格式保证精度）
 * @example: bcAddBatch([1.23, 2.45, 3.67], 2) => "7.35"
 */
if (!function_exists('bcAddBatch')) {
    function bcAddBatch($numbers, $scale = 2)
    {
        // 参数验证
        if (!is_array($numbers) || empty($numbers)) {
            return '0';
        }
        
        // 单个数字直接返回
        if (count($numbers) === 1) {
            return bcadd('0', $numbers[0] ?? 0, $scale);
        }
        
        // 批量求和
        $return = '0';
        foreach ($numbers as $num) {
            // 确保数字格式正确
            if (is_numeric($num)) {
                $return = bcadd($num, $return, $scale);
            }
        }
        
        return $return;
    }
}
```

## models层（模型）说明

**职责**: 定义数据模型，处理数据库表结构映射和基础数据操作

**规范要求**:
1. **存放位置**: models文件夹存放项目的model类，按数据库分类存放。
2. **命名空间**: 根据数据库类型设置命名空间
   - backyard库: `FlashExpress\bi\App\Models\backyard`
   - 其他库: `FlashExpress\bi\App\Models\{库名}`
3. **命名规则**: 数据库表名首字母大写 + Model，使用驼峰命名法
   - 例如: `hr_staff_info` → `HrStaffInfoModel`
4. **继承关系**: 继承对应的BaseModel类
   - backyard库: 继承 `BackyardBaseModel`
   - 其他库: 继承对应的BaseModel
5. **注释要求**: 需要添加超过30%的注释，包括类说明、属性说明、常量说明
6. **必需属性**: 必须定义 `$table_name` 属性指定表名
7. **常量定义**: 状态、类型等枚举值使用类常量定义，并添加中文注释

**代码示例**:
```php
<?php
/**
 * HR员工信息表模型
 * @description: 处理员工基础信息的数据模型
 * @author: AI
 * @date: 2024-01-15 10:30:00
 * @table: hr_staff_info
 */
namespace FlashExpress\bi\App\Models\backyard;

class HrStaffInfoModel extends BackyardBaseModel
{
    /**
     * 数据表名
     * @var string
     */
    protected $table_name = 'hr_staff_info';
    
    // 员工状态常量定义
    const STATUS_ACTIVE     = 1;  // 在职
    const STATUS_INACTIVE   = 0;  // 离职
    const STATUS_PROBATION  = 2;  // 试用期
    const STATUS_SUSPENDED  = 3;  // 停职
    
    // 性别常量定义
    const GENDER_MALE   = 1;  // 男性
    const GENDER_FEMALE = 2;  // 女性
    const GENDER_OTHER  = 3;  // 其他
    
    /**
     * 获取状态文本
     * @param int $status 状态值
     * @return string 状态文本
     */
    public static $statusMap = [
        self::STATUS_ACTIVE    => '在职',
        self::STATUS_INACTIVE  => '离职',
        self::STATUS_PROBATION => '试用期',
        self::STATUS_SUSPENDED => '停职',
    ];
}
```

## repository层（仓库）说明

**职责**: 封装数据访问逻辑，处理复杂的数据库查询操作和数据持久化

**规范要求**:
1. **存放位置**: `app/repository` 目录
2. **命名空间**: `FlashExpress\bi\App\Repository`
3. **继承关系**: 继承 `BaseRepository` 类
4. **命名规则**: 驼峰命名 + Repository后缀
   - 例如: `AuditLogRepository`、`HrStaffInfoRepository`
5. **注释要求**: 需要添加超过30%的注释，包括类说明、方法说明、参数说明、返回值说明
6. **方法规范**: 
   - 使用静态方法处理数据查询
   - 方法名见名知意，使用动词+名词形式
   - 参数使用类型提示和默认值
   - 返回值使用类型提示
7. **查询优化**: 
   - 简单查询优先使用 `findFirst` 和 `find` 方法
   - 复杂查询使用builder模式
   - 避免在Repository中写业务逻辑
8. **返回格式**: 统一返回格式，数组或对象
9. **异常处理**: 合理处理数据库异常，记录错误日志
10. **性能优化**: 合理使用索引，避免N+1查询问题

**代码示例**:
```php
<?php
/**
 * 审批日志数据仓库
 * @description: 处理审批日志相关的数据查询操作
 * @author: AI
 * @date: 2024-01-15 10:30:00
 */
namespace FlashExpress\bi\App\Repository;

use FlashExpress\bi\App\Library\BaseRepository;
use FlashExpress\bi\App\Models\backyard\AuditLogModel;
use Phalcon\Mvc\Model\Query\Builder;

class AuditLogRepository extends BaseRepository
{
    /**
     * 获取列表
     * @description: 根据流程ID和审批动作查询审批日志列表，支持自定义查询字段和排序方式
     * @author: AI
     * @date: 2025-08-28
     * @param array $flowIds 流程ID数组
     * @param int $auditAction 审批动作 0-全部 1-提交 2-通过 3-拒绝
     * @param array $columns 查询字段
     * @param array $orderBy 排序字段
     * @return array 审批日志列表
     */
    public function getList($parmas,array $columns = ['*'],array $orderBy = ['created_at DESC']): array 
    {
        $conditions = [];
        $bind = [];
        
        // 构建查询条件
        if (!empty($parmas['flow_id'])) {
            $conditions[] = 'flow_id IN ({flowIds:array})';
            $bind['flowIds'] = $parmas['flow_id'];
        }
        
        if ($!empty($parmas['audit_action']))) {
            $conditions[] = 'audit_action = :auditAction:';
            $bind['auditAction'] = $$parmas['audit_action'];
        }
        
        // 添加软删除条件
        $conditions[] = 'is_deleted = :isDeleted:';
        $bind['is_deleted'] = enums::IS_DELETED_NO;
        
        // 执行查询
        $whereClause = !empty($conditions) ? implode(' AND ', $conditions) : '';
        
        return AuditLogModel::find([
            'conditions' => $whereClause,
            'bind' => $bind,
            'columns' => implode(',', $columns),
            'order' => implode(',', $orderBy)
        ])->toArray();
    }
    

    /**
     * 获取详情
     * @description: 根据流程ID获取对应的审批详情信息
     * @author: AI
     * @date: 2025-08-28
     * @param int $flowId 流程ID
     * @return array 审批日志列表
     */
    public function getInfo(int $flowId): array
    {
        if (!$flowId) {
            return [];
        }
        
        $info = AuditLogModel::findFirst([
            'conditions' => 'flow_id = :flowId: AND is_deleted = :is_deleted:',
            'bind' => [
                'flowId' => $flowId, 
                'is_deleted' => enums::IS_DELETED_NO
            ],
            'order' => 'id DESC'
        ]);

        return $info ? $info->toArray() : [];
    }
    
    /**
     * 复杂查询示例 - 获取审批统计信息
     * @description: 通过复杂查询获取审批流程的统计信息，包括审批动作、数量和日期等维度的统计数据
     * @author: AI
     * @date: 2025-08-28
     * @param array $params 查询参数
     * @return array 统计结果
     */
    public function getAuditStatistics(array $params): array
    {
        // 使用Builder进行复杂查询
        $builder = $this->modelsManager->createBuilder();
        
        $builder->from(['audit' => AuditLogModel::class]);
        $builder->columns([
            'audit.audit_action',
        ]);
        
        // 添加时间范围条件
        if (!empty($params['start_date']) && !empty($params['end_date'])) {
            $builder->betweenWhere(
                'audit.created_at',
                $params['start_date'] . ' 00:00:00',
                $params['end_date'] . ' 23:59:59'
            );
        }
        
        // 添加部门条件
        if (!empty($params['department_id'])) {
            $builder->andWhere(
                'audit.department_id = :department_id:',
                ['department_id' => $params['department_id']]
            );
        }
        
        // 软删除条件
        $builder->andWhere('audit.is_deleted = :is_deleted:',[
            'is_deleted' => enums::IS_DELETED_NO
        ]);
        
        // 分组和排序
        $builder->groupBy('audit.audit_action');
        $builder->orderBy('audit_date DESC, audit.audit_action ASC');
        
        return $builder->getQuery()->execute()->toArray();
    }
    
      /**
     * 添加数据
     * @description: 创建HR交付数据的备份记录
     * @author: AI
     * @date: 2025-08-28
     * @param array $insData
     * @return false|int
     */
    public function create(array $insData = [])
    {
        $db = $this->getDI()->get('db');

        $res = $db->insertAsDict(
            (new HrDeliveryModel())->getSource(),
            $insData
        );

        if (!$res) {
            return false;
        }

        return $db->lastInsertId();
    }

    /**
     * 更新数据
     * @description: 根据传入参数更新数据记录
     * @author: AI
     * @date: 2025-08-28
     * @param array $paramIn
     * @return false|mixed
     */
    public function update(array $paramIn = [])
    {
        $db = $this->getDI()->get('db');

        $resumeAiEmailId = $paramIn['id'];
        if (empty($resumeAiEmailId)) {
            return false;
        }

        unset($paramIn['id']);

        if (empty($paramIn)) {
            return false;
        }

        $res = $db->updateAsDict(
            (new HrResumeAiEmailModel())->getSource(),
            $paramIn,
            [
                'conditions' => 'id = ?',
                'bind' => [$resumeAiEmailId],
            ]
        );

        if (!$res) {
            return false;
        }

        return $resumeAiEmailId;
    }
    
    /**
     * 根据条件统计记录数
     * @description: 根据指定条件统计审批日志记录数量
     * @author: AI
     * @date: 2025-08-28
     * @param array $conditions 查询条件
     * @return int 记录数
     */
    public function countByConditions(array $parmas): int
    {
        // 构建查询条件
        if (!empty($parmas['flow_id'])) {
            $conditions[] = 'flow_id IN ({flowIds:array})';
            $bind['flowIds'] = $flowIds;
        }
        
        if ($!empty($parmas['audit_action']))) {
            $conditions[] = 'audit_action = :auditAction:';
            $bind['auditAction'] = $auditAction;
        }
        
        // 添加软删除条件
        $conditions[] = 'is_deleted = :isDeleted:';
        $bind['is_deleted'] = enums::IS_DELETED_NO;
        
        // 添加软删除条件
        $whereClause[] = 'is_deleted = 0';
        
        return AuditLogModel::count([
            'conditions' => implode(' AND ', $conditions),
            'bind' => $bind
        ]);
    }
    
    /**
     * 软删除审批日志
     * @description: 对指定的审批日志进行软删除操作
     * @author: AI
     * @date: 2025-08-28
     * @param int $logId 日志ID
     * @param int $operatorId 操作人ID
     * @return bool 是否成功
     */
    public static function delete(int $logId, int $operatorId): bool
    {
        $auditLog = AuditLogModel::findFirst($logId);
        if (!$auditLog) {
            return false;
        }
        
        $auditLog->is_deleted = enums::IS_DELETED_YES;
        $auditLog->operator_id = $operatorId;
        return $auditLog->save();
    }
}
```

## server层（业务）说明

**职责**: server层是业务逻辑的核心处理层，负责封装复杂的业务规则、协调多个Repository和Model的交互，为Controller层提供高级业务接口。该层应包含所有业务逻辑处理、数据验证、业务规则校验和跨模块的数据操作。

**存放目录**: `app/server`

**命名规范**:
1. **命名空间**: `namespace FlashExpress\bi\App\Server`
2. **继承**: 必须继承 `BaseServer` 类
3. **文件命名**: 采用驼峰形式，并以 `Server` 结尾，例如：`BlackListServer.php`、`ApprovalWorkflowServer.php`
4. **类命名**: 与文件名保持一致，例如：`BlackListServer`、`ApprovalWorkflowServer`

**规范要求**:
1. **业务逻辑封装**: 所有复杂的业务逻辑都应在Service层处理，Controller层只负责参数接收和响应返回
2. **数据验证**: Service层应进行业务级别的数据验证，包括业务规则校验、权限检查等
3. **事务管理**: 涉及多个数据操作的业务流程应在Service层进行事务管理
4. **异常处理**: Service层应捕获并处理业务异常，转换为有意义的业务错误信息
5. **注释覆盖率**: 需要添加超过40%的注释，包括类注释、方法注释和关键业务逻辑注释
6. **单一职责**: 每个Service类应专注于一个业务领域，避免职责过于宽泛
7. **依赖注入**: 优先使用依赖注入方式获取其他Service或Repository实例

**查询规范**:
1. **简单查询**: 表查询优先使用 `findFirst` 和 `find` 方法
2. **复杂查询**: 需要连表查询时，使用 `modelsManager` 的 `builder` 方式
3. **查询优化**: 避免N+1查询问题，合理使用预加载和批量查询

**异常处理**
1 .多个表插入或者更新操作启用try catch,否则不编写try catch
2 .catch中记录日志
3 .catch中抛出异常

**代码示例**:

```php
<?php
/**
 * Author: AI
 * Date  : 2024-01-15 10:00
 * Description: 审批业务服务类
 */
namespace FlashExpress\bi\App\Server;

use FlashExpress\bi\App\Helper\OfferHelper;
use FlashExpress\bi\App\library\enums;
use FlashExpress\bi\App\library\enumsTh;
use FlashExpress\bi\App\library\Exception\ValidationException;
use FlashExpress\bi\App\library\Tools;
use FlashExpress\bi\App\Models\backyard\HireTypeImportListModel;
use FlashExpress\bi\App\Models\backyard\HrEntryModel;
use FlashExpress\bi\App\Models\backyard\HrInterviewOfferModel;
use FlashExpress\bi\App\Models\backyard\HrOfferSalaryOperatorLogModel;
use FlashExpress\bi\App\Models\backyard\HrResumeModel;

class OfferServer extends BaseServer
{
    public function __construct($lang = 'en')
    {
        parent::__construct($lang);
    }
    /**
     * Offer列表
     * @description 获取Offer列表数据，支持分页和导出功能
     * <AUTHOR>
     * @date 2025-08-28
     * @param array $params 查询参数
     * @param bool $isExport 是否导出
     * @return array
     */
    public function list($params, $isExport = false)
    {
        //定义分页熟悉
        $pageSize  = empty($params['page_size']) ? 20 : $params['page_size'];
        $pageNum   = empty($params['page_num']) ? 1 : $params['page_num'];
        $pageffset = $pageSize * ($pageNum - 1);

        //不是管理员 并且开启了菜单权限
        //生成数据库model对象
        $builder = $this->modelsManager->createBuilder();

        //组装参数
        $builder->from(['offer' => HrInterviewOfferModel::class]);
        $builder->columns('count(1) as total');
        $builder->innerJoin(HrEntryModel::class, 'offer.id=entry.interview_offer_id ', 'entry');
        $builder->leftJoin(HcModel::class, 'offer.hc_id=hc.hc_id ', 'hc');
        $builder->leftJoin(HrResumeModel::class, 'offer.resume_id=resume.id ', 'resume');

        //1发送 2取消发送 ，3-offer签字撤销，4-薪资审批撤销，5-offer签字驳回
//        $builder->where('offer.status = :status:', ['status' => 1]);

        //添加where条件
        $builder = $this->formatWhere($builder, $params);

        //设置排序
        $builder->orderBy('offer.id desc');

        $totalInfo = $builder->getQuery()->getSingleResult();
        $count     = $totalInfo->total;

        $columns = [
            'offer.resume_id as resume_id',
            'hc.hire_type',
            'offer.submitter_id as submitter_id',
            'entry.entry_date as entry_date', //到岗日期
            'entry.status as entry_status', //入职状态
            'entry.staff_id as staff_info_id', //工号
        ];

        if ($isExport) {
            $columns = array_merge($columns, [
                'money',    //试用期工资
                'trial_salary',    //通过试用期工资
                'position',    //岗位津贴
                'exp',    //经验津贴
                'rental',    //租车津贴
                'food',    //餐补
                'dangerously',//危险区域津贴
            ]);

            $builder->columns($columns);
            $builder->limit(10000);
            $items = $builder->getQuery()->execute()->toArray();
            return $this->formatInfo($items);
        }

        $builder->limit($pageSize, $pageffset);
        $builder->columns($columns);

        //获取结果
        $items = $builder->getQuery()->execute()->toArray();

        //格式化输出
        $items = $this->formatInfo($items);

        $data['data'] = [
            'dataList'   => $items,
            'pagination' => [
                'count'     => (string)$count,
                'pageNum'   => $pageNum, //每页多少条
                'pageSize'  => $pageSize,  //当前页
                'pageCount' => (string)ceil($count / $pageSize),  //总页数
            ],
        ];

        return $this->checkReturn($data);
    }

    /**
     * 格式化查询条件
     * @description 根据传入参数构建查询条件
     * <AUTHOR>
     * @date 2025-08-28
     * @param object $builder 查询构建器
     * @param array $params 查询参数
     * @return object
     */
    public function formatWhere($builder, $params)
    {
        //入职状态
        if (!empty($params['entry_status'])) {
            $builder->andWhere("entry.status = :entry_status:", ['entry_status' => $params['entry_status']]);
        }

        if (!empty($params['hire_type'])) {
            $builder->andWhere("hc.hire_type IN ({hire_type:array})", ['hire_type' => array_values($params['hire_type'])]);
        }

        return $builder;
    }

    /**
     * 格式化offer返回数据
     * @description 格式化Offer列表数据，添加相关联的员工、职位、部门等信息
     * <AUTHOR>
     * @date 2025-08-28
     * @param array $data 原始数据
     * @return array 格式化后的数据
     */
    public function formatInfo($data)
    {
        $sysListServer     = (new SysListServer());
        $sysListRepository = (new  SysListRepository());
        $resumeServer      = Tools::reBuildCountryInstance(new ResumeServer());
        $t                 = $this->getTranslation();
        $entry_status_text = [
            1 => $t->_('4903'),
            2 => $t->_('4904'),
            3 => $t->_('4905'),
        ];

        //查询用户operator_id
        $submitterIds                   = array_column($data, 'submitter_id');
        $resume_last_operator_staff_ids = array_column($data, 'resume_last_operator');

        $staffIds  = array_merge($submitterIds, $resume_last_operator_staff_ids);
        $staffIds  = array_values(array_unique($staffIds));
        $staffList = (new StaffServer())->getStaffListByIds($staffIds, [
            'staff_info_id',
            'name',
            'nick_name',
            'job_title',
        ]);

        $staffListKv = array_column($staffList, null, 'staff_info_id');


        //获取职位列表
        $jobTitleData = $sysListServer->getPositionList();
        $jobTitleListKv = array_column($jobTitleData, 'job_name', 'id');

        //查询部门表数据
        $departmentData   = $sysListRepository->getDepartmentList();
        $departmentListKv   = array_column($departmentData, 'name', 'id');

        //查询网点数据
        $storeData = [];
        $storeIds = array_values(array_unique(array_column($data, 'store_id')));
        if ($storeIds) {
            $storeIds    = getIdsStr($storeIds);
            $storeData = $sysListRepository->getStoreList(['ids' => $storeIds]);
            $storeData = array_column($storeData, null, 'id');
        }

        foreach ($data as & $v) {
            $v['created_at']        = show_time_zone($v['created_at']);
            //雇佣类型
            $v['hire_type_text'] = $v['hire_type'] ? $t->_('hire_type_' . $v['hire_type']) : '';

            //网点信息
            $storeInfo = $storeData[$v['store_id']] ?? [];

            //网点名称
            $v['store_name']   = $storeInfo['name'] ?? '';

            //所属区域
            $v['sorting_no'] = $resumeServer->getSortingNoByStoreInfo($storeInfo);

            //最新操作人职位
            $resumeLastOperatorInfo                   = $staffListKv[$v['resume_last_operator']] ?? [];
            $v['resume_last_operator_name']           = StaffServer::getStaffNameView($resumeLastOperatorInfo, 2);
            $v['resume_last_operator_job_title_name'] = $jobTitleListKv[$resumeLastOperatorInfo['job_title']] ?? '';

            //offer创建人职位
            $submitterInfo                      = $staffListKv[$v['submitter_id']] ?? [];
            $v['submitter_name']                = StaffServer::getStaffNameView($submitterInfo, 2);
            $v['submitter_name_job_title_name'] = $jobTitleListKv[$submitterInfo['job_title']] ?? '';
        }

        return $data;
    }

    /**
     * 删除任务
     * @description 删除指定的简历AI解析任务
     * <AUTHOR>
     * @date 2025-08-28
     * @param array $params 参数数组，包含id字段
     * @return mixed 删除结果
     * @throws ValidationException 参数验证异常
     */
    public function delete($params)
    {
        if (empty($params['id'])) {
            throw new ValidationException($this->getTranslation()->_('8402'));
        }

        $data = WinHrAsyncImportTaskRepository::getInfo([
            'id'          => $params['id'],
            'import_type' => WinHrAsyncImportTaskModel::IMPORT_TYPE_RESUME_AI_PARSE,
        ]);

        if (empty($data)) {
            throw new ValidationException($this->getTranslation()->_('8402'));
        }

        $data->is_deleted = enums::IS_DELETED;

        return $data->save();
    }

    /**
     * 解析简历批量上传数据
     * @description 处理简历解析任务，包括文件验证、状态更新、AI解析和结果存储
     * <AUTHOR>
     * @date 2025-08-28
     * @param array $params 参数数组
     * - type string 任务类型，必须为'resume_ai_parse'
     * - resume_ai_email_id int 简历解析记录ID
     * @return bool 处理结果，true表示处理成功
     * @throws ValidationException 参数验证异常
     * @throws BusinessException 业务处理异常
     * @throws Exception 系统异常
     */
    public function create($params): bool
    {
        $this->logger->write_log([
            'title'  => '解析简历批量上传数据',
            'opt'    => '开始',
            'func'   => __FUNCTION__,
            'params' => $params,
        ], 'info');

        $resumeAiRepository = (new ResumeAiRepository());
        //参数不正确
        if (
            empty($params['type']) || empty($params['resume_ai_email_id'])
        ) {
            throw new ValidationException($this->getTranslation()->_('missing_parameter'));
        }

        // 获取简历解析记录
        $emailInfo = $resumeAiRepository->getResumeAiEmailInfo($params['resume_ai_email_id']);

        // 验证数据是否存在,不存在重试
        if (empty($emailInfo) || $emailInfo['type'] != HrResumeAiEmailModel::TYPE_BATCH_UPLOAD) {
            throw new ValidationException($this->getTranslation()->_('8403'));
        }

        //开启事务进行数据存储
        $db = $this->getDI()->get('db');
        $db->begin();
        $resumeAiRepository = (new ResumeAiRepository());

        //主业务-合同修改
        try {
            //插入
            $updateData = [
                'id' => $params['resume_ai_email_id'],
                'parse_status' => HrResumeAiEmailModel::PARSE_STATUS_PENDING,
            ];

            //更新业务数据
            $res = (new ResumeAiRepository())->updateEmail($updateData);

            if (!$res) {
                throw new Exception('Resume Ai Parse Save Error ');
            }

            $this->logger->write_log([
                'title'  => '解析简历批量上传数据',
                'opt'    => '插入业务表完成',
                'func'   => __FUNCTION__,
                'result' => $updateData,
            ], 'info');

            //更新任务表
            if ($taskInfo['status'] == WinHrAsyncImportTaskModel::STATE_WAIT_EXECUTE) {
                $res = (new AsyncImportTaskServer())->updateTask([
                    'id' => $taskInfo['id'],
                ], WinHrAsyncImportTaskModel::STATE_PROGRESS);

                if (!$res) {
                    throw new Exception('Update Task Error ');
                }

                $this->logger->write_log([
                    'title'  => '解析简历批量上传数据',
                    'opt'    => '更新任务表完成',
                    'func'   => __FUNCTION__,
                    'result' => $updateData,
                ], 'info');
            }

            //更新AI业务表
            $res = $db->commit();

            if (!$res) {
                throw new Exception('Commit Save Error ');
            }
        } catch (Exception $e) {
            $db->rollback();
            throw $e;
        }

        return true;
    }

}
```

## modules层（多国家业务模块）说明

**职责**: Modules层是多国家业务的模块化架构层，负责按国家/地区划分业务逻辑，实现不同国家的本地化需求和特定业务规则。每个模块都是一个独立的业务单元，包含完整的MVC架构和业务逻辑处理能力。

**存放目录**: `app/modules`

**模块结构**: 按国家代码组织，目前支持的国家模块：
- `ID/` - 印尼（Indonesia）业务模块
- `LA/` - 老挝（Laos）业务模块  
- `MY/` - 马来西亚（Malaysia）业务模块
- `PH/` - 菲律宾（Philippines）业务模块
- `TH/` - 泰国（Thailand）业务模块
- `VN/` - 越南（Vietnam）业务模块

**命名规范**:
1. **模块目录**: 使用国家代码大写，例如：`MY`、`PH`、`TH`
2. **命名空间**: `namespace FlashExpress\bi\App\Modules\{CountryCode}\{Layer}`
3. **类命名**: 遵循各层的命名规范，在模块内保持一致性
4. **文件组织**: 每个模块内部采用标准的MVC+Service架构

**模块内部结构**:
{CountryCode}/
├── Module.php                    # 模块定义和自动加载配置
├── config/                       # 模块专用配置
│   └── config.php                    # 模块配置文件
├── controllers/                  # 模块控制器层
│   ├── ControllerBase.php            # 模块控制器基类
│   ├── API/                          # API子目录（可选）
│   └── *Controller.php               # 各业务控制器
├── library/                      # 模块专用类库
│   ├── enums.php                     # 模块枚举定义
│   ├── enumsContract.php             # 合同相关枚举
│   └── *Filter.php                   # 过滤器类
├── messages/                     # 多语言消息文件
│   ├── en.php                        # 英文语言包
│   ├── th.php                        # 泰语语言包
│   └── zh-CN.php                     # 中文语言包
├── models/                       # 模块专用模型（通常为空，使用全局模型）
├── server/                       # 模块业务逻辑层
│   ├── BaseServer.php                # 模块服务基类
│   └── *Server.php                   # 各业务服务类
└── tasks/                        # 模块任务脚本
└── *Task.php                     # 定时任务和批处理脚本


**规范要求**:

### 1. 模块定义规范
- 每个模块必须包含 `Module.php` 文件，实现 `ModuleDefinitionInterface` 接口
- 必须正确配置命名空间自动加载
- 支持模块级别的配置覆盖

### 2. 控制器规范
- 模块控制器必须继承模块内的 `ControllerBase`
- 模块 `ControllerBase` 必须继承全局 `ControllerBase`
- 实现国家特定的业务逻辑和验证规则
- 处理本地化的数据格式和验证

### 3. 业务逻辑规范
- 模块内的 `Server` 类处理国家特定的业务逻辑
- 必须继承模块内的 `BaseServer`
- 实现本地化的业务规则和流程
- 处理国家特定的第三方集成

### 4. 本地化规范
- 支持多语言消息文件
- 实现国家特定的数据格式验证
- 处理本地化的日期、货币、地址格式
- 支持国家特定的业务流程

### 5. 配置管理
- 模块配置可以覆盖全局配置
- 支持国家特定的参数设置
- 环境变量和敏感信息的安全管理


# 单元测试规范说明

## 测试文件存储位置规范

### 1. 全局层级测试文件位置
- **server层测试**: `app/server/` 下的类测试文件要放到 `tests/unit/server/` 目录下
- **repository层测试**: `app/repository/` 下的类测试文件要放到 `tests/unit/repository/` 目录下
- **controller层测试**: `app/controllers/` 下的类测试文件要放到 `tests/unit/controllers/` 目录下
- **helper层测试**: `app/helper/` 下的类测试文件要放到 `tests/unit/helper/` 目录下
- **library层测试**: `app/library/` 下的类测试文件要放到 `tests/unit/library/` 目录下

### 2. 模块层级测试文件位置
- **泰国模块**: `app/modules/TH/server/` 下的类单元测试放到 `tests/unit/modules/th/server/` 下
- **菲律宾模块**: `app/modules/PH/server/` 下的类单元测试放到 `tests/unit/modules/ph/server/` 下
- **越南模块**: `app/modules/VN/server/` 下的类单元测试放到 `tests/unit/modules/vn/server/` 下
- **老挝模块**: `app/modules/LA/server/` 下的类单元测试放到 `tests/unit/modules/la/server/` 下
- **印尼模块**: `app/modules/ID/server/` 下的类单元测试放到 `tests/unit/modules/id/server/` 下
- **马来西亚模块**: `app/modules/MY/server/` 下的类单元测试放到 `tests/unit/modules/my/server/` 下

## 命名空间和继承规范

### 1. 命名空间规范
- **全局测试类**: 使用 `tests\unit\{layer}` 为命名空间前缀，全部小写
  - 例如: `tests\unit\server`、`tests\unit\repository`
- **模块测试类**: 使用 `tests\unit\modules\{country}\{layer}` 为命名空间前缀
  - 例如: `tests\unit\modules\th\server`

### 2. 类命名规范
- 单元测试类名称和测试的类同名，后面加上 `Test` 后缀
- 例如: `HrStaffServer` 的测试类为 `HrStaffServerTest`
- 例如: `ApprovalRepository` 的测试类为 `ApprovalRepositoryTest`

### 3. 继承关系
- 所有单元测试类必须继承 `UnitTestCase` 类
- 同级目录下的类不需要额外引入

## 测试开发规范

### 1. 数据准备规范
- 遇到需要提供测试数据时，连接MCP模块的mysql服务
- 数据库名称为 `backyard`
- 使用真实数据进行测试，确保测试的有效性
- 测试数据应覆盖正常、边界和异常情况

### 2. 业务理解规范
- 遇到需要了解业务逻辑时，连接MCP模块的dify服务进行查询
- 确保测试用例覆盖核心业务场景
- 理解业务规则后编写相应的断言

### 3. 复杂方法处理规范
- 遇到代码超过1000行的方法且业务复杂时，连接MCP模块sequential-thinking服务进行拆解
- 将复杂测试场景分解为多个简单的测试用例
- 每个测试用例专注于测试一个特定功能点

### 4. 代码修改限制
- 所有的更改必须在 `tests/unit/` 目录及其子目录下进行
- 不可以改动其他文件下的代码
- 保持测试代码与业务代码的分离

### 5. 注释规范
- 单元测试逻辑代码注释需大于40%
- 每个测试方法必须包含详细的注释说明
- 注释应包括：测试目的、测试场景、预期结果
- 复杂的测试逻辑需要逐步注释

## 编码规则

### 1. 时间处理规范
- 所有时间使用UTC时区
- 测试中涉及时间比较时，统一使用UTC时间
- 时间格式统一使用 `Y-m-d H:i:s` 格式

### 2. 文件处理规范
- 所有文件上传测试适应分布式部署
- 使用OSS存储进行文件相关测试
- 测试文件路径使用相对路径

### 3. 异常处理规范
- 所有try代码块要分别验证业务错误和系统错误
- 测试异常场景时，使用 `expectException` 方法
- 验证异常消息的准确性

### 4. 数据库操作规范
- 遇到SQL操作，要测试是否正确使用索引
- 使用 `EXPLAIN` 语句验证查询性能
- 测试数据库操作的事务一致性

### 5. 并发测试规范
- 测试接口要检查是否正确加原子锁
- 模拟并发场景进行压力测试
- 验证数据一致性和完整性

### 6. SQL安全规范
- SQL不能拼接，必须使用参数绑定
- 测试SQL注入防护机制
- 使用 `insertAsDict`、`updateAsDict`、`delete` 方法，不允许 `execute`
- 参考Phalcon数据库抽象层最佳实践

### 7. 查询优化规范
- SQL中使用 `IN` 进行范围查询时，检查元素数量控制在500以内
- 测试大数据量查询的性能表现
- 验证分页查询的正确性

### 8. 代码标准规范
- 严格遵守PSR-4自动加载规范
- 代码风格遵循PSR-12编码标准
- 使用静态代码分析工具检查代码质量

## 基本代码规范

### 1. 命名规范
- **类属性命名**: 小写开头的驼峰式 (`$camelCase`)
- **类方法命名**: 小写开头的驼峰式 (`getUserInfo`)
- **方法参数命名**: 使用下划线分隔式 (`$user_id`)
- **测试方法命名**: `test` + 功能描述，使用驼峰式 (`testGetUserInfo`)

### 2. 类管理规范
- 遇到相同名称的类，在类内部更新方法，不覆盖源文件
- 使用版本控制管理测试代码变更
- 保持测试类的单一职责原则

### 3. 测试方法规范
- 每个测试方法只测试一个功能点
- 测试方法名要清晰表达测试意图
- 使用 `setUp()` 和 `tearDown()` 方法管理测试环境

## 测试类型和策略

### 1. 单元测试
- 测试单个方法或函数的功能
- 使用Mock对象隔离外部依赖
- 覆盖正常流程、边界条件和异常情况

### 2. 集成测试
- 测试多个组件之间的交互
- 验证数据流的正确性
- 测试第三方服务集成

### 3. 功能测试
- 测试完整的业务流程
- 验证用户场景的正确性
- 端到端的功能验证

### 4. 性能测试
- 测试关键方法的执行时间
- 验证内存使用情况
- 数据库查询性能测试

## 测试工具和框架

### 1. PHPUnit配置
- 使用项目根目录下的 `phpunit.xml` 配置文件
- 配置测试覆盖率报告
- 设置测试数据库连接

### 2. Mock和Stub
- 使用PHPUnit的Mock功能模拟外部依赖
- 创建测试替身隔离测试环境
- 验证方法调用次数和参数

### 3. 数据提供者
- 使用 `@dataProvider` 注解提供测试数据
- 创建可重用的测试数据集
- 支持参数化测试

## 代码示例

### 1. 基础测试类示例
```php
<?php
/**
 * 员工服务测试类
 * @description: 测试员工相关业务逻辑
 * @author: AI
 * @date: 2024-01-15 10:30:00
 */
namespace tests\unit\server;

use FlashExpress\bi\App\Server\HrStaffServer;
use FlashExpress\bi\App\Models\backyard\HrStaffInfoModel;

class HrStaffServerTest extends UnitTestCase
{
    /**
     * 测试前置设置
     * 初始化测试环境和依赖
     */
    public function setUp(): void
    {
        parent::setUp();
        // 初始化测试数据
        $this->initTestData();
    }
    
    /**
     * 测试获取员工信息 - 正常情况
     * @description: 验证根据员工ID获取员工信息的功能
     * @return void
     */
    public function testGetHrStaffInfoSuccess()
    {
        // 准备测试数据
        $staffId = 17245;
        
        // 执行测试方法
        $staffServer = new HrStaffServer();
        $staffInfo = $staffServer->getHrStaffInfo($staffId);
        
        // 验证返回结果
        $this->assertIsArray($staffInfo, '返回结果应该是数组');
        $this->assertArrayHasKey('staff_info_id', $staffInfo, '应该包含员工ID字段');
        $this->assertEquals($staffId, $staffInfo['staff_info_id'], '员工ID应该匹配');
        $this->assertArrayHasKey('staff_name', $staffInfo, '应该包含员工姓名字段');
        $this->assertNotEmpty($staffInfo['staff_name'], '员工姓名不应该为空');
    }
    
    /**
     * 测试获取员工信息 - 员工不存在
     * @description: 验证查询不存在员工时的处理
     * @return void
     */
    public function testGetHrStaffInfoNotFound()
    {
        // 准备测试数据 - 使用不存在的员工ID
        $nonExistentStaffId = 999999;
        
        // 执行测试方法
        $staffServer = new HrStaffServer();
        $staffInfo = $staffServer->getHrStaffInfo($nonExistentStaffId);
        
        // 验证返回结果
        $this->assertEmpty($staffInfo, '不存在的员工应该返回空结果');
    }
    
    /**
     * 测试获取员工信息 - 参数异常
     * @description: 验证传入无效参数时的异常处理
     * @return void
     */
    public function testGetHrStaffInfoInvalidParameter()
    {
        // 测试无效的员工ID
        $invalidStaffIds = [0, -1, null, '', 'invalid'];
        
        $staffServer = new HrStaffServer();
        
        foreach ($invalidStaffIds as $invalidId) {
            // 验证异常抛出
            $this->expectException(\InvalidArgumentException::class);
            $staffServer->getHrStaffInfo($invalidId);
        }
    }
    
    /**
     * 测试数据提供者示例
     * @return array 测试数据集
     */
    public function staffDataProvider(): array
    {
        return [
            '正常员工' => [17245, true],
            '另一个员工' => [22000, true],
            '不存在员工' => [999999, false],
        ];
    }
    
    /**
     * 使用数据提供者的测试方法
     * @dataProvider staffDataProvider
     * @param int $staffId 员工ID
     * @param bool $shouldExist 是否应该存在
     */
    public function testGetHrStaffInfoWithDataProvider(int $staffId, bool $shouldExist)
    {
        $staffServer = new HrStaffServer();
        $staffInfo = $staffServer->getHrStaffInfo($staffId);
        
        if ($shouldExist) {
            $this->assertNotEmpty($staffInfo, "员工ID {$staffId} 应该存在");
            $this->assertArrayHasKey('staff_info_id', $staffInfo);
        } else {
            $this->assertEmpty($staffInfo, "员工ID {$staffId} 不应该存在");
        }
    }
    
    /**
     * 初始化测试数据
     * @return void
     */
    private function initTestData(): void
    {
        // 清理测试数据
        $this->cleanTestData();
        
        // 创建测试数据
        // 这里可以插入必要的测试数据
    }
    
    /**
     * 清理测试数据
     * @return void
     */
    private function cleanTestData(): void
    {
        // 清理可能影响测试的数据
        // 确保测试环境的干净
    }
    
    /**
     * 测试后置清理
     * 清理测试环境
     */
    public function tearDown(): void
    {
        // 清理测试数据
        $this->cleanTestData();
        parent::tearDown();
    }
}
```