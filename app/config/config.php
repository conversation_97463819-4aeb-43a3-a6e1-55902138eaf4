<?php

/*
 * Modified: prepend directory path of current file, because of this file own different ENV under between Apache and command line.
 * NOTE: please remove this comment.
 */
defined('BASE_PATH') || define('BASE_PATH', getenv('BASE_PATH') ?: realpath(dirname(__FILE__) . '/../..'));
defined('APP_PATH') || define('APP_PATH', BASE_PATH . '/app');

return new \Phalcon\Config(
    [
        'database' => [
            'adapter' => env('database_adapter', 'Mysql'),
            'host' => env('database_host', '*************'),
            'username' => env('database_username', 'backyard'),
            'password' => env('database_password', '123456'),
            'dbname' => env('database_dbname', 'backyard'),
            'charset' => 'utf8',
        ],
        'database_rby' => [
            'adapter' => env('database_rby_adapter', 'Mysql'),
            'host' => env('database_rby_host', '*************'),
            'username' => env('database_rby_username', 'backyard'),
            'password' => env('database_rby_password', '123456'),
            'dbname' => env('database_rby_dbname', 'backyard'),
            'charset' => env('database_rby_charset', 'utf8'),
        ],
        'database_fle' => [
            'adapter' => env('database_fle_adapter', 'Mysql'),
            'host' => env('database_fle_host', '*************'),
            'username' => env('database_fle_username', 'fle_development'),
            'password' => env('database_fle_password', '123456'),
            'dbname' => env('database_fle_dbname', 'fle_development'),
            'charset' => 'utf8',
        ],
        'database_bi' => [
            'adapter' => env('database_bi_adapter', 'Mysql'),
            'host' => env('database_bi_host', '*************'),
            'username' => env('database_bi_username', 'bi'),
            'password' => env('database_bi_password', '123456'),
            'dbname' => env('database_bi_dbname', 'bi'),
            'charset' => 'utf8',
        ],
        'database_rbi' => [
            'adapter' => env('database_rbi_adapter', 'Mysql'),
            'host' => env('database_rbi_host', '*************'),
            'username' => env('database_rbi_username', 'bi'),
            'password' => env('database_rbi_password', '123456'),
            'dbname' => env('database_rbi_dbname', 'bi'),
            'charset' => 'utf8',
        ],
        'database_message_write' => [
            'adapter'     => env('database_coupon_adapter','Mysql'),
            'host'        => env('database_coupon_host','*************'),
            'port'        => env('database_coupon_port', '3306'),
            'username'    => env('database_coupon_username','coupon'),
            'password'    => env('database_coupon_password','123456'),
            'dbname'      => env('database_coupon_dbname','coupon'),
            'charset'     => env('database_coupon_charset','utf8mb4'),
        ],
        'database_message_read' => [
            'adapter'     => env('database_coupon_r_adapter','Mysql'),
            'host'        => env('database_coupon_r_host','*************'),
            'port'        => env('database_coupon_r_port', '3306'),
            'username'    => env('database_coupon_r_username','coupon'),
            'password'    => env('database_coupon_r_password','123456'),
            'dbname'      => env('database_coupon_r_dbname','coupon'),
            'charset'     => env('database_coupon_r_charset','utf8mb4'),
        ],
        'database_backyard' => [
            'adapter' => env('database_backyard_adapter', 'Mysql'),
            'host' => env('database_backyard_host', '*************'),
            'username' => env('database_backyard_username', 'backyard'),
            'password' => env('database_backyard_password', '123456'),
            'dbname' => env('database_backyard_dbname', 'backyard'),
            'charset' => 'utf8',
        ],
        'application' => [
            'app_name' => 'backyard',
            'appDir' => APP_PATH . '/',
            'configDir' => APP_PATH . '/config/',
            'controllersDir' => APP_PATH . '/controllers/',
            'modelsDir' => APP_PATH . '/models/',
            'viewsDir' => APP_PATH . '/views/',
            'pluginsDir' => APP_PATH . '/plugins/',
            'libraryDir' => APP_PATH . '/library/',
            'helperDir' => APP_PATH . '/helper/',
            'cacheDir' => BASE_PATH . '/cache/',
            'cacheDirTmp' => BASE_PATH . '/cache/tmp',
            'runtimeDir' => APP_PATH . '/runtime/',
            'runtimeCacheDir' => BASE_PATH . '/cache/apidata/',
            'excelCacheDir' => BASE_PATH . '/cache/excel/',
            'coreDir' => APP_PATH . '/core/',
            'tasksDir' => APP_PATH . '/tasks/',
            'traitsDir' => APP_PATH . '/traits/',
            'serverDir' => APP_PATH . '/server/',
            'repositoryDir' => APP_PATH . '/repository/',
            'DestructTime' => env('application_DestructTime', 600),//单位秒,计划作业回收间隔时间
            'MasInputTime' => env('application_MasInputTime', 300),//单位秒 系统定义的最大允许录入时间
            'DomainName' => env('DomainName', 'bi.flashexpress.com'),//域名
            'pre_url' => env('pre_url', 'http://bi.flashexpress.com/'),//url前缀
            'Languages' => ['th', 'en', 'zh-CN','zh'],
            'baseUri' => '/',
            'staticUri' => '/',//后台静态资源URL
            'timeZoneOfThailand' => env('timeZone', '+07:00'),
            'timeZone' => env('timeZone', '+07:00'),
            'mailAddress' => '<EMAIL>',//邮箱
            'mailPass' => 'Express123',//邮箱密码
            'img_prefix' => env('img_prefix', 'http://fle-staging-asset-internal.oss-ap-southeast-1.aliyuncs.com/'),//
            'async_task' => env('async_task', false),
            'reissueCardTime' => 3600 * 16,//16小时
            'authenticate' => env('auth_key', 'auth'),
            'timeZoneGMT' => env('timeZoneGMT','Etc/GMT-7'),
            'add_hour' => env('add_hour', 7),
            'mobile_area_code' => env('mobile_area_code', '66'),
            'country_code' => env('country_code', 'TH'),
        ],
        'bucket_name' => env('bucket_name', 'fle-staging-asset-internal'),
        'redis' => [
            'host' => env('redis_host', '*************'),
            'lifetime' => env('redis_lifetime', 24 * 60 * 60),
            'port' => env('redis_port', '6379'),
            'prefix' => env('redis_prefix', 'bk_'),
            'auth' => env('redis_auth'),
            'open' => env('redis_open', true),
            'index' => env('redis_index', 1),
        ],
        'mongodb' => [
            'host' => env('mongodb_host', '*************'),
            'port' => env('mongodb_port', '27017'),
            'user' => env('mongodb_admin', 'admin'),
            'pwd' => env('mongodb_pwd', 'admin'),
        ],
        'api' => [
            //登陆验证
            'api_verifytoken' => env(
                'jsonrpc_uri_verifytoken',
                'http://*************:8090/fle-svc/com.flashexpress.fle.svc.api.CustomerAuthSvc'
            ),
            'get_staff_info_by_token' => env(
                'get_staff_info_by_token',
                'http://*************:8090/fle-svc/com.flashexpress.fle.svc.api.StaffAuthSvc'
            ),
            'api_img_upload' => env(
                'api_img_upload',
                'http://*************:8090/fle-svc/com.flashexpress.fle.svc.api.OssSvc'
            ),
            'api_send_sms' => env('api_send_sms', 'http://*************:8003/rpc/sms'),
            'api_form_pdf' => env('api_pdf','https://dev01-la-pdf-svc.fex.pub'),//form 生成pdf接口
        ],
        'hr' => [
            'ceo' => env('hr_ceo'),
            'coo' => env('hr_coo'),
            'hrm' => env('hr_hrm'),
            'bi_url' => env('hr_bi_url'),
        ],
        'hc' => [
            'hc_by_url' => env('hc_by_url', 'http://backyard.test.com:8099'),
            'roleDepartmentIds' => env('roleDepartmentIds', '7'),
            'roleNullifyIds' => env('roleNullifyIds', '22203'),
        ],
        'excel_rpc' => env('excel_rpc', 'http://*************:8882/rpc'),
        'rocket_mq' => [
            'http_endpoint'        => env('rocket_mq_http_endpoint',
                'http://1956905707885195.mqrest.cn-qingdao-public.aliyuncs.com'),
            'access'               => env('rocket_mq_access_key', 'LTAI1YjpUu5SL6fD'),
            'secret'               => env('rocket_mq_secret_key', 'xrYCCGepKDZlYWr86D3GYxvQYUwjuz'),
            'staff_leave_group_id' => env('rocket_mq_staff_leave_group_id', 'GID-dev-hris-hr-staff-leave'),
            'staff_leave_topic'    => env('rocket_mq_staff_leave_topic', 'dev-hris-hr-staff-leave'),
            'instance_id'          => env('rocket_mq_instance_id', 'MQ_INST_1956905707885195_BauvlVUQ'),
        ],
    ]
);
